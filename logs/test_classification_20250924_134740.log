2025-09-24 13:47:40,389 - INFO - Logging initialized. Log file: logs/test_classification_20250924_134740.log
2025-09-24 13:47:40,390 - INFO - 📁 Found 9 files to process
2025-09-24 13:47:40,390 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 13:47:40,390 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 13:47:40,390 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 13:47:40,390 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 13:47:40,390 - INFO - ⬆️ [13:47:40] Uploading: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:47:41,984 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf -> s3://document-extraction-logistically/temp/f2b4defd_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:47:41,984 - INFO - 🔍 [13:47:41] Starting classification: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:47:41,985 - INFO - ⬆️ [13:47:41] Uploading: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:47:41,985 - INFO - Initializing TextractProcessor...
2025-09-24 13:47:41,997 - INFO - Initializing BedrockProcessor...
2025-09-24 13:47:42,003 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f2b4defd_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:47:42,003 - INFO - Processing PDF from S3...
2025-09-24 13:47:42,004 - INFO - Downloading PDF from S3 to /tmp/tmpkz_oul6i/f2b4defd_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:47:42,627 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf -> s3://document-extraction-logistically/temp/83d89b14_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:47:42,628 - INFO - 🔍 [13:47:42] Starting classification: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:47:42,629 - INFO - ⬆️ [13:47:42] Uploading: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:47:42,629 - INFO - Initializing TextractProcessor...
2025-09-24 13:47:42,644 - INFO - Initializing BedrockProcessor...
2025-09-24 13:47:42,646 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/83d89b14_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:47:42,646 - INFO - Processing PDF from S3...
2025-09-24 13:47:42,646 - INFO - Downloading PDF from S3 to /tmp/tmp94zvr4og/83d89b14_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:47:43,552 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf -> s3://document-extraction-logistically/temp/01011bf0_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:47:43,552 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:47:43,553 - INFO - 🔍 [13:47:43] Starting classification: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:47:43,553 - INFO - Splitting PDF into individual pages...
2025-09-24 13:47:43,554 - INFO - Initializing TextractProcessor...
2025-09-24 13:47:43,554 - INFO - ⬆️ [13:47:43] Uploading: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:47:43,564 - INFO - Splitting PDF f2b4defd_B3SIRREC9IAVZOJVDQSN into 1 pages
2025-09-24 13:47:43,567 - INFO - Initializing BedrockProcessor...
2025-09-24 13:47:43,576 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/01011bf0_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:47:43,576 - INFO - Processing PDF from S3...
2025-09-24 13:47:43,577 - INFO - Split PDF into 1 pages
2025-09-24 13:47:43,577 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:47:43,577 - INFO - Expected pages: [1]
2025-09-24 13:47:43,580 - INFO - Downloading PDF from S3 to /tmp/tmpsyi2uwpk/01011bf0_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:47:44,163 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:47:44,163 - INFO - Splitting PDF into individual pages...
2025-09-24 13:47:44,165 - INFO - Splitting PDF 83d89b14_CUF54EHGMLQ57HR93DRB into 1 pages
2025-09-24 13:47:44,175 - INFO - Split PDF into 1 pages
2025-09-24 13:47:44,176 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:47:44,176 - INFO - Expected pages: [1]
2025-09-24 13:47:44,773 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf -> s3://document-extraction-logistically/temp/5572a332_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:47:44,774 - INFO - 🔍 [13:47:44] Starting classification: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:47:44,775 - INFO - ⬆️ [13:47:44] Uploading: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:47:44,777 - INFO - Initializing TextractProcessor...
2025-09-24 13:47:44,803 - INFO - Initializing BedrockProcessor...
2025-09-24 13:47:44,811 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5572a332_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:47:44,811 - INFO - Processing PDF from S3...
2025-09-24 13:47:44,812 - INFO - Downloading PDF from S3 to /tmp/tmpsxm_swgt/5572a332_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:47:45,141 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:47:45,141 - INFO - Splitting PDF into individual pages...
2025-09-24 13:47:45,144 - INFO - Splitting PDF 01011bf0_FYQQGIW8Z9DSAPCL0S9G into 1 pages
2025-09-24 13:47:45,146 - INFO - Split PDF into 1 pages
2025-09-24 13:47:45,146 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:47:45,146 - INFO - Expected pages: [1]
2025-09-24 13:47:45,439 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf -> s3://document-extraction-logistically/temp/dd8c7488_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:47:45,440 - INFO - 🔍 [13:47:45] Starting classification: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:47:45,441 - INFO - ⬆️ [13:47:45] Uploading: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:47:45,447 - INFO - Initializing TextractProcessor...
2025-09-24 13:47:45,460 - INFO - Initializing BedrockProcessor...
2025-09-24 13:47:45,465 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dd8c7488_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:47:45,465 - INFO - Processing PDF from S3...
2025-09-24 13:47:45,465 - INFO - Downloading PDF from S3 to /tmp/tmpezhwm101/dd8c7488_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:47:46,400 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf -> s3://document-extraction-logistically/temp/fed69de5_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:47:46,400 - INFO - 🔍 [13:47:46] Starting classification: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:47:46,401 - INFO - ⬆️ [13:47:46] Uploading: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:47:46,404 - INFO - Initializing TextractProcessor...
2025-09-24 13:47:46,424 - INFO - Initializing BedrockProcessor...
2025-09-24 13:47:46,428 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fed69de5_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:47:46,428 - INFO - Processing PDF from S3...
2025-09-24 13:47:46,430 - INFO - Downloading PDF from S3 to /tmp/tmpz3zxnhh4/fed69de5_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:47:47,023 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf -> s3://document-extraction-logistically/temp/da4af933_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:47:47,023 - INFO - 🔍 [13:47:47] Starting classification: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:47:47,024 - INFO - ⬆️ [13:47:47] Uploading: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:47:47,024 - INFO - Initializing TextractProcessor...
2025-09-24 13:47:47,037 - INFO - Initializing BedrockProcessor...
2025-09-24 13:47:47,087 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/da4af933_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:47:47,088 - INFO - Processing PDF from S3...
2025-09-24 13:47:47,088 - INFO - Downloading PDF from S3 to /tmp/tmpu381w6gq/da4af933_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:47:47,188 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 13:47:47,188 - INFO - Splitting PDF into individual pages...
2025-09-24 13:47:47,190 - INFO - Splitting PDF 5572a332_IZTBXFPGXBFH3DV900G4 into 1 pages
2025-09-24 13:47:47,191 - INFO - Split PDF into 1 pages
2025-09-24 13:47:47,191 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:47:47,192 - INFO - Expected pages: [1]
2025-09-24 13:47:47,549 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:47:47,549 - INFO - Splitting PDF into individual pages...
2025-09-24 13:47:47,550 - INFO - Splitting PDF dd8c7488_MR6ONA8GK6HN1LCZHEX3 into 1 pages
2025-09-24 13:47:47,551 - INFO - Split PDF into 1 pages
2025-09-24 13:47:47,551 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:47:47,551 - INFO - Expected pages: [1]
2025-09-24 13:47:47,629 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf -> s3://document-extraction-logistically/temp/3a2742a5_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:47:47,629 - INFO - 🔍 [13:47:47] Starting classification: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:47:47,630 - INFO - ⬆️ [13:47:47] Uploading: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:47:47,630 - INFO - Initializing TextractProcessor...
2025-09-24 13:47:47,643 - INFO - Initializing BedrockProcessor...
2025-09-24 13:47:47,646 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3a2742a5_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:47:47,646 - INFO - Processing PDF from S3...
2025-09-24 13:47:47,647 - INFO - Downloading PDF from S3 to /tmp/tmp1xzi5t99/3a2742a5_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:47:48,273 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf -> s3://document-extraction-logistically/temp/498d9419_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:47:48,274 - INFO - 🔍 [13:47:48] Starting classification: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:47:48,276 - INFO - Initializing TextractProcessor...
2025-09-24 13:47:48,289 - INFO - Initializing BedrockProcessor...
2025-09-24 13:47:48,294 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/498d9419_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:47:48,294 - INFO - Processing PDF from S3...
2025-09-24 13:47:48,295 - INFO - Downloading PDF from S3 to /tmp/tmpp7960c0j/498d9419_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:47:48,793 - INFO - Page 1: Extracted 1454 characters, 85 lines from f2b4defd_B3SIRREC9IAVZOJVDQSN_a2aa80b8_page_001.pdf
2025-09-24 13:47:48,793 - INFO - Successfully processed page 1
2025-09-24 13:47:48,794 - INFO - Combined 1 pages into final text
2025-09-24 13:47:48,795 - INFO - Text validation for f2b4defd_B3SIRREC9IAVZOJVDQSN: 1471 characters, 1 pages
2025-09-24 13:47:48,795 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:47:48,795 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:47:48,795 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 13:47:48,799 - INFO - Splitting PDF into individual pages...
2025-09-24 13:47:48,801 - INFO - Splitting PDF fed69de5_PB67IAPSJB1DZWMDIE1H into 2 pages
2025-09-24 13:47:48,805 - INFO - Split PDF into 2 pages
2025-09-24 13:47:48,805 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:47:48,805 - INFO - Expected pages: [1, 2]
2025-09-24 13:47:48,872 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:47:48,872 - INFO - Splitting PDF into individual pages...
2025-09-24 13:47:48,873 - INFO - Splitting PDF da4af933_PEE2ZFMV7X0A0FL35G4G into 1 pages
2025-09-24 13:47:48,874 - INFO - Split PDF into 1 pages
2025-09-24 13:47:48,874 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:47:48,874 - INFO - Expected pages: [1]
2025-09-24 13:47:48,891 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:47:48,891 - INFO - Splitting PDF into individual pages...
2025-09-24 13:47:48,893 - INFO - Splitting PDF 3a2742a5_U7BB1XSF3ASMIAE1MQ5I into 1 pages
2025-09-24 13:47:48,895 - INFO - Split PDF into 1 pages
2025-09-24 13:47:48,895 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:47:48,895 - INFO - Expected pages: [1]
2025-09-24 13:47:49,806 - INFO - Page 1: Extracted 1255 characters, 79 lines from 83d89b14_CUF54EHGMLQ57HR93DRB_c77cfe40_page_001.pdf
2025-09-24 13:47:49,807 - INFO - Successfully processed page 1
2025-09-24 13:47:49,807 - INFO - Combined 1 pages into final text
2025-09-24 13:47:49,807 - INFO - Text validation for 83d89b14_CUF54EHGMLQ57HR93DRB: 1272 characters, 1 pages
2025-09-24 13:47:49,807 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:47:49,807 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:47:50,130 - INFO - Page 1: Extracted 1463 characters, 95 lines from 01011bf0_FYQQGIW8Z9DSAPCL0S9G_5cf4b84a_page_001.pdf
2025-09-24 13:47:50,130 - INFO - Successfully processed page 1
2025-09-24 13:47:50,130 - INFO - Combined 1 pages into final text
2025-09-24 13:47:50,131 - INFO - Text validation for 01011bf0_FYQQGIW8Z9DSAPCL0S9G: 1480 characters, 1 pages
2025-09-24 13:47:50,131 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:47:50,131 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:47:50,641 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 13:47:50,641 - INFO - Splitting PDF into individual pages...
2025-09-24 13:47:50,643 - INFO - Splitting PDF 498d9419_YL4HZPZH7D3B03FZZ5QK into 1 pages
2025-09-24 13:47:50,662 - INFO - Split PDF into 1 pages
2025-09-24 13:47:50,662 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:47:50,663 - INFO - Expected pages: [1]
2025-09-24 13:47:51,983 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b569f27b-afee-457d-b26a-3188fbd6c357', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:17:51 GMT', 'content-type': 'application/json', 'content-length': '993', 'connection': 'keep-alive', 'x-amzn-requestid': 'b569f27b-afee-457d-b26a-3188fbd6c357'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to identify if it is weight_and_inspection_cert, nmfc_cert, inspection_cert, etc. The text includes "We certify that this shipment has been inspected and/or reweighed" and "Weight and Research Section". It also mentions "DECLARED VALUE", "Tariff Authority", "Density", "Cube". This looks like a Weight and Inspection Certificate. It includes weight details, maybe weight and inspection cert. It does not mention NMFC specific language. So weight_and_inspection_cert. We need to output using function. Provide array of document with page_no:1 doc_type: "weight_and_inspection_cert".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2901, 'outputTokens': 161, 'totalTokens': 3062}, 'metrics': {'latencyMs': 975}}
2025-09-24 13:47:51,984 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/83d89b14_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:47:52,005 - INFO - 

CUF54EHGMLQ57HR93DRB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:47:52,005 - INFO - 

✓ Saved result: output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:47:52,118 - INFO - Page 1: Extracted 500 characters, 76 lines from dd8c7488_MR6ONA8GK6HN1LCZHEX3_1a1fd3de_page_001.pdf
2025-09-24 13:47:52,118 - INFO - Successfully processed page 1
2025-09-24 13:47:52,118 - INFO - Combined 1 pages into final text
2025-09-24 13:47:52,119 - INFO - Text validation for dd8c7488_MR6ONA8GK6HN1LCZHEX3: 517 characters, 1 pages
2025-09-24 13:47:52,119 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:47:52,119 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:47:52,184 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '37c9826a-9c91-45d4-9ae7-f70f3619c8f0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:17:52 GMT', 'content-type': 'application/json', 'content-length': '821', 'connection': 'keep-alive', 'x-amzn-requestid': '37c9826a-9c91-45d4-9ae7-f70f3619c8f0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content looks like an NMFC Classification Certificate. Keywords: "NMFC Classification Certificate" is present. It\'s a certificate. According to enum details, nmfc_cert is NMFC Classification Certificate or Correction notice. So categorize as nmfc_cert. There\'s no continuation signals. So output single page with doc_type nmfc_cert.\n\nWe need to output via tool call classify_logistics_doc_type with documents array containing one object: page_no:1, doc_type:"nmfc_cert".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3018, 'outputTokens': 121, 'totalTokens': 3139}, 'metrics': {'latencyMs': 797}}
2025-09-24 13:47:52,184 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/01011bf0_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:47:52,303 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/83d89b14_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:47:52,304 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '18a079dc-98d1-4e73-ab85-dc31ebeef9f3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:17:52 GMT', 'content-type': 'application/json', 'content-length': '2406', 'connection': 'keep-alive', 'x-amzn-requestid': '18a079dc-98d1-4e73-ab85-dc31ebeef9f3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. Determine doc_type. The content looks like a "Weight and Research Program" document. Keywords: "Weight and Research Program", "Weight", "Original Correction", "As found upon Examination", "We certify that this shipment has been inspected and/or reweighed". Also mentions "UPD CLASS PER NMFC 56840 ON BOL". It seems like a weight and inspection certificate? The doc type that matches is weight_and_inspection_cert. The instructions: For weight_and_inspection_cert, if it contains weight_and_inspection_cert keywords but also has nmfc_cert keywords, classify as nmfc_cert. The doc says "UPD CLASS PER NMFC 56840 ON BOL". That might indicate NMFC classification. Also "UPD CLASS PER NMFC 56840 ON BOL" is a note. But does it qualify as nmfc_cert? The nmfc_cert description: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued. Keywords: As described and As found or Original and inspection or Corrected class or Correction information. Other keywords: NMFC Code, class #." The doc has "Original Correction" at top: "Date of Issuing Original Correction". That indicates a correction? Also "UPD CLASS PER NMFC 56840 ON BOL". This seems like a correction or update. So maybe nmfc_cert. But also weight_and_inspection_cert is about certificate confirming weight. This doc includes weight details: "Weight", "Gross", "Net"? It has "Original Correction", "As found upon Examination". It also certifies inspection. This matches weight_and_inspection_cert. But also has nmfc correction. The instruction: "If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert". So we need to check if it has nmfc_cert keywords. It has "UPD CLASS PER NMFC 56840 ON BOL" which mentions NMFC. Also "Original Correction". That might count as "Original and inspection" or "Correction information". So we should classify as nmfc_cert.\n\nThus page1: doc_type: nmfc_cert. Output a JSON array with objects: page_no 1, doc_type "nmfc_cert". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2963, 'outputTokens': 494, 'totalTokens': 3457}, 'metrics': {'latencyMs': 2159}}
2025-09-24 13:47:52,304 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f2b4defd_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:47:52,320 - INFO - 

FYQQGIW8Z9DSAPCL0S9G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:47:52,320 - INFO - 

✓ Saved result: output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:47:52,506 - INFO - Page 1: Extracted 465 characters, 37 lines from 5572a332_IZTBXFPGXBFH3DV900G4_4f99df97_page_001.pdf
2025-09-24 13:47:52,506 - INFO - Successfully processed page 1
2025-09-24 13:47:52,506 - INFO - Combined 1 pages into final text
2025-09-24 13:47:52,507 - INFO - Text validation for 5572a332_IZTBXFPGXBFH3DV900G4: 482 characters, 1 pages
2025-09-24 13:47:52,507 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:47:52,507 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:47:52,622 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/01011bf0_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:47:52,653 - INFO - 

B3SIRREC9IAVZOJVDQSN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:47:52,653 - INFO - 

✓ Saved result: output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:47:52,939 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f2b4defd_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:47:53,757 - INFO - Page 1: Extracted 1119 characters, 62 lines from 3a2742a5_U7BB1XSF3ASMIAE1MQ5I_8a4fe531_page_001.pdf
2025-09-24 13:47:53,757 - INFO - Successfully processed page 1
2025-09-24 13:47:53,757 - INFO - Combined 1 pages into final text
2025-09-24 13:47:53,758 - INFO - Text validation for 3a2742a5_U7BB1XSF3ASMIAE1MQ5I: 1136 characters, 1 pages
2025-09-24 13:47:53,758 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:47:53,758 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:47:53,806 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '097ce410-8b22-47f5-b96b-c13160cf281f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:17:53 GMT', 'content-type': 'application/json', 'content-length': '575', 'connection': 'keep-alive', 'x-amzn-requestid': '097ce410-8b22-47f5-b96b-c13160cf281f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. The content includes "NMFC CLASSIFICATION CERTIFICATE". It likely matches nmfc_cert. So doc_type: nmfc_cert. No continuation. Output JSON array with one object: page_no 1, doc_type "nmfc_cert". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2707, 'outputTokens': 82, 'totalTokens': 2789}, 'metrics': {'latencyMs': 541}}
2025-09-24 13:47:53,807 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dd8c7488_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:47:53,831 - INFO - 

MR6ONA8GK6HN1LCZHEX3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:47:53,831 - INFO - 

✓ Saved result: output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:47:54,122 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dd8c7488_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:47:54,406 - INFO - Page 1: Extracted 477 characters, 75 lines from da4af933_PEE2ZFMV7X0A0FL35G4G_1e319711_page_001.pdf
2025-09-24 13:47:54,407 - INFO - Successfully processed page 1
2025-09-24 13:47:54,407 - INFO - Combined 1 pages into final text
2025-09-24 13:47:54,407 - INFO - Text validation for da4af933_PEE2ZFMV7X0A0FL35G4G: 494 characters, 1 pages
2025-09-24 13:47:54,407 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:47:54,407 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:47:54,744 - INFO - Page 2: Extracted 832 characters, 40 lines from fed69de5_PB67IAPSJB1DZWMDIE1H_069718bd_page_002.pdf
2025-09-24 13:47:54,744 - INFO - Successfully processed page 2
2025-09-24 13:47:55,764 - INFO - Page 1: Extracted 1225 characters, 82 lines from fed69de5_PB67IAPSJB1DZWMDIE1H_069718bd_page_001.pdf
2025-09-24 13:47:55,764 - INFO - Successfully processed page 1
2025-09-24 13:47:55,765 - INFO - Combined 2 pages into final text
2025-09-24 13:47:55,765 - INFO - Text validation for fed69de5_PB67IAPSJB1DZWMDIE1H: 2093 characters, 2 pages
2025-09-24 13:47:55,765 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:47:55,765 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:47:56,138 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '10a9fff0-e4ae-4530-a89e-7365a35febfb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:17:56 GMT', 'content-type': 'application/json', 'content-length': '559', 'connection': 'keep-alive', 'x-amzn-requestid': '10a9fff0-e4ae-4530-a89e-7365a35febfb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. There\'s only page1. Determine type. The page shows "NMFC CLASSIFICATION CERTIFICATE". That is a NMFC certificate. According to enum, nmfc_cert. So output: page_no 1, doc_type "nmfc_cert". Must output with tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2698, 'outputTokens': 79, 'totalTokens': 2777}, 'metrics': {'latencyMs': 584}}
2025-09-24 13:47:56,138 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/da4af933_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:47:56,148 - INFO - 

PEE2ZFMV7X0A0FL35G4G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:47:56,148 - INFO - 

✓ Saved result: output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:47:56,446 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/da4af933_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:47:57,017 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'bc26157d-d43f-453b-86b9-02878badaf4f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:17:56 GMT', 'content-type': 'application/json', 'content-length': '2679', 'connection': 'keep-alive', 'x-amzn-requestid': 'bc26157d-d43f-453b-86b9-02878badaf4f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content: "Weight & Inspection Freight Class Certificate H". It looks like a weight and inspection certificate. The keywords: "Weight & Inspection Freight Class Certificate". Also has "CORRECT TO CLASS: 250", "Density PCF", "Total Cube", etc. This matches weight_and_inspection_cert. Check that it doesn\'t have NMFC certification-specific keywords like "NMFC Code", "class #" maybe? It has "CORRECT TO CLASS". That may be a correct class. The instruction says: "Strictly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." Keywords for nmfc_cert include "NMFC Code", "class #", "Original", "Corrected class" etc. This page says "CORRECT TO CLASS: 250". That might be a corrected class? But does that qualify? The guideline: If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. The nmfc_cert keywords: "Corrected class" or "Correction information", "Original", "inspection", "class #". This page has "CORRECT TO CLASS". That\'s similar to "Corrected class". So maybe we should classify as nmfc_cert. But we need to see if it includes NMFC Code or class #. It doesn\'t show "NMFC Code" or class #. It\'s just "CORRECT TO CLASS: 250". There\'s no NMFC Code. The guidelines specify "If weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert". The keywords list for nmfc_cert includes "Original and inspection or Corrected class or Correction information". So "Corrected class" appears. So indeed weight_and_inspection_cert has the keyword "Corrected class". So we should classify as nmfc_cert. The wording: "If weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So yes, classify as nmfc_cert.\n\nWe must return using tool classify_logistics_doc_type. Provide documents array: one element with page_no: 1, doc_type: "nmfc_cert".\n\nCheck if the page is continuation? It says Page 3/3. But we only have this page. It is page 3 of 3. There\'s no indication of continuation at start. It\'s not continuation. So we classify as nmfc_cert.\n\nThus output: call tool with documents: [{page_no:1, doc_type:"nmfc_cert"}].'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2612, 'outputTokens': 559, 'totalTokens': 3171}, 'metrics': {'latencyMs': 2333}}
2025-09-24 13:47:57,017 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5572a332_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:47:57,033 - INFO - 

IZTBXFPGXBFH3DV900G4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:47:57,033 - INFO - 

✓ Saved result: output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:47:57,320 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5572a332_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:47:57,858 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '58db3134-f093-444e-bfa6-d3236d3a8f67', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:17:57 GMT', 'content-type': 'application/json', 'content-length': '3668', 'connection': 'keep-alive', 'x-amzn-requestid': '58db3134-f093-444e-bfa6-d3236d3a8f67'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. It\'s an inspection certificate. The content includes "Inspection Certificate" title. It has cube, weight, corrected bill information, etc. So likely weight_and_inspection_cert or inspection_cert? Let\'s check definitions:\n\n- weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also note: if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert. Also: Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise, inspection_cert.\n\nOur page mentions "WEIGHT 870", "CUBE 77.81", "DENSITY 11.18". So it\'s a weight and inspection certificate. Also NMFC Code present: NMFC 87560, NMFC 87552. There\'s "CORRECTED BILL INFORMATION". The content includes "CERTIFICATE INFORMATION". So yes weight_and_inspection_cert.\n\nAlso check if nmfc_cert: Keywords: "As described", "As found", "Original", "inspection", "Corrected class", "Correction information", "NMFC Code", "class #". We have "CORRECTED BILL INFORMATION" and "NMFC 87560". But nmfc_cert is for when a correction is made in weight_and_inspection_cert. In this page, it says "CORRECTED BILL INFORMATION". That suggests there\'s a corrected version. The definition for nmfc_cert says: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. So maybe this is nmfc_cert. But let\'s see: nmfc_cert keywords: "As described", "As found", "Original", "inspection", "Corrected class", "Correction information". We see "CORRECTED BILL INFORMATION". That might be a correction. Also nmfc_cert requirement: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." So likely this is nmfc_cert.\n\nBut also weight_and_inspection_cert includes "Certificate of Analysis", "Weight Certificate", etc. But we have "Inspection Certificate" and weight. So it\'s definitely one of those.\n\nThe content includes "INSPECTION DATE". So it\'s an inspection certificate. The question: Is it weight_and_inspection_cert or nmfc_cert? The definition of nmfc_cert is a correction notice for weight_and_inspection_cert. The page includes "CORRECTED BILL INFORMATION". That indicates a correction. So likely nmfc_cert. But the presence of "Inspection Certificate" header indicates weight_and_inspection_cert. However, the instructions: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So we need to see if the page has keywords that are mentioned in nmfc_cert. Yes "CORRECTED BILL INFORMATION" is similar to "Corrected class" or "Correction information". That seems to be a keyword. So we should classify as nmfc_cert.\n\nThus doc_type: nmfc_cert.\n\nReturn JSON with page_no 1 and doc_type nmfc_cert.\n\nWe must use tool classify_logistics_doc_type.'}}}, {'toolUse': {'toolUseId': 'tooluse_aHkhgRMzQBedVPmftiAJRg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2815, 'outputTokens': 728, 'totalTokens': 3543}, 'metrics': {'latencyMs': 2960}}
2025-09-24 13:47:57,858 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3a2742a5_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:47:57,879 - INFO - 

U7BB1XSF3ASMIAE1MQ5I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:47:57,879 - INFO - 

✓ Saved result: output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:47:58,052 - INFO - Page 1: Extracted 1248 characters, 88 lines from 498d9419_YL4HZPZH7D3B03FZZ5QK_22f64e29_page_001.pdf
2025-09-24 13:47:58,052 - INFO - Successfully processed page 1
2025-09-24 13:47:58,053 - INFO - Combined 1 pages into final text
2025-09-24 13:47:58,053 - INFO - Text validation for 498d9419_YL4HZPZH7D3B03FZZ5QK: 1265 characters, 1 pages
2025-09-24 13:47:58,053 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:47:58,053 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:47:58,165 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3a2742a5_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:48:00,260 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c64aaaa6-d224-4b8b-aaec-f6d1bcccbae9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:18:00 GMT', 'content-type': 'application/json', 'content-length': '3478', 'connection': 'keep-alive', 'x-amzn-requestid': 'c64aaaa6-d224-4b8b-aaec-f6d1bcccbae9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page as a doc_type. The content is a "WEIGHT & RESEARCH CERTIFICATE" page. That matches weight_and_inspection_cert or maybe nmfc_cert. The description says: "WEIGHT & RESEARCH CERTIFICATE" is likely a weight and inspection certificate. It has weight info, NMFC codes, etc. The instructions: For weight_and_inspection_cert, if it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert. Check if this page has nmfc_cert style keys: "NMFC: 73260 /SUB NMFC: 09" "CLASS: 70.0". That looks like NMFC classification info. The definition says: weight_and_inspection_cert: certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. nmfc_cert: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. Keywords: As described and As found or Original and inspection or Corrected class or Correction information. Also optional: NMFC Code, class #.\n\nThis page includes "W&R APPROVED" and "C W&R ISSUED". It also says "NMFC: 73260 /SUB NMFC: 09". This seems like a weight and research certificate. The question: is it a nmfc_cert or weight_and_inspection_cert? The instructions say: if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep as weight_and_inspection_cert. The keywords for nmfc_cert include "Original and inspection" or "Corrected class" or "Correction information". This page says "W&R APPROVED" and "C W&R ISSUED" and "NMFC: 73260 /SUB NMFC: 09". It also says "C W&R ISSUED". Not sure if that qualifies as nmfc_cert. It does have NMFC code. But is there a correction notice? It says "LESS DISCOUNT" and some adjustment. Also "W&R APPROVED". Might be a weight and research certificate, but maybe it\'s a nmfc_cert. The content includes "W&R APPROVED" which might indicate a weight & research certificate approved. The presence of "C W&R ISSUED" might indicate a correction. The instructions: "If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert". Keywords for nmfc_cert: "Original and inspection" or "Corrected class" or "Correction information". This page does not explicitly say "Corrected class" or "Correction information" but has "C W&R ISSUED" maybe indicates correction? But not sure.\n\nThus I\'d classify as weight_and_inspection_cert perhaps, because it\'s clearly a weight & research certificate. But the presence of NMFC codes may still be weight_and_inspection_cert. It also references "NMFC: 73260 /SUB NMFC: 09". But it\'s not explicitly a correction notice. I\'d go with weight_and_inspection_cert.\n\nAlso we must check continuation: page2 starts same header "SAIA MOTOR FREIGHT LINE, INC. PRO NBR: ..." So it\'s likely the same document. But each page gets same doc_type. So both pages are weight_and_inspection_cert.\n\nThus output: two objects, each with page_no 1 and 2, doc_type weight_and_inspection_cert.\n\nReturn via function call.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3306, 'outputTokens': 758, 'totalTokens': 4064}, 'metrics': {'latencyMs': 3334}}
2025-09-24 13:48:00,260 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fed69de5_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:48:00,294 - INFO - 

PB67IAPSJB1DZWMDIE1H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:48:00,294 - INFO - 

✓ Saved result: output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:48:00,591 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fed69de5_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:48:03,016 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'fe185f22-965f-429b-b45a-801fcd744000', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:18:02 GMT', 'content-type': 'application/json', 'content-length': '4213', 'connection': 'keep-alive', 'x-amzn-requestid': 'fe185f22-965f-429b-b45a-801fcd744000'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page but it says "Page 2 of 2" and "Correction Date". The header says "WEIGHTS & INSPECTION CERTIFICATE" and the document is a weight and inspection certificate. It includes NMFC, class, pieces, weight, etc. The question: classify pages. We must output via tool call classify_logistics_doc_type with array of objects: page_no: number, doc_type: ... It seems only one page but they provided page1 content. The content says "Page 2 of 2" meaning it\'s page 2 of the certificate. But we only have page 1 content? It says <page1> but the content includes "Page 2 of 2". We need to interpret: There\'s a two-page certificate; we only have page1 content? The page content includes "Page 2 of 2" which indicates the page number of the full document. So this is the second page. But we only have one page as input. We need to output classification for this page. According to the rules: we must output exactly one object describing that page. The classification: weight_and_inspection_cert. It says "Weight and Inspection Certificate" in the header. So doc_type: "weight_and_inspection_cert". Also check for nmfc_cert: If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. This doc is weight and inspection certificate, includes NMFC codes, but does it have keywords for nmfc_cert? The nmfc_cert keywords include: "NMFC Code", "class #" etc. This page includes NMFC numbers like 59420-2, 59420-1. That might be considered NMFC. However the rule says: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." The document is a weight and inspection certificate with NMFC codes. But is it a correction notice? It has "Correction Date". The category "nmfc_cert" is an NMFC Classification Certificate or Correction notice. The header says "WEIGHTS & INSPECTION CERTIFICATE". It also says "Correction Date: 06/20/2025". That might indicate it\'s a correction notice. But the doc type is weight_and_inspection_cert. However the rule: "If weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." The keywords for nmfc_cert: As described and As found or Original and inspection or Corrected class or Correction information. Also optional: NMFC Code, class #. This page has "Correction Date" and "Reclass Reweigh" etc. It also has NMFC codes. So it may be a correction notice (nmfc_cert). But the header still says "WEIGHTS & INSPECTION CERTIFICATE" which is weight_and_inspection_cert. But the instruction to override to nmfc_cert if keywords present. So we need to decide.\n\nLet\'s examine the nmfc_cert definition more: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued. Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information. Other keywords indication (Optional): NMFC Code, class #." This doc has Correction Date, "Reclass Reweigh", "Reclass S:"; "Corrected class"? It says "Reclass Reweigh" and "Reclass S:" and "Correction Date". That strongly indicates it\'s a correction. So we should classify as nmfc_cert. But is the header weight_and_inspection_cert? The instruction says to override if keywords present. So yes, nmfc_cert.\n\nThus output: page_no: 1, doc_type: "nmfc_cert".\n\nBut we only have one page. The "Page 2 of 2" indicates that\'s page 2. But we output page_no: 1 because it\'s the first page in our input? Actually the content is page 2 of 2, but we only have that page. The problem says "For every page in the input PDF you MUST return exactly one object describing that page." So we have one page. We should output page_no: 1. The doc_type: nmfc_cert.\n\nLet\'s produce tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2860, 'outputTokens': 929, 'totalTokens': 3789}, 'metrics': {'latencyMs': 3733}}
2025-09-24 13:48:03,016 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/498d9419_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:48:03,038 - INFO - 

YL4HZPZH7D3B03FZZ5QK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:48:03,038 - INFO - 

✓ Saved result: output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:48:03,387 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/498d9419_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:48:03,387 - INFO - 
📊 Processing Summary:
2025-09-24 13:48:03,388 - INFO -    Total files: 9
2025-09-24 13:48:03,388 - INFO -    Successful: 9
2025-09-24 13:48:03,388 - INFO -    Failed: 0
2025-09-24 13:48:03,388 - INFO -    Duration: 23.00 seconds
2025-09-24 13:48:03,388 - INFO -    Output directory: output
2025-09-24 13:48:03,388 - INFO - 
📋 Successfully Processed Files:
2025-09-24 13:48:03,388 - INFO -    📄 B3SIRREC9IAVZOJVDQSN.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:48:03,388 - INFO -    📄 CUF54EHGMLQ57HR93DRB.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:48:03,388 - INFO -    📄 FYQQGIW8Z9DSAPCL0S9G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:48:03,388 - INFO -    📄 IZTBXFPGXBFH3DV900G4.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:48:03,388 - INFO -    📄 MR6ONA8GK6HN1LCZHEX3.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:48:03,388 - INFO -    📄 PB67IAPSJB1DZWMDIE1H.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:48:03,388 - INFO -    📄 PEE2ZFMV7X0A0FL35G4G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:48:03,388 - INFO -    📄 U7BB1XSF3ASMIAE1MQ5I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:48:03,388 - INFO -    📄 YL4HZPZH7D3B03FZZ5QK.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:48:03,389 - INFO - 
============================================================================================================================================
2025-09-24 13:48:03,389 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 13:48:03,389 - INFO - ============================================================================================================================================
2025-09-24 13:48:03,389 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 13:48:03,389 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:48:03,389 - INFO - B3SIRREC9IAVZOJVDQSN.pdf                           1      nmfc_cert            run1_B3SIRREC9IAVZOJVDQSN.json                    
2025-09-24 13:48:03,389 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:48:03,389 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:48:03,389 - INFO - 
2025-09-24 13:48:03,389 - INFO - CUF54EHGMLQ57HR93DRB.pdf                           1      weight_and_inspect... run1_CUF54EHGMLQ57HR93DRB.json                    
2025-09-24 13:48:03,389 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:48:03,390 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:48:03,390 - INFO - 
2025-09-24 13:48:03,390 - INFO - FYQQGIW8Z9DSAPCL0S9G.pdf                           1      nmfc_cert            run1_FYQQGIW8Z9DSAPCL0S9G.json                    
2025-09-24 13:48:03,390 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:48:03,390 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:48:03,390 - INFO - 
2025-09-24 13:48:03,390 - INFO - IZTBXFPGXBFH3DV900G4.pdf                           1      nmfc_cert            run1_IZTBXFPGXBFH3DV900G4.json                    
2025-09-24 13:48:03,390 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:48:03,390 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:48:03,390 - INFO - 
2025-09-24 13:48:03,390 - INFO - MR6ONA8GK6HN1LCZHEX3.pdf                           1      nmfc_cert            run1_MR6ONA8GK6HN1LCZHEX3.json                    
2025-09-24 13:48:03,390 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:48:03,390 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:48:03,390 - INFO - 
2025-09-24 13:48:03,390 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           1      weight_and_inspect... run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:48:03,390 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:48:03,390 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:48:03,390 - INFO - 
2025-09-24 13:48:03,390 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           2      weight_and_inspect... run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:48:03,390 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:48:03,390 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:48:03,390 - INFO - 
2025-09-24 13:48:03,391 - INFO - PEE2ZFMV7X0A0FL35G4G.pdf                           1      nmfc_cert            run1_PEE2ZFMV7X0A0FL35G4G.json                    
2025-09-24 13:48:03,391 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:48:03,391 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:48:03,391 - INFO - 
2025-09-24 13:48:03,391 - INFO - U7BB1XSF3ASMIAE1MQ5I.pdf                           1      nmfc_cert            run1_U7BB1XSF3ASMIAE1MQ5I.json                    
2025-09-24 13:48:03,391 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:48:03,391 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:48:03,391 - INFO - 
2025-09-24 13:48:03,391 - INFO - YL4HZPZH7D3B03FZZ5QK.pdf                           1      nmfc_cert            run1_YL4HZPZH7D3B03FZZ5QK.json                    
2025-09-24 13:48:03,391 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:48:03,391 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:48:03,391 - INFO - 
2025-09-24 13:48:03,391 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:48:03,391 - INFO - Total entries: 10
2025-09-24 13:48:03,391 - INFO - ============================================================================================================================================
2025-09-24 13:48:03,391 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 13:48:03,391 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:48:03,391 - INFO -   1. B3SIRREC9IAVZOJVDQSN.pdf            Page 1   → nmfc_cert       | run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:48:03,391 - INFO -   2. CUF54EHGMLQ57HR93DRB.pdf            Page 1   → weight_and_inspection_cert | run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:48:03,391 - INFO -   3. FYQQGIW8Z9DSAPCL0S9G.pdf            Page 1   → nmfc_cert       | run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:48:03,391 - INFO -   4. IZTBXFPGXBFH3DV900G4.pdf            Page 1   → nmfc_cert       | run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:48:03,391 - INFO -   5. MR6ONA8GK6HN1LCZHEX3.pdf            Page 1   → nmfc_cert       | run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:48:03,391 - INFO -   6. PB67IAPSJB1DZWMDIE1H.pdf            Page 1   → weight_and_inspection_cert | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:48:03,392 - INFO -   7. PB67IAPSJB1DZWMDIE1H.pdf            Page 2   → weight_and_inspection_cert | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:48:03,392 - INFO -   8. PEE2ZFMV7X0A0FL35G4G.pdf            Page 1   → nmfc_cert       | run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:48:03,392 - INFO -   9. U7BB1XSF3ASMIAE1MQ5I.pdf            Page 1   → nmfc_cert       | run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:48:03,392 - INFO -  10. YL4HZPZH7D3B03FZZ5QK.pdf            Page 1   → nmfc_cert       | run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:48:03,392 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:48:03,392 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 22.997488, 'processed_files': [{'filename': 'B3SIRREC9IAVZOJVDQSN.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf'}, {'filename': 'CUF54EHGMLQ57HR93DRB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf'}, {'filename': 'FYQQGIW8Z9DSAPCL0S9G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf'}, {'filename': 'IZTBXFPGXBFH3DV900G4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf'}, {'filename': 'MR6ONA8GK6HN1LCZHEX3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf'}, {'filename': 'PB67IAPSJB1DZWMDIE1H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf'}, {'filename': 'PEE2ZFMV7X0A0FL35G4G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf'}, {'filename': 'U7BB1XSF3ASMIAE1MQ5I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf'}, {'filename': 'YL4HZPZH7D3B03FZZ5QK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf'}]}
