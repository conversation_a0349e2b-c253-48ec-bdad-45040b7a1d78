2025-09-24 14:28:39,719 - INFO - Logging initialized. Log file: logs/test_classification_20250924_142839.log
2025-09-24 14:28:39,720 - INFO - 📁 Found 10 files to process
2025-09-24 14:28:39,720 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:28:39,720 - INFO - 🚀 Processing 10 files in FORCED PARALLEL MODE...
2025-09-24 14:28:39,720 - INFO - 🚀 Creating 10 parallel tasks...
2025-09-24 14:28:39,720 - INFO - 🚀 All 10 tasks created - executing in parallel...
2025-09-24 14:28:39,720 - INFO - ⬆️ [14:28:39] Uploading: AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:28:41,227 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/AF0EUFN20TKQSN94KZCH.PDF -> s3://document-extraction-logistically/temp/d324fd1f_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:28:41,227 - INFO - 🔍 [14:28:41] Starting classification: AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:28:41,228 - INFO - ⬆️ [14:28:41] Uploading: FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:28:41,228 - INFO - Initializing TextractProcessor...
2025-09-24 14:28:41,252 - INFO - Initializing BedrockProcessor...
2025-09-24 14:28:41,258 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d324fd1f_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:28:41,258 - INFO - Processing PDF from S3...
2025-09-24 14:28:41,259 - INFO - Downloading PDF from S3 to /tmp/tmpvgxfdyf2/d324fd1f_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:28:41,821 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/FFJ2USKKEFCH3U0FO1S5.PDF -> s3://document-extraction-logistically/temp/d6b98bcd_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:28:41,822 - INFO - 🔍 [14:28:41] Starting classification: FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:28:41,823 - INFO - ⬆️ [14:28:41] Uploading: HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:28:41,825 - INFO - Initializing TextractProcessor...
2025-09-24 14:28:41,844 - INFO - Initializing BedrockProcessor...
2025-09-24 14:28:41,847 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d6b98bcd_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:28:41,847 - INFO - Processing PDF from S3...
2025-09-24 14:28:41,847 - INFO - Downloading PDF from S3 to /tmp/tmp5qzq_bkn/d6b98bcd_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:28:42,749 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:28:42,749 - INFO - Splitting PDF into individual pages...
2025-09-24 14:28:42,749 - INFO - Splitting PDF d324fd1f_AF0EUFN20TKQSN94KZCH into 1 pages
2025-09-24 14:28:42,752 - INFO - Split PDF into 1 pages
2025-09-24 14:28:42,752 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:28:42,752 - INFO - Expected pages: [1]
2025-09-24 14:28:43,077 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HBV9LT6SK8HIOJ5DI4P2.pdf -> s3://document-extraction-logistically/temp/83860982_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:28:43,077 - INFO - 🔍 [14:28:43] Starting classification: HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:28:43,078 - INFO - Initializing TextractProcessor...
2025-09-24 14:28:43,078 - INFO - ⬆️ [14:28:43] Uploading: HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:28:43,096 - INFO - Initializing BedrockProcessor...
2025-09-24 14:28:43,101 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/83860982_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:28:43,102 - INFO - Processing PDF from S3...
2025-09-24 14:28:43,102 - INFO - Downloading PDF from S3 to /tmp/tmpz7ywaful/83860982_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:28:43,358 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:28:43,358 - INFO - Splitting PDF into individual pages...
2025-09-24 14:28:43,359 - INFO - Splitting PDF d6b98bcd_FFJ2USKKEFCH3U0FO1S5 into 1 pages
2025-09-24 14:28:43,363 - INFO - Split PDF into 1 pages
2025-09-24 14:28:43,363 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:28:43,363 - INFO - Expected pages: [1]
2025-09-24 14:28:44,373 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HL4SSNKOC8T141SEURCG.pdf -> s3://document-extraction-logistically/temp/7a4fe329_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:28:44,374 - INFO - 🔍 [14:28:44] Starting classification: HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:28:44,375 - INFO - ⬆️ [14:28:44] Uploading: Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:28:44,376 - INFO - Initializing TextractProcessor...
2025-09-24 14:28:44,394 - INFO - Initializing BedrockProcessor...
2025-09-24 14:28:44,398 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7a4fe329_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:28:44,398 - INFO - Processing PDF from S3...
2025-09-24 14:28:44,399 - INFO - Downloading PDF from S3 to /tmp/tmpicpfpocq/7a4fe329_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:28:44,987 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Q9MLAQNGOP70MYYKOYFJ.pdf -> s3://document-extraction-logistically/temp/574788c4_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:28:44,988 - INFO - 🔍 [14:28:44] Starting classification: Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:28:44,988 - INFO - ⬆️ [14:28:44] Uploading: RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:28:44,989 - INFO - Initializing TextractProcessor...
2025-09-24 14:28:45,009 - INFO - Initializing BedrockProcessor...
2025-09-24 14:28:45,013 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/574788c4_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:28:45,013 - INFO - Processing PDF from S3...
2025-09-24 14:28:45,013 - INFO - Downloading PDF from S3 to /tmp/tmp3sa7zchb/574788c4_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:28:46,261 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:28:46,261 - INFO - Splitting PDF into individual pages...
2025-09-24 14:28:46,262 - INFO - Splitting PDF 574788c4_Q9MLAQNGOP70MYYKOYFJ into 1 pages
2025-09-24 14:28:46,263 - INFO - Split PDF into 1 pages
2025-09-24 14:28:46,263 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:28:46,263 - INFO - Expected pages: [1]
2025-09-24 14:28:46,285 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:28:46,285 - INFO - Splitting PDF into individual pages...
2025-09-24 14:28:46,286 - INFO - Splitting PDF 83860982_HBV9LT6SK8HIOJ5DI4P2 into 1 pages
2025-09-24 14:28:46,289 - INFO - Split PDF into 1 pages
2025-09-24 14:28:46,289 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:28:46,289 - INFO - Expected pages: [1]
2025-09-24 14:28:46,469 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 14:28:46,469 - INFO - Splitting PDF into individual pages...
2025-09-24 14:28:46,470 - INFO - Splitting PDF 7a4fe329_HL4SSNKOC8T141SEURCG into 1 pages
2025-09-24 14:28:46,471 - INFO - Split PDF into 1 pages
2025-09-24 14:28:46,471 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:28:46,472 - INFO - Expected pages: [1]
2025-09-24 14:28:46,880 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/RBKKSRU2KS6IJRASO4SB.pdf -> s3://document-extraction-logistically/temp/0e945146_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:28:46,880 - INFO - 🔍 [14:28:46] Starting classification: RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:28:46,880 - INFO - ⬆️ [14:28:46] Uploading: SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:28:46,884 - INFO - Initializing TextractProcessor...
2025-09-24 14:28:46,895 - INFO - Initializing BedrockProcessor...
2025-09-24 14:28:46,899 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0e945146_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:28:46,899 - INFO - Processing PDF from S3...
2025-09-24 14:28:46,899 - INFO - Downloading PDF from S3 to /tmp/tmptdrj_vh0/0e945146_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:28:46,908 - INFO - Page 1: Extracted 654 characters, 40 lines from d6b98bcd_FFJ2USKKEFCH3U0FO1S5_16d04338_page_001.pdf
2025-09-24 14:28:46,908 - INFO - Successfully processed page 1
2025-09-24 14:28:46,908 - INFO - Combined 1 pages into final text
2025-09-24 14:28:46,909 - INFO - Text validation for d6b98bcd_FFJ2USKKEFCH3U0FO1S5: 671 characters, 1 pages
2025-09-24 14:28:46,909 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:28:46,909 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:28:47,514 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/SV5IN68S36F6PA0633RU.pdf -> s3://document-extraction-logistically/temp/ca8dbb41_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:28:47,514 - INFO - 🔍 [14:28:47] Starting classification: SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:28:47,515 - INFO - Initializing TextractProcessor...
2025-09-24 14:28:47,518 - INFO - ⬆️ [14:28:47] Uploading: WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:28:47,570 - INFO - Initializing BedrockProcessor...
2025-09-24 14:28:47,586 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ca8dbb41_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:28:47,590 - INFO - Page 1: Extracted 717 characters, 43 lines from d324fd1f_AF0EUFN20TKQSN94KZCH_2324e876_page_001.pdf
2025-09-24 14:28:47,590 - INFO - Processing PDF from S3...
2025-09-24 14:28:47,590 - INFO - Successfully processed page 1
2025-09-24 14:28:47,590 - INFO - Combined 1 pages into final text
2025-09-24 14:28:47,590 - INFO - Downloading PDF from S3 to /tmp/tmpssnw3okx/ca8dbb41_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:28:47,590 - INFO - Text validation for d324fd1f_AF0EUFN20TKQSN94KZCH: 734 characters, 1 pages
2025-09-24 14:28:47,594 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:28:47,594 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:28:48,161 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/WDPSPQTC87MJOF3B6952.pdf -> s3://document-extraction-logistically/temp/363864a3_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:28:48,162 - INFO - 🔍 [14:28:48] Starting classification: WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:28:48,163 - INFO - ⬆️ [14:28:48] Uploading: XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:28:48,163 - INFO - Initializing TextractProcessor...
2025-09-24 14:28:48,176 - INFO - Initializing BedrockProcessor...
2025-09-24 14:28:48,178 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/363864a3_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:28:48,178 - INFO - Processing PDF from S3...
2025-09-24 14:28:48,179 - INFO - Downloading PDF from S3 to /tmp/tmpv_jcz9s7/363864a3_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:28:48,744 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/XVTBS5BQOTRUDPEUC4CC.pdf -> s3://document-extraction-logistically/temp/d8754c6c_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:28:48,744 - INFO - 🔍 [14:28:48] Starting classification: XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:28:48,745 - INFO - ⬆️ [14:28:48] Uploading: Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:28:48,747 - INFO - Initializing TextractProcessor...
2025-09-24 14:28:48,761 - INFO - Initializing BedrockProcessor...
2025-09-24 14:28:48,766 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d8754c6c_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:28:48,766 - INFO - Processing PDF from S3...
2025-09-24 14:28:48,766 - INFO - Downloading PDF from S3 to /tmp/tmprw88p10h/d8754c6c_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:28:48,920 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '594e2c0c-b861-4f72-a529-c54d063e6c57', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:58:48 GMT', 'content-type': 'application/json', 'content-length': '751', 'connection': 'keep-alive', 'x-amzn-requestid': '594e2c0c-b861-4f72-a529-c54d063e6c57'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Title: ORDER CONFIRMATION. It\'s about a purchase order or sales order confirmation from a warehouse. The page includes fields: Our order#, Winery order#, ship date, etc. It lists items and quantities. This matches "so_confirmation" (Sales Order Confirmation). The doc type "so_confirmation" is defined.\n\nWe should return a JSON object with page_no=1, doc_type="so_confirmation". Use the tool.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2728, 'outputTokens': 109, 'totalTokens': 2837}, 'metrics': {'latencyMs': 723}}
2025-09-24 14:28:48,922 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d6b98bcd_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:28:48,926 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:28:48,927 - INFO - Splitting PDF into individual pages...
2025-09-24 14:28:48,929 - INFO - Splitting PDF ca8dbb41_SV5IN68S36F6PA0633RU into 2 pages
2025-09-24 14:28:48,932 - INFO - Split PDF into 2 pages
2025-09-24 14:28:48,932 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:28:48,932 - INFO - Expected pages: [1, 2]
2025-09-24 14:28:49,256 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 14:28:49,256 - INFO - Splitting PDF into individual pages...
2025-09-24 14:28:49,258 - INFO - Splitting PDF 0e945146_RBKKSRU2KS6IJRASO4SB into 1 pages
2025-09-24 14:28:49,260 - INFO - Split PDF into 1 pages
2025-09-24 14:28:49,260 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:28:49,260 - INFO - Expected pages: [1]
2025-09-24 14:28:49,348 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Y6MOA7KJV1OU7NHZ4P4U.pdf -> s3://document-extraction-logistically/temp/21cdd181_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:28:49,348 - INFO - 🔍 [14:28:49] Starting classification: Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:28:49,349 - INFO - Initializing TextractProcessor...
2025-09-24 14:28:49,360 - INFO - Initializing BedrockProcessor...
2025-09-24 14:28:49,365 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/21cdd181_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:28:49,366 - INFO - Processing PDF from S3...
2025-09-24 14:28:49,368 - INFO - Downloading PDF from S3 to /tmp/tmp1ctkbktw/21cdd181_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:28:49,373 - INFO - 

FFJ2USKKEFCH3U0FO1S5.PDF

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:28:49,373 - INFO - 

✓ Saved result: output/run1_FFJ2USKKEFCH3U0FO1S5.json
2025-09-24 14:28:49,478 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:28:49,479 - INFO - Splitting PDF into individual pages...
2025-09-24 14:28:49,481 - INFO - Splitting PDF 363864a3_WDPSPQTC87MJOF3B6952 into 2 pages
2025-09-24 14:28:49,484 - INFO - Split PDF into 2 pages
2025-09-24 14:28:49,484 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:28:49,484 - INFO - Expected pages: [1, 2]
2025-09-24 14:28:49,541 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '301abcb2-f928-4f22-87b7-ae7699c24218', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:58:49 GMT', 'content-type': 'application/json', 'content-length': '766', 'connection': 'keep-alive', 'x-amzn-requestid': '301abcb2-f928-4f22-87b7-ae7699c24218'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1. Text looks like an order confirmation from a warehouse for a winery. It\'s a sales order confirmation. Keywords: ORDER CONFIRMATION, Our order#, wine etc. So doc_type likely "so_confirmation" (Sales Order Confirmation). There\'s no other doc types. So output: page_no 1, doc_type "so_confirmation". Use tool call.'}}}, {'toolUse': {'toolUseId': 'tooluse_Ryw7Ve_FTsa51Ra1olbarg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2750, 'outputTokens': 99, 'totalTokens': 2849}, 'metrics': {'latencyMs': 672}}
2025-09-24 14:28:49,541 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d324fd1f_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:28:49,653 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d6b98bcd_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:28:49,661 - INFO - 

AF0EUFN20TKQSN94KZCH.PDF

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:28:49,661 - INFO - 

✓ Saved result: output/run1_AF0EUFN20TKQSN94KZCH.json
2025-09-24 14:28:50,005 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d324fd1f_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:28:50,102 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:28:50,102 - INFO - Splitting PDF into individual pages...
2025-09-24 14:28:50,104 - INFO - Splitting PDF d8754c6c_XVTBS5BQOTRUDPEUC4CC into 2 pages
2025-09-24 14:28:50,107 - INFO - Split PDF into 2 pages
2025-09-24 14:28:50,107 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:28:50,108 - INFO - Expected pages: [1, 2]
2025-09-24 14:28:50,446 - INFO - Page 1: Extracted 1170 characters, 73 lines from 574788c4_Q9MLAQNGOP70MYYKOYFJ_4a01a999_page_001.pdf
2025-09-24 14:28:50,447 - INFO - Successfully processed page 1
2025-09-24 14:28:50,447 - INFO - Combined 1 pages into final text
2025-09-24 14:28:50,447 - INFO - Text validation for 574788c4_Q9MLAQNGOP70MYYKOYFJ: 1187 characters, 1 pages
2025-09-24 14:28:50,448 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:28:50,448 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:28:50,690 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:28:50,690 - INFO - Splitting PDF into individual pages...
2025-09-24 14:28:50,692 - INFO - Splitting PDF 21cdd181_Y6MOA7KJV1OU7NHZ4P4U into 2 pages
2025-09-24 14:28:50,694 - INFO - Split PDF into 2 pages
2025-09-24 14:28:50,694 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:28:50,695 - INFO - Expected pages: [1, 2]
2025-09-24 14:28:51,549 - INFO - Page 1: Extracted 1438 characters, 62 lines from 83860982_HBV9LT6SK8HIOJ5DI4P2_bf638286_page_001.pdf
2025-09-24 14:28:51,550 - INFO - Successfully processed page 1
2025-09-24 14:28:51,550 - INFO - Combined 1 pages into final text
2025-09-24 14:28:51,550 - INFO - Text validation for 83860982_HBV9LT6SK8HIOJ5DI4P2: 1455 characters, 1 pages
2025-09-24 14:28:51,550 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:28:51,550 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:28:52,451 - INFO - Page 2: Extracted 456 characters, 26 lines from ca8dbb41_SV5IN68S36F6PA0633RU_98a357c2_page_002.pdf
2025-09-24 14:28:52,451 - INFO - Successfully processed page 2
2025-09-24 14:28:52,684 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8f71ecbd-4dde-4b8e-a608-b22d1d04a643', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:58:52 GMT', 'content-type': 'application/json', 'content-length': '1179', 'connection': 'keep-alive', 'x-amzn-requestid': '8f71ecbd-4dde-4b8e-a608-b22d1d04a643'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. Determine document type. The content: "SALES ACKNOWLEDGMENT", "Sales Acknowledgement" page. This seems like a Sales Order Confirmation? The description: "Sales Acknowledgment" maybe a sales order confirmation. The guidelines: "so_confirmation — Sales Order Confirmation or Order acknowledgement". Keywords: Sales Order Confirmation, SO#, Order Confirmation. Here we have "SALES ACKNOWLEDGMENT" and "No: 16 CU-364478". It is similar to "Sales Order Confirmation". So likely \'so_confirmation\'.\n\nWe need to output via tool call. Format: JSON array of objects? The function expects: documents: array of objects with page_no and doc_type. So we return that.\n\nThus output:\n\n{\n "documents": [\n   { "page_no": 1, "doc_type": "so_confirmation" }\n ]\n}\n\nWe\'ll call the function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2931, 'outputTokens': 209, 'totalTokens': 3140}, 'metrics': {'latencyMs': 1015}}
2025-09-24 14:28:52,684 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/574788c4_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:28:52,702 - INFO - 

Q9MLAQNGOP70MYYKOYFJ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:28:52,702 - INFO - 

✓ Saved result: output/run1_Q9MLAQNGOP70MYYKOYFJ.json
2025-09-24 14:28:52,975 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/574788c4_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:28:53,145 - INFO - Page 2: Extracted 614 characters, 29 lines from 363864a3_WDPSPQTC87MJOF3B6952_f12947e2_page_002.pdf
2025-09-24 14:28:53,145 - INFO - Successfully processed page 2
2025-09-24 14:28:53,252 - INFO - Page 1: Extracted 1305 characters, 67 lines from ca8dbb41_SV5IN68S36F6PA0633RU_98a357c2_page_001.pdf
2025-09-24 14:28:53,252 - INFO - Successfully processed page 1
2025-09-24 14:28:53,252 - INFO - Combined 2 pages into final text
2025-09-24 14:28:53,253 - INFO - Text validation for ca8dbb41_SV5IN68S36F6PA0633RU: 1797 characters, 2 pages
2025-09-24 14:28:53,253 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:28:53,253 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:28:53,385 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '891793ac-7b0d-4b80-9391-80c73fabbd6a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:58:53 GMT', 'content-type': 'application/json', 'content-length': '782', 'connection': 'keep-alive', 'x-amzn-requestid': '891793ac-7b0d-4b80-9391-80c73fabbd6a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. It appears to be an Order Confirmation page. We see "Order Confirmation", "Order Number", "Customer PO", etc. It\'s a sales order confirmation. According to list, "so_confirmation" is Sales Order Confirmation or Order acknowledgement. So doc_type should be so_confirmation. Must output JSON via tool call classify_logistics_doc_type. Provide array with one object: page_no:1 doc_type: "so_confirmation".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2908, 'outputTokens': 113, 'totalTokens': 3021}, 'metrics': {'latencyMs': 669}}
2025-09-24 14:28:53,385 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/83860982_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:28:53,421 - INFO - 

HBV9LT6SK8HIOJ5DI4P2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:28:53,421 - INFO - 

✓ Saved result: output/run1_HBV9LT6SK8HIOJ5DI4P2.json
2025-09-24 14:28:53,492 - INFO - Page 1: Extracted 1443 characters, 97 lines from 7a4fe329_HL4SSNKOC8T141SEURCG_baf5666c_page_001.pdf
2025-09-24 14:28:53,492 - INFO - Successfully processed page 1
2025-09-24 14:28:53,492 - INFO - Combined 1 pages into final text
2025-09-24 14:28:53,493 - INFO - Text validation for 7a4fe329_HL4SSNKOC8T141SEURCG: 1460 characters, 1 pages
2025-09-24 14:28:53,493 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:28:53,493 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:28:53,753 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/83860982_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:28:54,022 - INFO - Page 1: Extracted 1308 characters, 67 lines from d8754c6c_XVTBS5BQOTRUDPEUC4CC_e13e666f_page_001.pdf
2025-09-24 14:28:54,022 - INFO - Successfully processed page 1
2025-09-24 14:28:54,737 - INFO - Page 2: Extracted 457 characters, 26 lines from d8754c6c_XVTBS5BQOTRUDPEUC4CC_e13e666f_page_002.pdf
2025-09-24 14:28:54,737 - INFO - Successfully processed page 2
2025-09-24 14:28:54,737 - INFO - Combined 2 pages into final text
2025-09-24 14:28:54,737 - INFO - Text validation for d8754c6c_XVTBS5BQOTRUDPEUC4CC: 1801 characters, 2 pages
2025-09-24 14:28:54,737 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:28:54,738 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:28:54,771 - INFO - Page 1: Extracted 1306 characters, 69 lines from 21cdd181_Y6MOA7KJV1OU7NHZ4P4U_ba109187_page_001.pdf
2025-09-24 14:28:54,771 - INFO - Successfully processed page 1
2025-09-24 14:28:54,800 - INFO - Page 1: Extracted 1364 characters, 74 lines from 363864a3_WDPSPQTC87MJOF3B6952_f12947e2_page_001.pdf
2025-09-24 14:28:54,800 - INFO - Successfully processed page 1
2025-09-24 14:28:54,801 - INFO - Combined 2 pages into final text
2025-09-24 14:28:54,801 - INFO - Text validation for 363864a3_WDPSPQTC87MJOF3B6952: 2014 characters, 2 pages
2025-09-24 14:28:54,801 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:28:54,802 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:28:54,992 - INFO - Page 2: Extracted 457 characters, 26 lines from 21cdd181_Y6MOA7KJV1OU7NHZ4P4U_ba109187_page_002.pdf
2025-09-24 14:28:54,993 - INFO - Successfully processed page 2
2025-09-24 14:28:54,993 - INFO - Combined 2 pages into final text
2025-09-24 14:28:54,993 - INFO - Text validation for 21cdd181_Y6MOA7KJV1OU7NHZ4P4U: 1799 characters, 2 pages
2025-09-24 14:28:54,994 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:28:54,994 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:28:55,416 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6dc4a110-1981-4cc1-ba62-9b7c4266bc71', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:58:55 GMT', 'content-type': 'application/json', 'content-length': '745', 'connection': 'keep-alive', 'x-amzn-requestid': '6dc4a110-1981-4cc1-ba62-9b7c4266bc71'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify the document type. The page is a "Sales Order" with header "SALES ORDER". The content includes "Sales Order Number: SO-385887", etc. This matches "so_confirmation" (Sales Order Confirmation or Order acknowledgement). The doc type is "so_confirmation". There\'s no other pages. So we output a single object with page_no 1, doc_type "so_confirmation". Use the function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3011, 'outputTokens': 111, 'totalTokens': 3122}, 'metrics': {'latencyMs': 652}}
2025-09-24 14:28:55,416 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7a4fe329_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:28:55,431 - INFO - 

HL4SSNKOC8T141SEURCG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:28:55,431 - INFO - 

✓ Saved result: output/run1_HL4SSNKOC8T141SEURCG.json
2025-09-24 14:28:55,717 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7a4fe329_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:28:56,149 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '296f7870-d182-495e-92b0-25a7f96bea2e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:58:56 GMT', 'content-type': 'application/json', 'content-length': '1714', 'connection': 'keep-alive', 'x-amzn-requestid': '296f7870-d182-495e-92b0-25a7f96bea2e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. These pages are a "Plant Order" from Calumet Refining. This looks like a purchase order from a customer to a manufacturer? Actually it\'s a Plant Order (PO). The page shows PO Number, Order Number, etc. It\'s a purchase order from Calumet Refining to Unique Candles. It\'s basically a purchase order form. So doc_type likely "po". It\'s a purchase order. The doc type "po" is defined as purchase order. So we classify page1 as po. Page2 is continuation and same doc. There\'s a note: "If document is not from any of catagories mentioned, classify as other. But before that check if it is continuation of previous page. If it is continuation then assign the same doc_type as previous page." Page2 seems to be a continuation page. It has same header and page: 2/2. So we should classify page2 as po too. So output two objects: page_no 1 with doc_type po, page_no 2 with doc_type po.\n\nWe must use the tool classify_logistics_doc_type. Provide array of objects.\n\nCheck if the correct output format: The tool expects a JSON with field documents: array of objects with page_no and doc_type. In the tool, we need to call the function with the argument. We\'ll produce that.\n\nThus:\n\n{\n "documents": [\n   {"page_no": 1, "doc_type": "po"},\n   {"page_no": 2, "doc_type": "po"}\n ]\n}\n\nWe need to use the tool exactly.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3127, 'outputTokens': 351, 'totalTokens': 3478}, 'metrics': {'latencyMs': 1674}}
2025-09-24 14:28:56,149 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ca8dbb41_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:28:56,177 - INFO - 

SV5IN68S36F6PA0633RU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:28:56,177 - INFO - 

✓ Saved result: output/run1_SV5IN68S36F6PA0633RU.json
2025-09-24 14:28:56,436 - INFO - Page 1: Extracted 1479 characters, 67 lines from 0e945146_RBKKSRU2KS6IJRASO4SB_f13d0f0c_page_001.pdf
2025-09-24 14:28:56,436 - INFO - Successfully processed page 1
2025-09-24 14:28:56,437 - INFO - Combined 1 pages into final text
2025-09-24 14:28:56,437 - INFO - Text validation for 0e945146_RBKKSRU2KS6IJRASO4SB: 1496 characters, 1 pages
2025-09-24 14:28:56,437 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:28:56,437 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:28:56,455 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ca8dbb41_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:28:57,165 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7246b997-9e88-4e1f-b35d-2669973718a6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:58:57 GMT', 'content-type': 'application/json', 'content-length': '1055', 'connection': 'keep-alive', 'x-amzn-requestid': '7246b997-9e88-4e1f-b35d-2669973718a6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': "We need to classify each page. The documents appear to be a Plant Order from Calumet Refining, LLC. This looks like a Purchase Order (PO) or plant order. The page contains PO Number, Order Number, etc. It's not a shipment document like BOL, POD, etc. It's more like a purchase order. So doc_type should be po. But we must consider that the first page also includes product description, quantities, gross weight, etc. It looks like a purchase order for goods. So doc_type=po.\n\nBoth pages are part of the same document. Page 1/2 and page 2/2. Page 2 is continuation. So each page gets same doc_type: po. We should output two objects: page_no 1 and 2 with doc_type 'po'. Use tool classify_logistics_doc_type."}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3211, 'outputTokens': 201, 'totalTokens': 3412}, 'metrics': {'latencyMs': 1112}}
2025-09-24 14:28:57,165 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/363864a3_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:28:57,198 - INFO - 

WDPSPQTC87MJOF3B6952.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:28:57,198 - INFO - 

✓ Saved result: output/run1_WDPSPQTC87MJOF3B6952.json
2025-09-24 14:28:57,377 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e49f3499-e96e-4a94-8861-a0046fa791f3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:58:57 GMT', 'content-type': 'application/json', 'content-length': '1214', 'connection': 'keep-alive', 'x-amzn-requestid': 'e49f3499-e96e-4a94-8861-a0046fa791f3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': "We have a two-page document. It looks like a plant order or purchase order from Calumet Refining. It appears to be a purchase order (PO). The content includes PO Number, Order Number, Order, etc. It's a Plant Order, likely a PO. The doc has fields typical of a PO: PO Number, Order Number, Order, Date Ordered, Status, Payment Terms, Sales Person, Shipping Terms, Ship To, Bill To, Carrier, Product Description, Order Qty, Gross Weight, Net Weight, HTS Number, etc. So classify as po.\n\nWe need to output each page's classification. The pages are consecutive; page 2 is continuation of page 1. The rule: if a page is a continuation, assign same doc_type. So both pages are po.\n\nWe need to output via tool call classify_logistics_doc_type with array of objects: page_no and doc_type. We must output exactly one object per page. Provide page numbers 1 and 2.\n\nOk."}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3126, 'outputTokens': 232, 'totalTokens': 3358}, 'metrics': {'latencyMs': 1106}}
2025-09-24 14:28:57,377 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/21cdd181_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:28:57,479 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/363864a3_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:28:57,497 - INFO - 

Y6MOA7KJV1OU7NHZ4P4U.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:28:57,497 - INFO - 

✓ Saved result: output/run1_Y6MOA7KJV1OU7NHZ4P4U.json
2025-09-24 14:28:57,658 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e9b18be8-bbb9-453e-9a63-5de20f59b24c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:58:57 GMT', 'content-type': 'application/json', 'content-length': '1869', 'connection': 'keep-alive', 'x-amzn-requestid': 'e9b18be8-bbb9-453e-9a63-5de20f59b24c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We are given two pages from a PDF: page1 and page2. We need to classify each page\'s document type. The pages appear to represent a "Plant Order" from Calumet Refining, LLC. It looks like a purchase order or an order confirmation. The text includes "PO Number:", "Order Number:", "Shipping Terms:", etc. It also shows "Ship To", "Ship From", "Bill To". This looks like a purchase order (PO). It includes product details, quantities, HTS number, gross weight, net weight. This is likely a purchase order (PO). There\'s no explicit header like "PO" but "PO Number:" is present. So doc_type: "po". The second page is continuation: it has same header but no new content. It likely is the same document. The second page starts with "CALUMET" etc. It is a continuation. According to the instruction, if page starts with "continued" or any indication of continuation, assign same doc_type. However, if it\'s just a continuation of the same document but no explicit "continued" text, we should still assign same doc_type because it\'s part of multi-page single document. The instruction says: "If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page)." So we just assign "po" to both pages.\n\nWe need to output via the tool classify_logistics_doc_type with array of objects, each with page_no and doc_type. Must output exactly those two objects.\n\nThus the JSON: [{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3113, 'outputTokens': 371, 'totalTokens': 3484}, 'metrics': {'latencyMs': 1624}}
2025-09-24 14:28:57,659 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d8754c6c_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:28:57,888 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/21cdd181_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:28:57,903 - INFO - 

XVTBS5BQOTRUDPEUC4CC.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:28:57,903 - INFO - 

✓ Saved result: output/run1_XVTBS5BQOTRUDPEUC4CC.json
2025-09-24 14:28:58,196 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d8754c6c_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:28:58,513 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '20dec41b-06b9-4100-8572-bea2fb2e2043', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:58:58 GMT', 'content-type': 'application/json', 'content-length': '873', 'connection': 'keep-alive', 'x-amzn-requestid': '20dec41b-06b9-4100-8572-bea2fb2e2043'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There is only page1. Determine doc_type. The page is an Order Acknowledgment: "ORDER ACKNOWLEDGMENT" with details, a sales order. Keywords: "ORDER ACKNOWLEDGMENT", "SO", "SO WEIGHT", "Detailed Sales Order". This matches "so_confirmation" (Sales Order Confirmation). It\'s a sales order confirmation or order acknowledgement. There is no other header. So doc_type = "so_confirmation". We output JSON array with single object: page_no: 1, doc_type: "so_confirmation". Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2922, 'outputTokens': 145, 'totalTokens': 3067}, 'metrics': {'latencyMs': 792}}
2025-09-24 14:28:58,513 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0e945146_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:28:58,527 - INFO - 

RBKKSRU2KS6IJRASO4SB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:28:58,528 - INFO - 

✓ Saved result: output/run1_RBKKSRU2KS6IJRASO4SB.json
