2025-09-24 13:38:11,375 - INFO - Logging initialized. Log file: logs/test_classification_20250924_133811.log
2025-09-24 13:38:11,375 - INFO - 📁 Found 9 files to process
2025-09-24 13:38:11,375 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 13:38:11,375 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 13:38:11,375 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 13:38:11,375 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 13:38:11,375 - INFO - ⬆️ [13:38:11] Uploading: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:38:13,269 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf -> s3://document-extraction-logistically/temp/881bb95e_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:38:13,270 - INFO - 🔍 [13:38:13] Starting classification: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:38:13,271 - INFO - ⬆️ [13:38:13] Uploading: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:38:13,272 - INFO - Initializing TextractProcessor...
2025-09-24 13:38:13,293 - INFO - Initializing BedrockProcessor...
2025-09-24 13:38:13,298 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/881bb95e_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:38:13,298 - INFO - Processing PDF from S3...
2025-09-24 13:38:13,299 - INFO - Downloading PDF from S3 to /tmp/tmpg7mo4j3f/881bb95e_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:38:13,892 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf -> s3://document-extraction-logistically/temp/4d9a7dd9_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:38:13,892 - INFO - 🔍 [13:38:13] Starting classification: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:38:13,892 - INFO - ⬆️ [13:38:13] Uploading: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:38:13,893 - INFO - Initializing TextractProcessor...
2025-09-24 13:38:13,910 - INFO - Initializing BedrockProcessor...
2025-09-24 13:38:13,915 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4d9a7dd9_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:38:13,915 - INFO - Processing PDF from S3...
2025-09-24 13:38:13,916 - INFO - Downloading PDF from S3 to /tmp/tmptvlxl4jw/4d9a7dd9_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:38:14,727 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:38:14,728 - INFO - Splitting PDF into individual pages...
2025-09-24 13:38:14,728 - INFO - Splitting PDF 881bb95e_B3SIRREC9IAVZOJVDQSN into 1 pages
2025-09-24 13:38:14,734 - INFO - Split PDF into 1 pages
2025-09-24 13:38:14,734 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:38:14,734 - INFO - Expected pages: [1]
2025-09-24 13:38:14,789 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf -> s3://document-extraction-logistically/temp/0fd53bb0_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:38:14,789 - INFO - 🔍 [13:38:14] Starting classification: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:38:14,790 - INFO - ⬆️ [13:38:14] Uploading: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:38:14,790 - INFO - Initializing TextractProcessor...
2025-09-24 13:38:14,806 - INFO - Initializing BedrockProcessor...
2025-09-24 13:38:14,810 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0fd53bb0_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:38:14,810 - INFO - Processing PDF from S3...
2025-09-24 13:38:14,810 - INFO - Downloading PDF from S3 to /tmp/tmp33b3mdfs/0fd53bb0_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:38:15,236 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:38:15,236 - INFO - Splitting PDF into individual pages...
2025-09-24 13:38:15,237 - INFO - Splitting PDF 4d9a7dd9_CUF54EHGMLQ57HR93DRB into 1 pages
2025-09-24 13:38:15,239 - INFO - Split PDF into 1 pages
2025-09-24 13:38:15,239 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:38:15,239 - INFO - Expected pages: [1]
2025-09-24 13:38:16,375 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:38:16,376 - INFO - Splitting PDF into individual pages...
2025-09-24 13:38:16,378 - INFO - Splitting PDF 0fd53bb0_FYQQGIW8Z9DSAPCL0S9G into 1 pages
2025-09-24 13:38:16,380 - INFO - Split PDF into 1 pages
2025-09-24 13:38:16,380 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:38:16,380 - INFO - Expected pages: [1]
2025-09-24 13:38:16,524 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf -> s3://document-extraction-logistically/temp/309c75f4_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:38:16,524 - INFO - 🔍 [13:38:16] Starting classification: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:38:16,525 - INFO - ⬆️ [13:38:16] Uploading: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:38:16,526 - INFO - Initializing TextractProcessor...
2025-09-24 13:38:16,540 - INFO - Initializing BedrockProcessor...
2025-09-24 13:38:16,544 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/309c75f4_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:38:16,545 - INFO - Processing PDF from S3...
2025-09-24 13:38:16,545 - INFO - Downloading PDF from S3 to /tmp/tmp_u9opvmk/309c75f4_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:38:17,160 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf -> s3://document-extraction-logistically/temp/77db471b_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:38:17,160 - INFO - 🔍 [13:38:17] Starting classification: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:38:17,161 - INFO - ⬆️ [13:38:17] Uploading: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:38:17,162 - INFO - Initializing TextractProcessor...
2025-09-24 13:38:17,177 - INFO - Initializing BedrockProcessor...
2025-09-24 13:38:17,181 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/77db471b_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:38:17,181 - INFO - Processing PDF from S3...
2025-09-24 13:38:17,182 - INFO - Downloading PDF from S3 to /tmp/tmp9s9j5xof/77db471b_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:38:18,431 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf -> s3://document-extraction-logistically/temp/a758382b_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:38:18,431 - INFO - 🔍 [13:38:18] Starting classification: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:38:18,432 - INFO - Initializing TextractProcessor...
2025-09-24 13:38:18,432 - INFO - ⬆️ [13:38:18] Uploading: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:38:18,446 - INFO - Initializing BedrockProcessor...
2025-09-24 13:38:18,451 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a758382b_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:38:18,451 - INFO - Processing PDF from S3...
2025-09-24 13:38:18,452 - INFO - Downloading PDF from S3 to /tmp/tmpko7cc76k/a758382b_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:38:18,625 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 13:38:18,625 - INFO - Splitting PDF into individual pages...
2025-09-24 13:38:18,627 - INFO - Splitting PDF 309c75f4_IZTBXFPGXBFH3DV900G4 into 1 pages
2025-09-24 13:38:18,629 - INFO - Split PDF into 1 pages
2025-09-24 13:38:18,629 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:38:18,629 - INFO - Expected pages: [1]
2025-09-24 13:38:18,962 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:38:18,963 - INFO - Splitting PDF into individual pages...
2025-09-24 13:38:18,964 - INFO - Splitting PDF 77db471b_MR6ONA8GK6HN1LCZHEX3 into 1 pages
2025-09-24 13:38:18,965 - INFO - Split PDF into 1 pages
2025-09-24 13:38:18,965 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:38:18,965 - INFO - Expected pages: [1]
2025-09-24 13:38:19,044 - INFO - Page 1: Extracted 1454 characters, 85 lines from 881bb95e_B3SIRREC9IAVZOJVDQSN_ee9a4061_page_001.pdf
2025-09-24 13:38:19,045 - INFO - Successfully processed page 1
2025-09-24 13:38:19,046 - INFO - Combined 1 pages into final text
2025-09-24 13:38:19,046 - INFO - Text validation for 881bb95e_B3SIRREC9IAVZOJVDQSN: 1471 characters, 1 pages
2025-09-24 13:38:19,046 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf -> s3://document-extraction-logistically/temp/91a84ddf_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:38:19,046 - INFO - 🔍 [13:38:19] Starting classification: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:38:19,046 - INFO - ⬆️ [13:38:19] Uploading: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:38:19,047 - INFO - Initializing TextractProcessor...
2025-09-24 13:38:19,049 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:38:19,054 - INFO - Initializing BedrockProcessor...
2025-09-24 13:38:19,054 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:38:19,057 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/91a84ddf_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:38:19,058 - INFO - Processing PDF from S3...
2025-09-24 13:38:19,058 - INFO - Downloading PDF from S3 to /tmp/tmpm_89hkpx/91a84ddf_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:38:19,473 - INFO - Page 1: Extracted 1255 characters, 79 lines from 4d9a7dd9_CUF54EHGMLQ57HR93DRB_18dfa1cb_page_001.pdf
2025-09-24 13:38:19,473 - INFO - Successfully processed page 1
2025-09-24 13:38:19,473 - INFO - Combined 1 pages into final text
2025-09-24 13:38:19,473 - INFO - Text validation for 4d9a7dd9_CUF54EHGMLQ57HR93DRB: 1272 characters, 1 pages
2025-09-24 13:38:19,473 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:38:19,473 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:38:19,639 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf -> s3://document-extraction-logistically/temp/34a1e9b4_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:38:19,639 - INFO - 🔍 [13:38:19] Starting classification: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:38:19,640 - INFO - ⬆️ [13:38:19] Uploading: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:38:19,646 - INFO - Initializing TextractProcessor...
2025-09-24 13:38:19,657 - INFO - Initializing BedrockProcessor...
2025-09-24 13:38:19,662 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/34a1e9b4_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:38:19,663 - INFO - Processing PDF from S3...
2025-09-24 13:38:19,663 - INFO - Downloading PDF from S3 to /tmp/tmpzcn7mn93/34a1e9b4_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:38:20,263 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf -> s3://document-extraction-logistically/temp/cffa246b_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:38:20,264 - INFO - 🔍 [13:38:20] Starting classification: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:38:20,265 - INFO - Initializing TextractProcessor...
2025-09-24 13:38:20,275 - INFO - Initializing BedrockProcessor...
2025-09-24 13:38:20,279 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cffa246b_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:38:20,279 - INFO - Processing PDF from S3...
2025-09-24 13:38:20,279 - INFO - Downloading PDF from S3 to /tmp/tmpub5sv8f5/cffa246b_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:38:20,779 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 13:38:20,780 - INFO - Splitting PDF into individual pages...
2025-09-24 13:38:20,781 - INFO - Splitting PDF a758382b_PB67IAPSJB1DZWMDIE1H into 2 pages
2025-09-24 13:38:20,784 - INFO - Split PDF into 2 pages
2025-09-24 13:38:20,784 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:38:20,784 - INFO - Expected pages: [1, 2]
2025-09-24 13:38:20,817 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:38:20,817 - INFO - Splitting PDF into individual pages...
2025-09-24 13:38:20,818 - INFO - Splitting PDF 91a84ddf_PEE2ZFMV7X0A0FL35G4G into 1 pages
2025-09-24 13:38:20,820 - INFO - Split PDF into 1 pages
2025-09-24 13:38:20,820 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:38:20,820 - INFO - Expected pages: [1]
2025-09-24 13:38:20,917 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:38:20,917 - INFO - Splitting PDF into individual pages...
2025-09-24 13:38:20,918 - INFO - Splitting PDF 34a1e9b4_U7BB1XSF3ASMIAE1MQ5I into 1 pages
2025-09-24 13:38:20,920 - INFO - Split PDF into 1 pages
2025-09-24 13:38:20,921 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:38:20,921 - INFO - Expected pages: [1]
2025-09-24 13:38:21,421 - INFO - Page 1: Extracted 1463 characters, 95 lines from 0fd53bb0_FYQQGIW8Z9DSAPCL0S9G_c008d1d4_page_001.pdf
2025-09-24 13:38:21,421 - INFO - Successfully processed page 1
2025-09-24 13:38:21,422 - INFO - Combined 1 pages into final text
2025-09-24 13:38:21,422 - INFO - Text validation for 0fd53bb0_FYQQGIW8Z9DSAPCL0S9G: 1480 characters, 1 pages
2025-09-24 13:38:21,422 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:38:21,422 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:38:22,369 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2ae54f8d-3c5d-4156-8d01-ec1ddc959151', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:08:22 GMT', 'content-type': 'application/json', 'content-length': '1476', 'connection': 'keep-alive', 'x-amzn-requestid': '2ae54f8d-3c5d-4156-8d01-ec1ddc959151'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. Need classify doc type. The page has weight and research program header, mentions "Weight and Research Program", "Weight and Research Section", "We certify that this shipment has been inspected and/or reweighed". Also contains declared value, HS? Not sure. The keywords: "Weight and Research Program", "Weight and Research Section" indicates weight and inspection? The category weight_and_inspection_cert: definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not recheck or reweight. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. This page seems to be a weight and research program report. It includes "Weight and Research Program" header, "Weight and Research Section". So likely "weight_and_inspection_cert". Also no mention of NMFC code. So weight_and_inspection_cert.\n\nWe need to output JSON object using tool classify_logistics_doc_type, with documents array containing one object: page_no 1, doc_type: "weight_and_inspection_cert".\n\nLet\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2901, 'outputTokens': 244, 'totalTokens': 3145}, 'metrics': {'latencyMs': 1331}}
2025-09-24 13:38:22,369 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4d9a7dd9_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:38:22,395 - INFO - 

CUF54EHGMLQ57HR93DRB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:38:22,395 - INFO - 

✓ Saved result: output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:38:22,690 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4d9a7dd9_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:38:22,704 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 13:38:22,704 - INFO - Splitting PDF into individual pages...
2025-09-24 13:38:22,705 - INFO - Splitting PDF cffa246b_YL4HZPZH7D3B03FZZ5QK into 1 pages
2025-09-24 13:38:22,719 - INFO - Split PDF into 1 pages
2025-09-24 13:38:22,720 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:38:22,720 - INFO - Expected pages: [1]
2025-09-24 13:38:23,006 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8082dcdc-fb3e-4810-9433-74753e2db40f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:08:22 GMT', 'content-type': 'application/json', 'content-length': '2080', 'connection': 'keep-alive', 'x-amzn-requestid': '8082dcdc-fb3e-4810-9433-74753e2db40f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify. The page contains "Weight and Research Program", "Weight and Research Section". It appears to be a Weight and Inspection Certificate? It\'s a "Weight and Research Program" with a "Weight and Research Section". There\'s "As found upon Examination", "We certify that this shipment has been inspected and/or reweighed", "and in accordance with governing tariffs." That sounds like a Weight and Inspection Certificate or a NMFC certification? The keywords: "Weight" and "Inspection" and "Certification" maybe. The description mentions "UPD CLASS PER NMFC 56840 ON BOL". This might be a NMFC certification? The doc type weight_and_inspection_cert: weight and inspection certificate. The note: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So we need to check if there are nmfc_cert keywords like "Correction", "Original", "As found upon Examination", "As found upon Examination" could be a correction? The page header "Original Correction" appears. The page says "DATE - 7/17/25", "Weight and Research Program", "Original Correction 7/02/25". That suggests it\'s a correction to a weight and inspection certificate. So it might be nmfc_cert. The definition: NMFC Certification is issued when correction is made. They mention "Original Correction" so that seems to be correction. Also "As found upon Examination" is a phrase used in NMFC corrections. So likely nmfc_cert. Let\'s see keywords: "Original Correction". That indicates a correction. So maybe nmfc_cert. So output doc_type: nmfc_cert. The page number 1. Output: {page_no:1, doc_type:"nmfc_cert"}.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2963, 'outputTokens': 405, 'totalTokens': 3368}, 'metrics': {'latencyMs': 1980}}
2025-09-24 13:38:23,006 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/881bb95e_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:38:23,041 - INFO - 

B3SIRREC9IAVZOJVDQSN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:38:23,041 - INFO - 

✓ Saved result: output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:38:23,300 - INFO - Page 1: Extracted 500 characters, 76 lines from 77db471b_MR6ONA8GK6HN1LCZHEX3_bd29da0d_page_001.pdf
2025-09-24 13:38:23,300 - INFO - Successfully processed page 1
2025-09-24 13:38:23,300 - INFO - Combined 1 pages into final text
2025-09-24 13:38:23,300 - INFO - Text validation for 77db471b_MR6ONA8GK6HN1LCZHEX3: 517 characters, 1 pages
2025-09-24 13:38:23,301 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:38:23,301 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:38:23,338 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/881bb95e_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:38:23,472 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '102961be-93bd-4742-815e-9f0f3a7d2f12', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:08:23 GMT', 'content-type': 'application/json', 'content-length': '832', 'connection': 'keep-alive', 'x-amzn-requestid': '102961be-93bd-4742-815e-9f0f3a7d2f12'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1. The content appears to be an NMFC Classification Certificate. Keywords: "NMFC Classification Certificate", "NMFC# 133300 S-3". Also mentions "Original Detail", "Inspection Detail".\n\nThus doc_type should be nmfc_cert. According to enum list: nmfc_cert — NMFC Classification Certificate or Correction notice. So classification: nmfc_cert.\n\nWe must output via function call with array of objects: each page: page_no: 1, doc_type: "nmfc_cert".\n\nReturn JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3018, 'outputTokens': 134, 'totalTokens': 3152}, 'metrics': {'latencyMs': 778}}
2025-09-24 13:38:23,472 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0fd53bb0_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:38:23,494 - INFO - Page 1: Extracted 465 characters, 37 lines from 309c75f4_IZTBXFPGXBFH3DV900G4_95e95874_page_001.pdf
2025-09-24 13:38:23,494 - INFO - Successfully processed page 1
2025-09-24 13:38:23,495 - INFO - Combined 1 pages into final text
2025-09-24 13:38:23,495 - INFO - Text validation for 309c75f4_IZTBXFPGXBFH3DV900G4: 482 characters, 1 pages
2025-09-24 13:38:23,496 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:38:23,496 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:38:23,499 - INFO - 

FYQQGIW8Z9DSAPCL0S9G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:38:23,499 - INFO - 

✓ Saved result: output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:38:23,791 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0fd53bb0_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:38:24,843 - INFO - Page 1: Extracted 1119 characters, 62 lines from 34a1e9b4_U7BB1XSF3ASMIAE1MQ5I_830fca42_page_001.pdf
2025-09-24 13:38:24,843 - INFO - Successfully processed page 1
2025-09-24 13:38:24,843 - INFO - Combined 1 pages into final text
2025-09-24 13:38:24,843 - INFO - Text validation for 34a1e9b4_U7BB1XSF3ASMIAE1MQ5I: 1136 characters, 1 pages
2025-09-24 13:38:24,843 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:38:24,844 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:38:25,044 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f5abc68a-10ed-4765-9d25-1a35268135e1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:08:24 GMT', 'content-type': 'application/json', 'content-length': '623', 'connection': 'keep-alive', 'x-amzn-requestid': 'f5abc68a-10ed-4765-9d25-1a35268135e1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page (page1). Determine doc_type. The page has header "NMFC CLASSIFICATION CERTIFICATE" and keywords "NMFC CLASSIFICATION CERTIFICATE". So likely nmfc_cert. The definition says NMFC Classification Certificate. So classify as nmfc_cert. Output JSON via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2707, 'outputTokens': 86, 'totalTokens': 2793}, 'metrics': {'latencyMs': 612}}
2025-09-24 13:38:25,045 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/77db471b_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:38:25,059 - INFO - 

MR6ONA8GK6HN1LCZHEX3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:38:25,059 - INFO - 

✓ Saved result: output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:38:25,117 - INFO - Page 1: Extracted 477 characters, 75 lines from 91a84ddf_PEE2ZFMV7X0A0FL35G4G_12125e9b_page_001.pdf
2025-09-24 13:38:25,117 - INFO - Successfully processed page 1
2025-09-24 13:38:25,117 - INFO - Combined 1 pages into final text
2025-09-24 13:38:25,118 - INFO - Text validation for 91a84ddf_PEE2ZFMV7X0A0FL35G4G: 494 characters, 1 pages
2025-09-24 13:38:25,118 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:38:25,118 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:38:25,358 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/77db471b_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:38:25,596 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8beaf3a1-591f-4c12-8895-69865483add1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:08:25 GMT', 'content-type': 'application/json', 'content-length': '1061', 'connection': 'keep-alive', 'x-amzn-requestid': '8beaf3a1-591f-4c12-8895-69865483add1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. The content includes "Weight & Inspection Freight Class Certificate H". Keywords: "Weight & Inspection", "Freight Class Certificate". This looks like a weight and inspection certificate (W&I). The description says "Weight & Inspection Freight Class Certificate". That matches weight_and_inspection_cert category. But need to check if nmfc_cert? It says "Freight Class Certificate" and "CORRECT CLASS PER DEFAULT DENSITY RATING". No mention of NMFC Code or correction notice. It doesn\'t mention NMFC. So likely weight_and_inspection_cert. The page shows page 3/3 but that\'s fine. So output: page_no 1, doc_type "weight_and_inspection_cert". Ensure tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2612, 'outputTokens': 177, 'totalTokens': 2789}, 'metrics': {'latencyMs': 911}}
2025-09-24 13:38:25,596 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/309c75f4_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:38:25,609 - INFO - 

IZTBXFPGXBFH3DV900G4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:38:25,609 - INFO - 

✓ Saved result: output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:38:25,910 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/309c75f4_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:38:26,683 - INFO - Page 2: Extracted 832 characters, 40 lines from a758382b_PB67IAPSJB1DZWMDIE1H_daeb1ec8_page_002.pdf
2025-09-24 13:38:26,684 - INFO - Successfully processed page 2
2025-09-24 13:38:26,801 - INFO - Page 1: Extracted 1225 characters, 82 lines from a758382b_PB67IAPSJB1DZWMDIE1H_daeb1ec8_page_001.pdf
2025-09-24 13:38:26,802 - INFO - Successfully processed page 1
2025-09-24 13:38:26,802 - INFO - Combined 2 pages into final text
2025-09-24 13:38:26,802 - INFO - Text validation for a758382b_PB67IAPSJB1DZWMDIE1H: 2093 characters, 2 pages
2025-09-24 13:38:26,803 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:38:26,803 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:38:27,033 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8b62862b-b7a7-450f-bf28-64b9a214a3ad', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:08:26 GMT', 'content-type': 'application/json', 'content-length': '825', 'connection': 'keep-alive', 'x-amzn-requestid': '8b62862b-b7a7-450f-bf28-64b9a214a3ad'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The header says "NMFC CLASSIFICATION CERTIFICATE". So doc_type should be nmfc_cert. According to spec, nmfc_cert is the NMFC Classification Certificate or Correction notice. Keywords: as described, NMFC code etc. This page shows NMFC classification certificate. So doc_type = nmfc_cert. Output must be JSON via function call classify_logistics_doc_type with array of documents: each object has page_no and doc_type. Only one page. Ensure correct format.\n\nReturn via function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2698, 'outputTokens': 127, 'totalTokens': 2825}, 'metrics': {'latencyMs': 760}}
2025-09-24 13:38:27,033 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/91a84ddf_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:38:27,045 - INFO - 

PEE2ZFMV7X0A0FL35G4G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:38:27,045 - INFO - 

✓ Saved result: output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:38:27,344 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/91a84ddf_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:38:28,276 - INFO - Page 1: Extracted 1248 characters, 88 lines from cffa246b_YL4HZPZH7D3B03FZZ5QK_bac98829_page_001.pdf
2025-09-24 13:38:28,277 - INFO - Successfully processed page 1
2025-09-24 13:38:28,277 - INFO - Combined 1 pages into final text
2025-09-24 13:38:28,277 - INFO - Text validation for cffa246b_YL4HZPZH7D3B03FZZ5QK: 1265 characters, 1 pages
2025-09-24 13:38:28,277 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:38:28,277 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:38:28,787 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e7844a40-48f6-4d06-8c44-988eee456b0d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:08:28 GMT', 'content-type': 'application/json', 'content-length': '3516', 'connection': 'keep-alive', 'x-amzn-requestid': 'e7844a40-48f6-4d06-8c44-988eee456b0d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to see if this is a specific doc type. The content: "ABF Freight System, Inc. ABF Freight Inspection Certificate" It says "Inspection Certificate". Also mention "Corrected Bill Information". The document seems to be an "Inspection Certificate" with weight, cube, density, NMFC, etc. It also says "CERTIFICATE INFORMATION" and "DOCUMENTATION HAS INVOICE" etc. Among categories, there is "inspection_cert" and "weight_and_inspection_cert" and "nmfc_cert".\n\nWe need to determine which. Let\'s check definition:\n\n- inspection_cert: Inspection Certificate, Cube Measurement certificate. Definition: Inspection certificate other than weight and inspection certificate which doesn\'t have weight mentioned.\n\n- weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not recheck or reweight. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. There is note: check if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep as weight_and_inspection_cert. Also check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise inspection_cert.\n\nThis appears to be a weight and inspection certificate: it contains weight (870), cube, density, etc. So likely weight_and_inspection_cert.\n\nBut there\'s also nmfc_cert: NMFC Classification Certificate or Correction notice. Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information. Also optional: NMFC Code, class #.\n\nThe document says "CORRECTED BILL INFORMATION" and "Corrected Bill Information". That suggests a correction. It also says "CERTIFICATE INFORMATION" and "DOCUMENTATION HAS INVOICE PACKING LIST PHOTO AVAILABLE OTHER DESCRIPTION No". It doesn\'t mention "class" but mentions NMFC 87560 class 92.5 and item. The header says "Inspection Certificate". So it\'s an inspection certificate, but might be a correction notice. The presence of "Corrected Bill Information" might indicate a correction. The note says "An inspection at an ABF terminal resulted in a correction". The header is "Inspection Certificate" not "Correction Notice". The note at bottom: "An inspection at an ABF terminal resulted in a correction". That suggests it\'s a corrected inspection certificate. The category "nmfc_cert" is for NMFC classification certificate or correction notice when a correction is made. The doc type "nmfc_cert" applies for correction notices. So likely nmfc_cert.\n\nCheck the note: "An inspection at an ABF terminal resulted in a correction" and "CORRECTED BILL INFORMATION" suggests a correction. In the definition of nmfc_cert: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." So yes.\n\nThus classify as nmfc_cert.\n\nWe need to output via tool call. Let\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2815, 'outputTokens': 658, 'totalTokens': 3473}, 'metrics': {'latencyMs': 2738}}
2025-09-24 13:38:28,788 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/34a1e9b4_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:38:28,804 - INFO - 

U7BB1XSF3ASMIAE1MQ5I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:38:28,804 - INFO - 

✓ Saved result: output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:38:29,100 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/34a1e9b4_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:38:30,799 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '390c91d0-4857-4f3d-a1e5-9d99c60dcb4c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:08:30 GMT', 'content-type': 'application/json', 'content-length': '1534', 'connection': 'keep-alive', 'x-amzn-requestid': '390c91d0-4857-4f3d-a1e5-9d99c60dcb4c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Input shows <page1> only? It says "Page 2 of 2" within page. That indicates two pages. But the input only includes page1 content. It may implicitly include page2? The content shows "Page 2 of 2" but no content of page 2. But we need classify pages. We should treat as single page? The instruction: "For every page in the input PDF you MUST return exactly one object describing that page." But we only have page1. Maybe the input only contains one page. The text has "Page 2 of 2" which suggests there are two pages but only first page is provided. We should classify page1. And if there is continuation note indicating page 2? It says "Page 2 of 2" indicates pagination. But we don\'t have page2 content. So we only classify page1. What type? The content shows "WEIGHTS & INSPECTION CERTIFICATE" and "Correction Date". This seems like a "weight_and_inspection_cert" (Weight and Inspection Certificate). It also includes "NMFC", "Class", "Total", etc. The presence of NMFC code, class, etc. So classification: weight_and_inspection_cert. We should output JSON array with one object: {page_no:1, doc_type:"weight_and_inspection_cert"}. Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2860, 'outputTokens': 308, 'totalTokens': 3168}, 'metrics': {'latencyMs': 1387}}
2025-09-24 13:38:30,799 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cffa246b_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:38:30,818 - INFO - 

YL4HZPZH7D3B03FZZ5QK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:38:30,818 - INFO - 

✓ Saved result: output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:38:30,832 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '45ce9cd3-aead-45d2-ba03-cf35dc7226ef', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:08:30 GMT', 'content-type': 'application/json', 'content-length': '3241', 'connection': 'keep-alive', 'x-amzn-requestid': '45ce9cd3-aead-45d2-ba03-cf35dc7226ef'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document seems to be a Weight & Research Certificate / Weight and Inspection Certificate. The page contains "WEIGHT & RESEARCH CERTIFICATE", "WR NBR", weight info etc. It also contains weight measurement, NMFC, classification, etc. The classification categories include weight_and_inspection_cert, inspection_cert, nmfc_cert. Need to decide. The description says: weight_and_inspection_cert: certificate/report issued by authority confirming weight possibly with length, width and height but not recheck or reweight. It must have weight and inspection. Also note: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." Wait: "If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So if the document contains nmfc_cert keywords, we should instead classify as nmfc_cert. But the nmfc_cert definition: NMFC Classification Certificate or Correction notice. Keywords: As described and As found or Original and inspection or Corrected class or Correction information. Also other keywords like NMFC Code, class #. This page has NMFC: 73260 /SUB NMFC: 09, CLASS: 70.0. Also mentions "W&R ISSUED". This is likely a Weight & Research certificate. But it also contains NMFC mention. Does that mean it\'s an NMFC certificate? The instruction says: "strictly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert". The keywords for nmfc_cert include "NMFC Code", "class #". This page has NMFC: 73260 /SUB NMFC: 09, CLASS: 70.0. That fits. Also the nmfc_cert is a correction notice. But is this a correction notice? It says "W&R ISSUED". It might be a W&R certificate, not necessarily an NMFC correction.\n\nBut maybe the classification guidelines for weight_and_inspection_cert say that if it has nmfc keywords, classify as nmfc_cert. So we should classify these pages as nmfc_cert.\n\nHowever, the page also has weight, cube, etc. The nmfc_cert definition is for NMFC classification certificate. The page states "NMFC: 73260 /SUB NMFC: 09". That indicates NMFC code. It might be a Weight & Research certificate referencing NMFC. Could be considered weight_and_inspection_cert. But the instruction clearly says to classify as nmfc_cert if it contains nmfc_cert keywords. So we will follow that.\n\nThus, both pages are nmfc_cert. However, we might need to check if page2 indicates "continued"? No. Page2 is not a continuation: page1 had weight & research certificate. Page2 also same header. They are separate pages of same document. So both are nmfc_cert.\n\nThus output:\n\npage_no 1, doc_type "nmfc_cert"\npage_no 2, doc_type "nmfc_cert"\n\nWe must return via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3306, 'outputTokens': 690, 'totalTokens': 3996}, 'metrics': {'latencyMs': 2842}}
2025-09-24 13:38:30,832 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a758382b_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:38:31,114 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cffa246b_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:38:31,142 - INFO - 

PB67IAPSJB1DZWMDIE1H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:38:31,142 - INFO - 

✓ Saved result: output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:38:31,439 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a758382b_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:38:31,440 - INFO - 
📊 Processing Summary:
2025-09-24 13:38:31,440 - INFO -    Total files: 9
2025-09-24 13:38:31,440 - INFO -    Successful: 9
2025-09-24 13:38:31,440 - INFO -    Failed: 0
2025-09-24 13:38:31,440 - INFO -    Duration: 20.06 seconds
2025-09-24 13:38:31,440 - INFO -    Output directory: output
2025-09-24 13:38:31,440 - INFO - 
📋 Successfully Processed Files:
2025-09-24 13:38:31,440 - INFO -    📄 B3SIRREC9IAVZOJVDQSN.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:38:31,440 - INFO -    📄 CUF54EHGMLQ57HR93DRB.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:38:31,440 - INFO -    📄 FYQQGIW8Z9DSAPCL0S9G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:38:31,440 - INFO -    📄 IZTBXFPGXBFH3DV900G4.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:38:31,440 - INFO -    📄 MR6ONA8GK6HN1LCZHEX3.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:38:31,440 - INFO -    📄 PB67IAPSJB1DZWMDIE1H.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}
2025-09-24 13:38:31,440 - INFO -    📄 PEE2ZFMV7X0A0FL35G4G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:38:31,440 - INFO -    📄 U7BB1XSF3ASMIAE1MQ5I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:38:31,440 - INFO -    📄 YL4HZPZH7D3B03FZZ5QK.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:38:31,441 - INFO - 
============================================================================================================================================
2025-09-24 13:38:31,441 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 13:38:31,441 - INFO - ============================================================================================================================================
2025-09-24 13:38:31,441 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 13:38:31,441 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:38:31,441 - INFO - B3SIRREC9IAVZOJVDQSN.pdf                           1      nmfc_cert            run1_B3SIRREC9IAVZOJVDQSN.json                    
2025-09-24 13:38:31,441 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:38:31,441 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:38:31,441 - INFO - 
2025-09-24 13:38:31,441 - INFO - CUF54EHGMLQ57HR93DRB.pdf                           1      weight_and_inspect... run1_CUF54EHGMLQ57HR93DRB.json                    
2025-09-24 13:38:31,441 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:38:31,441 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:38:31,441 - INFO - 
2025-09-24 13:38:31,441 - INFO - FYQQGIW8Z9DSAPCL0S9G.pdf                           1      nmfc_cert            run1_FYQQGIW8Z9DSAPCL0S9G.json                    
2025-09-24 13:38:31,441 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:38:31,441 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:38:31,441 - INFO - 
2025-09-24 13:38:31,441 - INFO - IZTBXFPGXBFH3DV900G4.pdf                           1      weight_and_inspect... run1_IZTBXFPGXBFH3DV900G4.json                    
2025-09-24 13:38:31,441 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:38:31,441 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:38:31,441 - INFO - 
2025-09-24 13:38:31,441 - INFO - MR6ONA8GK6HN1LCZHEX3.pdf                           1      nmfc_cert            run1_MR6ONA8GK6HN1LCZHEX3.json                    
2025-09-24 13:38:31,441 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:38:31,441 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:38:31,441 - INFO - 
2025-09-24 13:38:31,441 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           1      nmfc_cert            run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:38:31,441 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:38:31,441 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:38:31,441 - INFO - 
2025-09-24 13:38:31,441 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           2      nmfc_cert            run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:38:31,441 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:38:31,441 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:38:31,441 - INFO - 
2025-09-24 13:38:31,441 - INFO - PEE2ZFMV7X0A0FL35G4G.pdf                           1      nmfc_cert            run1_PEE2ZFMV7X0A0FL35G4G.json                    
2025-09-24 13:38:31,441 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:38:31,441 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:38:31,441 - INFO - 
2025-09-24 13:38:31,441 - INFO - U7BB1XSF3ASMIAE1MQ5I.pdf                           1      nmfc_cert            run1_U7BB1XSF3ASMIAE1MQ5I.json                    
2025-09-24 13:38:31,441 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:38:31,441 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:38:31,441 - INFO - 
2025-09-24 13:38:31,442 - INFO - YL4HZPZH7D3B03FZZ5QK.pdf                           1      weight_and_inspect... run1_YL4HZPZH7D3B03FZZ5QK.json                    
2025-09-24 13:38:31,442 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:38:31,442 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:38:31,442 - INFO - 
2025-09-24 13:38:31,442 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:38:31,442 - INFO - Total entries: 10
2025-09-24 13:38:31,442 - INFO - ============================================================================================================================================
2025-09-24 13:38:31,442 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 13:38:31,442 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:38:31,442 - INFO -   1. B3SIRREC9IAVZOJVDQSN.pdf            Page 1   → nmfc_cert       | run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:38:31,442 - INFO -   2. CUF54EHGMLQ57HR93DRB.pdf            Page 1   → weight_and_inspection_cert | run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:38:31,442 - INFO -   3. FYQQGIW8Z9DSAPCL0S9G.pdf            Page 1   → nmfc_cert       | run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:38:31,442 - INFO -   4. IZTBXFPGXBFH3DV900G4.pdf            Page 1   → weight_and_inspection_cert | run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:38:31,442 - INFO -   5. MR6ONA8GK6HN1LCZHEX3.pdf            Page 1   → nmfc_cert       | run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:38:31,442 - INFO -   6. PB67IAPSJB1DZWMDIE1H.pdf            Page 1   → nmfc_cert       | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:38:31,442 - INFO -   7. PB67IAPSJB1DZWMDIE1H.pdf            Page 2   → nmfc_cert       | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:38:31,442 - INFO -   8. PEE2ZFMV7X0A0FL35G4G.pdf            Page 1   → nmfc_cert       | run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:38:31,442 - INFO -   9. U7BB1XSF3ASMIAE1MQ5I.pdf            Page 1   → nmfc_cert       | run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:38:31,442 - INFO -  10. YL4HZPZH7D3B03FZZ5QK.pdf            Page 1   → weight_and_inspection_cert | run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:38:31,442 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:38:31,442 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 20.064983, 'processed_files': [{'filename': 'B3SIRREC9IAVZOJVDQSN.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf'}, {'filename': 'CUF54EHGMLQ57HR93DRB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf'}, {'filename': 'FYQQGIW8Z9DSAPCL0S9G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf'}, {'filename': 'IZTBXFPGXBFH3DV900G4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf'}, {'filename': 'MR6ONA8GK6HN1LCZHEX3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf'}, {'filename': 'PB67IAPSJB1DZWMDIE1H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf'}, {'filename': 'PEE2ZFMV7X0A0FL35G4G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf'}, {'filename': 'U7BB1XSF3ASMIAE1MQ5I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf'}, {'filename': 'YL4HZPZH7D3B03FZZ5QK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf'}]}
