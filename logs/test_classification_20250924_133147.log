2025-09-24 13:31:47,029 - INFO - Logging initialized. Log file: logs/test_classification_20250924_133147.log
2025-09-24 13:31:47,029 - INFO - 📁 Found 9 files to process
2025-09-24 13:31:47,029 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 13:31:47,029 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 13:31:47,029 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 13:31:47,029 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 13:31:47,029 - INFO - ⬆️ [13:31:47] Uploading: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:31:48,553 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf -> s3://document-extraction-logistically/temp/fbcbac1a_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:31:48,554 - INFO - 🔍 [13:31:48] Starting classification: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:31:48,555 - INFO - ⬆️ [13:31:48] Uploading: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:31:48,556 - INFO - Initializing TextractProcessor...
2025-09-24 13:31:48,579 - INFO - Initializing BedrockProcessor...
2025-09-24 13:31:48,588 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fbcbac1a_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:31:48,588 - INFO - Processing PDF from S3...
2025-09-24 13:31:48,588 - INFO - Downloading PDF from S3 to /tmp/tmpx60u037_/fbcbac1a_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:31:49,177 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf -> s3://document-extraction-logistically/temp/441274da_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:31:49,177 - INFO - 🔍 [13:31:49] Starting classification: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:31:49,178 - INFO - ⬆️ [13:31:49] Uploading: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:31:49,179 - INFO - Initializing TextractProcessor...
2025-09-24 13:31:49,197 - INFO - Initializing BedrockProcessor...
2025-09-24 13:31:49,201 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/441274da_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:31:49,201 - INFO - Processing PDF from S3...
2025-09-24 13:31:49,201 - INFO - Downloading PDF from S3 to /tmp/tmpwk09uo70/441274da_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:31:50,089 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:31:50,090 - INFO - Splitting PDF into individual pages...
2025-09-24 13:31:50,092 - INFO - Splitting PDF fbcbac1a_B3SIRREC9IAVZOJVDQSN into 1 pages
2025-09-24 13:31:50,099 - INFO - Split PDF into 1 pages
2025-09-24 13:31:50,099 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:31:50,099 - INFO - Expected pages: [1]
2025-09-24 13:31:50,186 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf -> s3://document-extraction-logistically/temp/82192404_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:31:50,186 - INFO - 🔍 [13:31:50] Starting classification: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:31:50,187 - INFO - ⬆️ [13:31:50] Uploading: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:31:50,189 - INFO - Initializing TextractProcessor...
2025-09-24 13:31:50,205 - INFO - Initializing BedrockProcessor...
2025-09-24 13:31:50,209 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/82192404_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:31:50,209 - INFO - Processing PDF from S3...
2025-09-24 13:31:50,209 - INFO - Downloading PDF from S3 to /tmp/tmp_97n1igw/82192404_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:31:50,704 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:31:50,705 - INFO - Splitting PDF into individual pages...
2025-09-24 13:31:50,706 - INFO - Splitting PDF 441274da_CUF54EHGMLQ57HR93DRB into 1 pages
2025-09-24 13:31:50,708 - INFO - Split PDF into 1 pages
2025-09-24 13:31:50,708 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:31:50,708 - INFO - Expected pages: [1]
2025-09-24 13:31:51,522 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf -> s3://document-extraction-logistically/temp/f68fece9_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:31:51,523 - INFO - 🔍 [13:31:51] Starting classification: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:31:51,524 - INFO - ⬆️ [13:31:51] Uploading: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:31:51,524 - INFO - Initializing TextractProcessor...
2025-09-24 13:31:51,543 - INFO - Initializing BedrockProcessor...
2025-09-24 13:31:51,547 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f68fece9_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:31:51,547 - INFO - Processing PDF from S3...
2025-09-24 13:31:51,547 - INFO - Downloading PDF from S3 to /tmp/tmpq6nu54vj/f68fece9_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:31:51,995 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:31:51,995 - INFO - Splitting PDF into individual pages...
2025-09-24 13:31:51,997 - INFO - Splitting PDF 82192404_FYQQGIW8Z9DSAPCL0S9G into 1 pages
2025-09-24 13:31:51,998 - INFO - Split PDF into 1 pages
2025-09-24 13:31:51,998 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:31:51,998 - INFO - Expected pages: [1]
2025-09-24 13:31:52,200 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf -> s3://document-extraction-logistically/temp/6b507ea0_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:31:52,201 - INFO - 🔍 [13:31:52] Starting classification: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:31:52,202 - INFO - ⬆️ [13:31:52] Uploading: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:31:52,208 - INFO - Initializing TextractProcessor...
2025-09-24 13:31:52,223 - INFO - Initializing BedrockProcessor...
2025-09-24 13:31:52,228 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6b507ea0_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:31:52,228 - INFO - Processing PDF from S3...
2025-09-24 13:31:52,229 - INFO - Downloading PDF from S3 to /tmp/tmpyn469j47/6b507ea0_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:31:53,555 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 13:31:53,555 - INFO - Splitting PDF into individual pages...
2025-09-24 13:31:53,556 - INFO - Splitting PDF f68fece9_IZTBXFPGXBFH3DV900G4 into 1 pages
2025-09-24 13:31:53,557 - INFO - Split PDF into 1 pages
2025-09-24 13:31:53,557 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:31:53,557 - INFO - Expected pages: [1]
2025-09-24 13:31:53,789 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf -> s3://document-extraction-logistically/temp/d6ae6b2f_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:31:53,789 - INFO - 🔍 [13:31:53] Starting classification: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:31:53,790 - INFO - ⬆️ [13:31:53] Uploading: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:31:53,792 - INFO - Initializing TextractProcessor...
2025-09-24 13:31:53,810 - INFO - Initializing BedrockProcessor...
2025-09-24 13:31:53,814 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d6ae6b2f_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:31:53,814 - INFO - Processing PDF from S3...
2025-09-24 13:31:53,815 - INFO - Downloading PDF from S3 to /tmp/tmpq7jpkx4s/d6ae6b2f_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:31:53,937 - INFO - Page 1: Extracted 1454 characters, 85 lines from fbcbac1a_B3SIRREC9IAVZOJVDQSN_dabebab1_page_001.pdf
2025-09-24 13:31:53,937 - INFO - Successfully processed page 1
2025-09-24 13:31:53,937 - INFO - Combined 1 pages into final text
2025-09-24 13:31:53,937 - INFO - Text validation for fbcbac1a_B3SIRREC9IAVZOJVDQSN: 1471 characters, 1 pages
2025-09-24 13:31:53,938 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:31:53,938 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:31:54,018 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:31:54,018 - INFO - Splitting PDF into individual pages...
2025-09-24 13:31:54,018 - INFO - Splitting PDF 6b507ea0_MR6ONA8GK6HN1LCZHEX3 into 1 pages
2025-09-24 13:31:54,019 - INFO - Split PDF into 1 pages
2025-09-24 13:31:54,019 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:31:54,019 - INFO - Expected pages: [1]
2025-09-24 13:31:54,479 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf -> s3://document-extraction-logistically/temp/6c174c7e_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:31:54,479 - INFO - 🔍 [13:31:54] Starting classification: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:31:54,480 - INFO - ⬆️ [13:31:54] Uploading: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:31:54,482 - INFO - Initializing TextractProcessor...
2025-09-24 13:31:54,496 - INFO - Initializing BedrockProcessor...
2025-09-24 13:31:54,500 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6c174c7e_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:31:54,501 - INFO - Processing PDF from S3...
2025-09-24 13:31:54,501 - INFO - Downloading PDF from S3 to /tmp/tmpcak9mrco/6c174c7e_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:31:54,660 - INFO - Page 1: Extracted 1255 characters, 79 lines from 441274da_CUF54EHGMLQ57HR93DRB_162642d8_page_001.pdf
2025-09-24 13:31:54,660 - INFO - Successfully processed page 1
2025-09-24 13:31:54,660 - INFO - Combined 1 pages into final text
2025-09-24 13:31:54,661 - INFO - Text validation for 441274da_CUF54EHGMLQ57HR93DRB: 1272 characters, 1 pages
2025-09-24 13:31:54,661 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:31:54,661 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:31:55,106 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf -> s3://document-extraction-logistically/temp/2f94480a_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:31:55,106 - INFO - 🔍 [13:31:55] Starting classification: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:31:55,107 - INFO - ⬆️ [13:31:55] Uploading: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:31:55,108 - INFO - Initializing TextractProcessor...
2025-09-24 13:31:55,126 - INFO - Initializing BedrockProcessor...
2025-09-24 13:31:55,132 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2f94480a_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:31:55,132 - INFO - Processing PDF from S3...
2025-09-24 13:31:55,132 - INFO - Downloading PDF from S3 to /tmp/tmppdwvhgs8/2f94480a_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:31:56,223 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:31:56,224 - INFO - Splitting PDF into individual pages...
2025-09-24 13:31:56,225 - INFO - Splitting PDF 6c174c7e_PEE2ZFMV7X0A0FL35G4G into 1 pages
2025-09-24 13:31:56,228 - INFO - Split PDF into 1 pages
2025-09-24 13:31:56,228 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:31:56,228 - INFO - Expected pages: [1]
2025-09-24 13:31:56,262 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 13:31:56,262 - INFO - Splitting PDF into individual pages...
2025-09-24 13:31:56,264 - INFO - Splitting PDF d6ae6b2f_PB67IAPSJB1DZWMDIE1H into 2 pages
2025-09-24 13:31:56,268 - INFO - Split PDF into 2 pages
2025-09-24 13:31:56,268 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:31:56,268 - INFO - Expected pages: [1, 2]
2025-09-24 13:31:56,347 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf -> s3://document-extraction-logistically/temp/b358e03c_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:31:56,347 - INFO - 🔍 [13:31:56] Starting classification: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:31:56,349 - INFO - Initializing TextractProcessor...
2025-09-24 13:31:56,365 - INFO - Initializing BedrockProcessor...
2025-09-24 13:31:56,369 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:31:56,369 - INFO - Splitting PDF into individual pages...
2025-09-24 13:31:56,370 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b358e03c_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:31:56,370 - INFO - Processing PDF from S3...
2025-09-24 13:31:56,371 - INFO - Downloading PDF from S3 to /tmp/tmp0jtm5zbf/b358e03c_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:31:56,371 - INFO - Splitting PDF 2f94480a_U7BB1XSF3ASMIAE1MQ5I into 1 pages
2025-09-24 13:31:56,378 - INFO - Split PDF into 1 pages
2025-09-24 13:31:56,378 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:31:56,378 - INFO - Expected pages: [1]
2025-09-24 13:31:56,749 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/441274da_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:31:56,766 - INFO - 

CUF54EHGMLQ57HR93DRB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:31:56,766 - INFO - 

✓ Saved result: output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:31:56,897 - INFO - Page 1: Extracted 1463 characters, 95 lines from 82192404_FYQQGIW8Z9DSAPCL0S9G_5964c9d6_page_001.pdf
2025-09-24 13:31:56,897 - INFO - Successfully processed page 1
2025-09-24 13:31:56,898 - INFO - Combined 1 pages into final text
2025-09-24 13:31:56,898 - INFO - Text validation for 82192404_FYQQGIW8Z9DSAPCL0S9G: 1480 characters, 1 pages
2025-09-24 13:31:56,898 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:31:56,898 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:31:57,055 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/441274da_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:31:58,476 - INFO - Page 1: Extracted 465 characters, 37 lines from f68fece9_IZTBXFPGXBFH3DV900G4_4eb551d0_page_001.pdf
2025-09-24 13:31:58,476 - INFO - Successfully processed page 1
2025-09-24 13:31:58,476 - INFO - Combined 1 pages into final text
2025-09-24 13:31:58,477 - INFO - Text validation for f68fece9_IZTBXFPGXBFH3DV900G4: 482 characters, 1 pages
2025-09-24 13:31:58,477 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:31:58,477 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:31:58,507 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fbcbac1a_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:31:58,524 - INFO - 

B3SIRREC9IAVZOJVDQSN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:31:58,524 - INFO - 

✓ Saved result: output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:31:58,647 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/82192404_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:31:58,679 - INFO - Page 1: Extracted 500 characters, 76 lines from 6b507ea0_MR6ONA8GK6HN1LCZHEX3_51ea0948_page_001.pdf
2025-09-24 13:31:58,679 - INFO - Successfully processed page 1
2025-09-24 13:31:58,679 - INFO - Combined 1 pages into final text
2025-09-24 13:31:58,679 - INFO - Text validation for 6b507ea0_MR6ONA8GK6HN1LCZHEX3: 517 characters, 1 pages
2025-09-24 13:31:58,680 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:31:58,680 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:31:58,729 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 13:31:58,730 - INFO - Splitting PDF into individual pages...
2025-09-24 13:31:58,730 - INFO - Splitting PDF b358e03c_YL4HZPZH7D3B03FZZ5QK into 1 pages
2025-09-24 13:31:58,741 - INFO - Split PDF into 1 pages
2025-09-24 13:31:58,741 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:31:58,741 - INFO - Expected pages: [1]
2025-09-24 13:31:58,819 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fbcbac1a_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:31:58,844 - INFO - 

FYQQGIW8Z9DSAPCL0S9G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:31:58,844 - INFO - 

✓ Saved result: output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:31:59,130 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/82192404_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:32:00,341 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6b507ea0_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:32:00,367 - INFO - 

MR6ONA8GK6HN1LCZHEX3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:32:00,367 - INFO - 

✓ Saved result: output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:32:00,548 - INFO - Page 1: Extracted 1119 characters, 62 lines from 2f94480a_U7BB1XSF3ASMIAE1MQ5I_4d25fef0_page_001.pdf
2025-09-24 13:32:00,548 - INFO - Successfully processed page 1
2025-09-24 13:32:00,549 - INFO - Combined 1 pages into final text
2025-09-24 13:32:00,549 - INFO - Text validation for 2f94480a_U7BB1XSF3ASMIAE1MQ5I: 1136 characters, 1 pages
2025-09-24 13:32:00,550 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:32:00,550 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:32:00,556 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f68fece9_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:32:00,675 - INFO - Page 1: Extracted 477 characters, 75 lines from 6c174c7e_PEE2ZFMV7X0A0FL35G4G_4fff36fd_page_001.pdf
2025-09-24 13:32:00,675 - INFO - Successfully processed page 1
2025-09-24 13:32:00,676 - INFO - Combined 1 pages into final text
2025-09-24 13:32:00,676 - INFO - Text validation for 6c174c7e_PEE2ZFMV7X0A0FL35G4G: 494 characters, 1 pages
2025-09-24 13:32:00,676 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6b507ea0_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:32:00,677 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:32:00,678 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:32:00,691 - INFO - 

IZTBXFPGXBFH3DV900G4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:32:00,691 - INFO - 

✓ Saved result: output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:32:00,981 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f68fece9_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:32:01,402 - INFO - Page 2: Extracted 832 characters, 40 lines from d6ae6b2f_PB67IAPSJB1DZWMDIE1H_0b50ee24_page_002.pdf
2025-09-24 13:32:01,403 - INFO - Successfully processed page 2
2025-09-24 13:32:02,190 - INFO - Page 1: Extracted 1225 characters, 82 lines from d6ae6b2f_PB67IAPSJB1DZWMDIE1H_0b50ee24_page_001.pdf
2025-09-24 13:32:02,190 - INFO - Successfully processed page 1
2025-09-24 13:32:02,190 - INFO - Combined 2 pages into final text
2025-09-24 13:32:02,191 - INFO - Text validation for d6ae6b2f_PB67IAPSJB1DZWMDIE1H: 2093 characters, 2 pages
2025-09-24 13:32:02,191 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:32:02,191 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:32:02,594 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6c174c7e_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:32:02,603 - INFO - 

PEE2ZFMV7X0A0FL35G4G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:32:02,603 - INFO - 

✓ Saved result: output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:32:02,893 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6c174c7e_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:32:04,042 - INFO - Page 1: Extracted 1248 characters, 88 lines from b358e03c_YL4HZPZH7D3B03FZZ5QK_3ceb7990_page_001.pdf
2025-09-24 13:32:04,042 - INFO - Successfully processed page 1
2025-09-24 13:32:04,042 - INFO - Combined 1 pages into final text
2025-09-24 13:32:04,042 - INFO - Text validation for b358e03c_YL4HZPZH7D3B03FZZ5QK: 1265 characters, 1 pages
2025-09-24 13:32:04,043 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:32:04,043 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:32:05,718 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-24 13:32:05,719 - ERROR - Processing failed for s3://document-extraction-logistically/temp/2f94480a_U7BB1XSF3ASMIAE1MQ5I.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:32:05,719 - ERROR - ❌ Classification failed for s3://document-extraction-logistically/temp/2f94480a_U7BB1XSF3ASMIAE1MQ5I.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:32:05,721 - ERROR - ❌ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 139, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 13:32:05,721 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 13:32:05,722 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:32:05,722 - ERROR - ✗ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 139, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 13:32:05,722 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 13:32:06,009 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2f94480a_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:32:07,300 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d6ae6b2f_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:32:07,336 - INFO - 

PB67IAPSJB1DZWMDIE1H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:32:07,336 - INFO - 

✓ Saved result: output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:32:07,401 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b358e03c_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:32:07,627 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d6ae6b2f_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:32:07,649 - INFO - 

YL4HZPZH7D3B03FZZ5QK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:32:07,649 - INFO - 

✓ Saved result: output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:32:08,007 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b358e03c_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:32:08,008 - INFO - 
📊 Processing Summary:
2025-09-24 13:32:08,009 - INFO -    Total files: 9
2025-09-24 13:32:08,009 - INFO -    Successful: 8
2025-09-24 13:32:08,009 - INFO -    Failed: 1
2025-09-24 13:32:08,009 - INFO -    Duration: 20.98 seconds
2025-09-24 13:32:08,009 - INFO -    Output directory: output
2025-09-24 13:32:08,009 - INFO - 
📋 Successfully Processed Files:
2025-09-24 13:32:08,009 - INFO -    📄 B3SIRREC9IAVZOJVDQSN.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:32:08,009 - INFO -    📄 CUF54EHGMLQ57HR93DRB.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:32:08,009 - INFO -    📄 FYQQGIW8Z9DSAPCL0S9G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:32:08,009 - INFO -    📄 IZTBXFPGXBFH3DV900G4.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:32:08,010 - INFO -    📄 MR6ONA8GK6HN1LCZHEX3.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:32:08,010 - INFO -    📄 PB67IAPSJB1DZWMDIE1H.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:32:08,010 - INFO -    📄 PEE2ZFMV7X0A0FL35G4G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:32:08,010 - INFO -    📄 YL4HZPZH7D3B03FZZ5QK.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:32:08,010 - ERROR - 
❌ Errors:
2025-09-24 13:32:08,010 - ERROR -    Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:32:08,010 - INFO - 
============================================================================================================================================
2025-09-24 13:32:08,011 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 13:32:08,011 - INFO - ============================================================================================================================================
2025-09-24 13:32:08,011 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 13:32:08,011 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:32:08,011 - INFO - B3SIRREC9IAVZOJVDQSN.pdf                           1      weight_and_inspect... run1_B3SIRREC9IAVZOJVDQSN.json                    
2025-09-24 13:32:08,011 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:32:08,011 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:32:08,011 - INFO - 
2025-09-24 13:32:08,011 - INFO - CUF54EHGMLQ57HR93DRB.pdf                           1      weight_and_inspect... run1_CUF54EHGMLQ57HR93DRB.json                    
2025-09-24 13:32:08,011 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:32:08,011 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:32:08,011 - INFO - 
2025-09-24 13:32:08,011 - INFO - FYQQGIW8Z9DSAPCL0S9G.pdf                           1      nmfc_cert            run1_FYQQGIW8Z9DSAPCL0S9G.json                    
2025-09-24 13:32:08,011 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:32:08,011 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:32:08,011 - INFO - 
2025-09-24 13:32:08,012 - INFO - IZTBXFPGXBFH3DV900G4.pdf                           1      weight_and_inspect... run1_IZTBXFPGXBFH3DV900G4.json                    
2025-09-24 13:32:08,012 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:32:08,012 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:32:08,012 - INFO - 
2025-09-24 13:32:08,012 - INFO - MR6ONA8GK6HN1LCZHEX3.pdf                           1      nmfc_cert            run1_MR6ONA8GK6HN1LCZHEX3.json                    
2025-09-24 13:32:08,012 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:32:08,012 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:32:08,012 - INFO - 
2025-09-24 13:32:08,012 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           1      weight_and_inspect... run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:32:08,012 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:32:08,012 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:32:08,012 - INFO - 
2025-09-24 13:32:08,012 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           2      weight_and_inspect... run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:32:08,012 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:32:08,012 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:32:08,012 - INFO - 
2025-09-24 13:32:08,012 - INFO - PEE2ZFMV7X0A0FL35G4G.pdf                           1      nmfc_cert            run1_PEE2ZFMV7X0A0FL35G4G.json                    
2025-09-24 13:32:08,012 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:32:08,012 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:32:08,013 - INFO - 
2025-09-24 13:32:08,013 - INFO - YL4HZPZH7D3B03FZZ5QK.pdf                           1      nmfc_cert            run1_YL4HZPZH7D3B03FZZ5QK.json                    
2025-09-24 13:32:08,013 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:32:08,013 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:32:08,013 - INFO - 
2025-09-24 13:32:08,013 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:32:08,013 - INFO - Total entries: 9
2025-09-24 13:32:08,013 - INFO - ============================================================================================================================================
2025-09-24 13:32:08,013 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 13:32:08,013 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:32:08,013 - INFO -   1. B3SIRREC9IAVZOJVDQSN.pdf            Page 1   → weight_and_inspection_cert | run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:32:08,013 - INFO -   2. CUF54EHGMLQ57HR93DRB.pdf            Page 1   → weight_and_inspection_cert | run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:32:08,013 - INFO -   3. FYQQGIW8Z9DSAPCL0S9G.pdf            Page 1   → nmfc_cert       | run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:32:08,013 - INFO -   4. IZTBXFPGXBFH3DV900G4.pdf            Page 1   → weight_and_inspection_cert | run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:32:08,013 - INFO -   5. MR6ONA8GK6HN1LCZHEX3.pdf            Page 1   → nmfc_cert       | run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:32:08,013 - INFO -   6. PB67IAPSJB1DZWMDIE1H.pdf            Page 1   → weight_and_inspection_cert | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:32:08,013 - INFO -   7. PB67IAPSJB1DZWMDIE1H.pdf            Page 2   → weight_and_inspection_cert | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:32:08,013 - INFO -   8. PEE2ZFMV7X0A0FL35G4G.pdf            Page 1   → nmfc_cert       | run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:32:08,013 - INFO -   9. YL4HZPZH7D3B03FZZ5QK.pdf            Page 1   → nmfc_cert       | run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:32:08,013 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:32:08,014 - ERROR - 🐛 Error occurred during processing - entering debugger...
2025-09-24 13:32:08,014 - ERROR - 🐛 Debug info: {'exception': Exception('Unexpected tool response format from Bedrock'), 'traceback': 'Traceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 139, in extract_tool_response\n    raise Exception("Stop reason is not tool_use")\nException: Stop reason is not tool_use\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file\n    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)\n  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync\n    result = loop.run_until_complete(llm_classification(s3_uri))\n  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete\n    return future.result()\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main\n    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock\n    content = bedrock_processor.extract_tool_response(response)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response\n    raise Exception("Unexpected tool response format from Bedrock")\nException: Unexpected tool response format from Bedrock\n', 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf', 's3_uri': 's3://document-extraction-logistically/temp/2f94480a_U7BB1XSF3ASMIAE1MQ5I.pdf', 'location': 'process_single_file'}
2025-09-24 13:32:08,014 - ERROR - 🐛 Location: process_single_file
2025-09-24 13:32:08,014 - ERROR - 🐛 Exception: Unexpected tool response format from Bedrock
2025-09-24 13:32:08,014 - ERROR - 🐛 Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 139, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

