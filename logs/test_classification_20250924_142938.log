2025-09-24 14:29:38,608 - INFO - Logging initialized. Log file: logs/test_classification_20250924_142938.log
2025-09-24 14:29:38,609 - INFO - 📁 Found 11 files to process
2025-09-24 14:29:38,609 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:29:38,609 - INFO - 🚀 Processing 11 files in FORCED PARALLEL MODE...
2025-09-24 14:29:38,609 - INFO - 🚀 Creating 11 parallel tasks...
2025-09-24 14:29:38,609 - INFO - 🚀 All 11 tasks created - executing in parallel...
2025-09-24 14:29:38,609 - INFO - ⬆️ [14:29:38] Uploading: A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:29:39,959 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/A331OW5F2Q6LZDSTAWR6.pdf -> s3://document-extraction-logistically/temp/866e290f_A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:29:39,959 - INFO - 🔍 [14:29:39] Starting classification: A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:29:39,959 - INFO - ⬆️ [14:29:39] Uploading: AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:29:39,960 - INFO - Initializing TextractProcessor...
2025-09-24 14:29:39,971 - INFO - Initializing BedrockProcessor...
2025-09-24 14:29:39,976 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/866e290f_A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:29:39,976 - INFO - Processing PDF from S3...
2025-09-24 14:29:39,976 - INFO - Downloading PDF from S3 to /tmp/tmpabzppket/866e290f_A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:29:41,334 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:29:41,334 - INFO - Splitting PDF into individual pages...
2025-09-24 14:29:41,335 - INFO - Splitting PDF 866e290f_A331OW5F2Q6LZDSTAWR6 into 1 pages
2025-09-24 14:29:41,342 - INFO - Split PDF into 1 pages
2025-09-24 14:29:41,342 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:29:41,342 - INFO - Expected pages: [1]
2025-09-24 14:29:41,417 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/AC4907XUC5FFS63AXXBG.pdf -> s3://document-extraction-logistically/temp/d2b62c5f_AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:29:41,418 - INFO - 🔍 [14:29:41] Starting classification: AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:29:41,419 - INFO - ⬆️ [14:29:41] Uploading: BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:29:41,421 - INFO - Initializing TextractProcessor...
2025-09-24 14:29:41,446 - INFO - Initializing BedrockProcessor...
2025-09-24 14:29:41,453 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d2b62c5f_AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:29:41,453 - INFO - Processing PDF from S3...
2025-09-24 14:29:41,453 - INFO - Downloading PDF from S3 to /tmp/tmpc8so0or4/d2b62c5f_AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:29:42,013 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/BPKB6NN7SWJR9KBHTWZD.pdf -> s3://document-extraction-logistically/temp/bee06c08_BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:29:42,014 - INFO - 🔍 [14:29:42] Starting classification: BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:29:42,015 - INFO - ⬆️ [14:29:42] Uploading: DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:29:42,016 - INFO - Initializing TextractProcessor...
2025-09-24 14:29:42,036 - INFO - Initializing BedrockProcessor...
2025-09-24 14:29:42,039 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bee06c08_BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:29:42,039 - INFO - Processing PDF from S3...
2025-09-24 14:29:42,039 - INFO - Downloading PDF from S3 to /tmp/tmpwln6_d7o/bee06c08_BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:29:42,576 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/DY354TSLN9XYYH2RWAW5.pdf -> s3://document-extraction-logistically/temp/9f7174a3_DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:29:42,576 - INFO - 🔍 [14:29:42] Starting classification: DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:29:42,577 - INFO - ⬆️ [14:29:42] Uploading: N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:29:42,578 - INFO - Initializing TextractProcessor...
2025-09-24 14:29:42,598 - INFO - Initializing BedrockProcessor...
2025-09-24 14:29:42,602 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9f7174a3_DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:29:42,602 - INFO - Processing PDF from S3...
2025-09-24 14:29:42,602 - INFO - Downloading PDF from S3 to /tmp/tmpc4cggo24/9f7174a3_DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:29:43,147 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/N5ZTF7GH4USQJIPCIXEA.pdf -> s3://document-extraction-logistically/temp/8debfa69_N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:29:43,147 - INFO - 🔍 [14:29:43] Starting classification: N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:29:43,148 - INFO - Initializing TextractProcessor...
2025-09-24 14:29:43,153 - INFO - ⬆️ [14:29:43] Uploading: NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:29:43,171 - INFO - Initializing BedrockProcessor...
2025-09-24 14:29:43,177 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8debfa69_N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:29:43,177 - INFO - Processing PDF from S3...
2025-09-24 14:29:43,178 - INFO - Downloading PDF from S3 to /tmp/tmpnb9c0iol/8debfa69_N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:29:43,790 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/NUYKWFH99COJT60C78IZ.pdf -> s3://document-extraction-logistically/temp/2af90277_NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:29:43,790 - INFO - 🔍 [14:29:43] Starting classification: NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:29:43,791 - INFO - ⬆️ [14:29:43] Uploading: OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:29:43,793 - INFO - Initializing TextractProcessor...
2025-09-24 14:29:43,813 - INFO - Initializing BedrockProcessor...
2025-09-24 14:29:43,816 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2af90277_NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:29:43,816 - INFO - Processing PDF from S3...
2025-09-24 14:29:43,817 - INFO - Downloading PDF from S3 to /tmp/tmp2er5jxwd/2af90277_NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:29:43,827 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:29:43,827 - INFO - Splitting PDF into individual pages...
2025-09-24 14:29:43,828 - INFO - Splitting PDF 9f7174a3_DY354TSLN9XYYH2RWAW5 into 1 pages
2025-09-24 14:29:43,834 - INFO - Split PDF into 1 pages
2025-09-24 14:29:43,835 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:29:43,835 - INFO - Expected pages: [1]
2025-09-24 14:29:43,918 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:29:43,918 - INFO - Splitting PDF into individual pages...
2025-09-24 14:29:43,919 - INFO - Splitting PDF bee06c08_BPKB6NN7SWJR9KBHTWZD into 1 pages
2025-09-24 14:29:43,927 - INFO - Split PDF into 1 pages
2025-09-24 14:29:43,927 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:29:43,927 - INFO - Expected pages: [1]
2025-09-24 14:29:44,379 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/OBUNADUYEXKR65OJ917C.pdf -> s3://document-extraction-logistically/temp/ace1d85a_OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:29:44,380 - INFO - 🔍 [14:29:44] Starting classification: OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:29:44,380 - INFO - ⬆️ [14:29:44] Uploading: S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:29:44,382 - INFO - Initializing TextractProcessor...
2025-09-24 14:29:44,434 - INFO - Initializing BedrockProcessor...
2025-09-24 14:29:44,436 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ace1d85a_OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:29:44,437 - INFO - Processing PDF from S3...
2025-09-24 14:29:44,437 - INFO - Downloading PDF from S3 to /tmp/tmppt2tq7fn/ace1d85a_OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:29:44,462 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:29:44,462 - INFO - Splitting PDF into individual pages...
2025-09-24 14:29:44,464 - INFO - Splitting PDF 8debfa69_N5ZTF7GH4USQJIPCIXEA into 1 pages
2025-09-24 14:29:44,472 - INFO - Split PDF into 1 pages
2025-09-24 14:29:44,473 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:29:44,473 - INFO - Expected pages: [1]
2025-09-24 14:29:44,548 - INFO - Page 1: Extracted 274 characters, 22 lines from 866e290f_A331OW5F2Q6LZDSTAWR6_1715813b_page_001.pdf
2025-09-24 14:29:44,549 - INFO - Successfully processed page 1
2025-09-24 14:29:44,549 - INFO - Combined 1 pages into final text
2025-09-24 14:29:44,549 - INFO - Text validation for 866e290f_A331OW5F2Q6LZDSTAWR6: 291 characters, 1 pages
2025-09-24 14:29:44,550 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:29:44,550 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:29:44,992 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/S917SYX1NI0B6KSG1VL8.pdf -> s3://document-extraction-logistically/temp/83e5a791_S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:29:44,992 - INFO - 🔍 [14:29:44] Starting classification: S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:29:44,992 - INFO - ⬆️ [14:29:44] Uploading: VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:29:44,993 - INFO - Initializing TextractProcessor...
2025-09-24 14:29:45,002 - INFO - Initializing BedrockProcessor...
2025-09-24 14:29:45,004 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/83e5a791_S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:29:45,004 - INFO - Processing PDF from S3...
2025-09-24 14:29:45,004 - INFO - Downloading PDF from S3 to /tmp/tmp5tmuyabi/83e5a791_S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:29:45,597 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/VIASDRA5CL365YHEEWOV.pdf -> s3://document-extraction-logistically/temp/cd349347_VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:29:45,598 - INFO - 🔍 [14:29:45] Starting classification: VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:29:45,599 - INFO - ⬆️ [14:29:45] Uploading: XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:29:45,602 - INFO - Initializing TextractProcessor...
2025-09-24 14:29:45,621 - INFO - Initializing BedrockProcessor...
2025-09-24 14:29:45,623 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/cd349347_VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:29:45,624 - INFO - Processing PDF from S3...
2025-09-24 14:29:45,624 - INFO - Downloading PDF from S3 to /tmp/tmp76d6wi3b/cd349347_VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:29:45,914 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 14:29:45,914 - INFO - Splitting PDF into individual pages...
2025-09-24 14:29:45,915 - INFO - Splitting PDF 2af90277_NUYKWFH99COJT60C78IZ into 1 pages
2025-09-24 14:29:45,922 - INFO - Split PDF into 1 pages
2025-09-24 14:29:45,922 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:29:45,922 - INFO - Expected pages: [1]
2025-09-24 14:29:46,180 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/XIYO2PDG2ILYXT57MES6.pdf -> s3://document-extraction-logistically/temp/d1c2c638_XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:29:46,180 - INFO - 🔍 [14:29:46] Starting classification: XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:29:46,181 - INFO - ⬆️ [14:29:46] Uploading: ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:29:46,184 - INFO - Initializing TextractProcessor...
2025-09-24 14:29:46,208 - INFO - Initializing BedrockProcessor...
2025-09-24 14:29:46,213 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d1c2c638_XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:29:46,213 - INFO - Processing PDF from S3...
2025-09-24 14:29:46,213 - INFO - Downloading PDF from S3 to /tmp/tmpz0ual88i/d1c2c638_XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:29:46,220 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5f246e7a-b418-487c-9ac5-736f03a37976', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:59:46 GMT', 'content-type': 'application/json', 'content-length': '499', 'connection': 'keep-alive', 'x-amzn-requestid': '5f246e7a-b418-487c-9ac5-736f03a37976'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It has header "Purchase Order". So doc_type: po. Output via tool classify_logistics_doc_type with documents array containing one object: page_no 1, doc_type "po".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2567, 'outputTokens': 61, 'totalTokens': 2628}, 'metrics': {'latencyMs': 452}}
2025-09-24 14:29:46,221 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/866e290f_A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:29:46,292 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:29:46,292 - INFO - Splitting PDF into individual pages...
2025-09-24 14:29:46,294 - INFO - Splitting PDF ace1d85a_OBUNADUYEXKR65OJ917C into 1 pages
2025-09-24 14:29:46,299 - INFO - Split PDF into 1 pages
2025-09-24 14:29:46,299 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:29:46,299 - INFO - Expected pages: [1]
2025-09-24 14:29:46,788 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/ZFBHUC7YFRE4Z58IJGC5.PDF -> s3://document-extraction-logistically/temp/be36d558_ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:29:46,788 - INFO - 🔍 [14:29:46] Starting classification: ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:29:46,789 - INFO - Initializing TextractProcessor...
2025-09-24 14:29:46,800 - INFO - Initializing BedrockProcessor...
2025-09-24 14:29:46,800 - INFO - 

A331OW5F2Q6LZDSTAWR6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:29:46,802 - INFO - 

✓ Saved result: output/run1_A331OW5F2Q6LZDSTAWR6.json
2025-09-24 14:29:46,805 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/be36d558_ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:29:46,805 - INFO - Processing PDF from S3...
2025-09-24 14:29:46,806 - INFO - Downloading PDF from S3 to /tmp/tmp9xbhs7e_/be36d558_ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:29:47,082 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/866e290f_A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:29:47,468 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:29:47,468 - INFO - Splitting PDF into individual pages...
2025-09-24 14:29:47,468 - INFO - Splitting PDF cd349347_VIASDRA5CL365YHEEWOV into 1 pages
2025-09-24 14:29:47,471 - INFO - Split PDF into 1 pages
2025-09-24 14:29:47,471 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:29:47,471 - INFO - Expected pages: [1]
2025-09-24 14:29:47,626 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:29:47,627 - INFO - Splitting PDF into individual pages...
2025-09-24 14:29:47,631 - INFO - Splitting PDF 83e5a791_S917SYX1NI0B6KSG1VL8 into 1 pages
2025-09-24 14:29:47,641 - INFO - Split PDF into 1 pages
2025-09-24 14:29:47,641 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:29:47,641 - INFO - Expected pages: [1]
2025-09-24 14:29:47,650 - INFO - Page 1: Extracted 272 characters, 23 lines from 8debfa69_N5ZTF7GH4USQJIPCIXEA_0f4cb2a4_page_001.pdf
2025-09-24 14:29:47,651 - INFO - Successfully processed page 1
2025-09-24 14:29:47,651 - INFO - Combined 1 pages into final text
2025-09-24 14:29:47,651 - INFO - Text validation for 8debfa69_N5ZTF7GH4USQJIPCIXEA: 289 characters, 1 pages
2025-09-24 14:29:47,652 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:29:47,652 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:29:47,774 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:29:47,774 - INFO - Splitting PDF into individual pages...
2025-09-24 14:29:47,775 - INFO - Splitting PDF d1c2c638_XIYO2PDG2ILYXT57MES6 into 1 pages
2025-09-24 14:29:47,787 - INFO - Split PDF into 1 pages
2025-09-24 14:29:47,788 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:29:47,788 - INFO - Expected pages: [1]
2025-09-24 14:29:48,308 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:29:48,309 - INFO - Splitting PDF into individual pages...
2025-09-24 14:29:48,310 - INFO - Splitting PDF be36d558_ZFBHUC7YFRE4Z58IJGC5 into 1 pages
2025-09-24 14:29:48,320 - INFO - Split PDF into 1 pages
2025-09-24 14:29:48,320 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:29:48,320 - INFO - Expected pages: [1]
2025-09-24 14:29:48,445 - INFO - Page 1: Extracted 1915 characters, 67 lines from 9f7174a3_DY354TSLN9XYYH2RWAW5_98977c53_page_001.pdf
2025-09-24 14:29:48,445 - INFO - Successfully processed page 1
2025-09-24 14:29:48,445 - INFO - Combined 1 pages into final text
2025-09-24 14:29:48,445 - INFO - Text validation for 9f7174a3_DY354TSLN9XYYH2RWAW5: 1932 characters, 1 pages
2025-09-24 14:29:48,446 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:29:48,446 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:29:48,564 - INFO - Page 1: Extracted 760 characters, 52 lines from bee06c08_BPKB6NN7SWJR9KBHTWZD_03eddeea_page_001.pdf
2025-09-24 14:29:48,565 - INFO - Successfully processed page 1
2025-09-24 14:29:48,566 - INFO - Combined 1 pages into final text
2025-09-24 14:29:48,566 - INFO - Text validation for bee06c08_BPKB6NN7SWJR9KBHTWZD: 777 characters, 1 pages
2025-09-24 14:29:48,566 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:29:48,566 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:29:49,289 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 14:29:49,289 - INFO - Splitting PDF into individual pages...
2025-09-24 14:29:49,291 - INFO - Splitting PDF d2b62c5f_AC4907XUC5FFS63AXXBG into 1 pages
2025-09-24 14:29:49,307 - INFO - Split PDF into 1 pages
2025-09-24 14:29:49,307 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:29:49,307 - INFO - Expected pages: [1]
2025-09-24 14:29:49,425 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '548328de-2996-417e-af99-867620fbabca', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:59:49 GMT', 'content-type': 'application/json', 'content-length': '544', 'connection': 'keep-alive', 'x-amzn-requestid': '548328de-2996-417e-af99-867620fbabca'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page, likely a Purchase Order. The header shows "Purchase Order" and fields: # Description, Price, Total, etc. So doc_type: "po". It is not a continuation page. So output page_no 1, doc_type "po". Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2570, 'outputTokens': 77, 'totalTokens': 2647}, 'metrics': {'latencyMs': 529}}
2025-09-24 14:29:49,426 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8debfa69_N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:29:49,437 - INFO - 

N5ZTF7GH4USQJIPCIXEA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:29:49,437 - INFO - 

✓ Saved result: output/run1_N5ZTF7GH4USQJIPCIXEA.json
2025-09-24 14:29:49,715 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8debfa69_N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:29:50,163 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '251fe4e1-ad9d-4c65-81f3-3a6d1218817e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:59:50 GMT', 'content-type': 'application/json', 'content-length': '501', 'connection': 'keep-alive', 'x-amzn-requestid': '251fe4e1-ad9d-4c65-81f3-3a6d1218817e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It\'s a purchase order. The keywords: "Framework Purchase Order", "PURCHASE ORDER NUMBER". So doc_type is po. No continuation. Output must be JSON via tool call.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2980, 'outputTokens': 59, 'totalTokens': 3039}, 'metrics': {'latencyMs': 468}}
2025-09-24 14:29:50,164 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9f7174a3_DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:29:50,197 - INFO - 

DY354TSLN9XYYH2RWAW5.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:29:50,197 - INFO - 

✓ Saved result: output/run1_DY354TSLN9XYYH2RWAW5.json
2025-09-24 14:29:50,220 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3ca2987b-b66c-4a02-bae1-5ba26571e729', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:59:50 GMT', 'content-type': 'application/json', 'content-length': '556', 'connection': 'keep-alive', 'x-amzn-requestid': '3ca2987b-b66c-4a02-bae1-5ba26571e729'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page PDF presumably. The content shows a "Purchase Order". The page header. So classify as "po". Must return an array of objects describing each page. There\'s only page1. So output: [{page_no: 1, doc_type: "po"}]. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2732, 'outputTokens': 78, 'totalTokens': 2810}, 'metrics': {'latencyMs': 512}}
2025-09-24 14:29:50,220 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bee06c08_BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:29:50,476 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9f7174a3_DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:29:50,497 - INFO - 

BPKB6NN7SWJR9KBHTWZD.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:29:50,497 - INFO - 

✓ Saved result: output/run1_BPKB6NN7SWJR9KBHTWZD.json
2025-09-24 14:29:50,777 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bee06c08_BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:29:50,976 - INFO - Page 1: Extracted 562 characters, 41 lines from ace1d85a_OBUNADUYEXKR65OJ917C_f22a1c85_page_001.pdf
2025-09-24 14:29:50,976 - INFO - Successfully processed page 1
2025-09-24 14:29:50,977 - INFO - Combined 1 pages into final text
2025-09-24 14:29:50,977 - INFO - Text validation for ace1d85a_OBUNADUYEXKR65OJ917C: 579 characters, 1 pages
2025-09-24 14:29:50,978 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:29:50,978 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:29:51,827 - INFO - Page 1: Extracted 613 characters, 46 lines from d1c2c638_XIYO2PDG2ILYXT57MES6_a3f8b3ec_page_001.pdf
2025-09-24 14:29:51,828 - INFO - Successfully processed page 1
2025-09-24 14:29:51,828 - INFO - Combined 1 pages into final text
2025-09-24 14:29:51,829 - INFO - Text validation for d1c2c638_XIYO2PDG2ILYXT57MES6: 630 characters, 1 pages
2025-09-24 14:29:51,829 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:29:51,830 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:29:52,026 - INFO - Page 1: Extracted 746 characters, 72 lines from cd349347_VIASDRA5CL365YHEEWOV_71025b1c_page_001.pdf
2025-09-24 14:29:52,027 - INFO - Successfully processed page 1
2025-09-24 14:29:52,027 - INFO - Combined 1 pages into final text
2025-09-24 14:29:52,028 - INFO - Text validation for cd349347_VIASDRA5CL365YHEEWOV: 763 characters, 1 pages
2025-09-24 14:29:52,028 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:29:52,028 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:29:52,054 - INFO - Page 1: Extracted 1223 characters, 85 lines from 2af90277_NUYKWFH99COJT60C78IZ_c4248043_page_001.pdf
2025-09-24 14:29:52,054 - INFO - Successfully processed page 1
2025-09-24 14:29:52,054 - INFO - Combined 1 pages into final text
2025-09-24 14:29:52,055 - INFO - Text validation for 2af90277_NUYKWFH99COJT60C78IZ: 1240 characters, 1 pages
2025-09-24 14:29:52,055 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:29:52,055 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:29:52,575 - INFO - Page 1: Extracted 664 characters, 59 lines from be36d558_ZFBHUC7YFRE4Z58IJGC5_158b1a9c_page_001.pdf
2025-09-24 14:29:52,576 - INFO - Successfully processed page 1
2025-09-24 14:29:52,576 - INFO - Combined 1 pages into final text
2025-09-24 14:29:52,576 - INFO - Text validation for be36d558_ZFBHUC7YFRE4Z58IJGC5: 681 characters, 1 pages
2025-09-24 14:29:52,576 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:29:52,576 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:29:52,583 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '22283c97-f9e0-41b2-a0de-62791b84cd3d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:59:52 GMT', 'content-type': 'application/json', 'content-length': '533', 'connection': 'keep-alive', 'x-amzn-requestid': '22283c97-f9e0-41b2-a0de-62791b84cd3d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. It\'s a purchase order. Classified as "po". Output via function call classify_logistics_doc_type with documents array containing one object: page_no:1 doc_type:"po".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2674, 'outputTokens': 64, 'totalTokens': 2738}, 'metrics': {'latencyMs': 479}}
2025-09-24 14:29:52,583 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ace1d85a_OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:29:52,592 - INFO - 

OBUNADUYEXKR65OJ917C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:29:52,592 - INFO - 

✓ Saved result: output/run1_OBUNADUYEXKR65OJ917C.json
2025-09-24 14:29:52,656 - INFO - Page 1: Extracted 1031 characters, 59 lines from 83e5a791_S917SYX1NI0B6KSG1VL8_2e0ff250_page_001.pdf
2025-09-24 14:29:52,657 - INFO - Successfully processed page 1
2025-09-24 14:29:52,658 - INFO - Combined 1 pages into final text
2025-09-24 14:29:52,658 - INFO - Text validation for 83e5a791_S917SYX1NI0B6KSG1VL8: 1048 characters, 1 pages
2025-09-24 14:29:52,658 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:29:52,658 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:29:52,915 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ace1d85a_OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:29:53,564 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '50c53aef-4143-4f26-9f86-3f71d7ff56e0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:59:53 GMT', 'content-type': 'application/json', 'content-length': '582', 'connection': 'keep-alive', 'x-amzn-requestid': '50c53aef-4143-4f26-9f86-3f71d7ff56e0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Here only page1. The content seems to be a Purchase Order. It says "Purchase Order" header. It lists items, vendor, etc. So it\'s a PO. According to enum, doc_type: "po". Return array with object page_no:1, doc_type:"po". Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2688, 'outputTokens': 87, 'totalTokens': 2775}, 'metrics': {'latencyMs': 605}}
2025-09-24 14:29:53,564 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d1c2c638_XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:29:53,568 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '09f279bb-e465-45eb-9d3c-1efce13108ed', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:59:53 GMT', 'content-type': 'application/json', 'content-length': '429', 'connection': 'keep-alive', 'x-amzn-requestid': '09f279bb-e465-45eb-9d3c-1efce13108ed'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. It\'s a purchase order. So doc_type "po". Return JSON via function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2733, 'outputTokens': 45, 'totalTokens': 2778}, 'metrics': {'latencyMs': 378}}
2025-09-24 14:29:53,569 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/cd349347_VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:29:53,593 - INFO - 

XIYO2PDG2ILYXT57MES6.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:29:53,593 - INFO - 

✓ Saved result: output/run1_XIYO2PDG2ILYXT57MES6.json
2025-09-24 14:29:53,765 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a58adc05-fd52-417e-be07-c5cba76e9dc4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:59:53 GMT', 'content-type': 'application/json', 'content-length': '621', 'connection': 'keep-alive', 'x-amzn-requestid': 'a58adc05-fd52-417e-be07-c5cba76e9dc4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page showing a purchase order. Need to classify each page. The document is a purchase order. So doc_type: "po". There\'s only one page. Output an array with one object: page_no: 1, doc_type: "po". Use tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_tZHzTTxcS1yoCHiLzFaA-g', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2902, 'outputTokens': 74, 'totalTokens': 2976}, 'metrics': {'latencyMs': 532}}
2025-09-24 14:29:53,765 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2af90277_NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:29:53,894 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d1c2c638_XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:29:53,916 - INFO - 

VIASDRA5CL365YHEEWOV.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:29:53,916 - INFO - 

✓ Saved result: output/run1_VIASDRA5CL365YHEEWOV.json
2025-09-24 14:29:54,203 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6744ebc3-25ed-4d29-8fe7-f64637a51486', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:59:54 GMT', 'content-type': 'application/json', 'content-length': '541', 'connection': 'keep-alive', 'x-amzn-requestid': '6744ebc3-25ed-4d29-8fe7-f64637a51486'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. It\'s a purchase order. Keywords: "PURCHASE ORDER", "PO #". So doc_type: po. Provide JSON array with object containing page_no 1 and doc_type "po". Let\'s respond via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2716, 'outputTokens': 72, 'totalTokens': 2788}, 'metrics': {'latencyMs': 485}}
2025-09-24 14:29:54,204 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/cd349347_VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:29:54,204 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/be36d558_ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:29:54,229 - INFO - 

NUYKWFH99COJT60C78IZ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:29:54,229 - INFO - 

✓ Saved result: output/run1_NUYKWFH99COJT60C78IZ.json
2025-09-24 14:29:54,445 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '79964b5c-d5c3-4f4d-97e1-cd66daea62c2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:59:54 GMT', 'content-type': 'application/json', 'content-length': '643', 'connection': 'keep-alive', 'x-amzn-requestid': '79964b5c-d5c3-4f4d-97e1-cd66daea62c2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It is a Purchase Order. So doc_type should be "po". Confirm keywords: "Purchase order", "PO#", "Purchase Order", etc. Yes. Output must use tool classify_logistics_doc_type. Provide array documents with page_no and doc_type. Since only one page, return array with one object.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2800, 'outputTokens': 92, 'totalTokens': 2892}, 'metrics': {'latencyMs': 624}}
2025-09-24 14:29:54,446 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/83e5a791_S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:29:54,516 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2af90277_NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:29:54,535 - INFO - 

ZFBHUC7YFRE4Z58IJGC5.PDF

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:29:54,535 - INFO - 

✓ Saved result: output/run1_ZFBHUC7YFRE4Z58IJGC5.json
2025-09-24 14:29:54,811 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/be36d558_ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:29:54,831 - INFO - 

S917SYX1NI0B6KSG1VL8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:29:54,831 - INFO - 

✓ Saved result: output/run1_S917SYX1NI0B6KSG1VL8.json
2025-09-24 14:29:55,129 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/83e5a791_S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:29:55,357 - INFO - Page 1: Extracted 826 characters, 39 lines from d2b62c5f_AC4907XUC5FFS63AXXBG_eb76d0d9_page_001.pdf
2025-09-24 14:29:55,357 - INFO - Successfully processed page 1
2025-09-24 14:29:55,357 - INFO - Combined 1 pages into final text
2025-09-24 14:29:55,357 - INFO - Text validation for d2b62c5f_AC4907XUC5FFS63AXXBG: 843 characters, 1 pages
2025-09-24 14:29:55,358 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:29:55,358 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:29:56,985 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c657a5d4-74cd-4880-9b8a-2a6d1f1aed7b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:59:56 GMT', 'content-type': 'application/json', 'content-length': '413', 'connection': 'keep-alive', 'x-amzn-requestid': 'c657a5d4-74cd-4880-9b8a-2a6d1f1aed7b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page with a header "Purchase Order". So doc_type = po. Need to output via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2725, 'outputTokens': 42, 'totalTokens': 2767}, 'metrics': {'latencyMs': 368}}
2025-09-24 14:29:56,985 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d2b62c5f_AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:29:57,002 - INFO - 

AC4907XUC5FFS63AXXBG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:29:57,003 - INFO - 

✓ Saved result: output/run1_AC4907XUC5FFS63AXXBG.json
2025-09-24 14:29:57,383 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d2b62c5f_AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:29:57,384 - INFO - 
📊 Processing Summary:
2025-09-24 14:29:57,384 - INFO -    Total files: 11
2025-09-24 14:29:57,384 - INFO -    Successful: 11
2025-09-24 14:29:57,384 - INFO -    Failed: 0
2025-09-24 14:29:57,384 - INFO -    Duration: 18.77 seconds
2025-09-24 14:29:57,384 - INFO -    Output directory: output
2025-09-24 14:29:57,384 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:29:57,385 - INFO -    📄 A331OW5F2Q6LZDSTAWR6.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:29:57,385 - INFO -    📄 AC4907XUC5FFS63AXXBG.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:29:57,385 - INFO -    📄 BPKB6NN7SWJR9KBHTWZD.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:29:57,385 - INFO -    📄 DY354TSLN9XYYH2RWAW5.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:29:57,385 - INFO -    📄 N5ZTF7GH4USQJIPCIXEA.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:29:57,385 - INFO -    📄 NUYKWFH99COJT60C78IZ.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:29:57,385 - INFO -    📄 OBUNADUYEXKR65OJ917C.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:29:57,385 - INFO -    📄 S917SYX1NI0B6KSG1VL8.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:29:57,385 - INFO -    📄 VIASDRA5CL365YHEEWOV.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:29:57,386 - INFO -    📄 XIYO2PDG2ILYXT57MES6.pdf: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:29:57,386 - INFO -    📄 ZFBHUC7YFRE4Z58IJGC5.PDF: {"documents":[{"page_no":1,"doc_type":"po"}]}
2025-09-24 14:29:57,387 - INFO - 
============================================================================================================================================
2025-09-24 14:29:57,387 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:29:57,387 - INFO - ============================================================================================================================================
2025-09-24 14:29:57,387 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:29:57,387 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:29:57,387 - INFO - A331OW5F2Q6LZDSTAWR6.pdf                           1      po                   run1_A331OW5F2Q6LZDSTAWR6.json                    
2025-09-24 14:29:57,387 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/A331OW5F2Q6LZDSTAWR6.pdf
2025-09-24 14:29:57,387 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A331OW5F2Q6LZDSTAWR6.json
2025-09-24 14:29:57,387 - INFO - 
2025-09-24 14:29:57,387 - INFO - AC4907XUC5FFS63AXXBG.pdf                           1      po                   run1_AC4907XUC5FFS63AXXBG.json                    
2025-09-24 14:29:57,387 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/AC4907XUC5FFS63AXXBG.pdf
2025-09-24 14:29:57,387 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AC4907XUC5FFS63AXXBG.json
2025-09-24 14:29:57,387 - INFO - 
2025-09-24 14:29:57,387 - INFO - BPKB6NN7SWJR9KBHTWZD.pdf                           1      po                   run1_BPKB6NN7SWJR9KBHTWZD.json                    
2025-09-24 14:29:57,387 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/BPKB6NN7SWJR9KBHTWZD.pdf
2025-09-24 14:29:57,387 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BPKB6NN7SWJR9KBHTWZD.json
2025-09-24 14:29:57,387 - INFO - 
2025-09-24 14:29:57,387 - INFO - DY354TSLN9XYYH2RWAW5.pdf                           1      po                   run1_DY354TSLN9XYYH2RWAW5.json                    
2025-09-24 14:29:57,388 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/DY354TSLN9XYYH2RWAW5.pdf
2025-09-24 14:29:57,388 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DY354TSLN9XYYH2RWAW5.json
2025-09-24 14:29:57,388 - INFO - 
2025-09-24 14:29:57,388 - INFO - N5ZTF7GH4USQJIPCIXEA.pdf                           1      po                   run1_N5ZTF7GH4USQJIPCIXEA.json                    
2025-09-24 14:29:57,388 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/N5ZTF7GH4USQJIPCIXEA.pdf
2025-09-24 14:29:57,388 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_N5ZTF7GH4USQJIPCIXEA.json
2025-09-24 14:29:57,388 - INFO - 
2025-09-24 14:29:57,388 - INFO - NUYKWFH99COJT60C78IZ.pdf                           1      po                   run1_NUYKWFH99COJT60C78IZ.json                    
2025-09-24 14:29:57,388 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/NUYKWFH99COJT60C78IZ.pdf
2025-09-24 14:29:57,388 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NUYKWFH99COJT60C78IZ.json
2025-09-24 14:29:57,388 - INFO - 
2025-09-24 14:29:57,388 - INFO - OBUNADUYEXKR65OJ917C.pdf                           1      po                   run1_OBUNADUYEXKR65OJ917C.json                    
2025-09-24 14:29:57,388 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/OBUNADUYEXKR65OJ917C.pdf
2025-09-24 14:29:57,388 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OBUNADUYEXKR65OJ917C.json
2025-09-24 14:29:57,388 - INFO - 
2025-09-24 14:29:57,388 - INFO - S917SYX1NI0B6KSG1VL8.pdf                           1      po                   run1_S917SYX1NI0B6KSG1VL8.json                    
2025-09-24 14:29:57,388 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/S917SYX1NI0B6KSG1VL8.pdf
2025-09-24 14:29:57,388 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_S917SYX1NI0B6KSG1VL8.json
2025-09-24 14:29:57,388 - INFO - 
2025-09-24 14:29:57,388 - INFO - VIASDRA5CL365YHEEWOV.pdf                           1      po                   run1_VIASDRA5CL365YHEEWOV.json                    
2025-09-24 14:29:57,388 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/VIASDRA5CL365YHEEWOV.pdf
2025-09-24 14:29:57,388 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_VIASDRA5CL365YHEEWOV.json
2025-09-24 14:29:57,388 - INFO - 
2025-09-24 14:29:57,389 - INFO - XIYO2PDG2ILYXT57MES6.pdf                           1      po                   run1_XIYO2PDG2ILYXT57MES6.json                    
2025-09-24 14:29:57,389 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/XIYO2PDG2ILYXT57MES6.pdf
2025-09-24 14:29:57,389 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_XIYO2PDG2ILYXT57MES6.json
2025-09-24 14:29:57,389 - INFO - 
2025-09-24 14:29:57,389 - INFO - ZFBHUC7YFRE4Z58IJGC5.PDF                           1      po                   run1_ZFBHUC7YFRE4Z58IJGC5.json                    
2025-09-24 14:29:57,389 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/ZFBHUC7YFRE4Z58IJGC5.PDF
2025-09-24 14:29:57,389 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ZFBHUC7YFRE4Z58IJGC5.json
2025-09-24 14:29:57,389 - INFO - 
2025-09-24 14:29:57,389 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:29:57,389 - INFO - Total entries: 11
2025-09-24 14:29:57,389 - INFO - ============================================================================================================================================
2025-09-24 14:29:57,389 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:29:57,389 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:29:57,389 - INFO -   1. A331OW5F2Q6LZDSTAWR6.pdf            Page 1   → po              | run1_A331OW5F2Q6LZDSTAWR6.json
2025-09-24 14:29:57,389 - INFO -   2. AC4907XUC5FFS63AXXBG.pdf            Page 1   → po              | run1_AC4907XUC5FFS63AXXBG.json
2025-09-24 14:29:57,389 - INFO -   3. BPKB6NN7SWJR9KBHTWZD.pdf            Page 1   → po              | run1_BPKB6NN7SWJR9KBHTWZD.json
2025-09-24 14:29:57,389 - INFO -   4. DY354TSLN9XYYH2RWAW5.pdf            Page 1   → po              | run1_DY354TSLN9XYYH2RWAW5.json
2025-09-24 14:29:57,389 - INFO -   5. N5ZTF7GH4USQJIPCIXEA.pdf            Page 1   → po              | run1_N5ZTF7GH4USQJIPCIXEA.json
2025-09-24 14:29:57,390 - INFO -   6. NUYKWFH99COJT60C78IZ.pdf            Page 1   → po              | run1_NUYKWFH99COJT60C78IZ.json
2025-09-24 14:29:57,390 - INFO -   7. OBUNADUYEXKR65OJ917C.pdf            Page 1   → po              | run1_OBUNADUYEXKR65OJ917C.json
2025-09-24 14:29:57,390 - INFO -   8. S917SYX1NI0B6KSG1VL8.pdf            Page 1   → po              | run1_S917SYX1NI0B6KSG1VL8.json
2025-09-24 14:29:57,390 - INFO -   9. VIASDRA5CL365YHEEWOV.pdf            Page 1   → po              | run1_VIASDRA5CL365YHEEWOV.json
2025-09-24 14:29:57,390 - INFO -  10. XIYO2PDG2ILYXT57MES6.pdf            Page 1   → po              | run1_XIYO2PDG2ILYXT57MES6.json
2025-09-24 14:29:57,390 - INFO -  11. ZFBHUC7YFRE4Z58IJGC5.PDF            Page 1   → po              | run1_ZFBHUC7YFRE4Z58IJGC5.json
2025-09-24 14:29:57,390 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:29:57,390 - INFO - 
✅ Test completed: {'total_files': 11, 'processed': 11, 'failed': 0, 'errors': [], 'duration_seconds': 18.774541, 'processed_files': [{'filename': 'A331OW5F2Q6LZDSTAWR6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/A331OW5F2Q6LZDSTAWR6.pdf'}, {'filename': 'AC4907XUC5FFS63AXXBG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/AC4907XUC5FFS63AXXBG.pdf'}, {'filename': 'BPKB6NN7SWJR9KBHTWZD.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/BPKB6NN7SWJR9KBHTWZD.pdf'}, {'filename': 'DY354TSLN9XYYH2RWAW5.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/DY354TSLN9XYYH2RWAW5.pdf'}, {'filename': 'N5ZTF7GH4USQJIPCIXEA.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/N5ZTF7GH4USQJIPCIXEA.pdf'}, {'filename': 'NUYKWFH99COJT60C78IZ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/NUYKWFH99COJT60C78IZ.pdf'}, {'filename': 'OBUNADUYEXKR65OJ917C.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/OBUNADUYEXKR65OJ917C.pdf'}, {'filename': 'S917SYX1NI0B6KSG1VL8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/S917SYX1NI0B6KSG1VL8.pdf'}, {'filename': 'VIASDRA5CL365YHEEWOV.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/VIASDRA5CL365YHEEWOV.pdf'}, {'filename': 'XIYO2PDG2ILYXT57MES6.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/XIYO2PDG2ILYXT57MES6.pdf'}, {'filename': 'ZFBHUC7YFRE4Z58IJGC5.PDF', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/po/ZFBHUC7YFRE4Z58IJGC5.PDF'}]}
