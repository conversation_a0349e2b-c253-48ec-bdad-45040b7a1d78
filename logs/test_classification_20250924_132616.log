2025-09-24 13:26:16,828 - INFO - Logging initialized. Log file: logs/test_classification_20250924_132616.log
2025-09-24 13:26:16,828 - INFO - 📁 Found 13 files to process
2025-09-24 13:26:16,828 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 13:26:16,828 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 13:26:16,828 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 13:26:16,828 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 13:26:16,828 - INFO - ⬆️ [13:26:16] Uploading: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:26:18,714 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf -> s3://document-extraction-logistically/temp/40af458d_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:26:18,714 - INFO - 🔍 [13:26:18] Starting classification: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:26:18,715 - INFO - ⬆️ [13:26:18] Uploading: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:26:18,717 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:18,738 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:18,745 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/40af458d_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:26:18,745 - INFO - Processing PDF from S3...
2025-09-24 13:26:18,745 - INFO - Downloading PDF from S3 to /tmp/tmpftjtadpr/40af458d_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:26:19,634 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf -> s3://document-extraction-logistically/temp/266994ce_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:26:19,634 - INFO - 🔍 [13:26:19] Starting classification: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:26:19,635 - INFO - ⬆️ [13:26:19] Uploading: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:26:19,636 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:19,655 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:19,660 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/266994ce_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:26:19,661 - INFO - Processing PDF from S3...
2025-09-24 13:26:19,661 - INFO - Downloading PDF from S3 to /tmp/tmpxdi7s9kj/266994ce_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:26:20,131 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:26:20,131 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:20,133 - INFO - Splitting PDF 40af458d_I_QHD3LC0DU6S8O2YVVS60 into 1 pages
2025-09-24 13:26:20,135 - INFO - Split PDF into 1 pages
2025-09-24 13:26:20,135 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:20,135 - INFO - Expected pages: [1]
2025-09-24 13:26:20,740 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf -> s3://document-extraction-logistically/temp/3da84574_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:26:20,741 - INFO - 🔍 [13:26:20] Starting classification: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:26:20,741 - INFO - ⬆️ [13:26:20] Uploading: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:26:20,749 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:20,762 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:20,765 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3da84574_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:26:20,766 - INFO - Processing PDF from S3...
2025-09-24 13:26:20,766 - INFO - Downloading PDF from S3 to /tmp/tmp151neo4f/3da84574_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:26:21,282 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:26:21,282 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:21,283 - INFO - Splitting PDF 266994ce_NMFC_DH0JZ2JWDGRHD26BX74C into 2 pages
2025-09-24 13:26:21,286 - INFO - Split PDF into 2 pages
2025-09-24 13:26:21,286 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:21,286 - INFO - Expected pages: [1, 2]
2025-09-24 13:26:21,900 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf -> s3://document-extraction-logistically/temp/ce264c5d_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:26:21,900 - INFO - 🔍 [13:26:21] Starting classification: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:26:21,901 - INFO - ⬆️ [13:26:21] Uploading: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:26:21,902 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:21,920 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:21,925 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ce264c5d_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:26:21,925 - INFO - Processing PDF from S3...
2025-09-24 13:26:21,926 - INFO - Downloading PDF from S3 to /tmp/tmp01psn8ga/ce264c5d_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:26:22,456 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf -> s3://document-extraction-logistically/temp/7e6b452a_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:26:22,456 - INFO - 🔍 [13:26:22] Starting classification: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:26:22,456 - INFO - ⬆️ [13:26:22] Uploading: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:26:22,459 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:22,471 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:22,473 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7e6b452a_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:26:22,473 - INFO - Processing PDF from S3...
2025-09-24 13:26:22,473 - INFO - Downloading PDF from S3 to /tmp/tmp5jne_y8q/7e6b452a_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:26:22,670 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:26:22,671 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:22,673 - INFO - Splitting PDF 3da84574_NMFC_NJ4WSZ8BUQAW48V6403N into 3 pages
2025-09-24 13:26:22,677 - INFO - Split PDF into 3 pages
2025-09-24 13:26:22,678 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:22,678 - INFO - Expected pages: [1, 2, 3]
2025-09-24 13:26:23,023 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf -> s3://document-extraction-logistically/temp/630e68b4_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:26:23,023 - INFO - 🔍 [13:26:23] Starting classification: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:26:23,024 - INFO - ⬆️ [13:26:23] Uploading: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:26:23,024 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:23,038 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:23,042 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/630e68b4_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:26:23,042 - INFO - Processing PDF from S3...
2025-09-24 13:26:23,042 - INFO - Downloading PDF from S3 to /tmp/tmpzvjlh4xw/630e68b4_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:26:23,637 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/8c7af580_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:26:23,637 - INFO - 🔍 [13:26:23] Starting classification: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:26:23,639 - INFO - ⬆️ [13:26:23] Uploading: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:26:23,641 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:23,661 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:23,707 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8c7af580_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:26:23,707 - INFO - Processing PDF from S3...
2025-09-24 13:26:23,708 - INFO - Downloading PDF from S3 to /tmp/tmp3v7x9jc0/8c7af580_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:26:23,721 - INFO - Page 1: Extracted 589 characters, 36 lines from 40af458d_I_QHD3LC0DU6S8O2YVVS60_10a847c7_page_001.pdf
2025-09-24 13:26:23,722 - INFO - Successfully processed page 1
2025-09-24 13:26:23,722 - INFO - Combined 1 pages into final text
2025-09-24 13:26:23,722 - INFO - Text validation for 40af458d_I_QHD3LC0DU6S8O2YVVS60: 606 characters, 1 pages
2025-09-24 13:26:23,723 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:23,723 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:23,732 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:26:23,732 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:23,734 - INFO - Splitting PDF 7e6b452a_NMFC_R1V0MO844PBLWNEAUETU into 2 pages
2025-09-24 13:26:23,739 - INFO - Split PDF into 2 pages
2025-09-24 13:26:23,739 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:23,739 - INFO - Expected pages: [1, 2]
2025-09-24 13:26:24,202 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/b3762a79_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:26:24,203 - INFO - 🔍 [13:26:24] Starting classification: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:26:24,203 - INFO - ⬆️ [13:26:24] Uploading: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:26:24,205 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:24,221 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:24,225 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b3762a79_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:26:24,225 - INFO - Processing PDF from S3...
2025-09-24 13:26:24,226 - INFO - Downloading PDF from S3 to /tmp/tmpowlai_38/b3762a79_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:26:24,484 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 13:26:24,484 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:24,485 - WARNING - Multiple definitions in dictionary at byte 0x9e9f6 for key /OpenAction
2025-09-24 13:26:24,485 - INFO - Splitting PDF ce264c5d_NMFC_OR9EL08KIKNQPZ3UV3HH into 2 pages
2025-09-24 13:26:24,530 - INFO - Split PDF into 2 pages
2025-09-24 13:26:24,530 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:24,530 - INFO - Expected pages: [1, 2]
2025-09-24 13:26:24,760 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf -> s3://document-extraction-logistically/temp/a80f9c90_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:26:24,760 - INFO - 🔍 [13:26:24] Starting classification: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:26:24,760 - INFO - ⬆️ [13:26:24] Uploading: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:26:24,761 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:24,769 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:24,770 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a80f9c90_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:26:24,770 - INFO - Processing PDF from S3...
2025-09-24 13:26:24,771 - INFO - Downloading PDF from S3 to /tmp/tmp6c_eqh72/a80f9c90_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:26:24,833 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:26:24,834 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:24,836 - INFO - Splitting PDF 630e68b4_NMFC_RUDVGETVRZO7XX6YNW7I into 1 pages
2025-09-24 13:26:24,838 - INFO - Split PDF into 1 pages
2025-09-24 13:26:24,838 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:24,838 - INFO - Expected pages: [1]
2025-09-24 13:26:25,309 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf -> s3://document-extraction-logistically/temp/543ae241_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:26:25,309 - INFO - 🔍 [13:26:25] Starting classification: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:26:25,310 - INFO - ⬆️ [13:26:25] Uploading: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:26:25,317 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:25,328 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:25,332 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/543ae241_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:26:25,332 - INFO - Processing PDF from S3...
2025-09-24 13:26:25,332 - INFO - Downloading PDF from S3 to /tmp/tmp520jxquo/543ae241_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:26:25,866 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf -> s3://document-extraction-logistically/temp/78aaac10_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:26:25,866 - INFO - 🔍 [13:26:25] Starting classification: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:26:25,867 - INFO - ⬆️ [13:26:25] Uploading: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:26:25,869 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:25,886 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:25,894 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/78aaac10_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:26:25,894 - INFO - Processing PDF from S3...
2025-09-24 13:26:25,895 - INFO - Downloading PDF from S3 to /tmp/tmpu1tcoikw/78aaac10_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:26:25,943 - INFO - Page 1: Extracted 854 characters, 69 lines from 266994ce_NMFC_DH0JZ2JWDGRHD26BX74C_f4344966_page_001.pdf
2025-09-24 13:26:25,943 - INFO - Successfully processed page 1
2025-09-24 13:26:25,957 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:26:25,958 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:25,959 - INFO - Splitting PDF b3762a79_W_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 13:26:25,961 - INFO - Split PDF into 1 pages
2025-09-24 13:26:25,961 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:25,961 - INFO - Expected pages: [1]
2025-09-24 13:26:26,186 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/40af458d_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:26:26,311 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 13:26:26,311 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:26,312 - INFO - Splitting PDF 8c7af580_W_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 13:26:26,312 - INFO - Split PDF into 1 pages
2025-09-24 13:26:26,312 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:26,312 - INFO - Expected pages: [1]
2025-09-24 13:26:26,394 - INFO - Page 2: Extracted 764 characters, 54 lines from 266994ce_NMFC_DH0JZ2JWDGRHD26BX74C_f4344966_page_002.pdf
2025-09-24 13:26:26,394 - INFO - Successfully processed page 2
2025-09-24 13:26:26,395 - INFO - Combined 2 pages into final text
2025-09-24 13:26:26,395 - INFO - Text validation for 266994ce_NMFC_DH0JZ2JWDGRHD26BX74C: 1654 characters, 2 pages
2025-09-24 13:26:26,395 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:26,395 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:26,422 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf -> s3://document-extraction-logistically/temp/c998aec0_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:26:26,422 - INFO - 🔍 [13:26:26] Starting classification: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:26:26,423 - INFO - ⬆️ [13:26:26] Uploading: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:26:26,425 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:26,445 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:26,449 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c998aec0_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:26:26,449 - INFO - Processing PDF from S3...
2025-09-24 13:26:26,449 - INFO - Downloading PDF from S3 to /tmp/tmpktlos6m6/c998aec0_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:26:26,461 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:26:26,461 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:26,463 - INFO - Splitting PDF a80f9c90_W_DFY1VDZWR7NBDLJV02G2 into 1 pages
2025-09-24 13:26:26,467 - INFO - Split PDF into 1 pages
2025-09-24 13:26:26,467 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:26,467 - INFO - Expected pages: [1]
2025-09-24 13:26:26,838 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:26:26,838 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:26,839 - INFO - Splitting PDF 543ae241_W_HFPAXYL947DH59AB12FL into 1 pages
2025-09-24 13:26:26,840 - INFO - Split PDF into 1 pages
2025-09-24 13:26:26,840 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:26,840 - INFO - Expected pages: [1]
2025-09-24 13:26:26,881 - INFO - Page 3: Extracted 850 characters, 59 lines from 3da84574_NMFC_NJ4WSZ8BUQAW48V6403N_7176a3e7_page_003.pdf
2025-09-24 13:26:26,881 - INFO - Successfully processed page 3
2025-09-24 13:26:26,993 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf -> s3://document-extraction-logistically/temp/8b3a2f24_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:26:26,994 - INFO - 🔍 [13:26:26] Starting classification: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:26:26,995 - INFO - Initializing TextractProcessor...
2025-09-24 13:26:27,010 - INFO - Initializing BedrockProcessor...
2025-09-24 13:26:27,017 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8b3a2f24_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:26:27,023 - INFO - Processing PDF from S3...
2025-09-24 13:26:27,025 - INFO - Downloading PDF from S3 to /tmp/tmp49qlgcfb/8b3a2f24_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:26:27,041 - INFO - 

I_QHD3LC0DU6S8O2YVVS60.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "inspection_cert"
        }
    ]
}
2025-09-24 13:26:27,041 - INFO - 

✓ Saved result: output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 13:26:27,058 - INFO - Page 1: Extracted 980 characters, 76 lines from 3da84574_NMFC_NJ4WSZ8BUQAW48V6403N_7176a3e7_page_001.pdf
2025-09-24 13:26:27,058 - INFO - Successfully processed page 1
2025-09-24 13:26:27,317 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/40af458d_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:26:27,410 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:26:27,410 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:27,411 - INFO - Splitting PDF 78aaac10_W_K9VSARJOKAIZHNJ5RBDT into 1 pages
2025-09-24 13:26:27,412 - INFO - Split PDF into 1 pages
2025-09-24 13:26:27,412 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:27,412 - INFO - Expected pages: [1]
2025-09-24 13:26:27,673 - INFO - Page 1: Extracted 1511 characters, 86 lines from 7e6b452a_NMFC_R1V0MO844PBLWNEAUETU_ec9671da_page_001.pdf
2025-09-24 13:26:27,676 - INFO - Page 2: Extracted 913 characters, 56 lines from 7e6b452a_NMFC_R1V0MO844PBLWNEAUETU_ec9671da_page_002.pdf
2025-09-24 13:26:27,676 - INFO - Successfully processed page 1
2025-09-24 13:26:27,677 - INFO - Successfully processed page 2
2025-09-24 13:26:27,677 - INFO - Combined 2 pages into final text
2025-09-24 13:26:27,677 - INFO - Text validation for 7e6b452a_NMFC_R1V0MO844PBLWNEAUETU: 2460 characters, 2 pages
2025-09-24 13:26:27,678 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:27,678 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:27,685 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:26:27,685 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:27,687 - INFO - Splitting PDF c998aec0_W_WRKSHW76B3QUG47QWR75 into 1 pages
2025-09-24 13:26:27,688 - INFO - Split PDF into 1 pages
2025-09-24 13:26:27,689 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:27,689 - INFO - Expected pages: [1]
2025-09-24 13:26:27,954 - INFO - Page 2: Extracted 850 characters, 59 lines from 3da84574_NMFC_NJ4WSZ8BUQAW48V6403N_7176a3e7_page_002.pdf
2025-09-24 13:26:27,955 - INFO - Successfully processed page 2
2025-09-24 13:26:27,955 - INFO - Combined 3 pages into final text
2025-09-24 13:26:27,956 - INFO - Text validation for 3da84574_NMFC_NJ4WSZ8BUQAW48V6403N: 2735 characters, 3 pages
2025-09-24 13:26:27,956 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:27,956 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:28,780 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:26:28,780 - INFO - Splitting PDF into individual pages...
2025-09-24 13:26:28,783 - INFO - Splitting PDF 8b3a2f24_W_XCJLXZK140FUS8020ZAG into 1 pages
2025-09-24 13:26:28,792 - INFO - Split PDF into 1 pages
2025-09-24 13:26:28,792 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:26:28,792 - INFO - Expected pages: [1]
2025-09-24 13:26:28,969 - INFO - Page 1: Extracted 443 characters, 72 lines from 630e68b4_NMFC_RUDVGETVRZO7XX6YNW7I_f60b96eb_page_001.pdf
2025-09-24 13:26:28,969 - INFO - Successfully processed page 1
2025-09-24 13:26:28,969 - INFO - Combined 1 pages into final text
2025-09-24 13:26:28,970 - INFO - Text validation for 630e68b4_NMFC_RUDVGETVRZO7XX6YNW7I: 460 characters, 1 pages
2025-09-24 13:26:28,970 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:28,970 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:29,205 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/266994ce_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:26:29,224 - INFO - 

NMFC_DH0JZ2JWDGRHD26BX74C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:26:29,224 - INFO - 

✓ Saved result: output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 13:26:29,498 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/266994ce_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:26:30,142 - INFO - Page 2: Extracted 540 characters, 29 lines from ce264c5d_NMFC_OR9EL08KIKNQPZ3UV3HH_4571e8bc_page_002.pdf
2025-09-24 13:26:30,142 - INFO - Successfully processed page 2
2025-09-24 13:26:30,429 - INFO - Page 1: Extracted 626 characters, 49 lines from a80f9c90_W_DFY1VDZWR7NBDLJV02G2_0e9a2e75_page_001.pdf
2025-09-24 13:26:30,430 - INFO - Successfully processed page 1
2025-09-24 13:26:30,430 - INFO - Combined 1 pages into final text
2025-09-24 13:26:30,430 - INFO - Text validation for a80f9c90_W_DFY1VDZWR7NBDLJV02G2: 643 characters, 1 pages
2025-09-24 13:26:30,430 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:30,430 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:30,545 - INFO - Page 1: Extracted 732 characters, 59 lines from b3762a79_W_DCY7SLNMWUXIENOREHQF_c3ab7ce5_page_001.pdf
2025-09-24 13:26:30,545 - INFO - Successfully processed page 1
2025-09-24 13:26:30,545 - INFO - Combined 1 pages into final text
2025-09-24 13:26:30,545 - INFO - Text validation for b3762a79_W_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 13:26:30,546 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:30,546 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:30,736 - INFO - Page 1: Extracted 1120 characters, 87 lines from ce264c5d_NMFC_OR9EL08KIKNQPZ3UV3HH_4571e8bc_page_001.pdf
2025-09-24 13:26:30,736 - INFO - Successfully processed page 1
2025-09-24 13:26:30,736 - INFO - Combined 2 pages into final text
2025-09-24 13:26:30,737 - INFO - Text validation for ce264c5d_NMFC_OR9EL08KIKNQPZ3UV3HH: 1696 characters, 2 pages
2025-09-24 13:26:30,738 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:30,738 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:31,012 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/630e68b4_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:26:31,024 - INFO - 

NMFC_RUDVGETVRZO7XX6YNW7I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:26:31,024 - INFO - 

✓ Saved result: output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 13:26:31,061 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7e6b452a_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:26:31,071 - INFO - Page 1: Extracted 802 characters, 30 lines from 78aaac10_W_K9VSARJOKAIZHNJ5RBDT_f44097d9_page_001.pdf
2025-09-24 13:26:31,071 - INFO - Successfully processed page 1
2025-09-24 13:26:31,072 - INFO - Combined 1 pages into final text
2025-09-24 13:26:31,072 - INFO - Text validation for 78aaac10_W_K9VSARJOKAIZHNJ5RBDT: 819 characters, 1 pages
2025-09-24 13:26:31,072 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:31,072 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:31,172 - INFO - Page 1: Extracted 517 characters, 31 lines from 543ae241_W_HFPAXYL947DH59AB12FL_93f9f849_page_001.pdf
2025-09-24 13:26:31,172 - INFO - Successfully processed page 1
2025-09-24 13:26:31,172 - INFO - Combined 1 pages into final text
2025-09-24 13:26:31,173 - INFO - Text validation for 543ae241_W_HFPAXYL947DH59AB12FL: 534 characters, 1 pages
2025-09-24 13:26:31,173 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:31,173 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:31,298 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/630e68b4_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:26:31,331 - INFO - 

NMFC_R1V0MO844PBLWNEAUETU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:26:31,331 - INFO - 

✓ Saved result: output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:26:31,361 - INFO - Page 1: Extracted 580 characters, 48 lines from c998aec0_W_WRKSHW76B3QUG47QWR75_3e0329d7_page_001.pdf
2025-09-24 13:26:31,361 - INFO - Successfully processed page 1
2025-09-24 13:26:31,361 - INFO - Combined 1 pages into final text
2025-09-24 13:26:31,361 - INFO - Text validation for c998aec0_W_WRKSHW76B3QUG47QWR75: 597 characters, 1 pages
2025-09-24 13:26:31,362 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:31,362 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:31,614 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7e6b452a_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:26:32,427 - INFO - Page 1: Extracted 939 characters, 64 lines from 8c7af580_W_A34CDFDJ66EDOZEKZWJL_d931e20e_page_001.pdf
2025-09-24 13:26:32,428 - INFO - Successfully processed page 1
2025-09-24 13:26:32,428 - INFO - Combined 1 pages into final text
2025-09-24 13:26:32,428 - INFO - Text validation for 8c7af580_W_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 13:26:32,429 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:32,429 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:32,780 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b3762a79_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:26:32,791 - INFO - 

W_DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:26:32,791 - INFO - 

✓ Saved result: output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 13:26:33,063 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b3762a79_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:26:33,081 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a80f9c90_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:26:33,105 - INFO - 

W_DFY1VDZWR7NBDLJV02G2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:26:33,106 - INFO - 

✓ Saved result: output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 13:26:33,127 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ce264c5d_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:26:33,164 - INFO - Page 1: Extracted 528 characters, 31 lines from 8b3a2f24_W_XCJLXZK140FUS8020ZAG_9cae46e2_page_001.pdf
2025-09-24 13:26:33,165 - INFO - Successfully processed page 1
2025-09-24 13:26:33,165 - INFO - Combined 1 pages into final text
2025-09-24 13:26:33,165 - INFO - Text validation for 8b3a2f24_W_XCJLXZK140FUS8020ZAG: 545 characters, 1 pages
2025-09-24 13:26:33,166 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:26:33,166 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:26:33,179 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/543ae241_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:26:33,307 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-24 13:26:33,308 - ERROR - Processing failed for s3://document-extraction-logistically/temp/3da84574_NMFC_NJ4WSZ8BUQAW48V6403N.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:26:33,308 - ERROR - ❌ Classification failed for s3://document-extraction-logistically/temp/3da84574_NMFC_NJ4WSZ8BUQAW48V6403N.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:26:33,309 - ERROR - ❌ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 13:26:33,310 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 13:26:33,376 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a80f9c90_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:26:33,405 - INFO - 

NMFC_OR9EL08KIKNQPZ3UV3HH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:26:33,405 - INFO - 

✓ Saved result: output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:26:33,510 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c998aec0_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:26:33,678 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ce264c5d_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:26:33,684 - INFO - 

W_HFPAXYL947DH59AB12FL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:26:33,684 - INFO - 

✓ Saved result: output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 13:26:33,833 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/78aaac10_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:26:33,958 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/543ae241_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:26:33,959 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:26:33,960 - ERROR - ✗ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 13:26:33,961 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 13:26:34,234 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3da84574_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:26:34,251 - INFO - 

W_WRKSHW76B3QUG47QWR75.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:26:34,251 - INFO - 

✓ Saved result: output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 13:26:34,525 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c998aec0_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:26:34,541 - INFO - 

W_K9VSARJOKAIZHNJ5RBDT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 13:26:34,541 - INFO - 

✓ Saved result: output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 13:26:34,614 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8c7af580_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:26:34,813 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/78aaac10_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:26:34,833 - INFO - 

W_A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:26:34,833 - INFO - 

✓ Saved result: output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 13:26:35,108 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8c7af580_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:26:35,567 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8b3a2f24_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:26:35,584 - INFO - 

W_XCJLXZK140FUS8020ZAG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:26:35,584 - INFO - 

✓ Saved result: output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 13:26:35,860 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8b3a2f24_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:26:35,861 - INFO - 
📊 Processing Summary:
2025-09-24 13:26:35,861 - INFO -    Total files: 13
2025-09-24 13:26:35,861 - INFO -    Successful: 12
2025-09-24 13:26:35,861 - INFO -    Failed: 1
2025-09-24 13:26:35,861 - INFO -    Duration: 19.03 seconds
2025-09-24 13:26:35,862 - INFO -    Output directory: output
2025-09-24 13:26:35,862 - INFO - 
📋 Successfully Processed Files:
2025-09-24 13:26:35,862 - INFO -    📄 I_QHD3LC0DU6S8O2YVVS60.pdf: {"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}
2025-09-24 13:26:35,862 - INFO -    📄 NMFC_DH0JZ2JWDGRHD26BX74C.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}
2025-09-24 13:26:35,862 - INFO -    📄 NMFC_OR9EL08KIKNQPZ3UV3HH.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:26:35,862 - INFO -    📄 NMFC_R1V0MO844PBLWNEAUETU.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}
2025-09-24 13:26:35,862 - INFO -    📄 NMFC_RUDVGETVRZO7XX6YNW7I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:26:35,862 - INFO -    📄 W_A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:26:35,862 - INFO -    📄 W_DCY7SLNMWUXIENOREHQF.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:26:35,862 - INFO -    📄 W_DFY1VDZWR7NBDLJV02G2.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:26:35,862 - INFO -    📄 W_HFPAXYL947DH59AB12FL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:26:35,862 - INFO -    📄 W_K9VSARJOKAIZHNJ5RBDT.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 13:26:35,862 - INFO -    📄 W_WRKSHW76B3QUG47QWR75.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:26:35,863 - INFO -    📄 W_XCJLXZK140FUS8020ZAG.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:26:35,863 - ERROR - 
❌ Errors:
2025-09-24 13:26:35,863 - ERROR -    Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:26:35,863 - INFO - 
============================================================================================================================================
2025-09-24 13:26:35,863 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 13:26:35,863 - INFO - ============================================================================================================================================
2025-09-24 13:26:35,863 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 13:26:35,863 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:26:35,864 - INFO - I_QHD3LC0DU6S8O2YVVS60.pdf                         1      inspection_cert      run1_I_QHD3LC0DU6S8O2YVVS60.json                  
2025-09-24 13:26:35,864 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:26:35,864 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 13:26:35,864 - INFO - 
2025-09-24 13:26:35,864 - INFO - NMFC_DH0JZ2JWDGRHD26BX74C.pdf                      1      nmfc_cert            run1_NMFC_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 13:26:35,864 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:26:35,864 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 13:26:35,864 - INFO - 
2025-09-24 13:26:35,864 - INFO - NMFC_DH0JZ2JWDGRHD26BX74C.pdf                      2      nmfc_cert            run1_NMFC_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 13:26:35,864 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:26:35,864 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 13:26:35,864 - INFO - 
2025-09-24 13:26:35,864 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      1      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 13:26:35,864 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:26:35,864 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:26:35,864 - INFO - 
2025-09-24 13:26:35,864 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      2      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 13:26:35,864 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:26:35,864 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:26:35,865 - INFO - 
2025-09-24 13:26:35,865 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      1      nmfc_cert            run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 13:26:35,865 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:26:35,865 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:26:35,865 - INFO - 
2025-09-24 13:26:35,865 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      2      nmfc_cert            run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 13:26:35,865 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:26:35,865 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:26:35,865 - INFO - 
2025-09-24 13:26:35,865 - INFO - NMFC_RUDVGETVRZO7XX6YNW7I.pdf                      1      nmfc_cert            run1_NMFC_RUDVGETVRZO7XX6YNW7I.json               
2025-09-24 13:26:35,865 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:26:35,865 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 13:26:35,865 - INFO - 
2025-09-24 13:26:35,865 - INFO - W_A34CDFDJ66EDOZEKZWJL.pdf                         1      weight_and_inspect... run1_W_A34CDFDJ66EDOZEKZWJL.json                  
2025-09-24 13:26:35,865 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:26:35,865 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 13:26:35,865 - INFO - 
2025-09-24 13:26:35,865 - INFO - W_DCY7SLNMWUXIENOREHQF.pdf                         1      weight_and_inspect... run1_W_DCY7SLNMWUXIENOREHQF.json                  
2025-09-24 13:26:35,865 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:26:35,865 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 13:26:35,865 - INFO - 
2025-09-24 13:26:35,866 - INFO - W_DFY1VDZWR7NBDLJV02G2.pdf                         1      weight_and_inspect... run1_W_DFY1VDZWR7NBDLJV02G2.json                  
2025-09-24 13:26:35,866 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:26:35,866 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 13:26:35,866 - INFO - 
2025-09-24 13:26:35,866 - INFO - W_HFPAXYL947DH59AB12FL.pdf                         1      weight_and_inspect... run1_W_HFPAXYL947DH59AB12FL.json                  
2025-09-24 13:26:35,866 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:26:35,866 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 13:26:35,866 - INFO - 
2025-09-24 13:26:35,866 - INFO - W_K9VSARJOKAIZHNJ5RBDT.pdf                         1      scale_ticket         run1_W_K9VSARJOKAIZHNJ5RBDT.json                  
2025-09-24 13:26:35,866 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:26:35,866 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 13:26:35,866 - INFO - 
2025-09-24 13:26:35,867 - INFO - W_WRKSHW76B3QUG47QWR75.pdf                         1      weight_and_inspect... run1_W_WRKSHW76B3QUG47QWR75.json                  
2025-09-24 13:26:35,867 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:26:35,867 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 13:26:35,867 - INFO - 
2025-09-24 13:26:35,867 - INFO - W_XCJLXZK140FUS8020ZAG.pdf                         1      weight_and_inspect... run1_W_XCJLXZK140FUS8020ZAG.json                  
2025-09-24 13:26:35,867 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:26:35,867 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 13:26:35,867 - INFO - 
2025-09-24 13:26:35,867 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:26:35,867 - INFO - Total entries: 15
2025-09-24 13:26:35,867 - INFO - ============================================================================================================================================
2025-09-24 13:26:35,868 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 13:26:35,868 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:26:35,868 - INFO -   1. I_QHD3LC0DU6S8O2YVVS60.pdf          Page 1   → inspection_cert | run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 13:26:35,868 - INFO -   2. NMFC_DH0JZ2JWDGRHD26BX74C.pdf       Page 1   → nmfc_cert       | run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 13:26:35,868 - INFO -   3. NMFC_DH0JZ2JWDGRHD26BX74C.pdf       Page 2   → nmfc_cert       | run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 13:26:35,868 - INFO -   4. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:26:35,868 - INFO -   5. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:26:35,868 - INFO -   6. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 1   → nmfc_cert       | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:26:35,868 - INFO -   7. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 2   → nmfc_cert       | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:26:35,868 - INFO -   8. NMFC_RUDVGETVRZO7XX6YNW7I.pdf       Page 1   → nmfc_cert       | run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 13:26:35,869 - INFO -   9. W_A34CDFDJ66EDOZEKZWJL.pdf          Page 1   → weight_and_inspection_cert | run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 13:26:35,869 - INFO -  10. W_DCY7SLNMWUXIENOREHQF.pdf          Page 1   → weight_and_inspection_cert | run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 13:26:35,869 - INFO -  11. W_DFY1VDZWR7NBDLJV02G2.pdf          Page 1   → weight_and_inspection_cert | run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 13:26:35,869 - INFO -  12. W_HFPAXYL947DH59AB12FL.pdf          Page 1   → weight_and_inspection_cert | run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 13:26:35,869 - INFO -  13. W_K9VSARJOKAIZHNJ5RBDT.pdf          Page 1   → scale_ticket    | run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 13:26:35,869 - INFO -  14. W_WRKSHW76B3QUG47QWR75.pdf          Page 1   → weight_and_inspection_cert | run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 13:26:35,869 - INFO -  15. W_XCJLXZK140FUS8020ZAG.pdf          Page 1   → weight_and_inspection_cert | run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 13:26:35,869 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:26:35,869 - ERROR - 🐛 Error occurred during processing - entering debugger...
2025-09-24 13:26:35,869 - ERROR - 🐛 Debug info: {'exception': Exception('Unexpected tool response format from Bedrock'), 'traceback': 'Traceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response\n    raise Exception("Stop reason is not tool_use")\nException: Stop reason is not tool_use\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file\n    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)\n  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync\n    result = loop.run_until_complete(llm_classification(s3_uri))\n  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete\n    return future.result()\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main\n    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock\n    content = bedrock_processor.extract_tool_response(response)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response\n    raise Exception("Unexpected tool response format from Bedrock")\nException: Unexpected tool response format from Bedrock\n', 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf', 's3_uri': 's3://document-extraction-logistically/temp/3da84574_NMFC_NJ4WSZ8BUQAW48V6403N.pdf', 'location': 'process_single_file'}
2025-09-24 13:26:35,870 - ERROR - 🐛 Location: process_single_file
2025-09-24 13:26:35,870 - ERROR - 🐛 Exception: Unexpected tool response format from Bedrock
2025-09-24 13:26:35,870 - ERROR - 🐛 Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 138, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 143, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

