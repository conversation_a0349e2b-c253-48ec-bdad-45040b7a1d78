2025-09-24 15:00:38,437 - INFO - Logging initialized. Log file: logs/test_classification_20250924_150038.log
2025-09-24 15:00:38,438 - INFO - 📁 Found 1 files to process
2025-09-24 15:00:38,439 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 15:00:38,439 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-24 15:00:38,439 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-24 15:00:38,439 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-24 15:00:38,439 - INFO - ⬆️ [15:00:38] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:00:42,320 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/83ed8f5b_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:00:42,320 - INFO - 🔍 [15:00:42] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:00:42,323 - INFO - Initializing TextractProcessor...
2025-09-24 15:00:42,354 - INFO - Initializing BedrockProcessor...
2025-09-24 15:00:42,362 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/83ed8f5b_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:00:42,363 - INFO - Processing PDF from S3...
2025-09-24 15:00:42,363 - INFO - Downloading PDF from S3 to /tmp/tmpm8sbn2hz/83ed8f5b_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:00:44,773 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 15:00:44,773 - INFO - Splitting PDF into individual pages...
2025-09-24 15:00:44,774 - INFO - Splitting PDF 83ed8f5b_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 15:00:44,776 - INFO - Split PDF into 1 pages
2025-09-24 15:00:44,776 - INFO - Processing pages with Textract in parallel...
2025-09-24 15:00:44,777 - INFO - Expected pages: [1]
2025-09-24 15:00:52,036 - INFO - Page 1: Extracted 939 characters, 64 lines from 83ed8f5b_A34CDFDJ66EDOZEKZWJL_92720e97_page_001.pdf
2025-09-24 15:00:52,037 - INFO - Successfully processed page 1
2025-09-24 15:00:52,037 - INFO - Combined 1 pages into final text
2025-09-24 15:00:52,038 - INFO - Text validation for 83ed8f5b_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 15:00:52,038 - INFO - Analyzing document types with Bedrock...
2025-09-24 15:00:52,038 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 15:00:55,480 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '45682f72-eaef-41b3-8cde-7f9ba455171e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:30:55 GMT', 'content-type': 'application/json', 'content-length': '1979', 'connection': 'keep-alive', 'x-amzn-requestid': '45682f72-eaef-41b3-8cde-7f9ba455171e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. We need to classify the document type. The page contains "Inspection Certificate" heading. Also contains weight, dimensions, etc. It says "This is to certify that the shipment transported on the referenced Pro number was measured and weighed..." It mentions "Dimensions and weights determined and certified by use of Mettler-Toledo Model CSN 840". This seems like a weight and inspection certificate. According to definitions: weight_and_inspection_cert: certificate confirming weight possibly with length, width, height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. It says inspection certificate. Also there\'s mention of weight and dimensions. That matches weight_and_inspection_cert.\n\nBut we also note that nmfc_cert is a special type: if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. nmfc_cert keywords: As described or As found or Original and inspection or Corrected class or Correction information. Also NMFC Code, class #. This page does not mention NMFC. It just has weight and dimensions. So it\'s weight_and_inspection_cert.\n\nCheck whether it\'s also an inspection certificate without weight? No, it includes weight. So weight_and_inspection_cert.\n\nThus single page, doc_type: weight_and_inspection_cert. Page no 1.\n\nWe must return array of objects? The function expects an array of documents {page_no, doc_type}. For each page. So we return:\n\n[{page_no:1, doc_type:"weight_and_inspection_cert"}]\n\nWe must call the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2826, 'outputTokens': 355, 'totalTokens': 3181}, 'metrics': {'latencyMs': 1700}}
2025-09-24 15:00:55,480 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/83ed8f5b_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:00:55,807 - INFO - 

A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 15:00:55,807 - INFO - 

✓ Saved result: output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:00:57,322 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/83ed8f5b_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:00:57,323 - INFO - 
📊 Processing Summary:
2025-09-24 15:00:57,323 - INFO -    Total files: 1
2025-09-24 15:00:57,324 - INFO -    Successful: 1
2025-09-24 15:00:57,324 - INFO -    Failed: 0
2025-09-24 15:00:57,324 - INFO -    Duration: 18.88 seconds
2025-09-24 15:00:57,324 - INFO -    Output directory: output
2025-09-24 15:00:57,324 - INFO - 
📋 Successfully Processed Files:
2025-09-24 15:00:57,324 - INFO -    📄 A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 15:00:57,325 - INFO - 
============================================================================================================================================
2025-09-24 15:00:57,325 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 15:00:57,326 - INFO - ============================================================================================================================================
2025-09-24 15:00:57,326 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 15:00:57,326 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:00:57,326 - INFO - A34CDFDJ66EDOZEKZWJL.pdf                           1      weight_and_inspect... run1_A34CDFDJ66EDOZEKZWJL.json                    
2025-09-24 15:00:57,326 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 15:00:57,327 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:00:57,327 - INFO - 
2025-09-24 15:00:57,327 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 15:00:57,327 - INFO - Total entries: 1
2025-09-24 15:00:57,327 - INFO - ============================================================================================================================================
2025-09-24 15:00:57,327 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 15:00:57,327 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:00:57,328 - INFO -   1. A34CDFDJ66EDOZEKZWJL.pdf            Page 1   → weight_and_inspection_cert | run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 15:00:57,328 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 15:00:57,328 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 18.884397, 'processed_files': [{'filename': 'A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf'}]}
