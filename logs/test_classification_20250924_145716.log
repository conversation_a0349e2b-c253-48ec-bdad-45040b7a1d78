2025-09-24 14:57:16,529 - INFO - Logging initialized. Log file: logs/test_classification_20250924_145716.log
2025-09-24 14:57:16,530 - INFO - 📁 Found 1 files to process
2025-09-24 14:57:16,530 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:57:16,530 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-24 14:57:16,530 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-24 14:57:16,530 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-24 14:57:16,530 - INFO - ⬆️ [14:57:16] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:57:20,003 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/5148f728_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:57:20,003 - INFO - 🔍 [14:57:20] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:57:20,004 - INFO - Initializing TextractProcessor...
2025-09-24 14:57:20,015 - INFO - Initializing BedrockProcessor...
2025-09-24 14:57:20,021 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5148f728_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:57:20,021 - INFO - Processing PDF from S3...
2025-09-24 14:57:20,021 - INFO - Downloading PDF from S3 to /tmp/tmpw6bn1orw/5148f728_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:57:22,718 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 14:57:22,718 - INFO - Splitting PDF into individual pages...
2025-09-24 14:57:22,719 - INFO - Splitting PDF 5148f728_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 14:57:22,722 - INFO - Split PDF into 1 pages
2025-09-24 14:57:22,722 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:57:22,722 - INFO - Expected pages: [1]
2025-09-24 14:57:29,987 - INFO - Page 1: Extracted 939 characters, 64 lines from 5148f728_A34CDFDJ66EDOZEKZWJL_f0cdbcfb_page_001.pdf
2025-09-24 14:57:29,988 - INFO - Successfully processed page 1
2025-09-24 14:57:29,988 - INFO - Combined 1 pages into final text
2025-09-24 14:57:29,988 - INFO - Text validation for 5148f728_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 14:57:29,988 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:57:29,988 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:57:32,860 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f81ee391-773c-4926-a70c-08adce59fd11', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:27:32 GMT', 'content-type': 'application/json', 'content-length': '1489', 'connection': 'keep-alive', 'x-amzn-requestid': 'f81ee391-773c-4926-a70c-08adce59fd11'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. It\'s Inspection Certificate. The doc_type should be inspection_cert? Let\'s check definitions. The page has "Inspection Certificate". Keywords: yes. Does it mention weight? It has weight in lbs. According to definitions: weight_and_inspection_cert: "Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate" and if weight and inspection certificate, classify as weight_and_inspection_cert. There\'s instruction: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So weight_and_inspection_cert is a type if weight is mentioned. This page has weight. Also "Inspection Certificate". So it should be weight_and_inspection_cert. But is there nmfc_cert? nmfc_cert keywords: "As described and As found or Original and inspection or Corrected class or Correction information; NMFC Code, class #". Not present. So weight_and_inspection_cert is correct.\n\nThus output: page_no 1, doc_type weight_and_inspection_cert.\n\nWe must return via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2826, 'outputTokens': 259, 'totalTokens': 3085}, 'metrics': {'latencyMs': 1345}}
2025-09-24 14:57:32,861 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5148f728_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:57:32,880 - INFO - 

A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:57:32,880 - INFO - 

✓ Saved result: output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:57:34,064 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5148f728_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:57:34,064 - INFO - 
📊 Processing Summary:
2025-09-24 14:57:34,065 - INFO -    Total files: 1
2025-09-24 14:57:34,065 - INFO -    Successful: 1
2025-09-24 14:57:34,065 - INFO -    Failed: 0
2025-09-24 14:57:34,065 - INFO -    Duration: 17.53 seconds
2025-09-24 14:57:34,065 - INFO -    Output directory: output
2025-09-24 14:57:34,065 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:57:34,065 - INFO -    📄 A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:57:34,065 - INFO - 
============================================================================================================================================
2025-09-24 14:57:34,065 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:57:34,065 - INFO - ============================================================================================================================================
2025-09-24 14:57:34,065 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:57:34,065 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:57:34,065 - INFO - A34CDFDJ66EDOZEKZWJL.pdf                           1      weight_and_inspect... run1_A34CDFDJ66EDOZEKZWJL.json                    
2025-09-24 14:57:34,066 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:57:34,066 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:57:34,066 - INFO - 
2025-09-24 14:57:34,066 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:57:34,066 - INFO - Total entries: 1
2025-09-24 14:57:34,066 - INFO - ============================================================================================================================================
2025-09-24 14:57:34,066 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:57:34,066 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:57:34,066 - INFO -   1. A34CDFDJ66EDOZEKZWJL.pdf            Page 1   → weight_and_inspection_cert | run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:57:34,066 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:57:34,066 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 17.53468, 'processed_files': [{'filename': 'A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf'}]}
