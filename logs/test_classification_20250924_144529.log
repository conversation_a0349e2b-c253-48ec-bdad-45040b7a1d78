2025-09-24 14:45:29,780 - INFO - Logging initialized. Log file: logs/test_classification_20250924_144529.log
2025-09-24 14:45:29,780 - INFO - 📁 Found 13 files to process
2025-09-24 14:45:29,780 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:45:29,780 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 14:45:29,780 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 14:45:29,780 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 14:45:29,780 - INFO - ⬆️ [14:45:29] Uploading: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:45:31,312 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf -> s3://document-extraction-logistically/temp/d1758743_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:45:31,313 - INFO - 🔍 [14:45:31] Starting classification: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:45:31,314 - INFO - ⬆️ [14:45:31] Uploading: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:45:31,317 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:31,342 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:31,352 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d1758743_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:45:31,352 - INFO - Processing PDF from S3...
2025-09-24 14:45:31,352 - INFO - Downloading PDF from S3 to /tmp/tmpyq6egto2/d1758743_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:45:31,894 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf -> s3://document-extraction-logistically/temp/2927a9db_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:45:31,894 - INFO - 🔍 [14:45:31] Starting classification: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:45:31,894 - INFO - ⬆️ [14:45:31] Uploading: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:45:31,895 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:31,903 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:31,907 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2927a9db_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:45:31,907 - INFO - Processing PDF from S3...
2025-09-24 14:45:31,907 - INFO - Downloading PDF from S3 to /tmp/tmpucagfieh/2927a9db_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:45:32,750 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:45:32,751 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:32,752 - INFO - Splitting PDF d1758743_B3SIRREC9IAVZOJVDQSN into 1 pages
2025-09-24 14:45:32,766 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf -> s3://document-extraction-logistically/temp/e4cbf262_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:45:32,766 - INFO - Split PDF into 1 pages
2025-09-24 14:45:32,767 - INFO - 🔍 [14:45:32] Starting classification: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:45:32,767 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:32,768 - INFO - ⬆️ [14:45:32] Uploading: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:45:32,769 - INFO - Expected pages: [1]
2025-09-24 14:45:32,769 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:32,790 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:32,792 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e4cbf262_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:45:32,793 - INFO - Processing PDF from S3...
2025-09-24 14:45:32,793 - INFO - Downloading PDF from S3 to /tmp/tmphoa_eupe/e4cbf262_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:45:33,364 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:45:33,364 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:33,365 - INFO - Splitting PDF 2927a9db_CUF54EHGMLQ57HR93DRB into 1 pages
2025-09-24 14:45:33,370 - INFO - Split PDF into 1 pages
2025-09-24 14:45:33,370 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:33,370 - INFO - Expected pages: [1]
2025-09-24 14:45:34,075 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf -> s3://document-extraction-logistically/temp/01c63dd5_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:45:34,075 - INFO - 🔍 [14:45:34] Starting classification: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:45:34,076 - INFO - ⬆️ [14:45:34] Uploading: KVKA9U3LUA7MXSHRCFYK.pdf
2025-09-24 14:45:34,077 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:34,094 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:34,099 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/01c63dd5_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:45:34,099 - INFO - Processing PDF from S3...
2025-09-24 14:45:34,100 - INFO - Downloading PDF from S3 to /tmp/tmp14xnoyi8/01c63dd5_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:45:34,653 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/KVKA9U3LUA7MXSHRCFYK.pdf -> s3://document-extraction-logistically/temp/15d5875f_KVKA9U3LUA7MXSHRCFYK.pdf
2025-09-24 14:45:34,654 - INFO - 🔍 [14:45:34] Starting classification: KVKA9U3LUA7MXSHRCFYK.pdf
2025-09-24 14:45:34,655 - INFO - ⬆️ [14:45:34] Uploading: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:45:34,657 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:34,680 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:34,687 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/15d5875f_KVKA9U3LUA7MXSHRCFYK.pdf
2025-09-24 14:45:34,687 - INFO - Processing PDF from S3...
2025-09-24 14:45:34,688 - INFO - Downloading PDF from S3 to /tmp/tmp6jrk8ula/15d5875f_KVKA9U3LUA7MXSHRCFYK.pdf
2025-09-24 14:45:34,759 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:45:34,759 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:34,761 - INFO - Splitting PDF e4cbf262_FYQQGIW8Z9DSAPCL0S9G into 1 pages
2025-09-24 14:45:34,761 - INFO - Split PDF into 1 pages
2025-09-24 14:45:34,762 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:34,762 - INFO - Expected pages: [1]
2025-09-24 14:45:35,332 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf -> s3://document-extraction-logistically/temp/d4fdcac8_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:45:35,332 - INFO - 🔍 [14:45:35] Starting classification: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:45:35,333 - INFO - ⬆️ [14:45:35] Uploading: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:45:35,333 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:35,355 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:35,359 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d4fdcac8_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:45:35,360 - INFO - Processing PDF from S3...
2025-09-24 14:45:35,360 - INFO - Downloading PDF from S3 to /tmp/tmpts8inof4/d4fdcac8_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:45:35,965 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:45:35,965 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:35,966 - INFO - Splitting PDF 15d5875f_KVKA9U3LUA7MXSHRCFYK into 1 pages
2025-09-24 14:45:35,968 - INFO - Split PDF into 1 pages
2025-09-24 14:45:35,968 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:35,968 - INFO - Expected pages: [1]
2025-09-24 14:45:36,170 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 14:45:36,171 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:36,171 - INFO - Splitting PDF 01c63dd5_IZTBXFPGXBFH3DV900G4 into 1 pages
2025-09-24 14:45:36,172 - INFO - Split PDF into 1 pages
2025-09-24 14:45:36,172 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:36,172 - INFO - Expected pages: [1]
2025-09-24 14:45:36,279 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf -> s3://document-extraction-logistically/temp/13e56dde_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:45:36,280 - INFO - 🔍 [14:45:36] Starting classification: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:45:36,281 - INFO - ⬆️ [14:45:36] Uploading: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:45:36,287 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:36,355 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:36,358 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/13e56dde_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:45:36,359 - INFO - Processing PDF from S3...
2025-09-24 14:45:36,359 - INFO - Downloading PDF from S3 to /tmp/tmpbikqcfiw/13e56dde_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:45:36,930 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf -> s3://document-extraction-logistically/temp/192755d0_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:45:36,930 - INFO - 🔍 [14:45:36] Starting classification: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:45:36,931 - INFO - ⬆️ [14:45:36] Uploading: RPWDVSCGR4H2ANDRNPUG.pdf
2025-09-24 14:45:36,933 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:36,952 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:36,957 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/192755d0_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:45:36,957 - INFO - Processing PDF from S3...
2025-09-24 14:45:36,957 - INFO - Downloading PDF from S3 to /tmp/tmptw_u9m8i/192755d0_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:45:37,220 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:45:37,221 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:37,221 - INFO - Splitting PDF d4fdcac8_MR6ONA8GK6HN1LCZHEX3 into 1 pages
2025-09-24 14:45:37,222 - INFO - Split PDF into 1 pages
2025-09-24 14:45:37,222 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:37,222 - INFO - Expected pages: [1]
2025-09-24 14:45:37,397 - INFO - Page 1: Extracted 1255 characters, 79 lines from 2927a9db_CUF54EHGMLQ57HR93DRB_c2bf10d7_page_001.pdf
2025-09-24 14:45:37,401 - INFO - Successfully processed page 1
2025-09-24 14:45:37,401 - INFO - Combined 1 pages into final text
2025-09-24 14:45:37,401 - INFO - Text validation for 2927a9db_CUF54EHGMLQ57HR93DRB: 1272 characters, 1 pages
2025-09-24 14:45:37,402 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:37,402 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:37,485 - INFO - Page 1: Extracted 1454 characters, 85 lines from d1758743_B3SIRREC9IAVZOJVDQSN_6395ea01_page_001.pdf
2025-09-24 14:45:37,485 - INFO - Successfully processed page 1
2025-09-24 14:45:37,485 - INFO - Combined 1 pages into final text
2025-09-24 14:45:37,485 - INFO - Text validation for d1758743_B3SIRREC9IAVZOJVDQSN: 1471 characters, 1 pages
2025-09-24 14:45:37,486 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:37,486 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:37,554 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/RPWDVSCGR4H2ANDRNPUG.pdf -> s3://document-extraction-logistically/temp/ca8b9371_RPWDVSCGR4H2ANDRNPUG.pdf
2025-09-24 14:45:37,554 - INFO - 🔍 [14:45:37] Starting classification: RPWDVSCGR4H2ANDRNPUG.pdf
2025-09-24 14:45:37,555 - INFO - ⬆️ [14:45:37] Uploading: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:45:37,557 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:37,573 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:37,581 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ca8b9371_RPWDVSCGR4H2ANDRNPUG.pdf
2025-09-24 14:45:37,581 - INFO - Processing PDF from S3...
2025-09-24 14:45:37,582 - INFO - Downloading PDF from S3 to /tmp/tmpxr0578zh/ca8b9371_RPWDVSCGR4H2ANDRNPUG.pdf
2025-09-24 14:45:38,135 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf -> s3://document-extraction-logistically/temp/570a7059_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:45:38,136 - INFO - 🔍 [14:45:38] Starting classification: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:45:38,137 - INFO - ⬆️ [14:45:38] Uploading: UWWC9JXK5060AH7CSALY.pdf
2025-09-24 14:45:38,139 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:38,157 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:38,161 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/570a7059_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:45:38,161 - INFO - Processing PDF from S3...
2025-09-24 14:45:38,162 - INFO - Downloading PDF from S3 to /tmp/tmpc2rt2z5t/570a7059_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:45:38,749 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/UWWC9JXK5060AH7CSALY.pdf -> s3://document-extraction-logistically/temp/0ef197c9_UWWC9JXK5060AH7CSALY.pdf
2025-09-24 14:45:38,749 - INFO - 🔍 [14:45:38] Starting classification: UWWC9JXK5060AH7CSALY.pdf
2025-09-24 14:45:38,750 - INFO - ⬆️ [14:45:38] Uploading: VRAK5HH2FS2C77OU55SV.pdf
2025-09-24 14:45:38,751 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:38,770 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:38,775 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0ef197c9_UWWC9JXK5060AH7CSALY.pdf
2025-09-24 14:45:38,776 - INFO - Processing PDF from S3...
2025-09-24 14:45:38,777 - INFO - Downloading PDF from S3 to /tmp/tmpbhdv5whl/0ef197c9_UWWC9JXK5060AH7CSALY.pdf
2025-09-24 14:45:38,778 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:45:38,779 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:38,785 - INFO - Splitting PDF 192755d0_PEE2ZFMV7X0A0FL35G4G into 1 pages
2025-09-24 14:45:38,787 - INFO - Split PDF into 1 pages
2025-09-24 14:45:38,788 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:38,788 - INFO - Expected pages: [1]
2025-09-24 14:45:39,142 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:45:39,143 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:39,144 - INFO - Splitting PDF ca8b9371_RPWDVSCGR4H2ANDRNPUG into 2 pages
2025-09-24 14:45:39,148 - INFO - Split PDF into 2 pages
2025-09-24 14:45:39,148 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:39,148 - INFO - Expected pages: [1, 2]
2025-09-24 14:45:39,290 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 14:45:39,291 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:39,292 - INFO - Splitting PDF 13e56dde_PB67IAPSJB1DZWMDIE1H into 2 pages
2025-09-24 14:45:39,296 - INFO - Split PDF into 2 pages
2025-09-24 14:45:39,297 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:39,297 - INFO - Expected pages: [1, 2]
2025-09-24 14:45:39,342 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/VRAK5HH2FS2C77OU55SV.pdf -> s3://document-extraction-logistically/temp/b102b667_VRAK5HH2FS2C77OU55SV.pdf
2025-09-24 14:45:39,342 - INFO - 🔍 [14:45:39] Starting classification: VRAK5HH2FS2C77OU55SV.pdf
2025-09-24 14:45:39,343 - INFO - ⬆️ [14:45:39] Uploading: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:45:39,344 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:39,365 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:39,370 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b102b667_VRAK5HH2FS2C77OU55SV.pdf
2025-09-24 14:45:39,370 - INFO - Processing PDF from S3...
2025-09-24 14:45:39,371 - INFO - Downloading PDF from S3 to /tmp/tmp3l3bmv1m/b102b667_VRAK5HH2FS2C77OU55SV.pdf
2025-09-24 14:45:39,384 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:45:39,384 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:39,385 - INFO - Splitting PDF 570a7059_U7BB1XSF3ASMIAE1MQ5I into 1 pages
2025-09-24 14:45:39,386 - INFO - Split PDF into 1 pages
2025-09-24 14:45:39,387 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:39,387 - INFO - Expected pages: [1]
2025-09-24 14:45:39,843 - INFO - Page 1: Extracted 1463 characters, 95 lines from e4cbf262_FYQQGIW8Z9DSAPCL0S9G_92ea0118_page_001.pdf
2025-09-24 14:45:39,843 - INFO - Successfully processed page 1
2025-09-24 14:45:39,843 - INFO - Combined 1 pages into final text
2025-09-24 14:45:39,844 - INFO - Text validation for e4cbf262_FYQQGIW8Z9DSAPCL0S9G: 1480 characters, 1 pages
2025-09-24 14:45:39,844 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:39,844 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:39,881 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a39b6263-7fe6-4472-9468-76b57fdc678c', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:39 GMT', 'content-type': 'application/json', 'content-length': '1541', 'connection': 'keep-alive', 'x-amzn-requestid': 'a39b6263-7fe6-4472-9468-76b57fdc678c'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have page 1 of a document. The task: classify each page. The page shows weight and research program, with statements "We certify that this shipment has been inspected and/or reweighed ... the commodity description(s) and/or weight(s) shown above is correct". Also mentions "Weight and Research Section". The keywords: "Weight and Research Program", "Weight and Research Section". There is also mention of "declared value" etc. This page likely is a "weight_and_inspection_cert". Let\'s confirm: weight_and_inspection_cert requires certificate of analysis, measurement certificate, weight certificate, inspection certificate, W&I. It also says "If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert." There\'s mention of "As found upon Examination" "We certify that this shipment has been inspected and/or reweighed". That indicates inspection of weight. There is no mention of NMFC code. So likely weight_and_inspection_cert.\n\nThus output a JSON array with one object: page_no 1, doc_type weight_and_inspection_cert.\n\nWe should return via tool call classify_logistics_doc_type with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2923, 'outputTokens': 267, 'totalTokens': 3190}, 'metrics': {'latencyMs': 1241}}
2025-09-24 14:45:39,882 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2927a9db_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:45:39,895 - INFO - Page 1: Extracted 804 characters, 39 lines from 15d5875f_KVKA9U3LUA7MXSHRCFYK_2293d969_page_001.pdf
2025-09-24 14:45:39,895 - INFO - Successfully processed page 1
2025-09-24 14:45:39,895 - INFO - Combined 1 pages into final text
2025-09-24 14:45:39,896 - INFO - Text validation for 15d5875f_KVKA9U3LUA7MXSHRCFYK: 821 characters, 1 pages
2025-09-24 14:45:39,896 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:39,896 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:40,014 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf -> s3://document-extraction-logistically/temp/8f3e1569_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:45:40,015 - INFO - 🔍 [14:45:40] Starting classification: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:45:40,016 - INFO - Initializing TextractProcessor...
2025-09-24 14:45:40,031 - INFO - Initializing BedrockProcessor...
2025-09-24 14:45:40,036 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8f3e1569_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:45:40,038 - INFO - Processing PDF from S3...
2025-09-24 14:45:40,041 - INFO - Downloading PDF from S3 to /tmp/tmpccq74n8x/8f3e1569_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:45:40,075 - INFO - 

CUF54EHGMLQ57HR93DRB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:45:40,075 - INFO - 

✓ Saved result: output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 14:45:40,305 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:45:40,305 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:40,307 - INFO - Splitting PDF 0ef197c9_UWWC9JXK5060AH7CSALY into 2 pages
2025-09-24 14:45:40,310 - INFO - Split PDF into 2 pages
2025-09-24 14:45:40,310 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:40,310 - INFO - Expected pages: [1, 2]
2025-09-24 14:45:40,355 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2927a9db_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:45:40,849 - INFO - Page 1: Extracted 465 characters, 37 lines from 01c63dd5_IZTBXFPGXBFH3DV900G4_f1aad4b0_page_001.pdf
2025-09-24 14:45:40,850 - INFO - Successfully processed page 1
2025-09-24 14:45:40,850 - INFO - Combined 1 pages into final text
2025-09-24 14:45:40,850 - INFO - Text validation for 01c63dd5_IZTBXFPGXBFH3DV900G4: 482 characters, 1 pages
2025-09-24 14:45:40,850 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:40,850 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:40,867 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:45:40,867 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:40,868 - INFO - Splitting PDF b102b667_VRAK5HH2FS2C77OU55SV into 1 pages
2025-09-24 14:45:40,870 - INFO - Split PDF into 1 pages
2025-09-24 14:45:40,870 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:40,870 - INFO - Expected pages: [1]
2025-09-24 14:45:40,904 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7d2ab52c-1d34-4c89-be00-620a7e10ab60', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:40 GMT', 'content-type': 'application/json', 'content-length': '2716', 'connection': 'keep-alive', 'x-amzn-requestid': '7d2ab52c-1d34-4c89-be00-620a7e10ab60'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. We need to determine doc_type. The content contains "Weight and Research Program", "As found upon Examination", "We certify that this shipment has been inspected and/or reweighed", "UPD CLASS PER NMFW 56840 ON BOL", "Automatic scale/weight update", "Tariff Authority". Likely this is a Weight and Inspection Certificate, or maybe a weight_and_inspection_cert.\n\nFrom definitions: weight_and_inspection_cert — Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also check nmfc_cert: if it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else weight_and_inspection_cert. Also check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise inspection_cert.\n\nIn this page, it says "We certify that this shipment has been inspected and/or reweighed". This is indeed a weight and inspection certificate, likely a "weight_and_inspection_cert". There\'s no mention of NMFC Code? Actually "UPD CLASS PER NMFC 56840 ON BOL" appears. That is an NMFC code. But the instruction: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So we have NMFC code mention. So we should classify as nmfc_cert? Let\'s interpret: weight_and_inspection_cert is a certificate that includes NMFC? The instruction says: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So if the weight_and_inspection_cert has NMFC keywords, classify as nmfc_cert. The page has "NMFC 56840". That is an NMFC code. So likely nmfc_cert. The page also has "We certify that this shipment has been inspected and/or reweighed" and "Automatic scale/weight update". That is a weight_and_inspection_cert that includes NMFC. So classify nmfc_cert.\n\nThus output: page_no 1 doc_type "nmfc_cert".\n\nWe must use tool classify_logistics_doc_type.'}}}, {'toolUse': {'toolUseId': 'tooluse_MFxd2EXmSu2oFHE44w-7wg', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2985, 'outputTokens': 529, 'totalTokens': 3514}, 'metrics': {'latencyMs': 2219}}
2025-09-24 14:45:40,904 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d1758743_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:45:40,930 - INFO - 

B3SIRREC9IAVZOJVDQSN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:45:40,930 - INFO - 

✓ Saved result: output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 14:45:41,213 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d1758743_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:45:41,573 - INFO - Page 1: Extracted 500 characters, 76 lines from d4fdcac8_MR6ONA8GK6HN1LCZHEX3_2c64fd17_page_001.pdf
2025-09-24 14:45:41,573 - INFO - Successfully processed page 1
2025-09-24 14:45:41,573 - INFO - Combined 1 pages into final text
2025-09-24 14:45:41,573 - INFO - Text validation for d4fdcac8_MR6ONA8GK6HN1LCZHEX3: 517 characters, 1 pages
2025-09-24 14:45:41,574 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:41,574 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:41,743 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '280287e7-3fa8-4985-b3d1-6af8f710a4cb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:41 GMT', 'content-type': 'application/json', 'content-length': '871', 'connection': 'keep-alive', 'x-amzn-requestid': '280287e7-3fa8-4985-b3d1-6af8f710a4cb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify this page. Page contains "NMFC Classification Certificate" heading at top. Also contains "Inspection Detail", density calculation, weight, etc. It looks like an NMFC Classification Certificate, which is a type of NMFC certificate. The provided enum includes nmfc_cert. So doc_type "nmfc_cert".\n\nNeed to output JSON array with one object: page_no 1, doc_type "nmfc_cert".\n\nWe must use the tool classify_logistics_doc_type. Let\'s call it.'}}}, {'toolUse': {'toolUseId': 'tooluse_U0ST7eShSRa8maeLNZk0Nw', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 3040, 'outputTokens': 121, 'totalTokens': 3161}, 'metrics': {'latencyMs': 699}}
2025-09-24 14:45:41,743 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e4cbf262_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:45:41,772 - INFO - 

FYQQGIW8Z9DSAPCL0S9G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:45:41,772 - INFO - 

✓ Saved result: output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 14:45:42,056 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e4cbf262_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:45:42,332 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 14:45:42,333 - INFO - Splitting PDF into individual pages...
2025-09-24 14:45:42,334 - INFO - Splitting PDF 8f3e1569_YL4HZPZH7D3B03FZZ5QK into 1 pages
2025-09-24 14:45:42,347 - INFO - Split PDF into 1 pages
2025-09-24 14:45:42,347 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:45:42,347 - INFO - Expected pages: [1]
2025-09-24 14:45:42,911 - INFO - Page 1: Extracted 490 characters, 27 lines from ca8b9371_RPWDVSCGR4H2ANDRNPUG_9c5cf481_page_001.pdf
2025-09-24 14:45:42,912 - INFO - Successfully processed page 1
2025-09-24 14:45:42,949 - INFO - Page 2: Extracted 490 characters, 27 lines from ca8b9371_RPWDVSCGR4H2ANDRNPUG_9c5cf481_page_002.pdf
2025-09-24 14:45:42,950 - INFO - Successfully processed page 2
2025-09-24 14:45:42,950 - INFO - Combined 2 pages into final text
2025-09-24 14:45:42,951 - INFO - Text validation for ca8b9371_RPWDVSCGR4H2ANDRNPUG: 1016 characters, 2 pages
2025-09-24 14:45:42,952 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:42,952 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:43,248 - INFO - Page 1: Extracted 1119 characters, 62 lines from 570a7059_U7BB1XSF3ASMIAE1MQ5I_bff51934_page_001.pdf
2025-09-24 14:45:43,249 - INFO - Successfully processed page 1
2025-09-24 14:45:43,250 - INFO - Combined 1 pages into final text
2025-09-24 14:45:43,250 - INFO - Text validation for 570a7059_U7BB1XSF3ASMIAE1MQ5I: 1136 characters, 1 pages
2025-09-24 14:45:43,250 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:43,250 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:43,341 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd0db43ae-d0fa-42ea-b38b-d44af423bc23', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:43 GMT', 'content-type': 'application/json', 'content-length': '654', 'connection': 'keep-alive', 'x-amzn-requestid': 'd0db43ae-d0fa-42ea-b38b-d44af423bc23'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only one page. It is a NMFC Classification Certificate. According to definitions: nmfc_cert. The doc_type should be "nmfc_cert" (not weight_and_inspection_cert). Keywords: NMFC Classification Certificate. Should be nmfc_cert. So output an array with one object: page_no 1, doc_type "nmfc_cert".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2729, 'outputTokens': 98, 'totalTokens': 2827}, 'metrics': {'latencyMs': 595}}
2025-09-24 14:45:43,342 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d4fdcac8_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:45:43,364 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '186607db-47f9-4b51-998d-061ecdf48118', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:43 GMT', 'content-type': 'application/json', 'content-length': '2953', 'connection': 'keep-alive', 'x-amzn-requestid': '186607db-47f9-4b51-998d-061ecdf48118'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. Need classify. The page is an inspection report, with NMFC code, weight, dimensions, summary of inspection, corrected, etc. The description indicates "Inspection Report" and it covers inspection of shipment. The keywords: "Inspection Report", "Corrected", "NMFC", "Inspection". This matches weight_and_inspection_cert or inspection_cert? Let\'s analyze definitions.\n\nweight_and_inspection_cert: "Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also note: If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert. Also check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise, classification inspection_cert.\n\nHere we have weight mentioned (1070.0). So it is a weight and inspection certificate. Also NMFC code included. That suggests it\'s a weight_and_inspection_cert. It is not exactly NMFC cert because that is for corrected weight. The definition of nmfc_cert: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." The page says "***Corrected". Possibly indicates correction made. But the description: "Original Bill Of Lading Information:" then "Corrected" maybe indicates corrections. It might be a corrected inspection report. The spec says if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. But we need to know if nmfc_cert has keywords "As described", "As found", "Original" etc. But here we see "Corrected" and "NMFC 176050-3 for Signs, NOI, density 2 but less than 4 pcf, CL 250 applies." It is a corrected report. So likely nmfc_cert.\n\nBut nmfc_cert has definition: "NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual." Keywords: As described or found or Original and inspection or Corrected class or Correction information. We have "Corrected" and "Original Bill Of Lading Information". So yes, could be nmfc_cert.\n\nThus doc_type likely nmfc_cert.\n\nBut double-check: nmfc_cert is a type of weight_and_inspection_cert but with correction. The page is an inspection report with "Corrected". So classification: nmfc_cert.\n\nThus output: one object, page_no 1, doc_type "nmfc_cert". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2733, 'outputTokens': 571, 'totalTokens': 3304}, 'metrics': {'latencyMs': 2332}}
2025-09-24 14:45:43,365 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/15d5875f_KVKA9U3LUA7MXSHRCFYK.pdf
2025-09-24 14:45:43,368 - INFO - 

MR6ONA8GK6HN1LCZHEX3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:45:43,368 - INFO - 

✓ Saved result: output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 14:45:43,455 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '954d2cd9-1fef-4d87-b5a0-72b619b2cceb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:43 GMT', 'content-type': 'application/json', 'content-length': '1521', 'connection': 'keep-alive', 'x-amzn-requestid': '954d2cd9-1fef-4d87-b5a0-72b619b2cceb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to determine document type.\n\nThe page shows:\n\n- Header: "Weight & Inspection Freight Class Certificate"\n- "Weight & Inspection"\n- "Freight Class Certificate"\n- Detailed weight and dimensions, cube, density, etc.\n- "CORRECT TO CLASS: 250"\n- "CORRECT CLASS PER DEFAULT DENSITY RATING & DIMENSIONS OBTAINED AT PICK UP"\n- "Mileage = 898.00"\n- "FCCW: 15352309"\n\nIt seems like a weight and inspection certificate. It contains weight and dimensions, likely for freight classification. So likely "weight_and_inspection_cert".\n\nCheck if nmfc_cert? There\'s no mention of NMFC code or class #. "CORRECT TO CLASS: 250" that\'s the freight class. But no NMFC code. So should be weight_and_inspection_cert.\n\nThus output one object: page_no: 1, doc_type: "weight_and_inspection_cert".\n\nWe need to use tool classify_logistics_doc_type.\n\nLet\'s produce JSON array? The spec says return exactly one object describing that page. But tool expects array of documents. The input to function: documents: array of objects each with page_no and doc_type. So we pass array with one element.\n\nThus call function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2634, 'outputTokens': 291, 'totalTokens': 2925}, 'metrics': {'latencyMs': 1451}}
2025-09-24 14:45:43,455 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/01c63dd5_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:45:43,651 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d4fdcac8_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:45:43,658 - INFO - 

KVKA9U3LUA7MXSHRCFYK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:45:43,658 - INFO - 

✓ Saved result: output/run1_KVKA9U3LUA7MXSHRCFYK.json
2025-09-24 14:45:43,727 - INFO - Page 1: Extracted 477 characters, 75 lines from 192755d0_PEE2ZFMV7X0A0FL35G4G_50dde521_page_001.pdf
2025-09-24 14:45:43,727 - INFO - Successfully processed page 1
2025-09-24 14:45:43,728 - INFO - Combined 1 pages into final text
2025-09-24 14:45:43,728 - INFO - Text validation for 192755d0_PEE2ZFMV7X0A0FL35G4G: 494 characters, 1 pages
2025-09-24 14:45:43,728 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:43,728 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:43,935 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/15d5875f_KVKA9U3LUA7MXSHRCFYK.pdf
2025-09-24 14:45:43,949 - INFO - 

IZTBXFPGXBFH3DV900G4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:45:43,949 - INFO - 

✓ Saved result: output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 14:45:44,009 - INFO - Page 2: Extracted 670 characters, 48 lines from 0ef197c9_UWWC9JXK5060AH7CSALY_b391a373_page_002.pdf
2025-09-24 14:45:44,009 - INFO - Successfully processed page 2
2025-09-24 14:45:44,181 - INFO - Page 2: Extracted 832 characters, 40 lines from 13e56dde_PB67IAPSJB1DZWMDIE1H_3ee4144d_page_002.pdf
2025-09-24 14:45:44,182 - INFO - Successfully processed page 2
2025-09-24 14:45:44,236 - INFO - Page 1: Extracted 689 characters, 40 lines from 0ef197c9_UWWC9JXK5060AH7CSALY_b391a373_page_001.pdf
2025-09-24 14:45:44,236 - INFO - Successfully processed page 1
2025-09-24 14:45:44,236 - INFO - Combined 2 pages into final text
2025-09-24 14:45:44,237 - INFO - Text validation for 0ef197c9_UWWC9JXK5060AH7CSALY: 1395 characters, 2 pages
2025-09-24 14:45:44,237 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:44,237 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:44,242 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/01c63dd5_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:45:44,667 - INFO - Page 1: Extracted 1225 characters, 82 lines from 13e56dde_PB67IAPSJB1DZWMDIE1H_3ee4144d_page_001.pdf
2025-09-24 14:45:44,667 - INFO - Successfully processed page 1
2025-09-24 14:45:44,667 - INFO - Combined 2 pages into final text
2025-09-24 14:45:44,667 - INFO - Text validation for 13e56dde_PB67IAPSJB1DZWMDIE1H: 2093 characters, 2 pages
2025-09-24 14:45:44,668 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:44,668 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:44,818 - INFO - Page 1: Extracted 588 characters, 62 lines from b102b667_VRAK5HH2FS2C77OU55SV_c70f0015_page_001.pdf
2025-09-24 14:45:44,819 - INFO - Successfully processed page 1
2025-09-24 14:45:44,819 - INFO - Combined 1 pages into final text
2025-09-24 14:45:44,819 - INFO - Text validation for b102b667_VRAK5HH2FS2C77OU55SV: 605 characters, 1 pages
2025-09-24 14:45:44,820 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:44,820 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:45,634 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e4ff08b0-612c-4c37-900d-902da66a4189', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:45 GMT', 'content-type': 'application/json', 'content-length': '637', 'connection': 'keep-alive', 'x-amzn-requestid': 'e4ff08b0-612c-4c37-900d-902da66a4189'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify page1. Document looks like NMFC classification certificate. Keywords: NMFC CLASSIFICATION CERTIFICATE. So doc_type: nmfc_cert. Also check if weight_and_inspection_cert? It has weight? It says WT 186. But NMFC cert. So type nmfc_cert. Output: array with one object page_no 1 doc_type nmfc_cert. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2720, 'outputTokens': 99, 'totalTokens': 2819}, 'metrics': {'latencyMs': 648}}
2025-09-24 14:45:45,635 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/192755d0_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:45:45,655 - INFO - 

PEE2ZFMV7X0A0FL35G4G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:45:45,655 - INFO - 

✓ Saved result: output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 14:45:45,953 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/192755d0_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:45:46,147 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3365477a-5178-45a7-8cc4-5f2401d0dd23', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:46 GMT', 'content-type': 'application/json', 'content-length': '2204', 'connection': 'keep-alive', 'x-amzn-requestid': '3365477a-5178-45a7-8cc4-5f2401d0dd23'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Both pages are same text. It appears to be a Certified Weight Certificate. This matches definition for "weight_and_inspection_cert" or "inspection_cert"? Let\'s review the definitions: weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Note: check if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert; if so classify as nmfc_cert else weight_and_inspection_cert.\n\nThis page has "CERTIFIED WEIGHT CERTIFICATE". Also includes "DECLARED WEIGHT: 2,260 lbs." and "CORRECT WEIGHT: 2,355 lbs." So it\'s a weight certificate confirming weight. That aligns with weight_and_inspection_cert.\n\nWe also have "CERTIFIED WEIGHT CERTIFICATE". Is there any mention of NMFC? No. So classify as weight_and_inspection_cert.\n\nBoth pages are same content. Are they continuations? The page starts with text; not "continued". But page 2 is identical copy of page 1; maybe a duplicate. It\'s not a continuation; it\'s a separate page that is the same content. So we should classify each page separately. Since they are same content, both weight_and_inspection_cert.\n\nReturn JSON array of objects with page_no and doc_type.\n\nWe must output via tool call classify_logistics_doc_type. The input includes documents array with page_no and doc_type. So we call tool with documents: [{page_no:1, doc_type:"weight_and_inspection_cert"}, {page_no:2, doc_type:"weight_and_inspection_cert"}].\n\nMake sure doc_type matches exactly the enum values. The enum includes "weight_and_inspection_cert". Good.\n\nWe output tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2815, 'outputTokens': 420, 'totalTokens': 3235}, 'metrics': {'latencyMs': 1949}}
2025-09-24 14:45:46,147 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ca8b9371_RPWDVSCGR4H2ANDRNPUG.pdf
2025-09-24 14:45:46,177 - INFO - 

RPWDVSCGR4H2ANDRNPUG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:45:46,178 - INFO - 

✓ Saved result: output/run1_RPWDVSCGR4H2ANDRNPUG.json
2025-09-24 14:45:46,339 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '99e9890e-478f-4b0c-a354-709278f95b50', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:46 GMT', 'content-type': 'application/json', 'content-length': '2341', 'connection': 'keep-alive', 'x-amzn-requestid': '99e9890e-478f-4b0c-a354-709278f95b50'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. The text shows "Inspection Certificate" heading, and details. It includes "Inspection Certificate", "CUBE", "TOTAL WEIGHT", "DENSITY", "CERTIFICATE INFORMATION". Also "Corrected Bill Information". This looks like a weight and inspection certificate. The guidelines: For weight_and_inspection_cert: It is a certificate/report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also it says: if weight_and_inspection_cert has keywords mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert. For nmfc_cert: keywords: As described or As found or Original and inspection or Corrected class or Correction information. Also optional NMFC Code, class #. The text says "CORRECTED BILL INFORMATION" and "Corrected Bill Information" appears. Also "NMFC 87560" and "NMFC 87552". Also "class 150". So it has "Corrected" phrase. That suggests nmfc_cert? The guideline: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." The nmfc_cert keywords: "As described or As found or Original and inspection or Corrected class or Correction information". The phrase "Corrected Bill Information" is present. "Corrected" is a keyword. So we should classify as nmfc_cert. Alternatively, maybe it\'s weight_and_inspection_cert. The doc type: It\'s an inspection certificate with weight and correction. So likely nmfc_cert. According to the guidelines, nmfc_cert is for NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with inspected against original or corrected against actual. Yes we have corrected. So nmfc_cert.\n\nThus output: page_no 1, doc_type "nmfc_cert". Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2837, 'outputTokens': 446, 'totalTokens': 3283}, 'metrics': {'latencyMs': 1884}}
2025-09-24 14:45:46,339 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/570a7059_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:45:46,456 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ca8b9371_RPWDVSCGR4H2ANDRNPUG.pdf
2025-09-24 14:45:46,479 - INFO - 

U7BB1XSF3ASMIAE1MQ5I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:45:46,479 - INFO - 

✓ Saved result: output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 14:45:46,771 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/570a7059_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:45:47,592 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7a6ccb9e-fb82-401c-bce7-ec8bd910e11a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:47 GMT', 'content-type': 'application/json', 'content-length': '1704', 'connection': 'keep-alive', 'x-amzn-requestid': '7a6ccb9e-fb82-401c-bce7-ec8bd910e11a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The page shows "Weight & Inspection Certificate" title. That strongly indicates weight_and_inspection_cert. We need to check if it has weight mentioned in lbs or tons, etc. It has weight values 490?? and 057300-02-100 etc. It says "Weight". There is "TM" maybe. This is definitely a weight and inspection certificate. Also note that nmfc_cert is a type of weight_and_inspection_cert but with correction notice or NMFC classification. The page has "Description Correction" and "Inspection Date". It seems it\'s a correction of weight? The heading "Weight & Inspection Certificate" with "Description Correction". That might be a correction certificate. But the definition says: nmfc_cert is when correction is made in weight_and_inspection_cert, nmfc_cert is issued. "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." So if it\'s a correction, we should classify as nmfc_cert. The page\'s heading includes "Description Correction". That suggests a correction. So likely nmfc_cert. Also the presence of "Inspection Date" and "Inspected By". So yes classification: nmfc_cert.\n\nWe need to output JSON via tool call. The tool expects array of objects: documents: array of objects with page_no and doc_type.\n\nWe have only one page, so return: {documents: [{page_no:1, doc_type:"nmfc_cert"}]}\n\nWe must call the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2667, 'outputTokens': 324, 'totalTokens': 2991}, 'metrics': {'latencyMs': 1458}}
2025-09-24 14:45:47,592 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b102b667_VRAK5HH2FS2C77OU55SV.pdf
2025-09-24 14:45:47,615 - INFO - 

VRAK5HH2FS2C77OU55SV.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:45:47,615 - INFO - 

✓ Saved result: output/run1_VRAK5HH2FS2C77OU55SV.json
2025-09-24 14:45:47,894 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b102b667_VRAK5HH2FS2C77OU55SV.pdf
2025-09-24 14:45:48,205 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'cb862126-abec-4897-a633-c4b96047cf1d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:48 GMT', 'content-type': 'application/json', 'content-length': '3503', 'connection': 'keep-alive', 'x-amzn-requestid': 'cb862126-abec-4897-a633-c4b96047cf1d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The content looks like a density certificate and commodity change certificate. We have to pick appropriate doc_type. Let\'s review options: Maybe "weight_and_inspection_cert" or "inspection_cert" or "nmfc_cert". There\'s also a "comm_invoice" but that seems more about HS codes etc. This is a density certificate, not a commercial invoice. There is a weight, density, cube. It\'s a "Density Certificate" presumably for shipping. Look at enum definitions: "weight_and_inspection_cert" is for certificate confirming weight possibly with length, width, height but not rechecked or reweighted. It says: "Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted." This matches. Also "inspection_cert" is for inspection certificate other than weight and inspection certificate which doesn\'t have weight mentioned. So weight_and_inspection_cert is correct.\n\nAlso "nmfc_cert" is NMFC classification certificate or correction notice. Not that.\n\nThus each page should be "weight_and_inspection_cert". Are these separate pages of same document? Page1 is density certificate. Page2 is commodity change certificate. Both are related to weight/density. Likely same doc type. But if they are separate documents? They share Freight Bill # and same content. Probably same type. The instructions: If a page is part of multi-page single document, each page gets same doc_type. So we need to decide if it\'s one document or two separate documents. They both have front page with header "DENSITY CERTIFICATE" and "Commodity Change Certificate". They might be separate documents but similar. In absence of page continuation indicator, we treat them as separate? The rule: If a page is part of a multi-page single document: each page still gets same doc_type. But if the second page has no "continued" marker, but it\'s a different type header. However, the header indicates "Commodity Change Certificate". That is not among our enums. So maybe it\'s a separate doc type "other" or maybe "weight_and_inspection_cert" because it\'s still a certificate. The header "Commodity Change Certificate" may be a kind of certificate. But we don\'t have a specific enum for that. We can classify as weight_and_inspection_cert because it\'s a certificate confirming weight, density. So both pages weight_and_inspection_cert.\n\nCheck continuation: page2 does not start with "continued". So not continuation. But we might still classify as same type because it\'s same certificate type logic? The instruction: If a page is part of a multi-page single document: each page still gets same doc_type. But we need to detect continuation. It\'s not indicated. But we can consider them separate documents. But we can still classify them as weight_and_inspection_cert because they share similar function. So both pages weight_and_inspection_cert.\n\nThus output:\n\n[\n  {page_no:1, doc_type:"weight_and_inspection_cert"},\n  {page_no:2, doc_type:"weight_and_inspection_cert"}\n]\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3009, 'outputTokens': 663, 'totalTokens': 3672}, 'metrics': {'latencyMs': 2734}}
2025-09-24 14:45:48,205 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0ef197c9_UWWC9JXK5060AH7CSALY.pdf
2025-09-24 14:45:48,228 - INFO - 

UWWC9JXK5060AH7CSALY.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:45:48,228 - INFO - 

✓ Saved result: output/run1_UWWC9JXK5060AH7CSALY.json
2025-09-24 14:45:48,511 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0ef197c9_UWWC9JXK5060AH7CSALY.pdf
2025-09-24 14:45:48,599 - INFO - Page 1: Extracted 1248 characters, 88 lines from 8f3e1569_YL4HZPZH7D3B03FZZ5QK_20296f51_page_001.pdf
2025-09-24 14:45:48,600 - INFO - Successfully processed page 1
2025-09-24 14:45:48,600 - INFO - Combined 1 pages into final text
2025-09-24 14:45:48,600 - INFO - Text validation for 8f3e1569_YL4HZPZH7D3B03FZZ5QK: 1265 characters, 1 pages
2025-09-24 14:45:48,601 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:45:48,601 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:45:48,821 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '83e03cb7-6abf-42dd-93cc-efddb4611bfb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:48 GMT', 'content-type': 'application/json', 'content-length': '3055', 'connection': 'keep-alive', 'x-amzn-requestid': '83e03cb7-6abf-42dd-93cc-efddb4611bfb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages of a document. Need to classify each page. The document appears to be a "Weight & Research Certificate" (W&R). This is likely a type: weight_and_inspection_cert? Or nmfc_cert? Let\'s see definitions. weight_and_inspection_cert: certificate confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also notes: If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. nmfc_cert: NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual. Keywords: As described and As found or Original and inspection or Corrected class or Correction information. Optional keywords: NMFC Code, class #.\n\nGiven W&R certificate includes NMFC: 73260 /SUB NMFC: 09 CLASS: 70.0. This has NMFC code and class. So likely nmfc_cert. Also weight_and_inspection_cert: This is a weight and inspection certificate. But the instruction says check if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. So this page should be nmfc_cert. However, maybe weight_and_inspection_cert is a subcategory. But given the presence of NMFC code, class, etc. The page is specifically a Weight & Research Certificate (W&R). That is a type of weight and inspection certificate. But the classification system includes weight_and_inspection_cert and nmfc_cert. We need to decide which fits. The instructions: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So this page has "NMFC: 73260 /SUB NMFC: 09 CLASS: 70.0", which are keywords for nmfc_cert (NMFC Code, class #). So we classify as nmfc_cert.\n\nThus both page1 and page2 are part of the same document. Page1 likely also nmfc_cert. It includes NMFC 73260-9 and classification. It says "NMFC 73260-9" and "70" etc. So both pages are nmfc_cert. But we should check if page1 also has "W&R ISSUED" etc. Page1 seems to be a W&R certificate (weight & research certificate). It includes "WEIGHT & RESEARCH CERTIFICATE" heading. That is likely a weight_and_inspection_cert type. But page1 also includes NMFC number. So following rule, it becomes nmfc_cert. So both pages are nmfc_cert.\n\nWe should return two objects: page_no 1 and 2, doc_type: nmfc_cert.\n\nWe need to use the tool classify_logistics_doc_type. It expects array of documents objects. Output must be a call to the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3328, 'outputTokens': 654, 'totalTokens': 3982}, 'metrics': {'latencyMs': 2940}}
2025-09-24 14:45:48,821 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/13e56dde_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:45:48,850 - INFO - 

PB67IAPSJB1DZWMDIE1H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:45:48,850 - INFO - 

✓ Saved result: output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 14:45:49,213 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/13e56dde_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:45:52,479 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1e185fa3-316e-400f-9a3b-c333fbbe1ef9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:15:52 GMT', 'content-type': 'application/json', 'content-length': '3113', 'connection': 'keep-alive', 'x-amzn-requestid': '1e185fa3-316e-400f-9a3b-c333fbbe1ef9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. Need to classify. The content: "WEIGHTS & INSPECTION CERTIFICATE" header. Also includes terms like "Correction Date", "Reclass Reweigh" and "Actual Items", "Inspector Comments", "Pieces, Gross, Tare, Net, Length, Width, Height". So likely a "weight_and_inspection_cert" or "inspection_cert". The definition: weight_and_inspection_cert is certificate confirming weight, possibly with measurement. It says weight and inspection certificate. There is weight info, piece counts, etc. Also includes "Correction Date" and "Reclass Reweigh". It is a weight and inspection certificate. It\'s not a normal inspection certificate because it includes weight. So classify as weight_and_inspection_cert.\n\nBut need to check if it might be nmfc_cert? The nmfc_cert definition: NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual. Keywords: As described or As found or Original and inspection or Corrected class or Correction information. This certificate includes "Correction Date" and "Reclass Reweigh". It also says "Reclass S". That seems like a correction. So it might be an nmfc_cert? The instruction: strictly check if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep as weight_and_inspection_cert. So we need to check if this page has keywords for nmfc_cert: "As described", "As found", "Original and inspection", "Corrected class", "Correction information". But we see "Correction Date" and "Reclass Reweigh". The text: "Correction Date: 06/20/2025". That is a correction. The nmfc_cert keyword list includes "Correction information" but not explicitly "Correction Date". But the instruction says "strictly check if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert". The keywords: "As described", "As found", "Original and inspection", "Corrected class", "Correction information". We don\'t have those exact phrases. But the document is a "Correction" to weight and inspection certificate. It might be considered nmfc_cert? But the instruction says check if it has "keywords that are mentioned in nmfc_cert". The document has "Correction Date". That is not in the list. It has "Reclass Reweigh" maybe "Corrected class". "Reclass S" maybe "Corrected class". That is close. "Reclass" is similar to "Corrected class". Might be considered. But I\'m not entirely sure. Let\'s be conservative: weight_and_inspection_cert.\n\nThus output: page_no 1, doc_type: weight_and_inspection_cert. Use tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_5lholpCqRT-5yvWWvajPDQ', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2882, 'outputTokens': 601, 'totalTokens': 3483}, 'metrics': {'latencyMs': 2681}}
2025-09-24 14:45:52,479 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8f3e1569_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:45:52,506 - INFO - 

YL4HZPZH7D3B03FZZ5QK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:45:52,506 - INFO - 

✓ Saved result: output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 14:45:52,801 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8f3e1569_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:45:52,802 - INFO - 
📊 Processing Summary:
2025-09-24 14:45:52,803 - INFO -    Total files: 13
2025-09-24 14:45:52,803 - INFO -    Successful: 13
2025-09-24 14:45:52,803 - INFO -    Failed: 0
2025-09-24 14:45:52,803 - INFO -    Duration: 23.02 seconds
2025-09-24 14:45:52,803 - INFO -    Output directory: output
2025-09-24 14:45:52,803 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:45:52,803 - INFO -    📄 B3SIRREC9IAVZOJVDQSN.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:45:52,803 - INFO -    📄 CUF54EHGMLQ57HR93DRB.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:45:52,803 - INFO -    📄 FYQQGIW8Z9DSAPCL0S9G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:45:52,804 - INFO -    📄 IZTBXFPGXBFH3DV900G4.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:45:52,804 - INFO -    📄 KVKA9U3LUA7MXSHRCFYK.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:45:52,804 - INFO -    📄 MR6ONA8GK6HN1LCZHEX3.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:45:52,804 - INFO -    📄 PB67IAPSJB1DZWMDIE1H.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}
2025-09-24 14:45:52,804 - INFO -    📄 PEE2ZFMV7X0A0FL35G4G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:45:52,804 - INFO -    📄 RPWDVSCGR4H2ANDRNPUG.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:45:52,804 - INFO -    📄 U7BB1XSF3ASMIAE1MQ5I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:45:52,804 - INFO -    📄 UWWC9JXK5060AH7CSALY.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:45:52,804 - INFO -    📄 VRAK5HH2FS2C77OU55SV.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:45:52,804 - INFO -    📄 YL4HZPZH7D3B03FZZ5QK.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:45:52,805 - INFO - 
============================================================================================================================================
2025-09-24 14:45:52,805 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:45:52,805 - INFO - ============================================================================================================================================
2025-09-24 14:45:52,805 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:45:52,805 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:45:52,805 - INFO - B3SIRREC9IAVZOJVDQSN.pdf                           1      nmfc_cert            run1_B3SIRREC9IAVZOJVDQSN.json                    
2025-09-24 14:45:52,805 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:45:52,806 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 14:45:52,806 - INFO - 
2025-09-24 14:45:52,806 - INFO - CUF54EHGMLQ57HR93DRB.pdf                           1      weight_and_inspect... run1_CUF54EHGMLQ57HR93DRB.json                    
2025-09-24 14:45:52,806 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:45:52,806 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 14:45:52,806 - INFO - 
2025-09-24 14:45:52,806 - INFO - FYQQGIW8Z9DSAPCL0S9G.pdf                           1      nmfc_cert            run1_FYQQGIW8Z9DSAPCL0S9G.json                    
2025-09-24 14:45:52,806 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:45:52,806 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 14:45:52,806 - INFO - 
2025-09-24 14:45:52,806 - INFO - IZTBXFPGXBFH3DV900G4.pdf                           1      weight_and_inspect... run1_IZTBXFPGXBFH3DV900G4.json                    
2025-09-24 14:45:52,806 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:45:52,806 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 14:45:52,806 - INFO - 
2025-09-24 14:45:52,806 - INFO - KVKA9U3LUA7MXSHRCFYK.pdf                           1      nmfc_cert            run1_KVKA9U3LUA7MXSHRCFYK.json                    
2025-09-24 14:45:52,806 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/KVKA9U3LUA7MXSHRCFYK.pdf
2025-09-24 14:45:52,806 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_KVKA9U3LUA7MXSHRCFYK.json
2025-09-24 14:45:52,806 - INFO - 
2025-09-24 14:45:52,807 - INFO - MR6ONA8GK6HN1LCZHEX3.pdf                           1      nmfc_cert            run1_MR6ONA8GK6HN1LCZHEX3.json                    
2025-09-24 14:45:52,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:45:52,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 14:45:52,807 - INFO - 
2025-09-24 14:45:52,807 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           1      nmfc_cert            run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 14:45:52,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:45:52,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 14:45:52,807 - INFO - 
2025-09-24 14:45:52,807 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           2      nmfc_cert            run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 14:45:52,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:45:52,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 14:45:52,807 - INFO - 
2025-09-24 14:45:52,807 - INFO - PEE2ZFMV7X0A0FL35G4G.pdf                           1      nmfc_cert            run1_PEE2ZFMV7X0A0FL35G4G.json                    
2025-09-24 14:45:52,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:45:52,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 14:45:52,807 - INFO - 
2025-09-24 14:45:52,807 - INFO - RPWDVSCGR4H2ANDRNPUG.pdf                           1      weight_and_inspect... run1_RPWDVSCGR4H2ANDRNPUG.json                    
2025-09-24 14:45:52,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/RPWDVSCGR4H2ANDRNPUG.pdf
2025-09-24 14:45:52,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RPWDVSCGR4H2ANDRNPUG.json
2025-09-24 14:45:52,807 - INFO - 
2025-09-24 14:45:52,807 - INFO - RPWDVSCGR4H2ANDRNPUG.pdf                           2      weight_and_inspect... run1_RPWDVSCGR4H2ANDRNPUG.json                    
2025-09-24 14:45:52,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/RPWDVSCGR4H2ANDRNPUG.pdf
2025-09-24 14:45:52,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RPWDVSCGR4H2ANDRNPUG.json
2025-09-24 14:45:52,807 - INFO - 
2025-09-24 14:45:52,807 - INFO - U7BB1XSF3ASMIAE1MQ5I.pdf                           1      nmfc_cert            run1_U7BB1XSF3ASMIAE1MQ5I.json                    
2025-09-24 14:45:52,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:45:52,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 14:45:52,807 - INFO - 
2025-09-24 14:45:52,807 - INFO - UWWC9JXK5060AH7CSALY.pdf                           1      weight_and_inspect... run1_UWWC9JXK5060AH7CSALY.json                    
2025-09-24 14:45:52,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/UWWC9JXK5060AH7CSALY.pdf
2025-09-24 14:45:52,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_UWWC9JXK5060AH7CSALY.json
2025-09-24 14:45:52,807 - INFO - 
2025-09-24 14:45:52,807 - INFO - UWWC9JXK5060AH7CSALY.pdf                           2      weight_and_inspect... run1_UWWC9JXK5060AH7CSALY.json                    
2025-09-24 14:45:52,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/UWWC9JXK5060AH7CSALY.pdf
2025-09-24 14:45:52,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_UWWC9JXK5060AH7CSALY.json
2025-09-24 14:45:52,807 - INFO - 
2025-09-24 14:45:52,807 - INFO - VRAK5HH2FS2C77OU55SV.pdf                           1      nmfc_cert            run1_VRAK5HH2FS2C77OU55SV.json                    
2025-09-24 14:45:52,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/VRAK5HH2FS2C77OU55SV.pdf
2025-09-24 14:45:52,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_VRAK5HH2FS2C77OU55SV.json
2025-09-24 14:45:52,807 - INFO - 
2025-09-24 14:45:52,807 - INFO - YL4HZPZH7D3B03FZZ5QK.pdf                           1      weight_and_inspect... run1_YL4HZPZH7D3B03FZZ5QK.json                    
2025-09-24 14:45:52,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:45:52,808 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 14:45:52,808 - INFO - 
2025-09-24 14:45:52,808 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:45:52,808 - INFO - Total entries: 16
2025-09-24 14:45:52,808 - INFO - ============================================================================================================================================
2025-09-24 14:45:52,808 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:45:52,808 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:45:52,808 - INFO -   1. B3SIRREC9IAVZOJVDQSN.pdf            Page 1   → nmfc_cert       | run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 14:45:52,808 - INFO -   2. CUF54EHGMLQ57HR93DRB.pdf            Page 1   → weight_and_inspection_cert | run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 14:45:52,808 - INFO -   3. FYQQGIW8Z9DSAPCL0S9G.pdf            Page 1   → nmfc_cert       | run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 14:45:52,808 - INFO -   4. IZTBXFPGXBFH3DV900G4.pdf            Page 1   → weight_and_inspection_cert | run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 14:45:52,808 - INFO -   5. KVKA9U3LUA7MXSHRCFYK.pdf            Page 1   → nmfc_cert       | run1_KVKA9U3LUA7MXSHRCFYK.json
2025-09-24 14:45:52,808 - INFO -   6. MR6ONA8GK6HN1LCZHEX3.pdf            Page 1   → nmfc_cert       | run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 14:45:52,808 - INFO -   7. PB67IAPSJB1DZWMDIE1H.pdf            Page 1   → nmfc_cert       | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 14:45:52,808 - INFO -   8. PB67IAPSJB1DZWMDIE1H.pdf            Page 2   → nmfc_cert       | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 14:45:52,808 - INFO -   9. PEE2ZFMV7X0A0FL35G4G.pdf            Page 1   → nmfc_cert       | run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 14:45:52,808 - INFO -  10. RPWDVSCGR4H2ANDRNPUG.pdf            Page 1   → weight_and_inspection_cert | run1_RPWDVSCGR4H2ANDRNPUG.json
2025-09-24 14:45:52,808 - INFO -  11. RPWDVSCGR4H2ANDRNPUG.pdf            Page 2   → weight_and_inspection_cert | run1_RPWDVSCGR4H2ANDRNPUG.json
2025-09-24 14:45:52,808 - INFO -  12. U7BB1XSF3ASMIAE1MQ5I.pdf            Page 1   → nmfc_cert       | run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 14:45:52,808 - INFO -  13. UWWC9JXK5060AH7CSALY.pdf            Page 1   → weight_and_inspection_cert | run1_UWWC9JXK5060AH7CSALY.json
2025-09-24 14:45:52,808 - INFO -  14. UWWC9JXK5060AH7CSALY.pdf            Page 2   → weight_and_inspection_cert | run1_UWWC9JXK5060AH7CSALY.json
2025-09-24 14:45:52,808 - INFO -  15. VRAK5HH2FS2C77OU55SV.pdf            Page 1   → nmfc_cert       | run1_VRAK5HH2FS2C77OU55SV.json
2025-09-24 14:45:52,808 - INFO -  16. YL4HZPZH7D3B03FZZ5QK.pdf            Page 1   → weight_and_inspection_cert | run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 14:45:52,808 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:45:52,808 - INFO - 
✅ Test completed: {'total_files': 13, 'processed': 13, 'failed': 0, 'errors': [], 'duration_seconds': 23.022298, 'processed_files': [{'filename': 'B3SIRREC9IAVZOJVDQSN.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf'}, {'filename': 'CUF54EHGMLQ57HR93DRB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf'}, {'filename': 'FYQQGIW8Z9DSAPCL0S9G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf'}, {'filename': 'IZTBXFPGXBFH3DV900G4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf'}, {'filename': 'KVKA9U3LUA7MXSHRCFYK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/KVKA9U3LUA7MXSHRCFYK.pdf'}, {'filename': 'MR6ONA8GK6HN1LCZHEX3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf'}, {'filename': 'PB67IAPSJB1DZWMDIE1H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf'}, {'filename': 'PEE2ZFMV7X0A0FL35G4G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf'}, {'filename': 'RPWDVSCGR4H2ANDRNPUG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/RPWDVSCGR4H2ANDRNPUG.pdf'}, {'filename': 'U7BB1XSF3ASMIAE1MQ5I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf'}, {'filename': 'UWWC9JXK5060AH7CSALY.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/UWWC9JXK5060AH7CSALY.pdf'}, {'filename': 'VRAK5HH2FS2C77OU55SV.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/VRAK5HH2FS2C77OU55SV.pdf'}, {'filename': 'YL4HZPZH7D3B03FZZ5QK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf'}]}
