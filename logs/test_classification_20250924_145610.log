2025-09-24 14:56:10,778 - INFO - Logging initialized. Log file: logs/test_classification_20250924_145610.log
2025-09-24 14:56:10,778 - INFO - 📁 Found 1 files to process
2025-09-24 14:56:10,778 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:56:10,778 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-24 14:56:10,778 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-24 14:56:10,779 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-24 14:56:10,779 - INFO - ⬆️ [14:56:10] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:56:15,588 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/e32cf0cf_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:56:15,588 - INFO - 🔍 [14:56:15] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:56:15,589 - INFO - Initializing TextractProcessor...
2025-09-24 14:56:15,606 - INFO - Initializing BedrockProcessor...
2025-09-24 14:56:15,611 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e32cf0cf_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:56:15,612 - INFO - Processing PDF from S3...
2025-09-24 14:56:15,612 - INFO - Downloading PDF from S3 to /tmp/tmp7l73x3r3/e32cf0cf_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:56:18,083 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 14:56:18,084 - INFO - Splitting PDF into individual pages...
2025-09-24 14:56:18,085 - INFO - Splitting PDF e32cf0cf_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 14:56:18,088 - INFO - Split PDF into 1 pages
2025-09-24 14:56:18,088 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:56:18,088 - INFO - Expected pages: [1]
2025-09-24 14:56:25,666 - INFO - Page 1: Extracted 939 characters, 64 lines from e32cf0cf_A34CDFDJ66EDOZEKZWJL_b4c53122_page_001.pdf
2025-09-24 14:56:25,667 - INFO - Successfully processed page 1
2025-09-24 14:56:25,667 - INFO - Combined 1 pages into final text
2025-09-24 14:56:25,667 - INFO - Text validation for e32cf0cf_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 14:56:25,668 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:56:25,668 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:56:28,930 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'edbeac73-39e0-45c1-a2d6-acfcf5e24121', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:26:28 GMT', 'content-type': 'application/json', 'content-length': '1187', 'connection': 'keep-alive', 'x-amzn-requestid': 'edbeac73-39e0-45c1-a2d6-acfcf5e24121'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We identify document type. The content: "Inspection Certificate" is header. Also "Weight(Lbs)" and dimensions. This seems like a Weight and Inspection Certificate. The doc is an inspection certificate for a shipment, includes weight, dimensions, density, etc. The question: weight_and_inspection_cert vs inspection_cert. The guidelines: weight_and_inspection_cert if weight mentioned, else inspection_cert. Also check nmfc_cert if it has keywords of nmfc_cert. Not present. So likely weight_and_inspection_cert. But also the doc header says "Inspection Certificate". But weight present. So weight_and_inspection_cert. There\'s no nmfc related. So doc_type: weight_and_inspection_cert.\n\nThus output using tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_59C851hQTIuBTmc7T5BXZQ', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2826, 'outputTokens': 180, 'totalTokens': 3006}, 'metrics': {'latencyMs': 920}}
2025-09-24 14:56:28,930 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e32cf0cf_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:56:28,943 - INFO - 

A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:56:28,943 - INFO - 

✓ Saved result: output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:56:30,160 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e32cf0cf_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:56:30,161 - INFO - 
📊 Processing Summary:
2025-09-24 14:56:30,161 - INFO -    Total files: 1
2025-09-24 14:56:30,161 - INFO -    Successful: 1
2025-09-24 14:56:30,161 - INFO -    Failed: 0
2025-09-24 14:56:30,162 - INFO -    Duration: 19.38 seconds
2025-09-24 14:56:30,162 - INFO -    Output directory: output
2025-09-24 14:56:30,162 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:56:30,162 - INFO -    📄 A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:56:30,162 - INFO - 
============================================================================================================================================
2025-09-24 14:56:30,162 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:56:30,163 - INFO - ============================================================================================================================================
2025-09-24 14:56:30,163 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:56:30,163 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:56:30,163 - INFO - A34CDFDJ66EDOZEKZWJL.pdf                           1      weight_and_inspect... run1_A34CDFDJ66EDOZEKZWJL.json                    
2025-09-24 14:56:30,163 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:56:30,163 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:56:30,163 - INFO - 
2025-09-24 14:56:30,163 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:56:30,163 - INFO - Total entries: 1
2025-09-24 14:56:30,163 - INFO - ============================================================================================================================================
2025-09-24 14:56:30,164 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:56:30,164 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:56:30,164 - INFO -   1. A34CDFDJ66EDOZEKZWJL.pdf            Page 1   → weight_and_inspection_cert | run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:56:30,164 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:56:30,164 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 19.382515, 'processed_files': [{'filename': 'A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf'}]}
