2025-09-24 14:26:55,232 - INFO - Logging initialized. Log file: logs/test_classification_20250924_142655.log
2025-09-24 14:26:55,233 - INFO - 📁 Found 10 files to process
2025-09-24 14:26:55,233 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:26:55,233 - INFO - 🚀 Processing 10 files in FORCED PARALLEL MODE...
2025-09-24 14:26:55,233 - INFO - 🚀 Creating 10 parallel tasks...
2025-09-24 14:26:55,233 - INFO - 🚀 All 10 tasks created - executing in parallel...
2025-09-24 14:26:55,233 - INFO - ⬆️ [14:26:55] Uploading: AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:26:57,105 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/AF0EUFN20TKQSN94KZCH.PDF -> s3://document-extraction-logistically/temp/4c868e8b_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:26:57,106 - INFO - 🔍 [14:26:57] Starting classification: AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:26:57,107 - INFO - ⬆️ [14:26:57] Uploading: FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:26:57,108 - INFO - Initializing TextractProcessor...
2025-09-24 14:26:57,136 - INFO - Initializing BedrockProcessor...
2025-09-24 14:26:57,144 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4c868e8b_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:26:57,145 - INFO - Processing PDF from S3...
2025-09-24 14:26:57,145 - INFO - Downloading PDF from S3 to /tmp/tmpzbydseww/4c868e8b_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:26:57,806 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/FFJ2USKKEFCH3U0FO1S5.PDF -> s3://document-extraction-logistically/temp/d44ca62e_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:26:57,806 - INFO - 🔍 [14:26:57] Starting classification: FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:26:57,807 - INFO - Initializing TextractProcessor...
2025-09-24 14:26:57,808 - INFO - ⬆️ [14:26:57] Uploading: HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:26:57,823 - INFO - Initializing BedrockProcessor...
2025-09-24 14:26:57,834 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d44ca62e_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:26:57,835 - INFO - Processing PDF from S3...
2025-09-24 14:26:57,835 - INFO - Downloading PDF from S3 to /tmp/tmp1r2kjmew/d44ca62e_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:26:58,628 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:26:58,628 - INFO - Splitting PDF into individual pages...
2025-09-24 14:26:58,631 - INFO - Splitting PDF 4c868e8b_AF0EUFN20TKQSN94KZCH into 1 pages
2025-09-24 14:26:58,643 - INFO - Split PDF into 1 pages
2025-09-24 14:26:58,643 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:26:58,643 - INFO - Expected pages: [1]
2025-09-24 14:26:59,318 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:26:59,318 - INFO - Splitting PDF into individual pages...
2025-09-24 14:26:59,320 - INFO - Splitting PDF d44ca62e_FFJ2USKKEFCH3U0FO1S5 into 1 pages
2025-09-24 14:26:59,325 - INFO - Split PDF into 1 pages
2025-09-24 14:26:59,325 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:26:59,325 - INFO - Expected pages: [1]
2025-09-24 14:26:59,334 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HBV9LT6SK8HIOJ5DI4P2.pdf -> s3://document-extraction-logistically/temp/2f99760e_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:26:59,334 - INFO - 🔍 [14:26:59] Starting classification: HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:26:59,335 - INFO - ⬆️ [14:26:59] Uploading: HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:26:59,336 - INFO - Initializing TextractProcessor...
2025-09-24 14:26:59,354 - INFO - Initializing BedrockProcessor...
2025-09-24 14:26:59,364 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2f99760e_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:26:59,365 - INFO - Processing PDF from S3...
2025-09-24 14:26:59,366 - INFO - Downloading PDF from S3 to /tmp/tmpgjnu54hs/2f99760e_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:27:00,268 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HL4SSNKOC8T141SEURCG.pdf -> s3://document-extraction-logistically/temp/1a492461_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:27:00,268 - INFO - 🔍 [14:27:00] Starting classification: HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:27:00,269 - INFO - ⬆️ [14:27:00] Uploading: Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:27:00,270 - INFO - Initializing TextractProcessor...
2025-09-24 14:27:00,296 - INFO - Initializing BedrockProcessor...
2025-09-24 14:27:00,302 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1a492461_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:27:00,302 - INFO - Processing PDF from S3...
2025-09-24 14:27:00,302 - INFO - Downloading PDF from S3 to /tmp/tmpruu7qx2w/1a492461_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:27:00,871 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Q9MLAQNGOP70MYYKOYFJ.pdf -> s3://document-extraction-logistically/temp/a0185c74_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:27:00,871 - INFO - 🔍 [14:27:00] Starting classification: Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:27:00,872 - INFO - ⬆️ [14:27:00] Uploading: RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:27:00,873 - INFO - Initializing TextractProcessor...
2025-09-24 14:27:00,882 - INFO - Initializing BedrockProcessor...
2025-09-24 14:27:00,885 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a0185c74_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:27:00,885 - INFO - Processing PDF from S3...
2025-09-24 14:27:00,885 - INFO - Downloading PDF from S3 to /tmp/tmpz19mwh1c/a0185c74_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:27:01,263 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:27:01,263 - INFO - Splitting PDF into individual pages...
2025-09-24 14:27:01,264 - INFO - Splitting PDF 2f99760e_HBV9LT6SK8HIOJ5DI4P2 into 1 pages
2025-09-24 14:27:01,267 - INFO - Split PDF into 1 pages
2025-09-24 14:27:01,267 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:27:01,267 - INFO - Expected pages: [1]
2025-09-24 14:27:01,767 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/RBKKSRU2KS6IJRASO4SB.pdf -> s3://document-extraction-logistically/temp/efbac575_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:27:01,768 - INFO - 🔍 [14:27:01] Starting classification: RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:27:01,769 - INFO - ⬆️ [14:27:01] Uploading: SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:27:01,769 - INFO - Initializing TextractProcessor...
2025-09-24 14:27:01,788 - INFO - Initializing BedrockProcessor...
2025-09-24 14:27:01,791 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/efbac575_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:27:01,791 - INFO - Processing PDF from S3...
2025-09-24 14:27:01,791 - INFO - Downloading PDF from S3 to /tmp/tmpzcyz60t8/efbac575_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:27:02,148 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:27:02,148 - INFO - Splitting PDF into individual pages...
2025-09-24 14:27:02,149 - INFO - Splitting PDF a0185c74_Q9MLAQNGOP70MYYKOYFJ into 1 pages
2025-09-24 14:27:02,150 - INFO - Split PDF into 1 pages
2025-09-24 14:27:02,150 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:27:02,150 - INFO - Expected pages: [1]
2025-09-24 14:27:02,308 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 14:27:02,308 - INFO - Splitting PDF into individual pages...
2025-09-24 14:27:02,309 - INFO - Splitting PDF 1a492461_HL4SSNKOC8T141SEURCG into 1 pages
2025-09-24 14:27:02,311 - INFO - Split PDF into 1 pages
2025-09-24 14:27:02,311 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:27:02,311 - INFO - Expected pages: [1]
2025-09-24 14:27:02,363 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/SV5IN68S36F6PA0633RU.pdf -> s3://document-extraction-logistically/temp/e9ce6f9d_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:27:02,363 - INFO - 🔍 [14:27:02] Starting classification: SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:27:02,364 - INFO - ⬆️ [14:27:02] Uploading: WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:27:02,365 - INFO - Initializing TextractProcessor...
2025-09-24 14:27:02,413 - INFO - Initializing BedrockProcessor...
2025-09-24 14:27:02,416 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e9ce6f9d_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:27:02,416 - INFO - Processing PDF from S3...
2025-09-24 14:27:02,416 - INFO - Downloading PDF from S3 to /tmp/tmp6oeeu17x/e9ce6f9d_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:27:02,927 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/WDPSPQTC87MJOF3B6952.pdf -> s3://document-extraction-logistically/temp/dc528001_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:27:02,928 - INFO - 🔍 [14:27:02] Starting classification: WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:27:02,928 - INFO - ⬆️ [14:27:02] Uploading: XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:27:02,930 - INFO - Initializing TextractProcessor...
2025-09-24 14:27:02,943 - INFO - Initializing BedrockProcessor...
2025-09-24 14:27:02,948 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dc528001_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:27:02,949 - INFO - Processing PDF from S3...
2025-09-24 14:27:02,949 - INFO - Downloading PDF from S3 to /tmp/tmp8r42azwz/dc528001_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:27:03,509 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/XVTBS5BQOTRUDPEUC4CC.pdf -> s3://document-extraction-logistically/temp/c0dfed18_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:27:03,509 - INFO - 🔍 [14:27:03] Starting classification: XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:27:03,509 - INFO - ⬆️ [14:27:03] Uploading: Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:27:03,509 - INFO - Initializing TextractProcessor...
2025-09-24 14:27:03,519 - INFO - Initializing BedrockProcessor...
2025-09-24 14:27:03,521 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c0dfed18_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:27:03,521 - INFO - Processing PDF from S3...
2025-09-24 14:27:03,521 - INFO - Downloading PDF from S3 to /tmp/tmpjn005a3f/c0dfed18_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:27:03,617 - INFO - Page 1: Extracted 717 characters, 43 lines from 4c868e8b_AF0EUFN20TKQSN94KZCH_9f658e06_page_001.pdf
2025-09-24 14:27:03,617 - INFO - Successfully processed page 1
2025-09-24 14:27:03,617 - INFO - Combined 1 pages into final text
2025-09-24 14:27:03,618 - INFO - Text validation for 4c868e8b_AF0EUFN20TKQSN94KZCH: 734 characters, 1 pages
2025-09-24 14:27:03,618 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:27:03,618 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:27:03,666 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:27:03,667 - INFO - Splitting PDF into individual pages...
2025-09-24 14:27:03,670 - INFO - Splitting PDF e9ce6f9d_SV5IN68S36F6PA0633RU into 2 pages
2025-09-24 14:27:03,675 - INFO - Split PDF into 2 pages
2025-09-24 14:27:03,675 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:27:03,675 - INFO - Expected pages: [1, 2]
2025-09-24 14:27:04,099 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Y6MOA7KJV1OU7NHZ4P4U.pdf -> s3://document-extraction-logistically/temp/d083802f_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:27:04,099 - INFO - 🔍 [14:27:04] Starting classification: Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:27:04,100 - INFO - Initializing TextractProcessor...
2025-09-24 14:27:04,106 - INFO - Initializing BedrockProcessor...
2025-09-24 14:27:04,109 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 14:27:04,109 - INFO - Splitting PDF into individual pages...
2025-09-24 14:27:04,110 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d083802f_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:27:04,110 - INFO - Processing PDF from S3...
2025-09-24 14:27:04,110 - INFO - Downloading PDF from S3 to /tmp/tmp_v2ui6er/d083802f_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:27:04,110 - INFO - Splitting PDF efbac575_RBKKSRU2KS6IJRASO4SB into 1 pages
2025-09-24 14:27:04,115 - INFO - Split PDF into 1 pages
2025-09-24 14:27:04,115 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:27:04,115 - INFO - Expected pages: [1]
2025-09-24 14:27:04,154 - INFO - Page 1: Extracted 654 characters, 40 lines from d44ca62e_FFJ2USKKEFCH3U0FO1S5_c9109f71_page_001.pdf
2025-09-24 14:27:04,155 - INFO - Successfully processed page 1
2025-09-24 14:27:04,155 - INFO - Combined 1 pages into final text
2025-09-24 14:27:04,155 - INFO - Text validation for d44ca62e_FFJ2USKKEFCH3U0FO1S5: 671 characters, 1 pages
2025-09-24 14:27:04,155 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:27:04,155 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:27:04,267 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:27:04,267 - INFO - Splitting PDF into individual pages...
2025-09-24 14:27:04,269 - INFO - Splitting PDF dc528001_WDPSPQTC87MJOF3B6952 into 2 pages
2025-09-24 14:27:04,271 - INFO - Split PDF into 2 pages
2025-09-24 14:27:04,272 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:27:04,272 - INFO - Expected pages: [1, 2]
2025-09-24 14:27:04,799 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:27:04,799 - INFO - Splitting PDF into individual pages...
2025-09-24 14:27:04,800 - INFO - Splitting PDF c0dfed18_XVTBS5BQOTRUDPEUC4CC into 2 pages
2025-09-24 14:27:04,801 - INFO - Split PDF into 2 pages
2025-09-24 14:27:04,802 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:27:04,802 - INFO - Expected pages: [1, 2]
2025-09-24 14:27:05,446 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:27:05,446 - INFO - Splitting PDF into individual pages...
2025-09-24 14:27:05,448 - INFO - Splitting PDF d083802f_Y6MOA7KJV1OU7NHZ4P4U into 2 pages
2025-09-24 14:27:05,453 - INFO - Split PDF into 2 pages
2025-09-24 14:27:05,453 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:27:05,453 - INFO - Expected pages: [1, 2]
2025-09-24 14:27:06,605 - INFO - Page 1: Extracted 1170 characters, 73 lines from a0185c74_Q9MLAQNGOP70MYYKOYFJ_d4cebf03_page_001.pdf
2025-09-24 14:27:06,607 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '46bf296e-acb8-4ce2-8238-1647ed58ae10', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:57:06 GMT', 'content-type': 'application/json', 'content-length': '908', 'connection': 'keep-alive', 'x-amzn-requestid': '46bf296e-acb8-4ce2-8238-1647ed58ae10'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. The content looks like an Order Confirmation (sales order confirmation). The page header says "ORDER CONFIRMATION". That is likely a sales order confirmation. The enumerated types: so_confirmation — Sales Order Confirmation. Yes.\n\nCheck that it does not match other categories like po: Purchase Order. The header is "ORDER CONFIRMATION" from the warehouse. It contains order numbers, shipping details. It\'s not a purchase order, it\'s a confirmation of an order. So doc_type should be so_confirmation.\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2725, 'outputTokens': 135, 'totalTokens': 2860}, 'metrics': {'latencyMs': 939}}
2025-09-24 14:27:06,607 - INFO - Successfully processed page 1
2025-09-24 14:27:06,607 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d44ca62e_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:27:06,607 - INFO - Combined 1 pages into final text
2025-09-24 14:27:06,608 - INFO - Text validation for a0185c74_Q9MLAQNGOP70MYYKOYFJ: 1187 characters, 1 pages
2025-09-24 14:27:06,609 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:27:06,609 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:27:06,619 - INFO - 

FFJ2USKKEFCH3U0FO1S5.PDF

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:27:06,619 - INFO - 

✓ Saved result: output/run1_FFJ2USKKEFCH3U0FO1S5.json
2025-09-24 14:27:06,680 - INFO - Page 1: Extracted 1438 characters, 62 lines from 2f99760e_HBV9LT6SK8HIOJ5DI4P2_e17bbad3_page_001.pdf
2025-09-24 14:27:06,680 - INFO - Successfully processed page 1
2025-09-24 14:27:06,680 - INFO - Combined 1 pages into final text
2025-09-24 14:27:06,680 - INFO - Text validation for 2f99760e_HBV9LT6SK8HIOJ5DI4P2: 1455 characters, 1 pages
2025-09-24 14:27:06,680 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:27:06,680 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:27:06,702 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1e203484-6ac7-41e2-a152-de85c873159f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:57:06 GMT', 'content-type': 'application/json', 'content-length': '1141', 'connection': 'keep-alive', 'x-amzn-requestid': '1e203484-6ac7-41e2-a152-de85c873159f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It looks like an "Order Confirmation" document. That is not explicitly in list. Possibly \'order confirmation\' could be a purchase order? But list includes PO, Sales Order Confirmation, etc. This is an order confirmation from GROSkoPF Warehouse. It includes order number, shipping details. It might be a Sales Order Confirmation? But "order confirmation" is similar to Sales Order Confirmation (SO confirmation). However the keywords: "ORDER CONFIRMATION" present. For so_confirmation: keywords include "Sales Order Confirmation", "SO#", "Order Confirmation". So yes, this page is order confirmation. So doc_type should be "so_confirmation". There\'s only one page. No continuation markers. So return object: [{page_no:1, doc_type:"so_confirmation"}].'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2747, 'outputTokens': 186, 'totalTokens': 2933}, 'metrics': {'latencyMs': 1052}}
2025-09-24 14:27:06,703 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4c868e8b_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:27:06,928 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d44ca62e_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:27:06,941 - INFO - 

AF0EUFN20TKQSN94KZCH.PDF

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:27:06,941 - INFO - 

✓ Saved result: output/run1_AF0EUFN20TKQSN94KZCH.json
2025-09-24 14:27:07,079 - INFO - Page 2: Extracted 456 characters, 26 lines from e9ce6f9d_SV5IN68S36F6PA0633RU_12ca9c26_page_002.pdf
2025-09-24 14:27:07,080 - INFO - Successfully processed page 2
2025-09-24 14:27:07,266 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4c868e8b_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:27:07,932 - INFO - Page 1: Extracted 1305 characters, 67 lines from e9ce6f9d_SV5IN68S36F6PA0633RU_12ca9c26_page_001.pdf
2025-09-24 14:27:07,932 - INFO - Successfully processed page 1
2025-09-24 14:27:07,932 - INFO - Combined 2 pages into final text
2025-09-24 14:27:07,933 - INFO - Text validation for e9ce6f9d_SV5IN68S36F6PA0633RU: 1797 characters, 2 pages
2025-09-24 14:27:07,933 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:27:07,934 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:27:08,350 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '75b38df3-08cf-4093-b9ca-26e4a096883d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:57:08 GMT', 'content-type': 'application/json', 'content-length': '567', 'connection': 'keep-alive', 'x-amzn-requestid': '75b38df3-08cf-4093-b9ca-26e4a096883d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1. It\'s an Order Confirmation, likely a sales order confirmation (so_confirmation). The doc type: "Sales Order Confirmation". So doc_type: "so_confirmation". Provide array with page_no 1 and doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2905, 'outputTokens': 71, 'totalTokens': 2976}, 'metrics': {'latencyMs': 491}}
2025-09-24 14:27:08,350 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2f99760e_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:27:08,366 - INFO - 

HBV9LT6SK8HIOJ5DI4P2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:27:08,366 - INFO - 

✓ Saved result: output/run1_HBV9LT6SK8HIOJ5DI4P2.json
2025-09-24 14:27:08,417 - INFO - Page 2: Extracted 457 characters, 26 lines from c0dfed18_XVTBS5BQOTRUDPEUC4CC_72b74de7_page_002.pdf
2025-09-24 14:27:08,417 - INFO - Successfully processed page 2
2025-09-24 14:27:08,564 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3ff4c0df-9bca-4a3f-a09d-32b6b9dbcd62', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:57:08 GMT', 'content-type': 'application/json', 'content-length': '832', 'connection': 'keep-alive', 'x-amzn-requestid': '3ff4c0df-9bca-4a3f-a09d-32b6b9dbcd62'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It looks like a sales acknowledgment, maybe a sales order acknowledgment. The prompt mentions "SALES ACKNOWLEDGMENT" and "Sales Acknowledgment". This likely corresponds to "so_confirmation" (Sales Order Confirmation), based on keywords "Sales Order Confirmation". The doc type is so_confirmation.\n\nNeed to output using the tool classify_logistics_doc_type with objects array of pages. For page 1, we output {page_no:1, doc_type:"so_confirmation"}.\n\nWe must call the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2928, 'outputTokens': 127, 'totalTokens': 3055}, 'metrics': {'latencyMs': 720}}
2025-09-24 14:27:08,564 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a0185c74_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:27:08,665 - INFO - Page 1: Extracted 1364 characters, 74 lines from dc528001_WDPSPQTC87MJOF3B6952_3b0697d0_page_001.pdf
2025-09-24 14:27:08,665 - INFO - Successfully processed page 1
2025-09-24 14:27:08,677 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2f99760e_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:27:08,686 - INFO - Page 2: Extracted 614 characters, 29 lines from dc528001_WDPSPQTC87MJOF3B6952_3b0697d0_page_002.pdf
2025-09-24 14:27:08,689 - INFO - Successfully processed page 2
2025-09-24 14:27:08,694 - INFO - Combined 2 pages into final text
2025-09-24 14:27:08,696 - INFO - Text validation for dc528001_WDPSPQTC87MJOF3B6952: 2014 characters, 2 pages
2025-09-24 14:27:08,706 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:27:08,707 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:27:08,714 - INFO - 

Q9MLAQNGOP70MYYKOYFJ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:27:08,714 - INFO - 

✓ Saved result: output/run1_Q9MLAQNGOP70MYYKOYFJ.json
2025-09-24 14:27:09,026 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a0185c74_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:27:09,231 - INFO - Page 2: Extracted 457 characters, 26 lines from d083802f_Y6MOA7KJV1OU7NHZ4P4U_717c106a_page_002.pdf
2025-09-24 14:27:09,231 - INFO - Successfully processed page 2
2025-09-24 14:27:09,444 - INFO - Page 1: Extracted 1443 characters, 97 lines from 1a492461_HL4SSNKOC8T141SEURCG_9813ca3e_page_001.pdf
2025-09-24 14:27:09,445 - INFO - Successfully processed page 1
2025-09-24 14:27:09,445 - INFO - Combined 1 pages into final text
2025-09-24 14:27:09,445 - INFO - Text validation for 1a492461_HL4SSNKOC8T141SEURCG: 1460 characters, 1 pages
2025-09-24 14:27:09,445 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:27:09,445 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:27:09,726 - INFO - Page 1: Extracted 1308 characters, 67 lines from c0dfed18_XVTBS5BQOTRUDPEUC4CC_72b74de7_page_001.pdf
2025-09-24 14:27:09,726 - INFO - Successfully processed page 1
2025-09-24 14:27:09,726 - INFO - Combined 2 pages into final text
2025-09-24 14:27:09,726 - INFO - Text validation for c0dfed18_XVTBS5BQOTRUDPEUC4CC: 1801 characters, 2 pages
2025-09-24 14:27:09,727 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:27:09,727 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:27:09,858 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '67afdc11-8ba8-4183-9994-83441df8f4c4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:57:09 GMT', 'content-type': 'application/json', 'content-length': '689', 'connection': 'keep-alive', 'x-amzn-requestid': '67afdc11-8ba8-4183-9994-83441df8f4c4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document is a Plant Order, basically a purchase order from a company. It\'s a PO. So doc_type should be "po". Page 1 has PO details, page 2 is continuation. So both pages are "po". We output array of objects with page_no and doc_type.\n\nReturn via tool: classify_logistics_doc_type with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3124, 'outputTokens': 106, 'totalTokens': 3230}, 'metrics': {'latencyMs': 675}}
2025-09-24 14:27:09,858 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e9ce6f9d_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:27:09,879 - INFO - 

SV5IN68S36F6PA0633RU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:27:09,879 - INFO - 

✓ Saved result: output/run1_SV5IN68S36F6PA0633RU.json
2025-09-24 14:27:10,075 - INFO - Page 1: Extracted 1479 characters, 67 lines from efbac575_RBKKSRU2KS6IJRASO4SB_18e68851_page_001.pdf
2025-09-24 14:27:10,075 - INFO - Successfully processed page 1
2025-09-24 14:27:10,075 - INFO - Combined 1 pages into final text
2025-09-24 14:27:10,076 - INFO - Text validation for efbac575_RBKKSRU2KS6IJRASO4SB: 1496 characters, 1 pages
2025-09-24 14:27:10,076 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:27:10,076 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:27:10,218 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e9ce6f9d_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:27:10,974 - INFO - Page 1: Extracted 1306 characters, 69 lines from d083802f_Y6MOA7KJV1OU7NHZ4P4U_717c106a_page_001.pdf
2025-09-24 14:27:10,975 - INFO - Successfully processed page 1
2025-09-24 14:27:10,975 - INFO - Combined 2 pages into final text
2025-09-24 14:27:10,976 - INFO - Text validation for d083802f_Y6MOA7KJV1OU7NHZ4P4U: 1799 characters, 2 pages
2025-09-24 14:27:10,977 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:27:10,977 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:27:10,998 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '782bcc70-71a9-472f-9446-9463d9778231', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:57:10 GMT', 'content-type': 'application/json', 'content-length': '1024', 'connection': 'keep-alive', 'x-amzn-requestid': '782bcc70-71a9-472f-9446-9463d9778231'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages of a document. We need to classify each page\'s doc_type. The content is a "Plant Order" from Calumet Refining, with PO number, order number, shipping terms, product description, etc. This looks like a Purchase Order (PO). There are also product details and shipping notes - seems like a PO or maybe a vendor order form. The page starts with "Plant Order" heading. It\'s not an invoice: there\'s no invoice number, amount due. It\'s a purchase order. So doc_type = po.\n\nWe need to return an array of objects: each with page_no and doc_type. There are 2 pages. Both pages should have same doc_type "po". Ensure ascending order.\n\nReturn via function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3208, 'outputTokens': 180, 'totalTokens': 3388}, 'metrics': {'latencyMs': 992}}
2025-09-24 14:27:10,999 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dc528001_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:27:11,020 - INFO - 

WDPSPQTC87MJOF3B6952.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:27:11,020 - INFO - 

✓ Saved result: output/run1_WDPSPQTC87MJOF3B6952.json
2025-09-24 14:27:11,216 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8ac4aa45-1077-4f9c-95c6-25bef7cc527b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:57:11 GMT', 'content-type': 'application/json', 'content-length': '641', 'connection': 'keep-alive', 'x-amzn-requestid': '8ac4aa45-1077-4f9c-95c6-25bef7cc527b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. It\'s a "Sales Order" page. According to enum, sales order confirmation is "so_confirmation". So doc_type should be so_confirmation.\n\nWe must use tool classify_logistics_doc_type with documents array with one entry: page_no 1, doc_type "so_confirmation".\n\nReturn via the tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3008, 'outputTokens': 87, 'totalTokens': 3095}, 'metrics': {'latencyMs': 583}}
2025-09-24 14:27:11,216 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1a492461_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:27:11,318 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dc528001_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:27:11,337 - INFO - 

HL4SSNKOC8T141SEURCG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:27:11,337 - INFO - 

✓ Saved result: output/run1_HL4SSNKOC8T141SEURCG.json
2025-09-24 14:27:11,669 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1a492461_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:27:11,989 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '337a4365-b600-401b-ae15-a420737f5ccb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:57:11 GMT', 'content-type': 'application/json', 'content-length': '1050', 'connection': 'keep-alive', 'x-amzn-requestid': '337a4365-b600-401b-ae15-a420737f5ccb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a PDF with two pages. The content appears to be a Plant Order (a purchase order). Let\'s analyze keywords: "PO Number", "Order Number", "Ship To", "Ship From", "Bill To", "Product Description", "Order Qty", "Gross Weight", "Net Weight". It\'s a Purchase Order. Should classify as "po". Both pages are part of same document. So page 1 is po, page 2 is continuation. The tool expects array of documents with page_no and doc_type. Must output single JSON with array of objects? The function signature: classify_logistics_doc_type({ documents: [ {page_no: number, doc_type: ...} ] }) -> any. So we call tool with documents array, each page. Both pages same type. Let\'s output.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3110, 'outputTokens': 190, 'totalTokens': 3300}, 'metrics': {'latencyMs': 1024}}
2025-09-24 14:27:11,989 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c0dfed18_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:27:12,018 - INFO - 

XVTBS5BQOTRUDPEUC4CC.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:27:12,018 - INFO - 

✓ Saved result: output/run1_XVTBS5BQOTRUDPEUC4CC.json
2025-09-24 14:27:12,111 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f76c3771-a761-4e94-b008-3fd98f1e4d68', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:57:11 GMT', 'content-type': 'application/json', 'content-length': '725', 'connection': 'keep-alive', 'x-amzn-requestid': 'f76c3771-a761-4e94-b008-3fd98f1e4d68'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided: page1. It\'s an Order Acknowledgment from Interlake Mecalux. Not a standard doc type. Keywords: "ORDER ACKNOWLEDGMENT". Not listed. Could be part of a purchase order? But it\'s an acknowledgment of a sales order. Doesn\'t match any of our categories. So classify as "other". No continuation. So output array with one object: page_no 1, doc_type "other".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2919, 'outputTokens': 114, 'totalTokens': 3033}, 'metrics': {'latencyMs': 724}}
2025-09-24 14:27:12,111 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/efbac575_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:27:12,312 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c0dfed18_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:27:12,330 - INFO - 

RBKKSRU2KS6IJRASO4SB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 14:27:12,330 - INFO - 

✓ Saved result: output/run1_RBKKSRU2KS6IJRASO4SB.json
2025-09-24 14:27:12,623 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/efbac575_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:27:13,239 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b7b43af1-2749-4a88-a059-e5c5939afdd5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:57:13 GMT', 'content-type': 'application/json', 'content-length': '1159', 'connection': 'keep-alive', 'x-amzn-requestid': 'b7b43af1-2749-4a88-a059-e5c5939afdd5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages, a plant order document. This appears to be a purchase order from Calumet Refining, LLC. It includes PO number. The document type likely "po" (Purchase Order). There\'s no other type. It\'s a purchase order. The pages are part of same document; page 2 is continuation. The instruction: "If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page." So page 1 doc_type is "po". Page 2 is continuation, so also "po". Provide output via function call. Each page one object. Output pages in ascending order. Use tool classify_logistics_doc_type. Provide array of objects: each object must have page_no and doc_type. Provide for page1 and page2.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3123, 'outputTokens': 211, 'totalTokens': 3334}, 'metrics': {'latencyMs': 1044}}
2025-09-24 14:27:13,239 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d083802f_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:27:13,267 - INFO - 

Y6MOA7KJV1OU7NHZ4P4U.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:27:13,267 - INFO - 

✓ Saved result: output/run1_Y6MOA7KJV1OU7NHZ4P4U.json
2025-09-24 14:27:13,587 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d083802f_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:27:13,589 - INFO - 
📊 Processing Summary:
2025-09-24 14:27:13,589 - INFO -    Total files: 10
2025-09-24 14:27:13,589 - INFO -    Successful: 10
2025-09-24 14:27:13,589 - INFO -    Failed: 0
2025-09-24 14:27:13,589 - INFO -    Duration: 18.36 seconds
2025-09-24 14:27:13,589 - INFO -    Output directory: output
2025-09-24 14:27:13,589 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:27:13,589 - INFO -    📄 AF0EUFN20TKQSN94KZCH.PDF: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-24 14:27:13,589 - INFO -    📄 FFJ2USKKEFCH3U0FO1S5.PDF: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-24 14:27:13,589 - INFO -    📄 HBV9LT6SK8HIOJ5DI4P2.pdf: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-24 14:27:13,589 - INFO -    📄 HL4SSNKOC8T141SEURCG.pdf: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-24 14:27:13,590 - INFO -    📄 Q9MLAQNGOP70MYYKOYFJ.pdf: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-24 14:27:13,590 - INFO -    📄 RBKKSRU2KS6IJRASO4SB.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 14:27:13,590 - INFO -    📄 SV5IN68S36F6PA0633RU.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-24 14:27:13,590 - INFO -    📄 WDPSPQTC87MJOF3B6952.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-24 14:27:13,590 - INFO -    📄 XVTBS5BQOTRUDPEUC4CC.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-24 14:27:13,590 - INFO -    📄 Y6MOA7KJV1OU7NHZ4P4U.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-24 14:27:13,591 - INFO - 
============================================================================================================================================
2025-09-24 14:27:13,591 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:27:13,591 - INFO - ============================================================================================================================================
2025-09-24 14:27:13,591 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:27:13,591 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:27:13,591 - INFO - AF0EUFN20TKQSN94KZCH.PDF                           1      so_confirmation      run1_AF0EUFN20TKQSN94KZCH.json                    
2025-09-24 14:27:13,591 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:27:13,591 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AF0EUFN20TKQSN94KZCH.json
2025-09-24 14:27:13,591 - INFO - 
2025-09-24 14:27:13,591 - INFO - FFJ2USKKEFCH3U0FO1S5.PDF                           1      so_confirmation      run1_FFJ2USKKEFCH3U0FO1S5.json                    
2025-09-24 14:27:13,591 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:27:13,591 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FFJ2USKKEFCH3U0FO1S5.json
2025-09-24 14:27:13,591 - INFO - 
2025-09-24 14:27:13,591 - INFO - HBV9LT6SK8HIOJ5DI4P2.pdf                           1      so_confirmation      run1_HBV9LT6SK8HIOJ5DI4P2.json                    
2025-09-24 14:27:13,591 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:27:13,592 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HBV9LT6SK8HIOJ5DI4P2.json
2025-09-24 14:27:13,592 - INFO - 
2025-09-24 14:27:13,592 - INFO - HL4SSNKOC8T141SEURCG.pdf                           1      so_confirmation      run1_HL4SSNKOC8T141SEURCG.json                    
2025-09-24 14:27:13,592 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:27:13,592 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HL4SSNKOC8T141SEURCG.json
2025-09-24 14:27:13,592 - INFO - 
2025-09-24 14:27:13,592 - INFO - Q9MLAQNGOP70MYYKOYFJ.pdf                           1      so_confirmation      run1_Q9MLAQNGOP70MYYKOYFJ.json                    
2025-09-24 14:27:13,592 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:27:13,592 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Q9MLAQNGOP70MYYKOYFJ.json
2025-09-24 14:27:13,592 - INFO - 
2025-09-24 14:27:13,592 - INFO - RBKKSRU2KS6IJRASO4SB.pdf                           1      other                run1_RBKKSRU2KS6IJRASO4SB.json                    
2025-09-24 14:27:13,592 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:27:13,592 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RBKKSRU2KS6IJRASO4SB.json
2025-09-24 14:27:13,592 - INFO - 
2025-09-24 14:27:13,592 - INFO - SV5IN68S36F6PA0633RU.pdf                           1      po                   run1_SV5IN68S36F6PA0633RU.json                    
2025-09-24 14:27:13,592 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:27:13,592 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SV5IN68S36F6PA0633RU.json
2025-09-24 14:27:13,592 - INFO - 
2025-09-24 14:27:13,592 - INFO - SV5IN68S36F6PA0633RU.pdf                           2      po                   run1_SV5IN68S36F6PA0633RU.json                    
2025-09-24 14:27:13,592 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:27:13,592 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SV5IN68S36F6PA0633RU.json
2025-09-24 14:27:13,593 - INFO - 
2025-09-24 14:27:13,593 - INFO - WDPSPQTC87MJOF3B6952.pdf                           1      po                   run1_WDPSPQTC87MJOF3B6952.json                    
2025-09-24 14:27:13,593 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:27:13,593 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WDPSPQTC87MJOF3B6952.json
2025-09-24 14:27:13,593 - INFO - 
2025-09-24 14:27:13,593 - INFO - WDPSPQTC87MJOF3B6952.pdf                           2      po                   run1_WDPSPQTC87MJOF3B6952.json                    
2025-09-24 14:27:13,593 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:27:13,593 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WDPSPQTC87MJOF3B6952.json
2025-09-24 14:27:13,593 - INFO - 
2025-09-24 14:27:13,593 - INFO - XVTBS5BQOTRUDPEUC4CC.pdf                           1      po                   run1_XVTBS5BQOTRUDPEUC4CC.json                    
2025-09-24 14:27:13,593 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:27:13,593 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_XVTBS5BQOTRUDPEUC4CC.json
2025-09-24 14:27:13,593 - INFO - 
2025-09-24 14:27:13,593 - INFO - XVTBS5BQOTRUDPEUC4CC.pdf                           2      po                   run1_XVTBS5BQOTRUDPEUC4CC.json                    
2025-09-24 14:27:13,593 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:27:13,593 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_XVTBS5BQOTRUDPEUC4CC.json
2025-09-24 14:27:13,593 - INFO - 
2025-09-24 14:27:13,593 - INFO - Y6MOA7KJV1OU7NHZ4P4U.pdf                           1      po                   run1_Y6MOA7KJV1OU7NHZ4P4U.json                    
2025-09-24 14:27:13,593 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:27:13,593 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y6MOA7KJV1OU7NHZ4P4U.json
2025-09-24 14:27:13,593 - INFO - 
2025-09-24 14:27:13,594 - INFO - Y6MOA7KJV1OU7NHZ4P4U.pdf                           2      po                   run1_Y6MOA7KJV1OU7NHZ4P4U.json                    
2025-09-24 14:27:13,594 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:27:13,594 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y6MOA7KJV1OU7NHZ4P4U.json
2025-09-24 14:27:13,594 - INFO - 
2025-09-24 14:27:13,594 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:27:13,594 - INFO - Total entries: 14
2025-09-24 14:27:13,594 - INFO - ============================================================================================================================================
2025-09-24 14:27:13,594 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:27:13,594 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:27:13,594 - INFO -   1. AF0EUFN20TKQSN94KZCH.PDF            Page 1   → so_confirmation | run1_AF0EUFN20TKQSN94KZCH.json
2025-09-24 14:27:13,594 - INFO -   2. FFJ2USKKEFCH3U0FO1S5.PDF            Page 1   → so_confirmation | run1_FFJ2USKKEFCH3U0FO1S5.json
2025-09-24 14:27:13,594 - INFO -   3. HBV9LT6SK8HIOJ5DI4P2.pdf            Page 1   → so_confirmation | run1_HBV9LT6SK8HIOJ5DI4P2.json
2025-09-24 14:27:13,594 - INFO -   4. HL4SSNKOC8T141SEURCG.pdf            Page 1   → so_confirmation | run1_HL4SSNKOC8T141SEURCG.json
2025-09-24 14:27:13,594 - INFO -   5. Q9MLAQNGOP70MYYKOYFJ.pdf            Page 1   → so_confirmation | run1_Q9MLAQNGOP70MYYKOYFJ.json
2025-09-24 14:27:13,594 - INFO -   6. RBKKSRU2KS6IJRASO4SB.pdf            Page 1   → other           | run1_RBKKSRU2KS6IJRASO4SB.json
2025-09-24 14:27:13,594 - INFO -   7. SV5IN68S36F6PA0633RU.pdf            Page 1   → po              | run1_SV5IN68S36F6PA0633RU.json
2025-09-24 14:27:13,594 - INFO -   8. SV5IN68S36F6PA0633RU.pdf            Page 2   → po              | run1_SV5IN68S36F6PA0633RU.json
2025-09-24 14:27:13,594 - INFO -   9. WDPSPQTC87MJOF3B6952.pdf            Page 1   → po              | run1_WDPSPQTC87MJOF3B6952.json
2025-09-24 14:27:13,594 - INFO -  10. WDPSPQTC87MJOF3B6952.pdf            Page 2   → po              | run1_WDPSPQTC87MJOF3B6952.json
2025-09-24 14:27:13,594 - INFO -  11. XVTBS5BQOTRUDPEUC4CC.pdf            Page 1   → po              | run1_XVTBS5BQOTRUDPEUC4CC.json
2025-09-24 14:27:13,595 - INFO -  12. XVTBS5BQOTRUDPEUC4CC.pdf            Page 2   → po              | run1_XVTBS5BQOTRUDPEUC4CC.json
2025-09-24 14:27:13,595 - INFO -  13. Y6MOA7KJV1OU7NHZ4P4U.pdf            Page 1   → po              | run1_Y6MOA7KJV1OU7NHZ4P4U.json
2025-09-24 14:27:13,595 - INFO -  14. Y6MOA7KJV1OU7NHZ4P4U.pdf            Page 2   → po              | run1_Y6MOA7KJV1OU7NHZ4P4U.json
2025-09-24 14:27:13,595 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:27:13,595 - INFO - 
✅ Test completed: {'total_files': 10, 'processed': 10, 'failed': 0, 'errors': [], 'duration_seconds': 18.355788, 'processed_files': [{'filename': 'AF0EUFN20TKQSN94KZCH.PDF', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/AF0EUFN20TKQSN94KZCH.PDF'}, {'filename': 'FFJ2USKKEFCH3U0FO1S5.PDF', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/FFJ2USKKEFCH3U0FO1S5.PDF'}, {'filename': 'HBV9LT6SK8HIOJ5DI4P2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HBV9LT6SK8HIOJ5DI4P2.pdf'}, {'filename': 'HL4SSNKOC8T141SEURCG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HL4SSNKOC8T141SEURCG.pdf'}, {'filename': 'Q9MLAQNGOP70MYYKOYFJ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Q9MLAQNGOP70MYYKOYFJ.pdf'}, {'filename': 'RBKKSRU2KS6IJRASO4SB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/RBKKSRU2KS6IJRASO4SB.pdf'}, {'filename': 'SV5IN68S36F6PA0633RU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/SV5IN68S36F6PA0633RU.pdf'}, {'filename': 'WDPSPQTC87MJOF3B6952.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/WDPSPQTC87MJOF3B6952.pdf'}, {'filename': 'XVTBS5BQOTRUDPEUC4CC.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/XVTBS5BQOTRUDPEUC4CC.pdf'}, {'filename': 'Y6MOA7KJV1OU7NHZ4P4U.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Y6MOA7KJV1OU7NHZ4P4U.pdf'}]}
