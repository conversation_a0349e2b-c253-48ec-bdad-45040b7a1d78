2025-09-24 13:13:32,091 - INFO - Logging initialized. Log file: logs/test_classification_20250924_131332.log
2025-09-24 13:13:32,091 - INFO - 📁 Found 13 files to process
2025-09-24 13:13:32,091 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 13:13:32,091 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 13:13:32,091 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 13:13:32,091 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 13:13:32,092 - INFO - ⬆️ [13:13:32] Uploading: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:13:34,209 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf -> s3://document-extraction-logistically/temp/3807f861_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:13:34,209 - INFO - 🔍 [13:13:34] Starting classification: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:13:34,210 - INFO - ⬆️ [13:13:34] Uploading: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:13:34,210 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:34,222 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:34,231 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3807f861_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:13:34,231 - INFO - Processing PDF from S3...
2025-09-24 13:13:34,231 - INFO - Downloading PDF from S3 to /tmp/tmpda260jg9/3807f861_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:13:35,220 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf -> s3://document-extraction-logistically/temp/3635a99d_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:13:35,220 - INFO - 🔍 [13:13:35] Starting classification: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:13:35,221 - INFO - ⬆️ [13:13:35] Uploading: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:13:35,223 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:35,236 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:35,241 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3635a99d_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:13:35,242 - INFO - Processing PDF from S3...
2025-09-24 13:13:35,242 - INFO - Downloading PDF from S3 to /tmp/tmpsfkl3ekd/3635a99d_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:13:35,688 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:13:35,688 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:35,691 - INFO - Splitting PDF 3807f861_I_QHD3LC0DU6S8O2YVVS60 into 1 pages
2025-09-24 13:13:35,694 - INFO - Split PDF into 1 pages
2025-09-24 13:13:35,694 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:35,694 - INFO - Expected pages: [1]
2025-09-24 13:13:36,283 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf -> s3://document-extraction-logistically/temp/de42532a_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:13:36,283 - INFO - 🔍 [13:13:36] Starting classification: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:13:36,284 - INFO - ⬆️ [13:13:36] Uploading: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:13:36,290 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:36,298 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:36,300 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/de42532a_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:13:36,300 - INFO - Processing PDF from S3...
2025-09-24 13:13:36,300 - INFO - Downloading PDF from S3 to /tmp/tmp4dy9_8k9/de42532a_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:13:36,986 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:13:36,987 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:36,988 - INFO - Splitting PDF 3635a99d_NMFC_DH0JZ2JWDGRHD26BX74C into 2 pages
2025-09-24 13:13:36,991 - INFO - Split PDF into 2 pages
2025-09-24 13:13:36,991 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:36,991 - INFO - Expected pages: [1, 2]
2025-09-24 13:13:37,659 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf -> s3://document-extraction-logistically/temp/d4956a87_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:13:37,659 - INFO - 🔍 [13:13:37] Starting classification: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:13:37,660 - INFO - ⬆️ [13:13:37] Uploading: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:13:37,661 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:37,677 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:37,682 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d4956a87_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:13:37,683 - INFO - Processing PDF from S3...
2025-09-24 13:13:37,683 - INFO - Downloading PDF from S3 to /tmp/tmpuw1hpeog/d4956a87_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:13:38,327 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf -> s3://document-extraction-logistically/temp/9de5a9bc_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:13:38,327 - INFO - 🔍 [13:13:38] Starting classification: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:13:38,328 - INFO - ⬆️ [13:13:38] Uploading: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:13:38,329 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:38,342 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:38,345 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9de5a9bc_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:13:38,345 - INFO - Processing PDF from S3...
2025-09-24 13:13:38,345 - INFO - Downloading PDF from S3 to /tmp/tmp77q412ve/9de5a9bc_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:13:38,640 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:13:38,641 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:38,642 - INFO - Splitting PDF de42532a_NMFC_NJ4WSZ8BUQAW48V6403N into 3 pages
2025-09-24 13:13:38,646 - INFO - Split PDF into 3 pages
2025-09-24 13:13:38,646 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:38,646 - INFO - Expected pages: [1, 2, 3]
2025-09-24 13:13:39,028 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf -> s3://document-extraction-logistically/temp/daa70478_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:13:39,028 - INFO - 🔍 [13:13:39] Starting classification: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:13:39,029 - INFO - ⬆️ [13:13:39] Uploading: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:13:39,030 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:39,045 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:39,048 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/daa70478_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:13:39,049 - INFO - Processing PDF from S3...
2025-09-24 13:13:39,049 - INFO - Downloading PDF from S3 to /tmp/tmpwim_six0/daa70478_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:13:39,744 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:13:39,745 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:39,745 - INFO - Splitting PDF 9de5a9bc_NMFC_R1V0MO844PBLWNEAUETU into 2 pages
2025-09-24 13:13:39,748 - INFO - Split PDF into 2 pages
2025-09-24 13:13:39,748 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:39,748 - INFO - Expected pages: [1, 2]
2025-09-24 13:13:39,790 - INFO - Page 1: Extracted 589 characters, 36 lines from 3807f861_I_QHD3LC0DU6S8O2YVVS60_a0caaa56_page_001.pdf
2025-09-24 13:13:39,790 - INFO - Successfully processed page 1
2025-09-24 13:13:39,791 - INFO - Combined 1 pages into final text
2025-09-24 13:13:39,791 - INFO - Text validation for 3807f861_I_QHD3LC0DU6S8O2YVVS60: 606 characters, 1 pages
2025-09-24 13:13:39,791 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:39,791 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:39,822 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/0512a3c9_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:13:39,822 - INFO - 🔍 [13:13:39] Starting classification: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:13:39,823 - INFO - ⬆️ [13:13:39] Uploading: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:13:39,824 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:39,883 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:39,885 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0512a3c9_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:13:39,885 - INFO - Processing PDF from S3...
2025-09-24 13:13:39,885 - INFO - Downloading PDF from S3 to /tmp/tmprotgaa43/0512a3c9_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:13:40,510 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/33b674b0_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:13:40,510 - INFO - 🔍 [13:13:40] Starting classification: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:13:40,511 - INFO - ⬆️ [13:13:40] Uploading: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:13:40,513 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:40,529 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:40,534 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/33b674b0_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:13:40,534 - INFO - Processing PDF from S3...
2025-09-24 13:13:40,535 - INFO - Downloading PDF from S3 to /tmp/tmpm33fx5ew/33b674b0_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:13:40,641 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 13:13:40,641 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:40,643 - WARNING - Multiple definitions in dictionary at byte 0x9e9f6 for key /OpenAction
2025-09-24 13:13:40,643 - INFO - Splitting PDF d4956a87_NMFC_OR9EL08KIKNQPZ3UV3HH into 2 pages
2025-09-24 13:13:40,672 - INFO - Split PDF into 2 pages
2025-09-24 13:13:40,672 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:40,672 - INFO - Expected pages: [1, 2]
2025-09-24 13:13:41,090 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:13:41,090 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:41,090 - INFO - Splitting PDF daa70478_NMFC_RUDVGETVRZO7XX6YNW7I into 1 pages
2025-09-24 13:13:41,091 - INFO - Split PDF into 1 pages
2025-09-24 13:13:41,091 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:41,091 - INFO - Expected pages: [1]
2025-09-24 13:13:41,178 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf -> s3://document-extraction-logistically/temp/932398b0_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:13:41,179 - INFO - 🔍 [13:13:41] Starting classification: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:13:41,179 - INFO - ⬆️ [13:13:41] Uploading: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:13:41,180 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:41,195 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:41,198 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/932398b0_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:13:41,199 - INFO - Processing PDF from S3...
2025-09-24 13:13:41,199 - INFO - Downloading PDF from S3 to /tmp/tmp93ayx94l/932398b0_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:13:41,281 - INFO - Page 2: Extracted 764 characters, 54 lines from 3635a99d_NMFC_DH0JZ2JWDGRHD26BX74C_33f42e38_page_002.pdf
2025-09-24 13:13:41,282 - INFO - Successfully processed page 2
2025-09-24 13:13:41,818 - INFO - Page 1: Extracted 854 characters, 69 lines from 3635a99d_NMFC_DH0JZ2JWDGRHD26BX74C_33f42e38_page_001.pdf
2025-09-24 13:13:41,818 - INFO - Successfully processed page 1
2025-09-24 13:13:41,819 - INFO - Combined 2 pages into final text
2025-09-24 13:13:41,819 - INFO - Text validation for 3635a99d_NMFC_DH0JZ2JWDGRHD26BX74C: 1654 characters, 2 pages
2025-09-24 13:13:41,819 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:41,819 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:41,885 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf -> s3://document-extraction-logistically/temp/a80fc2b3_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:13:41,885 - INFO - 🔍 [13:13:41] Starting classification: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:13:41,886 - INFO - ⬆️ [13:13:41] Uploading: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:13:41,888 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:41,902 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:41,906 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a80fc2b3_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:13:41,906 - INFO - Processing PDF from S3...
2025-09-24 13:13:41,906 - INFO - Downloading PDF from S3 to /tmp/tmp3m3om1r0/a80fc2b3_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:13:42,574 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 13:13:42,575 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:42,576 - INFO - Splitting PDF 0512a3c9_W_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 13:13:42,578 - INFO - Split PDF into 1 pages
2025-09-24 13:13:42,578 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:42,578 - INFO - Expected pages: [1]
2025-09-24 13:13:42,616 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf -> s3://document-extraction-logistically/temp/d37714ed_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:13:42,616 - INFO - 🔍 [13:13:42] Starting classification: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:13:42,617 - INFO - ⬆️ [13:13:42] Uploading: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:13:42,619 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:42,635 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:42,642 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d37714ed_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:13:42,642 - INFO - Processing PDF from S3...
2025-09-24 13:13:42,642 - INFO - Downloading PDF from S3 to /tmp/tmpsm6ua8zh/d37714ed_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:13:42,840 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:13:42,841 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:42,841 - INFO - Splitting PDF 33b674b0_W_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 13:13:42,842 - INFO - Split PDF into 1 pages
2025-09-24 13:13:42,842 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:42,842 - INFO - Expected pages: [1]
2025-09-24 13:13:42,892 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3807f861_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:13:42,973 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:13:42,973 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:42,976 - INFO - Splitting PDF 932398b0_W_DFY1VDZWR7NBDLJV02G2 into 1 pages
2025-09-24 13:13:42,980 - INFO - Split PDF into 1 pages
2025-09-24 13:13:42,980 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:42,980 - INFO - Expected pages: [1]
2025-09-24 13:13:43,300 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf -> s3://document-extraction-logistically/temp/2523d41a_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:13:43,300 - INFO - 🔍 [13:13:43] Starting classification: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:13:43,301 - INFO - ⬆️ [13:13:43] Uploading: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:13:43,305 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:43,316 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:43,320 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2523d41a_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:13:43,321 - INFO - Processing PDF from S3...
2025-09-24 13:13:43,321 - INFO - Downloading PDF from S3 to /tmp/tmpp8odb1o3/2523d41a_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:13:43,997 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf -> s3://document-extraction-logistically/temp/8009423f_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:13:43,997 - INFO - 🔍 [13:13:43] Starting classification: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:13:43,999 - INFO - Initializing TextractProcessor...
2025-09-24 13:13:44,014 - INFO - Initializing BedrockProcessor...
2025-09-24 13:13:44,024 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8009423f_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:13:44,027 - INFO - Processing PDF from S3...
2025-09-24 13:13:44,031 - INFO - Downloading PDF from S3 to /tmp/tmplktblui0/8009423f_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:13:44,039 - INFO - 

I_QHD3LC0DU6S8O2YVVS60.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:13:44,040 - INFO - 

✓ Saved result: output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 13:13:44,127 - INFO - Page 2: Extracted 913 characters, 56 lines from 9de5a9bc_NMFC_R1V0MO844PBLWNEAUETU_74404559_page_002.pdf
2025-09-24 13:13:44,127 - INFO - Successfully processed page 2
2025-09-24 13:13:44,184 - INFO - Page 1: Extracted 980 characters, 76 lines from de42532a_NMFC_NJ4WSZ8BUQAW48V6403N_4959003e_page_001.pdf
2025-09-24 13:13:44,184 - INFO - Successfully processed page 1
2025-09-24 13:13:44,279 - INFO - Page 1: Extracted 1511 characters, 86 lines from 9de5a9bc_NMFC_R1V0MO844PBLWNEAUETU_74404559_page_001.pdf
2025-09-24 13:13:44,280 - INFO - Successfully processed page 1
2025-09-24 13:13:44,280 - INFO - Combined 2 pages into final text
2025-09-24 13:13:44,280 - INFO - Text validation for 9de5a9bc_NMFC_R1V0MO844PBLWNEAUETU: 2460 characters, 2 pages
2025-09-24 13:13:44,281 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:44,281 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:44,299 - INFO - Page 3: Extracted 850 characters, 59 lines from de42532a_NMFC_NJ4WSZ8BUQAW48V6403N_4959003e_page_003.pdf
2025-09-24 13:13:44,299 - INFO - Successfully processed page 3
2025-09-24 13:13:44,345 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:13:44,345 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:44,348 - INFO - Splitting PDF d37714ed_W_K9VSARJOKAIZHNJ5RBDT into 1 pages
2025-09-24 13:13:44,350 - INFO - Split PDF into 1 pages
2025-09-24 13:13:44,350 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:44,350 - INFO - Expected pages: [1]
2025-09-24 13:13:44,378 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3807f861_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:13:44,611 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:13:44,611 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:44,613 - INFO - Splitting PDF a80fc2b3_W_HFPAXYL947DH59AB12FL into 1 pages
2025-09-24 13:13:44,615 - INFO - Split PDF into 1 pages
2025-09-24 13:13:44,616 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:44,616 - INFO - Expected pages: [1]
2025-09-24 13:13:44,763 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:13:44,763 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:44,763 - INFO - Splitting PDF 2523d41a_W_WRKSHW76B3QUG47QWR75 into 1 pages
2025-09-24 13:13:44,764 - INFO - Split PDF into 1 pages
2025-09-24 13:13:44,764 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:44,764 - INFO - Expected pages: [1]
2025-09-24 13:13:44,849 - INFO - Page 2: Extracted 850 characters, 59 lines from de42532a_NMFC_NJ4WSZ8BUQAW48V6403N_4959003e_page_002.pdf
2025-09-24 13:13:44,849 - INFO - Successfully processed page 2
2025-09-24 13:13:44,849 - INFO - Combined 3 pages into final text
2025-09-24 13:13:44,850 - INFO - Text validation for de42532a_NMFC_NJ4WSZ8BUQAW48V6403N: 2735 characters, 3 pages
2025-09-24 13:13:44,850 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:44,850 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:44,938 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3635a99d_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:13:44,965 - INFO - 

NMFC_DH0JZ2JWDGRHD26BX74C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:13:44,965 - INFO - 

✓ Saved result: output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 13:13:45,295 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3635a99d_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:13:45,783 - INFO - Page 1: Extracted 443 characters, 72 lines from daa70478_NMFC_RUDVGETVRZO7XX6YNW7I_2fae5f17_page_001.pdf
2025-09-24 13:13:45,783 - INFO - Successfully processed page 1
2025-09-24 13:13:45,783 - INFO - Combined 1 pages into final text
2025-09-24 13:13:45,784 - INFO - Text validation for daa70478_NMFC_RUDVGETVRZO7XX6YNW7I: 460 characters, 1 pages
2025-09-24 13:13:45,784 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:45,784 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:46,076 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:13:46,076 - INFO - Splitting PDF into individual pages...
2025-09-24 13:13:46,078 - INFO - Splitting PDF 8009423f_W_XCJLXZK140FUS8020ZAG into 1 pages
2025-09-24 13:13:46,085 - INFO - Split PDF into 1 pages
2025-09-24 13:13:46,085 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:13:46,085 - INFO - Expected pages: [1]
2025-09-24 13:13:46,366 - INFO - Page 2: Extracted 540 characters, 29 lines from d4956a87_NMFC_OR9EL08KIKNQPZ3UV3HH_9f7cdc09_page_002.pdf
2025-09-24 13:13:46,366 - INFO - Successfully processed page 2
2025-09-24 13:13:46,831 - INFO - Page 1: Extracted 1120 characters, 87 lines from d4956a87_NMFC_OR9EL08KIKNQPZ3UV3HH_9f7cdc09_page_001.pdf
2025-09-24 13:13:46,831 - INFO - Successfully processed page 1
2025-09-24 13:13:46,831 - INFO - Combined 2 pages into final text
2025-09-24 13:13:46,831 - INFO - Text validation for d4956a87_NMFC_OR9EL08KIKNQPZ3UV3HH: 1696 characters, 2 pages
2025-09-24 13:13:46,832 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:46,832 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:47,272 - INFO - Page 1: Extracted 732 characters, 59 lines from 33b674b0_W_DCY7SLNMWUXIENOREHQF_d3578bdd_page_001.pdf
2025-09-24 13:13:47,272 - INFO - Successfully processed page 1
2025-09-24 13:13:47,272 - INFO - Combined 1 pages into final text
2025-09-24 13:13:47,272 - INFO - Text validation for 33b674b0_W_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 13:13:47,272 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:47,272 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:47,670 - INFO - Page 1: Extracted 626 characters, 49 lines from 932398b0_W_DFY1VDZWR7NBDLJV02G2_989b14c8_page_001.pdf
2025-09-24 13:13:47,670 - INFO - Successfully processed page 1
2025-09-24 13:13:47,671 - INFO - Combined 1 pages into final text
2025-09-24 13:13:47,671 - INFO - Text validation for 932398b0_W_DFY1VDZWR7NBDLJV02G2: 643 characters, 1 pages
2025-09-24 13:13:47,671 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:47,671 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:47,690 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/de42532a_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:13:47,721 - INFO - 

NMFC_NJ4WSZ8BUQAW48V6403N.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 3,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:13:47,721 - INFO - 

✓ Saved result: output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:13:47,778 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9de5a9bc_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:13:48,050 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/de42532a_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:13:48,072 - INFO - 

NMFC_R1V0MO844PBLWNEAUETU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:13:48,072 - INFO - 

✓ Saved result: output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:13:48,404 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9de5a9bc_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:13:48,486 - INFO - Page 1: Extracted 802 characters, 30 lines from d37714ed_W_K9VSARJOKAIZHNJ5RBDT_348a0445_page_001.pdf
2025-09-24 13:13:48,486 - INFO - Successfully processed page 1
2025-09-24 13:13:48,486 - INFO - Combined 1 pages into final text
2025-09-24 13:13:48,486 - INFO - Text validation for d37714ed_W_K9VSARJOKAIZHNJ5RBDT: 819 characters, 1 pages
2025-09-24 13:13:48,487 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:48,487 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:48,917 - INFO - Page 1: Extracted 517 characters, 31 lines from a80fc2b3_W_HFPAXYL947DH59AB12FL_c719c797_page_001.pdf
2025-09-24 13:13:48,918 - INFO - Successfully processed page 1
2025-09-24 13:13:48,918 - INFO - Combined 1 pages into final text
2025-09-24 13:13:48,918 - INFO - Text validation for a80fc2b3_W_HFPAXYL947DH59AB12FL: 534 characters, 1 pages
2025-09-24 13:13:48,918 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:48,919 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:49,359 - INFO - Page 1: Extracted 580 characters, 48 lines from 2523d41a_W_WRKSHW76B3QUG47QWR75_91309c38_page_001.pdf
2025-09-24 13:13:49,359 - INFO - Successfully processed page 1
2025-09-24 13:13:49,360 - INFO - Combined 1 pages into final text
2025-09-24 13:13:49,360 - INFO - Text validation for 2523d41a_W_WRKSHW76B3QUG47QWR75: 597 characters, 1 pages
2025-09-24 13:13:49,360 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:49,360 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:49,471 - INFO - Page 1: Extracted 939 characters, 64 lines from 0512a3c9_W_A34CDFDJ66EDOZEKZWJL_4df39183_page_001.pdf
2025-09-24 13:13:49,471 - INFO - Successfully processed page 1
2025-09-24 13:13:49,471 - INFO - Combined 1 pages into final text
2025-09-24 13:13:49,472 - INFO - Text validation for 0512a3c9_W_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 13:13:49,472 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:49,472 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:50,011 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/daa70478_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:13:50,022 - INFO - 

NMFC_RUDVGETVRZO7XX6YNW7I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:13:50,022 - INFO - 

✓ Saved result: output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 13:13:50,357 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/daa70478_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:13:50,431 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/932398b0_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:13:50,456 - INFO - 

W_DFY1VDZWR7NBDLJV02G2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:13:50,456 - INFO - 

✓ Saved result: output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 13:13:50,790 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/932398b0_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:13:50,831 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/33b674b0_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:13:50,845 - INFO - 

W_DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:13:50,846 - INFO - 

✓ Saved result: output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 13:13:51,215 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/33b674b0_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:13:51,372 - INFO - Page 1: Extracted 528 characters, 31 lines from 8009423f_W_XCJLXZK140FUS8020ZAG_071227a5_page_001.pdf
2025-09-24 13:13:51,372 - INFO - Successfully processed page 1
2025-09-24 13:13:51,373 - INFO - Combined 1 pages into final text
2025-09-24 13:13:51,373 - INFO - Text validation for 8009423f_W_XCJLXZK140FUS8020ZAG: 545 characters, 1 pages
2025-09-24 13:13:51,374 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:13:51,374 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:13:51,467 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a80fc2b3_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:13:51,482 - INFO - 

W_HFPAXYL947DH59AB12FL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:13:51,482 - INFO - 

✓ Saved result: output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 13:13:51,733 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d4956a87_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:13:51,795 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0512a3c9_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:13:51,809 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a80fc2b3_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:13:51,832 - INFO - 

NMFC_OR9EL08KIKNQPZ3UV3HH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:13:51,832 - INFO - 

✓ Saved result: output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:13:51,924 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2523d41a_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:13:51,989 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d37714ed_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:13:52,163 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d4956a87_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:13:52,177 - INFO - 

W_A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:13:52,177 - INFO - 

✓ Saved result: output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 13:13:52,510 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0512a3c9_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:13:52,521 - INFO - 

W_WRKSHW76B3QUG47QWR75.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:13:52,521 - INFO - 

✓ Saved result: output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 13:13:52,859 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2523d41a_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:13:52,874 - INFO - 

W_K9VSARJOKAIZHNJ5RBDT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 13:13:52,875 - INFO - 

✓ Saved result: output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 13:13:53,208 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d37714ed_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:13:53,901 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8009423f_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:13:53,920 - INFO - 

W_XCJLXZK140FUS8020ZAG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 13:13:53,920 - INFO - 

✓ Saved result: output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 13:13:54,254 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8009423f_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:13:54,255 - INFO - 
📊 Processing Summary:
2025-09-24 13:13:54,255 - INFO -    Total files: 13
2025-09-24 13:13:54,255 - INFO -    Successful: 13
2025-09-24 13:13:54,255 - INFO -    Failed: 0
2025-09-24 13:13:54,255 - INFO -    Duration: 22.16 seconds
2025-09-24 13:13:54,255 - INFO -    Output directory: output
2025-09-24 13:13:54,255 - INFO - 
📋 Successfully Processed Files:
2025-09-24 13:13:54,255 - INFO -    📄 I_QHD3LC0DU6S8O2YVVS60.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:13:54,256 - INFO -    📄 NMFC_DH0JZ2JWDGRHD26BX74C.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}
2025-09-24 13:13:54,256 - INFO -    📄 NMFC_NJ4WSZ8BUQAW48V6403N.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"},{"page_no":3,"doc_type":"nmfc_cert"}]}
2025-09-24 13:13:54,256 - INFO -    📄 NMFC_OR9EL08KIKNQPZ3UV3HH.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:13:54,256 - INFO -    📄 NMFC_R1V0MO844PBLWNEAUETU.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:13:54,256 - INFO -    📄 NMFC_RUDVGETVRZO7XX6YNW7I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:13:54,256 - INFO -    📄 W_A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:13:54,256 - INFO -    📄 W_DCY7SLNMWUXIENOREHQF.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:13:54,256 - INFO -    📄 W_DFY1VDZWR7NBDLJV02G2.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:13:54,256 - INFO -    📄 W_HFPAXYL947DH59AB12FL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:13:54,256 - INFO -    📄 W_K9VSARJOKAIZHNJ5RBDT.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 13:13:54,256 - INFO -    📄 W_WRKSHW76B3QUG47QWR75.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:13:54,257 - INFO -    📄 W_XCJLXZK140FUS8020ZAG.pdf: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 13:13:54,257 - INFO - 
============================================================================================================================================
2025-09-24 13:13:54,257 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 13:13:54,257 - INFO - ============================================================================================================================================
2025-09-24 13:13:54,258 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 13:13:54,258 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:13:54,258 - INFO - I_QHD3LC0DU6S8O2YVVS60.pdf                         1      weight_and_inspect... run1_I_QHD3LC0DU6S8O2YVVS60.json                  
2025-09-24 13:13:54,258 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:13:54,258 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 13:13:54,258 - INFO - 
2025-09-24 13:13:54,258 - INFO - NMFC_DH0JZ2JWDGRHD26BX74C.pdf                      1      nmfc_cert            run1_NMFC_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 13:13:54,258 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:13:54,258 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 13:13:54,258 - INFO - 
2025-09-24 13:13:54,258 - INFO - NMFC_DH0JZ2JWDGRHD26BX74C.pdf                      2      nmfc_cert            run1_NMFC_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 13:13:54,258 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:13:54,258 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 13:13:54,258 - INFO - 
2025-09-24 13:13:54,259 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      1      nmfc_cert            run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 13:13:54,259 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:13:54,259 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:13:54,259 - INFO - 
2025-09-24 13:13:54,259 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      2      nmfc_cert            run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 13:13:54,259 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:13:54,259 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:13:54,259 - INFO - 
2025-09-24 13:13:54,259 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      3      nmfc_cert            run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 13:13:54,259 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:13:54,259 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:13:54,259 - INFO - 
2025-09-24 13:13:54,259 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      1      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 13:13:54,259 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:13:54,259 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:13:54,259 - INFO - 
2025-09-24 13:13:54,259 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      2      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 13:13:54,259 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:13:54,260 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:13:54,260 - INFO - 
2025-09-24 13:13:54,260 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      1      weight_and_inspect... run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 13:13:54,260 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:13:54,260 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:13:54,260 - INFO - 
2025-09-24 13:13:54,260 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      2      weight_and_inspect... run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 13:13:54,260 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:13:54,260 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:13:54,260 - INFO - 
2025-09-24 13:13:54,260 - INFO - NMFC_RUDVGETVRZO7XX6YNW7I.pdf                      1      nmfc_cert            run1_NMFC_RUDVGETVRZO7XX6YNW7I.json               
2025-09-24 13:13:54,260 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:13:54,260 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 13:13:54,260 - INFO - 
2025-09-24 13:13:54,260 - INFO - W_A34CDFDJ66EDOZEKZWJL.pdf                         1      weight_and_inspect... run1_W_A34CDFDJ66EDOZEKZWJL.json                  
2025-09-24 13:13:54,260 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:13:54,260 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 13:13:54,260 - INFO - 
2025-09-24 13:13:54,260 - INFO - W_DCY7SLNMWUXIENOREHQF.pdf                         1      weight_and_inspect... run1_W_DCY7SLNMWUXIENOREHQF.json                  
2025-09-24 13:13:54,260 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:13:54,260 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 13:13:54,261 - INFO - 
2025-09-24 13:13:54,261 - INFO - W_DFY1VDZWR7NBDLJV02G2.pdf                         1      weight_and_inspect... run1_W_DFY1VDZWR7NBDLJV02G2.json                  
2025-09-24 13:13:54,261 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:13:54,261 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 13:13:54,261 - INFO - 
2025-09-24 13:13:54,261 - INFO - W_HFPAXYL947DH59AB12FL.pdf                         1      weight_and_inspect... run1_W_HFPAXYL947DH59AB12FL.json                  
2025-09-24 13:13:54,261 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:13:54,261 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 13:13:54,261 - INFO - 
2025-09-24 13:13:54,261 - INFO - W_K9VSARJOKAIZHNJ5RBDT.pdf                         1      scale_ticket         run1_W_K9VSARJOKAIZHNJ5RBDT.json                  
2025-09-24 13:13:54,261 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:13:54,261 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 13:13:54,261 - INFO - 
2025-09-24 13:13:54,261 - INFO - W_WRKSHW76B3QUG47QWR75.pdf                         1      weight_and_inspect... run1_W_WRKSHW76B3QUG47QWR75.json                  
2025-09-24 13:13:54,261 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:13:54,261 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 13:13:54,261 - INFO - 
2025-09-24 13:13:54,261 - INFO - W_XCJLXZK140FUS8020ZAG.pdf                         1      other                run1_W_XCJLXZK140FUS8020ZAG.json                  
2025-09-24 13:13:54,261 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:13:54,261 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 13:13:54,262 - INFO - 
2025-09-24 13:13:54,262 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:13:54,262 - INFO - Total entries: 18
2025-09-24 13:13:54,262 - INFO - ============================================================================================================================================
2025-09-24 13:13:54,262 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 13:13:54,262 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:13:54,262 - INFO -   1. I_QHD3LC0DU6S8O2YVVS60.pdf          Page 1   → weight_and_inspection_cert | run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 13:13:54,262 - INFO -   2. NMFC_DH0JZ2JWDGRHD26BX74C.pdf       Page 1   → nmfc_cert       | run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 13:13:54,262 - INFO -   3. NMFC_DH0JZ2JWDGRHD26BX74C.pdf       Page 2   → nmfc_cert       | run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 13:13:54,262 - INFO -   4. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 1   → nmfc_cert       | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:13:54,263 - INFO -   5. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 2   → nmfc_cert       | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:13:54,263 - INFO -   6. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 3   → nmfc_cert       | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 13:13:54,263 - INFO -   7. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:13:54,263 - INFO -   8. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:13:54,263 - INFO -   9. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:13:54,263 - INFO -  10. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:13:54,263 - INFO -  11. NMFC_RUDVGETVRZO7XX6YNW7I.pdf       Page 1   → nmfc_cert       | run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 13:13:54,263 - INFO -  12. W_A34CDFDJ66EDOZEKZWJL.pdf          Page 1   → weight_and_inspection_cert | run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 13:13:54,263 - INFO -  13. W_DCY7SLNMWUXIENOREHQF.pdf          Page 1   → weight_and_inspection_cert | run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 13:13:54,263 - INFO -  14. W_DFY1VDZWR7NBDLJV02G2.pdf          Page 1   → weight_and_inspection_cert | run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 13:13:54,263 - INFO -  15. W_HFPAXYL947DH59AB12FL.pdf          Page 1   → weight_and_inspection_cert | run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 13:13:54,264 - INFO -  16. W_K9VSARJOKAIZHNJ5RBDT.pdf          Page 1   → scale_ticket    | run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 13:13:54,264 - INFO -  17. W_WRKSHW76B3QUG47QWR75.pdf          Page 1   → weight_and_inspection_cert | run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 13:13:54,264 - INFO -  18. W_XCJLXZK140FUS8020ZAG.pdf          Page 1   → other           | run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 13:13:54,264 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:13:54,264 - INFO - 
✅ Test completed: {'total_files': 13, 'processed': 13, 'failed': 0, 'errors': [], 'duration_seconds': 22.16323, 'processed_files': [{'filename': 'I_QHD3LC0DU6S8O2YVVS60.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf'}, {'filename': 'NMFC_DH0JZ2JWDGRHD26BX74C.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf'}, {'filename': 'NMFC_NJ4WSZ8BUQAW48V6403N.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'nmfc_cert'}, {'page_no': 3, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf'}, {'filename': 'NMFC_OR9EL08KIKNQPZ3UV3HH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf'}, {'filename': 'NMFC_R1V0MO844PBLWNEAUETU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf'}, {'filename': 'NMFC_RUDVGETVRZO7XX6YNW7I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf'}, {'filename': 'W_A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf'}, {'filename': 'W_DCY7SLNMWUXIENOREHQF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf'}, {'filename': 'W_DFY1VDZWR7NBDLJV02G2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf'}, {'filename': 'W_HFPAXYL947DH59AB12FL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf'}, {'filename': 'W_K9VSARJOKAIZHNJ5RBDT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf'}, {'filename': 'W_WRKSHW76B3QUG47QWR75.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf'}, {'filename': 'W_XCJLXZK140FUS8020ZAG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'other'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf'}]}
