2025-09-24 14:05:40,129 - INFO - Logging initialized. Log file: logs/test_classification_20250924_140540.log
2025-09-24 14:05:40,130 - INFO - 📁 Found 13 files to process
2025-09-24 14:05:40,130 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:05:40,130 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 14:05:40,130 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 14:05:40,130 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 14:05:40,130 - INFO - ⬆️ [14:05:40] Uploading: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:05:41,798 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf -> s3://document-extraction-logistically/temp/1c9e736d_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:05:41,799 - INFO - 🔍 [14:05:41] Starting classification: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:05:41,800 - INFO - ⬆️ [14:05:41] Uploading: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:05:41,802 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:41,829 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:41,842 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1c9e736d_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:05:41,842 - INFO - Processing PDF from S3...
2025-09-24 14:05:41,842 - INFO - Downloading PDF from S3 to /tmp/tmpci4wgmpx/1c9e736d_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:05:42,855 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf -> s3://document-extraction-logistically/temp/bcbfe59f_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:05:42,856 - INFO - 🔍 [14:05:42] Starting classification: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:05:42,857 - INFO - ⬆️ [14:05:42] Uploading: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:05:42,859 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:42,874 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:42,877 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/bcbfe59f_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:05:42,878 - INFO - Processing PDF from S3...
2025-09-24 14:05:42,878 - INFO - Downloading PDF from S3 to /tmp/tmpe8x0vbyo/bcbfe59f_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:05:43,125 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:05:43,125 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:43,127 - INFO - Splitting PDF 1c9e736d_I_QHD3LC0DU6S8O2YVVS60 into 1 pages
2025-09-24 14:05:43,129 - INFO - Split PDF into 1 pages
2025-09-24 14:05:43,129 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:43,129 - INFO - Expected pages: [1]
2025-09-24 14:05:44,044 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf -> s3://document-extraction-logistically/temp/564fa18c_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:05:44,044 - INFO - 🔍 [14:05:44] Starting classification: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:05:44,045 - INFO - ⬆️ [14:05:44] Uploading: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:05:44,047 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:44,067 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:44,071 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/564fa18c_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:05:44,071 - INFO - Processing PDF from S3...
2025-09-24 14:05:44,072 - INFO - Downloading PDF from S3 to /tmp/tmpx_09ajn6/564fa18c_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:05:44,395 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:05:44,395 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:44,396 - INFO - Splitting PDF bcbfe59f_NMFC_DH0JZ2JWDGRHD26BX74C into 2 pages
2025-09-24 14:05:44,398 - INFO - Split PDF into 2 pages
2025-09-24 14:05:44,398 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:44,398 - INFO - Expected pages: [1, 2]
2025-09-24 14:05:46,193 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:05:46,193 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:46,194 - INFO - Splitting PDF 564fa18c_NMFC_NJ4WSZ8BUQAW48V6403N into 3 pages
2025-09-24 14:05:46,197 - INFO - Split PDF into 3 pages
2025-09-24 14:05:46,198 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:46,198 - INFO - Expected pages: [1, 2, 3]
2025-09-24 14:05:46,209 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf -> s3://document-extraction-logistically/temp/c7acc745_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:05:46,209 - INFO - 🔍 [14:05:46] Starting classification: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:05:46,210 - INFO - ⬆️ [14:05:46] Uploading: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:05:46,211 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:46,228 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:46,232 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/c7acc745_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:05:46,232 - INFO - Processing PDF from S3...
2025-09-24 14:05:46,232 - INFO - Downloading PDF from S3 to /tmp/tmpaliqpw17/c7acc745_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:05:46,767 - INFO - Page 1: Extracted 589 characters, 36 lines from 1c9e736d_I_QHD3LC0DU6S8O2YVVS60_d589cb58_page_001.pdf
2025-09-24 14:05:46,767 - INFO - Successfully processed page 1
2025-09-24 14:05:46,767 - INFO - Combined 1 pages into final text
2025-09-24 14:05:46,767 - INFO - Text validation for 1c9e736d_I_QHD3LC0DU6S8O2YVVS60: 606 characters, 1 pages
2025-09-24 14:05:46,768 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:46,768 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:46,789 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf -> s3://document-extraction-logistically/temp/931a6495_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:05:46,790 - INFO - 🔍 [14:05:46] Starting classification: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:05:46,790 - INFO - ⬆️ [14:05:46] Uploading: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:05:46,791 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:46,806 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:46,808 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/931a6495_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:05:46,809 - INFO - Processing PDF from S3...
2025-09-24 14:05:46,809 - INFO - Downloading PDF from S3 to /tmp/tmpehh5wjql/931a6495_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:05:47,521 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf -> s3://document-extraction-logistically/temp/5230824c_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:05:47,521 - INFO - 🔍 [14:05:47] Starting classification: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:05:47,523 - INFO - ⬆️ [14:05:47] Uploading: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:05:47,525 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:47,545 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:47,549 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5230824c_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:05:47,549 - INFO - Processing PDF from S3...
2025-09-24 14:05:47,549 - INFO - Downloading PDF from S3 to /tmp/tmptbtzbw76/5230824c_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:05:47,980 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:05:47,980 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:47,982 - INFO - Splitting PDF 931a6495_NMFC_R1V0MO844PBLWNEAUETU into 2 pages
2025-09-24 14:05:47,988 - INFO - Split PDF into 2 pages
2025-09-24 14:05:47,988 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:47,988 - INFO - Expected pages: [1, 2]
2025-09-24 14:05:48,248 - INFO - Page 2: Extracted 764 characters, 54 lines from bcbfe59f_NMFC_DH0JZ2JWDGRHD26BX74C_01eb9445_page_002.pdf
2025-09-24 14:05:48,248 - INFO - Successfully processed page 2
2025-09-24 14:05:48,505 - INFO - Page 1: Extracted 854 characters, 69 lines from bcbfe59f_NMFC_DH0JZ2JWDGRHD26BX74C_01eb9445_page_001.pdf
2025-09-24 14:05:48,506 - INFO - Successfully processed page 1
2025-09-24 14:05:48,506 - INFO - Combined 2 pages into final text
2025-09-24 14:05:48,506 - INFO - Text validation for bcbfe59f_NMFC_DH0JZ2JWDGRHD26BX74C: 1654 characters, 2 pages
2025-09-24 14:05:48,506 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:48,506 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:49,103 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b63c3611-334c-462f-a4ac-8980aa2885be', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:35:48 GMT', 'content-type': 'application/json', 'content-length': '1342', 'connection': 'keep-alive', 'x-amzn-requestid': 'b63c3611-334c-462f-a4ac-8980aa2885be'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided: page1. We need to identify type: It\'s a "CUBE MEASUREMENT CERTIFICATE" that includes measurement details, weight? Actually "Cube Measurement Certificate" is not in the list but similar to "inspection_cert" or "weight_and_inspection_cert". The description: "Cube Measurement Certificate" likely refers to a "Cube Measurement Certificate" which is a type of inspection certificate for cube measurement, which is a certificate that includes dimensions (length, width, height) and cubic feet. This is similar to "inspection_cert" because it\'s a certificate about dimensions, not weight. The "inspection_cert" definition: "Inspection certificate, Cube Measurement certificate". So we classify as "inspection_cert". The page content has keyword "CUBE MEASUREMENT CERTIFICATE". That matches inspection_cert. So output:\n\n{\n  page_no: 1,\n  doc_type: "inspection_cert"\n}\n\nWe have to use tool classify_logistics_doc_type with documents array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2615, 'outputTokens': 223, 'totalTokens': 2838}, 'metrics': {'latencyMs': 1135}}
2025-09-24 14:05:49,103 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1c9e736d_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:05:49,120 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/dbe60f03_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:05:49,120 - INFO - 🔍 [14:05:49] Starting classification: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:05:49,121 - INFO - ⬆️ [14:05:49] Uploading: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:05:49,123 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:49,140 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:49,143 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dbe60f03_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:05:49,143 - INFO - Processing PDF from S3...
2025-09-24 14:05:49,143 - INFO - Downloading PDF from S3 to /tmp/tmpy15es40o/dbe60f03_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:05:49,767 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/f6a16796_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:05:49,768 - INFO - 🔍 [14:05:49] Starting classification: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:05:49,768 - INFO - ⬆️ [14:05:49] Uploading: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:05:49,769 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:49,777 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:49,780 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f6a16796_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:05:49,780 - INFO - Processing PDF from S3...
2025-09-24 14:05:49,781 - INFO - Downloading PDF from S3 to /tmp/tmplz8xsu4t/f6a16796_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:05:50,338 - INFO - Page 3: Extracted 850 characters, 59 lines from 564fa18c_NMFC_NJ4WSZ8BUQAW48V6403N_8ad5bb10_page_003.pdf
2025-09-24 14:05:50,339 - INFO - Successfully processed page 3
2025-09-24 14:05:50,410 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf -> s3://document-extraction-logistically/temp/ed3c3359_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:05:50,411 - INFO - 🔍 [14:05:50] Starting classification: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:05:50,411 - INFO - ⬆️ [14:05:50] Uploading: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:05:50,412 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:50,428 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:50,431 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ed3c3359_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:05:50,431 - INFO - Processing PDF from S3...
2025-09-24 14:05:50,432 - INFO - Downloading PDF from S3 to /tmp/tmp66bj2p4t/ed3c3359_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:05:50,439 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:05:50,440 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:50,441 - INFO - Splitting PDF 5230824c_NMFC_RUDVGETVRZO7XX6YNW7I into 1 pages
2025-09-24 14:05:50,442 - INFO - Split PDF into 1 pages
2025-09-24 14:05:50,442 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:50,442 - INFO - Expected pages: [1]
2025-09-24 14:05:50,743 - INFO - Page 2: Extracted 850 characters, 59 lines from 564fa18c_NMFC_NJ4WSZ8BUQAW48V6403N_8ad5bb10_page_002.pdf
2025-09-24 14:05:50,744 - INFO - Successfully processed page 2
2025-09-24 14:05:50,951 - INFO - Page 1: Extracted 980 characters, 76 lines from 564fa18c_NMFC_NJ4WSZ8BUQAW48V6403N_8ad5bb10_page_001.pdf
2025-09-24 14:05:50,951 - INFO - Successfully processed page 1
2025-09-24 14:05:50,952 - INFO - Combined 3 pages into final text
2025-09-24 14:05:50,952 - INFO - Text validation for 564fa18c_NMFC_NJ4WSZ8BUQAW48V6403N: 2735 characters, 3 pages
2025-09-24 14:05:50,952 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:50,952 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:51,066 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf -> s3://document-extraction-logistically/temp/3989db5a_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:05:51,066 - INFO - 🔍 [14:05:51] Starting classification: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:05:51,067 - INFO - ⬆️ [14:05:51] Uploading: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:05:51,073 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:51,086 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:51,090 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3989db5a_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:05:51,091 - INFO - Processing PDF from S3...
2025-09-24 14:05:51,091 - INFO - Downloading PDF from S3 to /tmp/tmpe9d6lmwl/3989db5a_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:05:51,420 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 14:05:51,420 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:51,420 - INFO - Splitting PDF dbe60f03_W_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 14:05:51,421 - INFO - Split PDF into 1 pages
2025-09-24 14:05:51,421 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:51,421 - INFO - Expected pages: [1]
2025-09-24 14:05:51,520 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:05:51,520 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:51,521 - INFO - Splitting PDF f6a16796_W_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 14:05:51,522 - INFO - Split PDF into 1 pages
2025-09-24 14:05:51,522 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:51,522 - INFO - Expected pages: [1]
2025-09-24 14:05:51,704 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf -> s3://document-extraction-logistically/temp/548e16d4_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:05:51,704 - INFO - 🔍 [14:05:51] Starting classification: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:05:51,704 - INFO - ⬆️ [14:05:51] Uploading: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:05:51,708 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:51,715 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:51,717 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/548e16d4_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:05:51,717 - INFO - Processing PDF from S3...
2025-09-24 14:05:51,717 - INFO - Downloading PDF from S3 to /tmp/tmp8rrynb8s/548e16d4_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:05:51,875 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:05:51,875 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:51,876 - INFO - Splitting PDF ed3c3359_W_DFY1VDZWR7NBDLJV02G2 into 1 pages
2025-09-24 14:05:51,878 - INFO - Split PDF into 1 pages
2025-09-24 14:05:51,878 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:51,878 - INFO - Expected pages: [1]
2025-09-24 14:05:51,925 - INFO - Page 2: Extracted 913 characters, 56 lines from 931a6495_NMFC_R1V0MO844PBLWNEAUETU_94dec153_page_002.pdf
2025-09-24 14:05:51,925 - INFO - Successfully processed page 2
2025-09-24 14:05:52,330 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf -> s3://document-extraction-logistically/temp/dbfb9dda_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:05:52,330 - INFO - 🔍 [14:05:52] Starting classification: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:05:52,331 - INFO - ⬆️ [14:05:52] Uploading: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:05:52,333 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:52,345 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:52,352 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dbfb9dda_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:05:52,353 - INFO - Processing PDF from S3...
2025-09-24 14:05:52,353 - INFO - Downloading PDF from S3 to /tmp/tmpif5udlc7/dbfb9dda_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:05:52,391 - INFO - Page 1: Extracted 1511 characters, 86 lines from 931a6495_NMFC_R1V0MO844PBLWNEAUETU_94dec153_page_001.pdf
2025-09-24 14:05:52,392 - INFO - Successfully processed page 1
2025-09-24 14:05:52,392 - INFO - Combined 2 pages into final text
2025-09-24 14:05:52,392 - INFO - Text validation for 931a6495_NMFC_R1V0MO844PBLWNEAUETU: 2460 characters, 2 pages
2025-09-24 14:05:52,392 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:52,393 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:52,573 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:05:52,574 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:52,575 - INFO - Splitting PDF 3989db5a_W_HFPAXYL947DH59AB12FL into 1 pages
2025-09-24 14:05:52,578 - INFO - Split PDF into 1 pages
2025-09-24 14:05:52,578 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:52,578 - INFO - Expected pages: [1]
2025-09-24 14:05:52,655 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e7c16c65-e57f-4dad-9a41-19beb36df8c4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:35:52 GMT', 'content-type': 'application/json', 'content-length': '3608', 'connection': 'keep-alive', 'x-amzn-requestid': 'e7c16c65-e57f-4dad-9a41-19beb36df8c4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The content: It appears to be an "Inspection Correction Notice" document. The definition: "inspection_cert" or "weight_and_inspection_cert" or "nmfc_cert". The doc type "inspection_cert" is an inspection certificate other than weight and inspection certificate which doesn\'t have weight mentioned. "weight_and_inspection_cert" requires weight mention, and if weight and inspection cert but with NMFC certificate specifics, might be nmfc_cert.\n\nLet\'s analyze.\n\nPage1: Contains "Inspection Correction Notice" header. Contains columns: "Piece, Commodity, NMFC, Before, After, Count, Description, Item, Sub, Weight, Class". So includes weight. So it\'s a weight and inspection certificate. Also there are NMFC codes. The guidance: For weight_and_inspection_cert, if it has NMFC certificate keywords, classify as nmfc_cert else keep as weight_and_inspection_cert. Let\'s see: "NMFC" is present. The guidance says: "Strickly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." Does the document contain keywords for nmfc_cert? nmfc_cert keywords: "As described and As found or Original and inspection or Corrected class or Correction information". Also "NMFC Code, class #". The doc shows "NMFC" column and the data shows "189430" etc. There is "Correction Details" etc. The guidance maybe ambiguous. But likely this is a weight and inspection certificate with NMFC details. But nmfc_cert is specifically for NMFC classification certificate or correction notice or weight & inspection certificate but with "Inspected against original or Corrected against Actual". The doc has "Inspection Correction Notice" and "Correction Details". That might fit nmfc_cert. So likely doc_type=nmfc_cert. Let\'s confirm: The doc is an inspection correction notice with NMFC codes. So classification: nmfc_cert.\n\nPage2: Looks identical content, but missing some of the columns? Actually page2 has fewer columns: It lists "Weigh, Dimensioner, Inspection, Freight Detail, Piece, Commodity, NMFC, Before, After, Count, Description, Item, Sub, Weight, Class, Weight, Class, Correction Details, Applied Rules and Accessorial, Dimensions, Pieces, Length, Width, Height, Weight, Pieces, Length, Width, Height, Weight, Average Density, Cube, Comments, 651041600, This will certify that this shipment has been (Inspected/Dimensioned/Weighed) and the descriptions have been found to be as indicated above, Analyst, 26709, CWIREVT". It seems similar but maybe some columns omitted. But same type: nmfc_cert.\n\nAlso check for continuation: The second page is likely continuation of the first page. But we still assign same doc_type. The instructions: If a page is part of a multi-page single document, each page still gets the same doc_type. So each page: doc_type:nmfc_cert. But we must output objects: each page with page_no and doc_type.\n\nWe must call classify_logistics_doc_type with array of objects.\n\nSo output: documents: [{page_no:1, doc_type:"nmfc_cert"}, {page_no:2, doc_type:"nmfc_cert"}].\n\nBut we must be careful: The doc_type enum includes "nmfc_cert". Yes.\n\nReturn via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2963, 'outputTokens': 744, 'totalTokens': 3707}, 'metrics': {'latencyMs': 3058}}
2025-09-24 14:05:52,655 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/bcbfe59f_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:05:53,022 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf -> s3://document-extraction-logistically/temp/df6d32b7_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:05:53,022 - INFO - 🔍 [14:05:53] Starting classification: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:05:53,024 - INFO - Initializing TextractProcessor...
2025-09-24 14:05:53,037 - INFO - Initializing BedrockProcessor...
2025-09-24 14:05:53,047 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/df6d32b7_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:05:53,047 - INFO - Processing PDF from S3...
2025-09-24 14:05:53,049 - INFO - Downloading PDF from S3 to /tmp/tmp6awwhs49/df6d32b7_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:05:53,063 - INFO - 

I_QHD3LC0DU6S8O2YVVS60.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "inspection_cert"
        }
    ]
}
2025-09-24 14:05:53,063 - INFO - 

✓ Saved result: output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 14:05:53,196 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:05:53,196 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:53,199 - INFO - Splitting PDF 548e16d4_W_K9VSARJOKAIZHNJ5RBDT into 1 pages
2025-09-24 14:05:53,200 - INFO - Split PDF into 1 pages
2025-09-24 14:05:53,200 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:53,201 - INFO - Expected pages: [1]
2025-09-24 14:05:53,361 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1c9e736d_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:05:53,387 - INFO - 

NMFC_DH0JZ2JWDGRHD26BX74C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:05:53,387 - INFO - 

✓ Saved result: output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 14:05:53,673 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/bcbfe59f_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:05:53,764 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:05:53,764 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:53,766 - INFO - Splitting PDF dbfb9dda_W_WRKSHW76B3QUG47QWR75 into 1 pages
2025-09-24 14:05:53,768 - INFO - Split PDF into 1 pages
2025-09-24 14:05:53,768 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:53,769 - INFO - Expected pages: [1]
2025-09-24 14:05:54,655 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6b2ac518-3ef5-4ea9-a749-faf8c8568fd9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:35:54 GMT', 'content-type': 'application/json', 'content-length': '2841', 'connection': 'keep-alive', 'x-amzn-requestid': '6b2ac518-3ef5-4ea9-a749-faf8c8568fd9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The content is "Inspection Correction Notice" and some details. This looks like a Customs or Inspection Document. The description: "Inspection Correction Notice" is a type of NMFC Certification or Inspection Certificate? Let\'s recall categories: weight_and_inspection_cert, inspection_cert, nmfc_cert, customs_doc.\n\nWe have keywords: "Inspection Correction Notice", "NMFC" etc. The classification: This appears to be a NMFC correction notice or a weight and inspection certificate. The schema notes: "Inspection Correction Notice" likely is NMFC correction? Let\'s see description: NMFC Certification can be for weight & inspection certificate or correction. The text includes "Inspection Correction Notice" and "NMFC to 189800-4". That seems to be a correction notice for NMFC classification. So likely nmfc_cert.\n\nBut we need to examine fields: "Inspection Correction Notice" indicates a correction. The doc type likely "nmfc_cert". Let\'s check the definition: nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual. Keywords: "As described and As found" etc. But this doc says "Inspection Correction Notice" and "NMFC to 189800-4". So yes, nmfc_cert.\n\nThus each page is nmfc_cert. Are pages continuation? The pages repeated header but not "continued". But they are same content. Each page likely part of same document. But we must decide if page is continuation of previous page. The content doesn\'t have "continued". But the page 2 content is missing some details? Actually page2: The row "Piece Commodity NMFC Before After Count Description Item Sub Weight Class Weight Class Correction Details Applied Rules and Accessorial Dimensions Pieces Length Width Height Weight Pieces Length Width Height Weight Average Density Cube 5.23 38.24 ..." No "Before" and "After" columns? Actually page2 had same header but no "Before" and "After" values. But maybe truncated. The page marks "Inspection Correction Notice" same. The presence of "continued" is not indicated. So we treat each page as having its own doc_type, same type. So output 3 objects with doc_type nmfc_cert.\n\nWe must use the tool classify_logistics_doc_type with documents array. Each object: page_no: number, doc_type: "nmfc_cert". Return object array.\n\nLet\'s produce.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"},{"page_no":3,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3323, 'outputTokens': 541, 'totalTokens': 3864}, 'metrics': {'latencyMs': 2520}}
2025-09-24 14:05:54,655 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/564fa18c_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:05:54,697 - INFO - 

NMFC_NJ4WSZ8BUQAW48V6403N.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 3,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:05:54,697 - INFO - 

✓ Saved result: output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:05:54,791 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:05:54,791 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:54,794 - INFO - Splitting PDF df6d32b7_W_XCJLXZK140FUS8020ZAG into 1 pages
2025-09-24 14:05:54,801 - INFO - Split PDF into 1 pages
2025-09-24 14:05:54,801 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:54,801 - INFO - Expected pages: [1]
2025-09-24 14:05:54,931 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '00adbdef-7f81-420a-a915-0318ea961635', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:35:54 GMT', 'content-type': 'application/json', 'content-length': '1620', 'connection': 'keep-alive', 'x-amzn-requestid': '00adbdef-7f81-420a-a915-0318ea961635'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. This appears to be a weight and inspection certificate? It\'s a "Weight and Research Program" page, with weight details, "We certify that this shipment has been inspected and/or reweighed". And "As found upon Examination" suggests weight inspection. So likely weight_and_inspection_cert.\n\nCheck keywords: "Weight and Research Program", "We certify that this shipment has been inspected and/or reweighed". This matches weight_and_inspection_cert. Also "Weight and Research Section". So doc_type weight_and_inspection_cert. But also check nmfc_cert: if weight_and_inspection_cert has keywords from nmfc_cert. Let\'s see nmfc_cert keywords: "As described", "As found or Original and inspection", "Corrected class", "Correction information", "NMFC Code, class #". I don\'t see those. So weight_and_inspection_cert.\n\nThus page1 and page2 are same type. Also check if continuation: page2 has "CONT" in As found upon Examination (CONT). So page2 is continuation of page1. But we still classify each page with same doc_type weight_and_inspection_cert.\n\nReturn array with two objects: page_no 1 doc_type weight_and_inspection_cert, page_no 2 doc_type weight_and_inspection_cert.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3299, 'outputTokens': 299, 'totalTokens': 3598}, 'metrics': {'latencyMs': 1390}}
2025-09-24 14:05:54,931 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/931a6495_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:05:54,988 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/564fa18c_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:05:55,022 - INFO - 

NMFC_R1V0MO844PBLWNEAUETU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:05:55,022 - INFO - 

✓ Saved result: output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 14:05:55,033 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 14:05:55,033 - INFO - Splitting PDF into individual pages...
2025-09-24 14:05:55,038 - INFO - Page 1: Extracted 443 characters, 72 lines from 5230824c_NMFC_RUDVGETVRZO7XX6YNW7I_0ddeee6c_page_001.pdf
2025-09-24 14:05:55,039 - INFO - Successfully processed page 1
2025-09-24 14:05:55,039 - INFO - Combined 1 pages into final text
2025-09-24 14:05:55,039 - INFO - Text validation for 5230824c_NMFC_RUDVGETVRZO7XX6YNW7I: 460 characters, 1 pages
2025-09-24 14:05:55,040 - WARNING - Multiple definitions in dictionary at byte 0x9e9f6 for key /OpenAction
2025-09-24 14:05:55,040 - INFO - Splitting PDF c7acc745_NMFC_OR9EL08KIKNQPZ3UV3HH into 2 pages
2025-09-24 14:05:55,041 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:55,043 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:55,070 - INFO - Split PDF into 2 pages
2025-09-24 14:05:55,070 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:05:55,070 - INFO - Expected pages: [1, 2]
2025-09-24 14:05:55,325 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/931a6495_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:05:56,515 - INFO - Page 1: Extracted 732 characters, 59 lines from f6a16796_W_DCY7SLNMWUXIENOREHQF_f0c56b4b_page_001.pdf
2025-09-24 14:05:56,516 - INFO - Successfully processed page 1
2025-09-24 14:05:56,516 - INFO - Combined 1 pages into final text
2025-09-24 14:05:56,516 - INFO - Text validation for f6a16796_W_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 14:05:56,516 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:56,516 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:56,561 - INFO - Page 1: Extracted 517 characters, 31 lines from 3989db5a_W_HFPAXYL947DH59AB12FL_3728a1d4_page_001.pdf
2025-09-24 14:05:56,561 - INFO - Successfully processed page 1
2025-09-24 14:05:56,561 - INFO - Combined 1 pages into final text
2025-09-24 14:05:56,561 - INFO - Text validation for 3989db5a_W_HFPAXYL947DH59AB12FL: 534 characters, 1 pages
2025-09-24 14:05:56,561 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:56,561 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:56,691 - INFO - Page 1: Extracted 626 characters, 49 lines from ed3c3359_W_DFY1VDZWR7NBDLJV02G2_aa354681_page_001.pdf
2025-09-24 14:05:56,691 - INFO - Successfully processed page 1
2025-09-24 14:05:56,691 - INFO - Combined 1 pages into final text
2025-09-24 14:05:56,692 - INFO - Text validation for ed3c3359_W_DFY1VDZWR7NBDLJV02G2: 643 characters, 1 pages
2025-09-24 14:05:56,692 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:56,692 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:56,698 - INFO - Page 1: Extracted 802 characters, 30 lines from 548e16d4_W_K9VSARJOKAIZHNJ5RBDT_b4c53c59_page_001.pdf
2025-09-24 14:05:56,699 - INFO - Successfully processed page 1
2025-09-24 14:05:56,699 - INFO - Combined 1 pages into final text
2025-09-24 14:05:56,699 - INFO - Text validation for 548e16d4_W_K9VSARJOKAIZHNJ5RBDT: 819 characters, 1 pages
2025-09-24 14:05:56,699 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:56,699 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:57,057 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '602555a3-949b-4081-a56c-37dce90964ae', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:35:56 GMT', 'content-type': 'application/json', 'content-length': '827', 'connection': 'keep-alive', 'x-amzn-requestid': '602555a3-949b-4081-a56c-37dce90964ae'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. The document appears to be a NMFC Classification Certificate. The page includes "NMFC CLASSIFICATION CERTIFICATE" in the header. So doc_type should be nmfc_cert. The instructions say if page is continuation of previous page, but it\'s first page. It has "4 of 6" indicating it\'s page 4 of 6. But this is the first page of the input. We just classify as nmfc_cert.\n\nSo we need to call the function classify_logistics_doc_type with documents array with page_no 1, doc_type: "nmfc_cert".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2710, 'outputTokens': 139, 'totalTokens': 2849}, 'metrics': {'latencyMs': 831}}
2025-09-24 14:05:57,057 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5230824c_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:05:57,069 - INFO - 

NMFC_RUDVGETVRZO7XX6YNW7I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:05:57,069 - INFO - 

✓ Saved result: output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 14:05:57,367 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5230824c_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:05:57,836 - INFO - Page 1: Extracted 580 characters, 48 lines from dbfb9dda_W_WRKSHW76B3QUG47QWR75_049fe0a8_page_001.pdf
2025-09-24 14:05:57,836 - INFO - Successfully processed page 1
2025-09-24 14:05:57,836 - INFO - Combined 1 pages into final text
2025-09-24 14:05:57,837 - INFO - Text validation for dbfb9dda_W_WRKSHW76B3QUG47QWR75: 597 characters, 1 pages
2025-09-24 14:05:57,837 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:57,837 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:57,949 - INFO - Page 1: Extracted 939 characters, 64 lines from dbe60f03_W_A34CDFDJ66EDOZEKZWJL_a52139f1_page_001.pdf
2025-09-24 14:05:57,949 - INFO - Successfully processed page 1
2025-09-24 14:05:57,950 - INFO - Combined 1 pages into final text
2025-09-24 14:05:57,950 - INFO - Text validation for dbe60f03_W_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 14:05:57,950 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:57,950 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:58,507 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e95d6c8b-a7ee-44bb-8081-b81a0febd1e7', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:35:58 GMT', 'content-type': 'application/json', 'content-length': '889', 'connection': 'keep-alive', 'x-amzn-requestid': 'e95d6c8b-a7ee-44bb-8081-b81a0febd1e7'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page with a Freight Certificate with details like "Dimension/Cube". The keywords: "CERTIFICATE", "Total Weight", "Total Rated Dimensions", etc. This looks like a weight and inspection certificate? The definition: weight_and_inspection_cert - weight and inspection certificate. Also nmfc_cert is specific with NMFC codes etc. Not present. So classify as weight_and_inspection_cert.\n\nReturn JSON via tool classify_logistics_doc_type. We need array of documents: only one page, page_no:1, doc_type: "weight_and_inspection_cert".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2603, 'outputTokens': 138, 'totalTokens': 2741}, 'metrics': {'latencyMs': 815}}
2025-09-24 14:05:58,507 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3989db5a_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:05:58,519 - INFO - 

W_HFPAXYL947DH59AB12FL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:05:58,519 - INFO - 

✓ Saved result: output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 14:05:58,543 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1510bcd7-9ffe-480e-9e10-8a1d2acd8d25', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:35:58 GMT', 'content-type': 'application/json', 'content-length': '840', 'connection': 'keep-alive', 'x-amzn-requestid': '1510bcd7-9ffe-480e-9e10-8a1d2acd8d25'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to determine doc_type. The page is a "WEIGHT VALIDATION CERTIFICATE" with weight records, scale operator, etc. This matches "scale_ticket" as per definition: weight record with Gross, Tare, Net, Weighed At, etc. It includes weight, operator, date, location. So doc_type: scale_ticket. Must return via tool call classify_logistics_doc_type with documents array containing {page_no:1, doc_type:"scale_ticket"}.\n\nWe must output exactly that JSON via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2715, 'outputTokens': 137, 'totalTokens': 2852}, 'metrics': {'latencyMs': 844}}
2025-09-24 14:05:58,543 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f6a16796_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:05:58,820 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3989db5a_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:05:58,837 - INFO - 

W_DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "scale_ticket"
        }
    ]
}
2025-09-24 14:05:58,837 - INFO - 

✓ Saved result: output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 14:05:59,122 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'b0275d5e-4f6f-489e-9ce5-e24130e1f589', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:35:59 GMT', 'content-type': 'application/json', 'content-length': '1450', 'connection': 'keep-alive', 'x-amzn-requestid': 'b0275d5e-4f6f-489e-9ce5-e24130e1f589'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. It has "WEIGHT CERTIFICATE", "Freight Bill Number", "Weighed Date", "Weighing performed at ...", "WEIGHT CERTIFICATION" and weight details. This is likely a weight and inspection certificate? Actually "WEIGHT CERTIFICATE" is a weight and inspection certificate? Checking enum: weight_and_inspection_cert - Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. So this page is a weight certificate. So doc_type likely weight_and_inspection_cert. But we need check if nmfc_cert? nmfc_cert indicates NMFC classification certificate or correction notice. This page mentions NMFC 100-series items 360 and 995 etc. But nmfc_cert specifically: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." Not a correction. So weight_and_inspection_cert.\n\nThus output: {page_no: 1, doc_type: "weight_and_inspection_cert"}\n\nWe must use function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2669, 'outputTokens': 257, 'totalTokens': 2926}, 'metrics': {'latencyMs': 1217}}
2025-09-24 14:05:59,122 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/548e16d4_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:05:59,160 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f6a16796_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:05:59,181 - INFO - 

W_K9VSARJOKAIZHNJ5RBDT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:05:59,182 - INFO - 

✓ Saved result: output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 14:05:59,301 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c1441a61-2ce4-4724-acea-41aa78c5b01e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:35:59 GMT', 'content-type': 'application/json', 'content-length': '1591', 'connection': 'keep-alive', 'x-amzn-requestid': 'c1441a61-2ce4-4724-acea-41aa78c5b01e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page with "Weight & Inspection" and "Certified Weight Certificate" etc. The doc seems to be a "weight_and_inspection_cert" or "nmfc_cert". It contains "corrected weight" and "certified weight certificate". The description: weight_and_inspection_cert vs nmfc_cert. The difference: if it has weight mention, weight_and_inspection_cert. If it also has nmfc correction indicators. The content: "Weight Certificate" "Corrected Weight: 1425" "Declared Weight: 1148". It seems a weight certificate. Does it have NMFC-related terms? Not seeing NMFC Code, class #, or correction words like "Corrected". It does say "reweighed" and "corrected weight". But does that qualify as nmfc_cert? The instruction: "Strickly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." The nmfc_cert keywords: "Inspected against original", "Corrected against Actual", "Correction information", "NMFC Code", "class #". There\'s no mention of those. So classify as weight_and_inspection_cert.\n\nWe have only one page. We need to output: array of objects with page_no 1 and doc_type "weight_and_inspection_cert". Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2682, 'outputTokens': 308, 'totalTokens': 2990}, 'metrics': {'latencyMs': 1390}}
2025-09-24 14:05:59,301 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ed3c3359_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:05:59,311 - INFO - Page 1: Extracted 528 characters, 31 lines from df6d32b7_W_XCJLXZK140FUS8020ZAG_05904a10_page_001.pdf
2025-09-24 14:05:59,313 - INFO - Successfully processed page 1
2025-09-24 14:05:59,314 - INFO - Combined 1 pages into final text
2025-09-24 14:05:59,314 - INFO - Text validation for df6d32b7_W_XCJLXZK140FUS8020ZAG: 545 characters, 1 pages
2025-09-24 14:05:59,314 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:05:59,314 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:05:59,505 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/548e16d4_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:05:59,521 - INFO - 

W_DFY1VDZWR7NBDLJV02G2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:05:59,522 - INFO - 

✓ Saved result: output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 14:05:59,831 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ed3c3359_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:06:00,198 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '373cc856-7f6f-45bc-8b12-c94d2efdf344', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:36:00 GMT', 'content-type': 'application/json', 'content-length': '1159', 'connection': 'keep-alive', 'x-amzn-requestid': '373cc856-7f6f-45bc-8b12-c94d2efdf344'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1 provided. Determine doc type. The content shows "Inspection Certificate" header and text: "This is to certify that the shipment transported on the referenced Pro number was measured and weighed..." It includes weight, dimensions, etc. It seems like a Weight and Inspection Certificate. Keywords: "Inspection Certificate", weight, dimensions. According to definitions: weight_and_inspection_cert – certificate confirming weight. Also inspection_cert – non-weight inspection. So choose weight_and_inspection_cert.\n\nCheck nmfc_cert conditions: must have keywords like "Inspected against original" etc. Not present. So weight_and_inspection_cert.\n\nThus output an array with one object: page_no: 1, doc_type: "weight_and_inspection_cert".\n\nWe must call the function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2823, 'outputTokens': 182, 'totalTokens': 3005}, 'metrics': {'latencyMs': 1079}}
2025-09-24 14:06:00,198 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dbe60f03_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:06:00,224 - INFO - Page 2: Extracted 540 characters, 29 lines from c7acc745_NMFC_OR9EL08KIKNQPZ3UV3HH_4b47e785_page_002.pdf
2025-09-24 14:06:00,224 - INFO - Successfully processed page 2
2025-09-24 14:06:00,224 - INFO - 

W_A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:06:00,225 - INFO - 

✓ Saved result: output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:06:00,525 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dbe60f03_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:06:00,977 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8bf7b8dd-4da2-4126-afb7-3bd34425e98d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:36:00 GMT', 'content-type': 'application/json', 'content-length': '2176', 'connection': 'keep-alive', 'x-amzn-requestid': '8bf7b8dd-4da2-4126-afb7-3bd34425e98d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. Let\'s examine content: "Weight and Research Linear Certificate FREIGHT CERTIFIED ORIGINAL Linear Certification We certify that the above shipment has been measured and that the linear feet as stated is the true and accurate length of the shipment W&R # ... Date of Correction: 11/04/22 ... Linear Feet: 15.0 ... Description: SKIDS ... Length 40 X 36 X 24 X 9 ... 180.00 ... 7294 / Total Cu Ft ... Density 40.52". This looks like a "Weight and Inspection Certificate" or "Weight and Inspection Certificate"? But the header says "Linear Certificate" and "Weight and Research" "Linear Certificate". It mentions "Linear feet", "Length, Width, Height, Units, Cu Ft". It also has "W&R #" which might stand for "Weight & Research"? The keywords: "Weight and Research" maybe "Weight and Inspection Certificate"? There\'s "W&R" maybe "Weight and Research" but the classification list: "weight_and_inspection_cert" includes "Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate". The page also says "CERTIFIED ORIGINAL" and "Linear Certification". The key field: "W&R #" seems like "Weight & Research" maybe stands for "Weight & Research"? The presence of "Linear Certificate" and "Weight and Research" suggests it\'s a weight and inspection certificate. Does it include "W&I"? There\'s no explicit "W&I". But "Weight and Research" could be akin to "Weight & Research" which might be a certificate. So likely "weight_and_inspection_cert". However, it might also be "inspection_cert" if no weight. But it includes weight? It lists "Pounds 7294". So weight given. So it\'s weight and inspection certificate.\n\nTherefore assign doc_type: weight_and_inspection_cert.\n\nReturn JSON via tool call.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2637, 'outputTokens': 416, 'totalTokens': 3053}, 'metrics': {'latencyMs': 1938}}
2025-09-24 14:06:00,977 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dbfb9dda_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:06:00,989 - INFO - 

W_WRKSHW76B3QUG47QWR75.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:06:00,989 - INFO - 

✓ Saved result: output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 14:06:01,024 - INFO - Page 1: Extracted 1120 characters, 87 lines from c7acc745_NMFC_OR9EL08KIKNQPZ3UV3HH_4b47e785_page_001.pdf
2025-09-24 14:06:01,024 - INFO - Successfully processed page 1
2025-09-24 14:06:01,024 - INFO - Combined 2 pages into final text
2025-09-24 14:06:01,024 - INFO - Text validation for c7acc745_NMFC_OR9EL08KIKNQPZ3UV3HH: 1696 characters, 2 pages
2025-09-24 14:06:01,025 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:06:01,025 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:06:01,329 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dbfb9dda_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:06:01,795 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ca6fb62d-2338-4462-845b-26a1a9ee382a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:36:01 GMT', 'content-type': 'application/json', 'content-length': '1379', 'connection': 'keep-alive', 'x-amzn-requestid': 'ca6fb62d-2338-4462-845b-26a1a9ee382a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content shows "ABF DIMENSION/CUBE Freight CERTIFICATE" and "Freight Certificate" with pronumber, trailer no, shipper, total weight, dimensions. This looks like a freight certificate, possibly a weight and inspection certificate? The definition: weight_and_inspection_cert requires weight and inspection certificate. This looks like a freight certificate that includes weight and dimensions. The keywords: "Total Weight" indicates weight. Also "Total Rated Dimensions". The header: "Freight CERTIFICATE". That matches weight and inspection certificate? The description of weight_and_inspection_cert: "Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". This matches. Also the specification says if it includes weight and inspection, classify as weight_and_inspection_cert. So doc_type should be weight_and_inspection_cert.\n\nWe return an array of documents: only one page, page_no: 1, doc_type: "weight_and_inspection_cert".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2610, 'outputTokens': 227, 'totalTokens': 2837}, 'metrics': {'latencyMs': 1182}}
2025-09-24 14:06:01,796 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/df6d32b7_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:06:01,815 - INFO - 

W_XCJLXZK140FUS8020ZAG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:06:01,815 - INFO - 

✓ Saved result: output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 14:06:02,148 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/df6d32b7_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:06:04,215 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'bf6add3e-60ad-4dbf-9c6e-df9187ec2d99', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:36:04 GMT', 'content-type': 'application/json', 'content-length': '2246', 'connection': 'keep-alive', 'x-amzn-requestid': 'bf6add3e-60ad-4dbf-9c6e-df9187ec2d99'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Page 1 and page 2. The document appears to be a "WEIGHT & RESEARCH CERTIFICATE" (probably "Weight and Inspection Certificate"?). The definition: weight_and_inspection_cert: certificate/report issued by authority confirming weight, with weight. nmfc_cert is a special. They mention "WEIGHT & RESEARCH CERTIFICATE" but likely intended as "Weight and Research Certificate" maybe a type of weight and inspection certificate. For classification, we should see if it\'s weight_and_inspection_cert or nmfc_cert. It says "WEIGHT & RESEARCH CERTIFICATE" and contains weight details, but also maybe "W & R" codes. Not sure. The instruction says: "If it has weight & inspection certificate with weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise inspection_cert." Also nmfc_cert is a correction notice or weight & inspection certificate but with certain keywords like "Original and inspection" or "Corrected class". This doc has "W&R ISSUED" and "W&R CODES". Might stand for "Weight & Research". So likely weight_and_inspection_cert. There\'s no mention of NMFC codes. So assign weight_and_inspection_cert.\n\nWe need to check continuation: Are both pages part of same document? Yes. The pages are same header. They likely continue. Page 2 has "continued"? No. But page 2 starts with same header. The instructions: If page is continuation, assign same doc_type as previous page. But we need to determine if page 2 is a continuation of page 1. It has same header and content indicates more details. It is likely continuation. So we classify both pages as weight_and_inspection_cert.\n\nThus output:\n\n{\n  documents: [\n    {page_no: 1, doc_type: "weight_and_inspection_cert"},\n    {page_no: 2, doc_type: "weight_and_inspection_cert"}\n  ]\n}\n\nMake sure to call tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3121, 'outputTokens': 445, 'totalTokens': 3566}, 'metrics': {'latencyMs': 1921}}
2025-09-24 14:06:04,215 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/c7acc745_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:06:04,245 - INFO - 

NMFC_OR9EL08KIKNQPZ3UV3HH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:06:04,245 - INFO - 

✓ Saved result: output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 14:06:04,543 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/c7acc745_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:06:04,544 - INFO - 
📊 Processing Summary:
2025-09-24 14:06:04,544 - INFO -    Total files: 13
2025-09-24 14:06:04,544 - INFO -    Successful: 13
2025-09-24 14:06:04,544 - INFO -    Failed: 0
2025-09-24 14:06:04,545 - INFO -    Duration: 24.41 seconds
2025-09-24 14:06:04,545 - INFO -    Output directory: output
2025-09-24 14:06:04,545 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:06:04,545 - INFO -    📄 I_QHD3LC0DU6S8O2YVVS60.pdf: {"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}
2025-09-24 14:06:04,545 - INFO -    📄 NMFC_DH0JZ2JWDGRHD26BX74C.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}
2025-09-24 14:06:04,545 - INFO -    📄 NMFC_NJ4WSZ8BUQAW48V6403N.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"},{"page_no":3,"doc_type":"nmfc_cert"}]}
2025-09-24 14:06:04,545 - INFO -    📄 NMFC_OR9EL08KIKNQPZ3UV3HH.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:06:04,545 - INFO -    📄 NMFC_R1V0MO844PBLWNEAUETU.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:06:04,545 - INFO -    📄 NMFC_RUDVGETVRZO7XX6YNW7I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:06:04,545 - INFO -    📄 W_A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:06:04,545 - INFO -    📄 W_DCY7SLNMWUXIENOREHQF.pdf: {"documents":[{"page_no":1,"doc_type":"scale_ticket"}]}
2025-09-24 14:06:04,545 - INFO -    📄 W_DFY1VDZWR7NBDLJV02G2.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:06:04,546 - INFO -    📄 W_HFPAXYL947DH59AB12FL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:06:04,546 - INFO -    📄 W_K9VSARJOKAIZHNJ5RBDT.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:06:04,546 - INFO -    📄 W_WRKSHW76B3QUG47QWR75.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:06:04,546 - INFO -    📄 W_XCJLXZK140FUS8020ZAG.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:06:04,546 - INFO - 
============================================================================================================================================
2025-09-24 14:06:04,547 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:06:04,547 - INFO - ============================================================================================================================================
2025-09-24 14:06:04,547 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:06:04,547 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:06:04,547 - INFO - I_QHD3LC0DU6S8O2YVVS60.pdf                         1      inspection_cert      run1_I_QHD3LC0DU6S8O2YVVS60.json                  
2025-09-24 14:06:04,547 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:06:04,547 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 14:06:04,547 - INFO - 
2025-09-24 14:06:04,547 - INFO - NMFC_DH0JZ2JWDGRHD26BX74C.pdf                      1      nmfc_cert            run1_NMFC_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 14:06:04,547 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:06:04,547 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 14:06:04,547 - INFO - 
2025-09-24 14:06:04,547 - INFO - NMFC_DH0JZ2JWDGRHD26BX74C.pdf                      2      nmfc_cert            run1_NMFC_DH0JZ2JWDGRHD26BX74C.json               
2025-09-24 14:06:04,547 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:06:04,547 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 14:06:04,547 - INFO - 
2025-09-24 14:06:04,547 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      1      nmfc_cert            run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 14:06:04,547 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:06:04,548 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:06:04,548 - INFO - 
2025-09-24 14:06:04,548 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      2      nmfc_cert            run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 14:06:04,548 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:06:04,548 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:06:04,548 - INFO - 
2025-09-24 14:06:04,548 - INFO - NMFC_NJ4WSZ8BUQAW48V6403N.pdf                      3      nmfc_cert            run1_NMFC_NJ4WSZ8BUQAW48V6403N.json               
2025-09-24 14:06:04,548 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:06:04,548 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:06:04,548 - INFO - 
2025-09-24 14:06:04,548 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      1      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 14:06:04,548 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:06:04,548 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 14:06:04,548 - INFO - 
2025-09-24 14:06:04,548 - INFO - NMFC_OR9EL08KIKNQPZ3UV3HH.pdf                      2      weight_and_inspect... run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json               
2025-09-24 14:06:04,548 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:06:04,548 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 14:06:04,548 - INFO - 
2025-09-24 14:06:04,548 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      1      weight_and_inspect... run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 14:06:04,548 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:06:04,548 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 14:06:04,548 - INFO - 
2025-09-24 14:06:04,548 - INFO - NMFC_R1V0MO844PBLWNEAUETU.pdf                      2      weight_and_inspect... run1_NMFC_R1V0MO844PBLWNEAUETU.json               
2025-09-24 14:06:04,548 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:06:04,548 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 14:06:04,548 - INFO - 
2025-09-24 14:06:04,549 - INFO - NMFC_RUDVGETVRZO7XX6YNW7I.pdf                      1      nmfc_cert            run1_NMFC_RUDVGETVRZO7XX6YNW7I.json               
2025-09-24 14:06:04,549 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:06:04,549 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 14:06:04,549 - INFO - 
2025-09-24 14:06:04,549 - INFO - W_A34CDFDJ66EDOZEKZWJL.pdf                         1      weight_and_inspect... run1_W_A34CDFDJ66EDOZEKZWJL.json                  
2025-09-24 14:06:04,549 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:06:04,549 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:06:04,549 - INFO - 
2025-09-24 14:06:04,549 - INFO - W_DCY7SLNMWUXIENOREHQF.pdf                         1      scale_ticket         run1_W_DCY7SLNMWUXIENOREHQF.json                  
2025-09-24 14:06:04,549 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:06:04,549 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 14:06:04,549 - INFO - 
2025-09-24 14:06:04,549 - INFO - W_DFY1VDZWR7NBDLJV02G2.pdf                         1      weight_and_inspect... run1_W_DFY1VDZWR7NBDLJV02G2.json                  
2025-09-24 14:06:04,549 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:06:04,549 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 14:06:04,549 - INFO - 
2025-09-24 14:06:04,549 - INFO - W_HFPAXYL947DH59AB12FL.pdf                         1      weight_and_inspect... run1_W_HFPAXYL947DH59AB12FL.json                  
2025-09-24 14:06:04,549 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:06:04,549 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 14:06:04,549 - INFO - 
2025-09-24 14:06:04,549 - INFO - W_K9VSARJOKAIZHNJ5RBDT.pdf                         1      weight_and_inspect... run1_W_K9VSARJOKAIZHNJ5RBDT.json                  
2025-09-24 14:06:04,549 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:06:04,549 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 14:06:04,549 - INFO - 
2025-09-24 14:06:04,549 - INFO - W_WRKSHW76B3QUG47QWR75.pdf                         1      weight_and_inspect... run1_W_WRKSHW76B3QUG47QWR75.json                  
2025-09-24 14:06:04,549 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:06:04,549 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 14:06:04,549 - INFO - 
2025-09-24 14:06:04,549 - INFO - W_XCJLXZK140FUS8020ZAG.pdf                         1      weight_and_inspect... run1_W_XCJLXZK140FUS8020ZAG.json                  
2025-09-24 14:06:04,549 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:06:04,549 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 14:06:04,549 - INFO - 
2025-09-24 14:06:04,549 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:06:04,549 - INFO - Total entries: 18
2025-09-24 14:06:04,549 - INFO - ============================================================================================================================================
2025-09-24 14:06:04,549 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:06:04,549 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:06:04,549 - INFO -   1. I_QHD3LC0DU6S8O2YVVS60.pdf          Page 1   → inspection_cert | run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 14:06:04,549 - INFO -   2. NMFC_DH0JZ2JWDGRHD26BX74C.pdf       Page 1   → nmfc_cert       | run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 14:06:04,549 - INFO -   3. NMFC_DH0JZ2JWDGRHD26BX74C.pdf       Page 2   → nmfc_cert       | run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 14:06:04,549 - INFO -   4. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 1   → nmfc_cert       | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:06:04,549 - INFO -   5. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 2   → nmfc_cert       | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:06:04,549 - INFO -   6. NMFC_NJ4WSZ8BUQAW48V6403N.pdf       Page 3   → nmfc_cert       | run1_NMFC_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:06:04,549 - INFO -   7. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 14:06:04,550 - INFO -   8. NMFC_OR9EL08KIKNQPZ3UV3HH.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 14:06:04,550 - INFO -   9. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 1   → weight_and_inspection_cert | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 14:06:04,550 - INFO -  10. NMFC_R1V0MO844PBLWNEAUETU.pdf       Page 2   → weight_and_inspection_cert | run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 14:06:04,550 - INFO -  11. NMFC_RUDVGETVRZO7XX6YNW7I.pdf       Page 1   → nmfc_cert       | run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 14:06:04,550 - INFO -  12. W_A34CDFDJ66EDOZEKZWJL.pdf          Page 1   → weight_and_inspection_cert | run1_W_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:06:04,550 - INFO -  13. W_DCY7SLNMWUXIENOREHQF.pdf          Page 1   → scale_ticket    | run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 14:06:04,550 - INFO -  14. W_DFY1VDZWR7NBDLJV02G2.pdf          Page 1   → weight_and_inspection_cert | run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 14:06:04,550 - INFO -  15. W_HFPAXYL947DH59AB12FL.pdf          Page 1   → weight_and_inspection_cert | run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 14:06:04,550 - INFO -  16. W_K9VSARJOKAIZHNJ5RBDT.pdf          Page 1   → weight_and_inspection_cert | run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 14:06:04,550 - INFO -  17. W_WRKSHW76B3QUG47QWR75.pdf          Page 1   → weight_and_inspection_cert | run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 14:06:04,550 - INFO -  18. W_XCJLXZK140FUS8020ZAG.pdf          Page 1   → weight_and_inspection_cert | run1_W_XCJLXZK140FUS8020ZAG.json
2025-09-24 14:06:04,550 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:06:04,550 - INFO - 
✅ Test completed: {'total_files': 13, 'processed': 13, 'failed': 0, 'errors': [], 'duration_seconds': 24.414405, 'processed_files': [{'filename': 'I_QHD3LC0DU6S8O2YVVS60.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf'}, {'filename': 'NMFC_DH0JZ2JWDGRHD26BX74C.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf'}, {'filename': 'NMFC_NJ4WSZ8BUQAW48V6403N.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'nmfc_cert'}, {'page_no': 3, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf'}, {'filename': 'NMFC_OR9EL08KIKNQPZ3UV3HH.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf'}, {'filename': 'NMFC_R1V0MO844PBLWNEAUETU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf'}, {'filename': 'NMFC_RUDVGETVRZO7XX6YNW7I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf'}, {'filename': 'W_A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf'}, {'filename': 'W_DCY7SLNMWUXIENOREHQF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'scale_ticket'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf'}, {'filename': 'W_DFY1VDZWR7NBDLJV02G2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf'}, {'filename': 'W_HFPAXYL947DH59AB12FL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf'}, {'filename': 'W_K9VSARJOKAIZHNJ5RBDT.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf'}, {'filename': 'W_WRKSHW76B3QUG47QWR75.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf'}, {'filename': 'W_XCJLXZK140FUS8020ZAG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf'}]}
