2025-09-24 13:46:13,902 - INFO - Logging initialized. Log file: logs/test_classification_20250924_134613.log
2025-09-24 13:46:13,903 - INFO - 📁 Found 9 files to process
2025-09-24 13:46:13,903 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 13:46:13,903 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 13:46:13,903 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 13:46:13,903 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 13:46:13,903 - INFO - ⬆️ [13:46:13] Uploading: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:46:15,490 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf -> s3://document-extraction-logistically/temp/04107562_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:46:15,491 - INFO - 🔍 [13:46:15] Starting classification: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:46:15,491 - INFO - ⬆️ [13:46:15] Uploading: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:46:15,498 - INFO - Initializing TextractProcessor...
2025-09-24 13:46:15,514 - INFO - Initializing BedrockProcessor...
2025-09-24 13:46:15,523 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/04107562_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:46:15,524 - INFO - Processing PDF from S3...
2025-09-24 13:46:15,524 - INFO - Downloading PDF from S3 to /tmp/tmpewwny91a/04107562_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:46:16,101 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf -> s3://document-extraction-logistically/temp/e79697eb_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:46:16,102 - INFO - 🔍 [13:46:16] Starting classification: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:46:16,102 - INFO - ⬆️ [13:46:16] Uploading: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:46:16,104 - INFO - Initializing TextractProcessor...
2025-09-24 13:46:16,120 - INFO - Initializing BedrockProcessor...
2025-09-24 13:46:16,124 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e79697eb_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:46:16,125 - INFO - Processing PDF from S3...
2025-09-24 13:46:16,125 - INFO - Downloading PDF from S3 to /tmp/tmp9rigrg0x/e79697eb_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:46:17,027 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:46:17,028 - INFO - Splitting PDF into individual pages...
2025-09-24 13:46:17,030 - INFO - Splitting PDF 04107562_B3SIRREC9IAVZOJVDQSN into 1 pages
2025-09-24 13:46:17,039 - INFO - Split PDF into 1 pages
2025-09-24 13:46:17,039 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:46:17,040 - INFO - Expected pages: [1]
2025-09-24 13:46:17,124 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf -> s3://document-extraction-logistically/temp/ba51f149_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:46:17,125 - INFO - 🔍 [13:46:17] Starting classification: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:46:17,125 - INFO - ⬆️ [13:46:17] Uploading: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:46:17,126 - INFO - Initializing TextractProcessor...
2025-09-24 13:46:17,135 - INFO - Initializing BedrockProcessor...
2025-09-24 13:46:17,137 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ba51f149_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:46:17,137 - INFO - Processing PDF from S3...
2025-09-24 13:46:17,137 - INFO - Downloading PDF from S3 to /tmp/tmpxhdyxqxz/ba51f149_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:46:17,846 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:46:17,847 - INFO - Splitting PDF into individual pages...
2025-09-24 13:46:17,848 - INFO - Splitting PDF e79697eb_CUF54EHGMLQ57HR93DRB into 1 pages
2025-09-24 13:46:17,853 - INFO - Split PDF into 1 pages
2025-09-24 13:46:17,853 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:46:17,853 - INFO - Expected pages: [1]
2025-09-24 13:46:18,491 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf -> s3://document-extraction-logistically/temp/63a7faab_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:46:18,491 - INFO - 🔍 [13:46:18] Starting classification: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:46:18,491 - INFO - ⬆️ [13:46:18] Uploading: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:46:18,492 - INFO - Initializing TextractProcessor...
2025-09-24 13:46:18,505 - INFO - Initializing BedrockProcessor...
2025-09-24 13:46:18,508 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/63a7faab_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:46:18,508 - INFO - Processing PDF from S3...
2025-09-24 13:46:18,509 - INFO - Downloading PDF from S3 to /tmp/tmp9rkdgx0e/63a7faab_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:46:18,734 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:46:18,734 - INFO - Splitting PDF into individual pages...
2025-09-24 13:46:18,736 - INFO - Splitting PDF ba51f149_FYQQGIW8Z9DSAPCL0S9G into 1 pages
2025-09-24 13:46:18,738 - INFO - Split PDF into 1 pages
2025-09-24 13:46:18,738 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:46:18,738 - INFO - Expected pages: [1]
2025-09-24 13:46:19,145 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf -> s3://document-extraction-logistically/temp/025de700_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:46:19,145 - INFO - 🔍 [13:46:19] Starting classification: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:46:19,146 - INFO - Initializing TextractProcessor...
2025-09-24 13:46:19,146 - INFO - ⬆️ [13:46:19] Uploading: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:46:19,155 - INFO - Initializing BedrockProcessor...
2025-09-24 13:46:19,157 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/025de700_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:46:19,157 - INFO - Processing PDF from S3...
2025-09-24 13:46:19,157 - INFO - Downloading PDF from S3 to /tmp/tmp5stdg5ph/025de700_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:46:20,123 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf -> s3://document-extraction-logistically/temp/ed2093c3_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:46:20,124 - INFO - 🔍 [13:46:20] Starting classification: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:46:20,124 - INFO - ⬆️ [13:46:20] Uploading: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:46:20,125 - INFO - Initializing TextractProcessor...
2025-09-24 13:46:20,138 - INFO - Initializing BedrockProcessor...
2025-09-24 13:46:20,141 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ed2093c3_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:46:20,141 - INFO - Processing PDF from S3...
2025-09-24 13:46:20,142 - INFO - Downloading PDF from S3 to /tmp/tmp_jbfcy0w/ed2093c3_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:46:20,766 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf -> s3://document-extraction-logistically/temp/9e8ea504_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:46:20,766 - INFO - 🔍 [13:46:20] Starting classification: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:46:20,767 - INFO - ⬆️ [13:46:20] Uploading: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:46:20,770 - INFO - Initializing TextractProcessor...
2025-09-24 13:46:20,786 - INFO - Initializing BedrockProcessor...
2025-09-24 13:46:20,837 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9e8ea504_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:46:20,838 - INFO - Processing PDF from S3...
2025-09-24 13:46:20,838 - INFO - Downloading PDF from S3 to /tmp/tmproja32gs/9e8ea504_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:46:21,063 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 13:46:21,063 - INFO - Splitting PDF into individual pages...
2025-09-24 13:46:21,064 - INFO - Splitting PDF 63a7faab_IZTBXFPGXBFH3DV900G4 into 1 pages
2025-09-24 13:46:21,065 - INFO - Split PDF into 1 pages
2025-09-24 13:46:21,065 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:46:21,065 - INFO - Expected pages: [1]
2025-09-24 13:46:21,207 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:46:21,207 - INFO - Splitting PDF into individual pages...
2025-09-24 13:46:21,208 - INFO - Splitting PDF 025de700_MR6ONA8GK6HN1LCZHEX3 into 1 pages
2025-09-24 13:46:21,210 - INFO - Split PDF into 1 pages
2025-09-24 13:46:21,210 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:46:21,210 - INFO - Expected pages: [1]
2025-09-24 13:46:21,357 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf -> s3://document-extraction-logistically/temp/dfd02459_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:46:21,357 - INFO - 🔍 [13:46:21] Starting classification: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:46:21,358 - INFO - ⬆️ [13:46:21] Uploading: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:46:21,358 - INFO - Initializing TextractProcessor...
2025-09-24 13:46:21,374 - INFO - Initializing BedrockProcessor...
2025-09-24 13:46:21,377 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/dfd02459_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:46:21,378 - INFO - Processing PDF from S3...
2025-09-24 13:46:21,378 - INFO - Downloading PDF from S3 to /tmp/tmp9nm_9ryy/dfd02459_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:46:21,713 - INFO - Page 1: Extracted 1255 characters, 79 lines from e79697eb_CUF54EHGMLQ57HR93DRB_56fb9c15_page_001.pdf
2025-09-24 13:46:21,714 - INFO - Successfully processed page 1
2025-09-24 13:46:21,714 - INFO - Combined 1 pages into final text
2025-09-24 13:46:21,714 - INFO - Text validation for e79697eb_CUF54EHGMLQ57HR93DRB: 1272 characters, 1 pages
2025-09-24 13:46:21,714 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:46:21,714 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:46:21,816 - INFO - Page 1: Extracted 1454 characters, 85 lines from 04107562_B3SIRREC9IAVZOJVDQSN_82d6fa31_page_001.pdf
2025-09-24 13:46:21,816 - INFO - Successfully processed page 1
2025-09-24 13:46:21,817 - INFO - Combined 1 pages into final text
2025-09-24 13:46:21,817 - INFO - Text validation for 04107562_B3SIRREC9IAVZOJVDQSN: 1471 characters, 1 pages
2025-09-24 13:46:21,817 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:46:21,817 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:46:21,987 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf -> s3://document-extraction-logistically/temp/0197ff29_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:46:21,987 - INFO - 🔍 [13:46:21] Starting classification: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:46:21,989 - INFO - Initializing TextractProcessor...
2025-09-24 13:46:21,999 - INFO - Initializing BedrockProcessor...
2025-09-24 13:46:22,004 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0197ff29_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:46:22,005 - INFO - Processing PDF from S3...
2025-09-24 13:46:22,005 - INFO - Downloading PDF from S3 to /tmp/tmpfi_49bk6/0197ff29_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:46:22,547 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 13:46:22,547 - INFO - Splitting PDF into individual pages...
2025-09-24 13:46:22,549 - INFO - Splitting PDF ed2093c3_PB67IAPSJB1DZWMDIE1H into 2 pages
2025-09-24 13:46:22,554 - INFO - Split PDF into 2 pages
2025-09-24 13:46:22,554 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:46:22,554 - INFO - Expected pages: [1, 2]
2025-09-24 13:46:22,577 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:46:22,578 - INFO - Splitting PDF into individual pages...
2025-09-24 13:46:22,580 - INFO - Splitting PDF dfd02459_U7BB1XSF3ASMIAE1MQ5I into 1 pages
2025-09-24 13:46:22,583 - INFO - Split PDF into 1 pages
2025-09-24 13:46:22,583 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:46:22,583 - INFO - Expected pages: [1]
2025-09-24 13:46:22,677 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:46:22,677 - INFO - Splitting PDF into individual pages...
2025-09-24 13:46:22,678 - INFO - Splitting PDF 9e8ea504_PEE2ZFMV7X0A0FL35G4G into 1 pages
2025-09-24 13:46:22,680 - INFO - Split PDF into 1 pages
2025-09-24 13:46:22,680 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:46:22,680 - INFO - Expected pages: [1]
2025-09-24 13:46:23,486 - INFO - Page 1: Extracted 1463 characters, 95 lines from ba51f149_FYQQGIW8Z9DSAPCL0S9G_65cbfffc_page_001.pdf
2025-09-24 13:46:23,486 - INFO - Successfully processed page 1
2025-09-24 13:46:23,486 - INFO - Combined 1 pages into final text
2025-09-24 13:46:23,486 - INFO - Text validation for ba51f149_FYQQGIW8Z9DSAPCL0S9G: 1480 characters, 1 pages
2025-09-24 13:46:23,487 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:46:23,487 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:46:24,299 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 13:46:24,299 - INFO - Splitting PDF into individual pages...
2025-09-24 13:46:24,300 - INFO - Splitting PDF 0197ff29_YL4HZPZH7D3B03FZZ5QK into 1 pages
2025-09-24 13:46:24,315 - INFO - Split PDF into 1 pages
2025-09-24 13:46:24,315 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:46:24,315 - INFO - Expected pages: [1]
2025-09-24 13:46:24,782 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '57f6c7a3-f348-4403-912e-c58b23fa3e9f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:16:24 GMT', 'content-type': 'application/json', 'content-length': '2005', 'connection': 'keep-alive', 'x-amzn-requestid': '57f6c7a3-f348-4403-912e-c58b23fa3e9f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to determine doc_type for each page. Only one page present. The content seems a weight and inspection certificate. Let\'s see keywords: "Weight and Research Program", "Weight and Research Section", "Weight and Research Program", "Weight and Research Section" suggests weight. Also "We certify ... inspected and/or reweighed". "DEclared value" but no HS code etc. So likely weight_and_inspection_cert according to definition. We must ensure not classified as nmfc_cert. The note: if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. The nmfc_cert keywords: "As described and As found or Original and inspection or Corrected class or Correction information or Declared item and actual item" and optional NMFC Code, class #. The text includes "Original Correction" at top. Also "As found upon Examination" present. It has "Original Correction". That might be a sign of correction. So could be nmfc_cert? The note: if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. The keywords that overlap: "original", "as found", "correction" etc. The page says "Original Correction" at top, "As found upon Examination". This seems like a correction certificate, so should be nmfc_cert. There\'s no NMFC code visible. But the presence of "Original Correction" indicates a correction notice. So we should classify as nmfc_cert. Alternatively, weight_and_inspection_cert requires weight mentioned. It has weight info. But the presence of correction indicates nmfc_cert. So answer: nmfc_cert.\n\nThus output: array of one object: page_no:1, doc_type:"nmfc_cert". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2930, 'outputTokens': 381, 'totalTokens': 3311}, 'metrics': {'latencyMs': 1825}}
2025-09-24 13:46:24,783 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e79697eb_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:46:24,807 - INFO - 

CUF54EHGMLQ57HR93DRB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:46:24,807 - INFO - 

✓ Saved result: output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:46:25,097 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e79697eb_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:46:25,507 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ce8955d6-245b-4b37-94f2-e3ef773cbeec', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:16:25 GMT', 'content-type': 'application/json', 'content-length': '2901', 'connection': 'keep-alive', 'x-amzn-requestid': 'ce8955d6-245b-4b37-94f2-e3ef773cbeec'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page given. We must examine keywords. Page includes "Weight and Research Program", "Weight and Research Section", "We certify that this shipment has been inspected and/or reweighed" "UPD CLASS PER NMFC 56840 ON BOL". It seems to be a weight and inspection certificate? Keywords: "Weight", "Inspection", "Certification". It says "We certify that this shipment has been inspected and/or reweighed" and "Weight and Research Section". This looks like a weight and inspection certificate. It also mentions NMFC code. According to definitions, weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I. There\'s also note: if it has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else weight_and_inspection_cert. Also note: rechecked or reweighted or reexamined certificate is also considered as nmfc_cert, not weight_and_inspection_cert.\n\nThis page appears to be a certification of weight. It might be a reweight? The text says "We certify that this shipment has been inspected and/or reweighed". That indicates possibly a reissue after reweigh. "UPD CLASS PER NMFC 56840 ON BOL" indicates NMFC code. The note: If it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. Keywords: \'As found upon Examination\', \'Updated class per NMFC\', \'Corrected class\' maybe. The text includes \'UPD CLASS PER NMFC 56840 ON BOL\', not \'Corrected\' but \'Updated class\'. nmfc_cert description: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. Keywords: As described and As found or Original and inspection or Corrected class or Correction information or Declared item and actual item. Also optional: NMFC Code, class #.\n\nThis page has "UPD CLASS PER NMFC". That might be considered updated class, a correction. The note says: if it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. So likely nmfc_cert.\n\nBut check whether this is a weight and inspection certificate or NMFC classification certificate. It has weight details. But the presence of "UPD CLASS PER NMFC" suggests NMFC classification certificate.\n\nThe difference: weight_and_inspection_cert: Certificate of weight. nmfc_cert: when correction is made. Since it says "UPD CLASS PER NMFC" i.e. updated class. So classification: nmfc_cert.\n\nThus output: page_no 1, doc_type: "nmfc_cert"\n\nWe must output tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2992, 'outputTokens': 590, 'totalTokens': 3582}, 'metrics': {'latencyMs': 2477}}
2025-09-24 13:46:25,507 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/04107562_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:46:25,529 - INFO - 

B3SIRREC9IAVZOJVDQSN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:46:25,530 - INFO - 

✓ Saved result: output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:46:25,619 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '81331031-3d61-4b46-8410-d58499996b00', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:16:25 GMT', 'content-type': 'application/json', 'content-length': '943', 'connection': 'keep-alive', 'x-amzn-requestid': '81331031-3d61-4b46-8410-d58499996b00'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. Determine doc_type. Keywords: "NMFC Classification Certificate" appears. That indicates nmfc_cert. There\'s also "INSPECTION DETAIL" but classification. The doc appears to be a NMFC Classification Certificate (as header). The description includes weight, density, etc. According to the guidelines, nmfc_cert is used for NMFC classification certificate or correction notice. The presence of "NMFC Classification Certificate" explicitly matches nmfc_cert. So doc_type: nmfc_cert. Page no 1. We output JSON array with one element. Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3047, 'outputTokens': 148, 'totalTokens': 3195}, 'metrics': {'latencyMs': 907}}
2025-09-24 13:46:25,619 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ba51f149_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:46:25,820 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/04107562_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:46:25,850 - INFO - 

FYQQGIW8Z9DSAPCL0S9G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:46:25,850 - INFO - 

✓ Saved result: output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:46:26,142 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ba51f149_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:46:26,245 - INFO - Page 1: Extracted 465 characters, 37 lines from 63a7faab_IZTBXFPGXBFH3DV900G4_bdc6be60_page_001.pdf
2025-09-24 13:46:26,246 - INFO - Successfully processed page 1
2025-09-24 13:46:26,247 - INFO - Combined 1 pages into final text
2025-09-24 13:46:26,247 - INFO - Text validation for 63a7faab_IZTBXFPGXBFH3DV900G4: 482 characters, 1 pages
2025-09-24 13:46:26,247 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:46:26,247 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:46:26,426 - INFO - Page 1: Extracted 500 characters, 76 lines from 025de700_MR6ONA8GK6HN1LCZHEX3_e6f44aba_page_001.pdf
2025-09-24 13:46:26,426 - INFO - Successfully processed page 1
2025-09-24 13:46:26,426 - INFO - Combined 1 pages into final text
2025-09-24 13:46:26,426 - INFO - Text validation for 025de700_MR6ONA8GK6HN1LCZHEX3: 517 characters, 1 pages
2025-09-24 13:46:26,426 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:46:26,426 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:46:26,737 - INFO - Page 1: Extracted 1119 characters, 62 lines from dfd02459_U7BB1XSF3ASMIAE1MQ5I_442c9196_page_001.pdf
2025-09-24 13:46:26,737 - INFO - Successfully processed page 1
2025-09-24 13:46:26,737 - INFO - Combined 1 pages into final text
2025-09-24 13:46:26,737 - INFO - Text validation for dfd02459_U7BB1XSF3ASMIAE1MQ5I: 1136 characters, 1 pages
2025-09-24 13:46:26,738 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:46:26,738 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:46:27,700 - INFO - Page 1: Extracted 477 characters, 75 lines from 9e8ea504_PEE2ZFMV7X0A0FL35G4G_9db4d35d_page_001.pdf
2025-09-24 13:46:27,701 - INFO - Successfully processed page 1
2025-09-24 13:46:27,701 - INFO - Combined 1 pages into final text
2025-09-24 13:46:27,702 - INFO - Text validation for 9e8ea504_PEE2ZFMV7X0A0FL35G4G: 494 characters, 1 pages
2025-09-24 13:46:27,702 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:46:27,702 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:46:27,947 - INFO - Page 2: Extracted 832 characters, 40 lines from ed2093c3_PB67IAPSJB1DZWMDIE1H_dff2ef83_page_002.pdf
2025-09-24 13:46:27,947 - INFO - Successfully processed page 2
2025-09-24 13:46:28,246 - INFO - Page 1: Extracted 1225 characters, 82 lines from ed2093c3_PB67IAPSJB1DZWMDIE1H_dff2ef83_page_001.pdf
2025-09-24 13:46:28,246 - INFO - Successfully processed page 1
2025-09-24 13:46:28,246 - INFO - Combined 2 pages into final text
2025-09-24 13:46:28,246 - INFO - Text validation for ed2093c3_PB67IAPSJB1DZWMDIE1H: 2093 characters, 2 pages
2025-09-24 13:46:28,247 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:46:28,247 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:46:28,283 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '10323c5f-b930-4103-81c6-1647f0c33a23', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:16:28 GMT', 'content-type': 'application/json', 'content-length': '731', 'connection': 'keep-alive', 'x-amzn-requestid': '10323c5f-b930-4103-81c6-1647f0c33a23'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It appears to be a "NMFC CLASSIFICATION CERTIFICATE" page. The document type for NMFC classification certificate is "nmfc_cert". The page has header "NMFC CLASSIFICATION CERTIFICATE". So we classify as "nmfc_cert". Output must be via the function call classify_logistics_doc_type with documents array containing page_no:1 and doc_type: "nmfc_cert".\n\nWe must output exactly that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2736, 'outputTokens': 110, 'totalTokens': 2846}, 'metrics': {'latencyMs': 715}}
2025-09-24 13:46:28,284 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/025de700_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:46:28,307 - INFO - 

MR6ONA8GK6HN1LCZHEX3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:46:28,307 - INFO - 

✓ Saved result: output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:46:28,541 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8bbd5739-f1ef-42e7-9fd9-a16f19efb4c4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:16:28 GMT', 'content-type': 'application/json', 'content-length': '1169', 'connection': 'keep-alive', 'x-amzn-requestid': '8bbd5739-f1ef-42e7-9fd9-a16f19efb4c4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It contains "Weight & Inspection Freight Class Certificate" and "CORRECT TO CLASS: 250" etc. It\'s a weight and inspection certificate. It seems to be a weight and inspection certificate. Not sure if it\'s nmfc_cert. Let\'s see definition: weight_and_inspection_cert: certificate confirming weight possibly with length, width, height. If rechecked or reweighted or reexamined, it is nmfc_cert. This page is not rechecked, just "Correct to Class". Might be weight_and_inspection_cert. No mention of NMFC corrections. So doc_type: weight_and_inspection_cert. The page number 3/3 indicates continuation of maybe earlier pages, but only this page provided. But we still classify. The tool requires array of objects with page_no and doc_type. So we produce JSON array with one object.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2641, 'outputTokens': 203, 'totalTokens': 2844}, 'metrics': {'latencyMs': 1098}}
2025-09-24 13:46:28,541 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/63a7faab_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:46:28,600 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/025de700_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:46:28,607 - INFO - 

IZTBXFPGXBFH3DV900G4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:46:28,607 - INFO - 

✓ Saved result: output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:46:28,905 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/63a7faab_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:46:28,974 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1ce50be3-cf98-4eea-807e-f914375ee5ab', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:16:28 GMT', 'content-type': 'application/json', 'content-length': '1318', 'connection': 'keep-alive', 'x-amzn-requestid': '1ce50be3-cf98-4eea-807e-f914375ee5ab'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify according to keywords. This page is an inspection certificate. It has "Inspection Certificate" title? Actually "Inspection Certificate" appears in header: "ABF Freight System, Inc. ABF Freight Inspection Certificate". Also contains "CORRECTED BILL INFORMATION", "CUBE", "TOTAL WEIGHT", "DENSITY". This looks like a Weight and Inspection Certificate. The description says "Inspection at an ABF terminal resulted in a correction". So it\'s a weight and inspection certificate. Under enumeration, weight_and_inspection_cert vs inspection_cert vs nmfc_cert. Since there\'s weight and cube measurement, with weight mentioned. So weight_and_inspection_cert. It has also NMFC code. It is not rechecked or reweighted. So classification weight_and_inspection_cert.\n\nWe must output JSON with pages array: each page object: page_no and doc_type. The tool expects: classify_logistics_doc_type with documents array.\n\nReturn exactly that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2844, 'outputTokens': 220, 'totalTokens': 3064}, 'metrics': {'latencyMs': 1080}}
2025-09-24 13:46:28,974 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/dfd02459_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:46:28,999 - INFO - 

U7BB1XSF3ASMIAE1MQ5I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:46:28,999 - INFO - 

✓ Saved result: output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:46:29,297 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/dfd02459_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:46:29,570 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd56b3804-ad8e-40dd-90bc-5a408f9856f7', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:16:29 GMT', 'content-type': 'application/json', 'content-length': '774', 'connection': 'keep-alive', 'x-amzn-requestid': 'd56b3804-ad8e-40dd-90bc-5a408f9856f7'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page, page1. The content: "NMFC CLASSIFICATION CERTIFICATE" header. It\'s a certificate. Based on the enum definitions: "nmfc_cert" is NMFC Classification Certificate or Correction notice. Keyword: NMFC Classification Certificate. So doc_type: nmfc_cert. The tool expects an array of objects, each with page_no and doc_type. So we output JSON with one object: page_no 1, doc_type "nmfc_cert". Use function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2727, 'outputTokens': 125, 'totalTokens': 2852}, 'metrics': {'latencyMs': 701}}
2025-09-24 13:46:29,570 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/9e8ea504_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:46:29,593 - INFO - 

PEE2ZFMV7X0A0FL35G4G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:46:29,593 - INFO - 

✓ Saved result: output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:46:29,882 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9e8ea504_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:46:29,935 - INFO - Page 1: Extracted 1248 characters, 88 lines from 0197ff29_YL4HZPZH7D3B03FZZ5QK_75f01e0b_page_001.pdf
2025-09-24 13:46:29,936 - INFO - Successfully processed page 1
2025-09-24 13:46:29,936 - INFO - Combined 1 pages into final text
2025-09-24 13:46:29,936 - INFO - Text validation for 0197ff29_YL4HZPZH7D3B03FZZ5QK: 1265 characters, 1 pages
2025-09-24 13:46:29,937 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:46:29,937 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:46:31,188 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'dcb87ee2-f6f2-46dd-a8a1-9011eb5da07e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:16:31 GMT', 'content-type': 'application/json', 'content-length': '2045', 'connection': 'keep-alive', 'x-amzn-requestid': 'dcb87ee2-f6f2-46dd-a8a1-9011eb5da07e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages of a document. We need to classify each page\'s document type. From content, page1 and page2 are a "Weight & Research Certificate" - which is likely a "weight_and_inspection_cert" or "inspection_cert"? The description says "WEIGHT & RESEARCH CERTIFICATE". Likely a weight and inspection certificate. The enum keywords: weight_and_inspection_cert is a certificate confirming weight possibly with measurement. The content includes weight, measurement, NMFC code. It also says "W&R APPROVED". So classification: weight_and_inspection_cert.\n\nWe need to check if page2 is a continuation of page1. Page2 starts with same header "SAIA MOTOR FREIGHT LINE, INC." etc. There\'s a line "W&R APPROVED" and "C W&R ISSUED". It\'s likely the same document (Weight & Research Certificate). The page header is same, and the content appears to be continuation. There\'s no "continued" but it\'s likely continuation. The instruction says: If a page is part of a multi-page single document: each page still gets same doc_type. So both pages should be weight_and_inspection_cert.\n\nWe need to output via tool call classify_logistics_doc_type with documents array containing two objects: page_no 1 doc_type weight_and_inspection_cert, page_no 2 doc_type weight_and_inspection_cert.\n\nWe must ensure format: JSON array? The function expects an object with key "documents" that is an array of objects each with page_no and doc_type. So:\n\n{\n  "documents": [\n    {"page_no": 1, "doc_type": "weight_and_inspection_cert"},\n    {"page_no": 2, "doc_type": "weight_and_inspection_cert"}\n  ]\n}\n\nReturn that via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3335, 'outputTokens': 403, 'totalTokens': 3738}, 'metrics': {'latencyMs': 1740}}
2025-09-24 13:46:31,188 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ed2093c3_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:46:31,213 - INFO - 

PB67IAPSJB1DZWMDIE1H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:46:31,213 - INFO - 

✓ Saved result: output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:46:31,504 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ed2093c3_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:46:33,652 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '58ce87fd-145a-4a05-8130-83424f635481', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:16:33 GMT', 'content-type': 'application/json', 'content-length': '2775', 'connection': 'keep-alive', 'x-amzn-requestid': '58ce87fd-145a-4a05-8130-83424f635481'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need classify. The content: header "WEIGHTS & INSPECTION CERTIFICATE". Also has "Correction Date: 06/20/2025" etc. It has description, NMFC, class, weight. It\'s a "Weights & Inspection Certificate". That matches "weight_and_inspection_cert". Also note the definition: weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height. Yes. So doc_type likely weight_and_inspection_cert. Check if it\'s nmfc_cert? It says "Correction Date". But nmfc_cert is for correction to weight_and_inspection_cert. The note: if weight_and_inspection_cert has keywords of nmfc_cert, classify as nmfc_cert. Let\'s see nmfc_cert keywords: "As described and As found or Original and inspection or Corrected class or Correction information or Declared item and actual item. Other optional NMFC Code, class #." This page has "Correction Date" and "Pro #". It says "Reclass Reweigh". Might be a correction? But the header says "Weights & Inspection Certificate". The presence of "Correction Date" indicates this is a corrected certificate. However the nmfc_cert is for NMFC classification certificate or correction notice. But does this page include NMFC classification? It includes NMFC codes. The note says: if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. The keywords for nmfc_cert include "Corrected class" or "Correction information". This page says "Correction Date: 06/20/2025". Also "Reclass Reweigh". That might indicate a correction. Also "Pro #". The nmfc_cert is a certificate for NMFC classification or correction notice. This page includes NMFC codes and possibly a corrected class. The weight_and_inspection_cert definition says it\'s a certificate confirming weight possibly with length, width, height. The nmfc_cert is a NMFC classification certificate or correction notice. This page might be a certification of weight and also classification. The presence of "Correction Date" strongly suggests it\'s a corrected certificate. According to note: If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. So we should classify as nmfc_cert. But the doc_type enum includes nmfc_cert. Yes. So answer: doc_type: nmfc_cert.\n\nWe need a JSON array of objects for each page. Only one page. Page_no 1. So output: [{page_no:1, doc_type:"nmfc_cert"}]. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2889, 'outputTokens': 557, 'totalTokens': 3446}, 'metrics': {'latencyMs': 2560}}
2025-09-24 13:46:33,652 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0197ff29_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:46:33,682 - INFO - 

YL4HZPZH7D3B03FZZ5QK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:46:33,682 - INFO - 

✓ Saved result: output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:46:33,978 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0197ff29_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:46:33,980 - INFO - 
📊 Processing Summary:
2025-09-24 13:46:33,980 - INFO -    Total files: 9
2025-09-24 13:46:33,980 - INFO -    Successful: 9
2025-09-24 13:46:33,980 - INFO -    Failed: 0
2025-09-24 13:46:33,980 - INFO -    Duration: 20.08 seconds
2025-09-24 13:46:33,980 - INFO -    Output directory: output
2025-09-24 13:46:33,980 - INFO - 
📋 Successfully Processed Files:
2025-09-24 13:46:33,980 - INFO -    📄 B3SIRREC9IAVZOJVDQSN.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:46:33,981 - INFO -    📄 CUF54EHGMLQ57HR93DRB.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:46:33,981 - INFO -    📄 FYQQGIW8Z9DSAPCL0S9G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:46:33,981 - INFO -    📄 IZTBXFPGXBFH3DV900G4.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:46:33,981 - INFO -    📄 MR6ONA8GK6HN1LCZHEX3.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:46:33,981 - INFO -    📄 PB67IAPSJB1DZWMDIE1H.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:46:33,981 - INFO -    📄 PEE2ZFMV7X0A0FL35G4G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:46:33,981 - INFO -    📄 U7BB1XSF3ASMIAE1MQ5I.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:46:33,981 - INFO -    📄 YL4HZPZH7D3B03FZZ5QK.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:46:33,982 - INFO - 
============================================================================================================================================
2025-09-24 13:46:33,982 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 13:46:33,982 - INFO - ============================================================================================================================================
2025-09-24 13:46:33,982 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 13:46:33,982 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:46:33,982 - INFO - B3SIRREC9IAVZOJVDQSN.pdf                           1      nmfc_cert            run1_B3SIRREC9IAVZOJVDQSN.json                    
2025-09-24 13:46:33,982 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:46:33,982 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:46:33,983 - INFO - 
2025-09-24 13:46:33,983 - INFO - CUF54EHGMLQ57HR93DRB.pdf                           1      nmfc_cert            run1_CUF54EHGMLQ57HR93DRB.json                    
2025-09-24 13:46:33,983 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:46:33,983 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:46:33,983 - INFO - 
2025-09-24 13:46:33,983 - INFO - FYQQGIW8Z9DSAPCL0S9G.pdf                           1      nmfc_cert            run1_FYQQGIW8Z9DSAPCL0S9G.json                    
2025-09-24 13:46:33,983 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:46:33,983 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:46:33,983 - INFO - 
2025-09-24 13:46:33,983 - INFO - IZTBXFPGXBFH3DV900G4.pdf                           1      weight_and_inspect... run1_IZTBXFPGXBFH3DV900G4.json                    
2025-09-24 13:46:33,983 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:46:33,983 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:46:33,983 - INFO - 
2025-09-24 13:46:33,983 - INFO - MR6ONA8GK6HN1LCZHEX3.pdf                           1      nmfc_cert            run1_MR6ONA8GK6HN1LCZHEX3.json                    
2025-09-24 13:46:33,984 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:46:33,984 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:46:33,984 - INFO - 
2025-09-24 13:46:33,984 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           1      weight_and_inspect... run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:46:33,984 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:46:33,984 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:46:33,984 - INFO - 
2025-09-24 13:46:33,984 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           2      weight_and_inspect... run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:46:33,984 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:46:33,984 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:46:33,984 - INFO - 
2025-09-24 13:46:33,984 - INFO - PEE2ZFMV7X0A0FL35G4G.pdf                           1      nmfc_cert            run1_PEE2ZFMV7X0A0FL35G4G.json                    
2025-09-24 13:46:33,984 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:46:33,984 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:46:33,984 - INFO - 
2025-09-24 13:46:33,984 - INFO - U7BB1XSF3ASMIAE1MQ5I.pdf                           1      weight_and_inspect... run1_U7BB1XSF3ASMIAE1MQ5I.json                    
2025-09-24 13:46:33,984 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:46:33,984 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:46:33,985 - INFO - 
2025-09-24 13:46:33,985 - INFO - YL4HZPZH7D3B03FZZ5QK.pdf                           1      nmfc_cert            run1_YL4HZPZH7D3B03FZZ5QK.json                    
2025-09-24 13:46:33,985 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:46:33,985 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:46:33,985 - INFO - 
2025-09-24 13:46:33,985 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:46:33,985 - INFO - Total entries: 10
2025-09-24 13:46:33,985 - INFO - ============================================================================================================================================
2025-09-24 13:46:33,985 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 13:46:33,985 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:46:33,985 - INFO -   1. B3SIRREC9IAVZOJVDQSN.pdf            Page 1   → nmfc_cert       | run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:46:33,985 - INFO -   2. CUF54EHGMLQ57HR93DRB.pdf            Page 1   → nmfc_cert       | run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:46:33,985 - INFO -   3. FYQQGIW8Z9DSAPCL0S9G.pdf            Page 1   → nmfc_cert       | run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:46:33,985 - INFO -   4. IZTBXFPGXBFH3DV900G4.pdf            Page 1   → weight_and_inspection_cert | run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:46:33,985 - INFO -   5. MR6ONA8GK6HN1LCZHEX3.pdf            Page 1   → nmfc_cert       | run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:46:33,985 - INFO -   6. PB67IAPSJB1DZWMDIE1H.pdf            Page 1   → weight_and_inspection_cert | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:46:33,985 - INFO -   7. PB67IAPSJB1DZWMDIE1H.pdf            Page 2   → weight_and_inspection_cert | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:46:33,985 - INFO -   8. PEE2ZFMV7X0A0FL35G4G.pdf            Page 1   → nmfc_cert       | run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:46:33,985 - INFO -   9. U7BB1XSF3ASMIAE1MQ5I.pdf            Page 1   → weight_and_inspection_cert | run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:46:33,985 - INFO -  10. YL4HZPZH7D3B03FZZ5QK.pdf            Page 1   → nmfc_cert       | run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:46:33,985 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:46:33,985 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 20.076933, 'processed_files': [{'filename': 'B3SIRREC9IAVZOJVDQSN.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf'}, {'filename': 'CUF54EHGMLQ57HR93DRB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf'}, {'filename': 'FYQQGIW8Z9DSAPCL0S9G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf'}, {'filename': 'IZTBXFPGXBFH3DV900G4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf'}, {'filename': 'MR6ONA8GK6HN1LCZHEX3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf'}, {'filename': 'PB67IAPSJB1DZWMDIE1H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}, {'page_no': 2, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf'}, {'filename': 'PEE2ZFMV7X0A0FL35G4G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf'}, {'filename': 'U7BB1XSF3ASMIAE1MQ5I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf'}, {'filename': 'YL4HZPZH7D3B03FZZ5QK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf'}]}
