2025-09-24 13:42:46,637 - INFO - Logging initialized. Log file: logs/test_classification_20250924_134246.log
2025-09-24 13:42:46,638 - INFO - 📁 Found 9 files to process
2025-09-24 13:42:46,638 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 13:42:46,638 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 13:42:46,638 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 13:42:46,638 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 13:42:46,638 - INFO - ⬆️ [13:42:46] Uploading: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:42:48,000 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf -> s3://document-extraction-logistically/temp/8821fd27_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:42:48,000 - INFO - 🔍 [13:42:48] Starting classification: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:42:48,001 - INFO - ⬆️ [13:42:48] Uploading: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:42:48,001 - INFO - Initializing TextractProcessor...
2025-09-24 13:42:48,012 - INFO - Initializing BedrockProcessor...
2025-09-24 13:42:48,017 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8821fd27_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:42:48,018 - INFO - Processing PDF from S3...
2025-09-24 13:42:48,018 - INFO - Downloading PDF from S3 to /tmp/tmpp64onq2m/8821fd27_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:42:48,634 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf -> s3://document-extraction-logistically/temp/53ff2fde_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:42:48,634 - INFO - 🔍 [13:42:48] Starting classification: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:42:48,635 - INFO - ⬆️ [13:42:48] Uploading: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:42:48,636 - INFO - Initializing TextractProcessor...
2025-09-24 13:42:48,652 - INFO - Initializing BedrockProcessor...
2025-09-24 13:42:48,656 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/53ff2fde_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:42:48,656 - INFO - Processing PDF from S3...
2025-09-24 13:42:48,656 - INFO - Downloading PDF from S3 to /tmp/tmpxx2tc6ym/53ff2fde_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:42:49,552 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf -> s3://document-extraction-logistically/temp/ada4121c_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:42:49,553 - INFO - 🔍 [13:42:49] Starting classification: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:42:49,553 - INFO - ⬆️ [13:42:49] Uploading: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:42:49,555 - INFO - Initializing TextractProcessor...
2025-09-24 13:42:49,572 - INFO - Initializing BedrockProcessor...
2025-09-24 13:42:49,581 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ada4121c_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:42:49,582 - INFO - Processing PDF from S3...
2025-09-24 13:42:49,583 - INFO - Downloading PDF from S3 to /tmp/tmp633kz5tc/ada4121c_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:42:49,875 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:42:49,876 - INFO - Splitting PDF into individual pages...
2025-09-24 13:42:49,876 - INFO - Splitting PDF 53ff2fde_CUF54EHGMLQ57HR93DRB into 1 pages
2025-09-24 13:42:49,878 - INFO - Split PDF into 1 pages
2025-09-24 13:42:49,878 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:42:49,878 - INFO - Expected pages: [1]
2025-09-24 13:42:50,255 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:42:50,255 - INFO - Splitting PDF into individual pages...
2025-09-24 13:42:50,256 - INFO - Splitting PDF 8821fd27_B3SIRREC9IAVZOJVDQSN into 1 pages
2025-09-24 13:42:50,258 - INFO - Split PDF into 1 pages
2025-09-24 13:42:50,258 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:42:50,258 - INFO - Expected pages: [1]
2025-09-24 13:42:50,704 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf -> s3://document-extraction-logistically/temp/321813fe_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:42:50,704 - INFO - 🔍 [13:42:50] Starting classification: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:42:50,704 - INFO - ⬆️ [13:42:50] Uploading: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:42:50,706 - INFO - Initializing TextractProcessor...
2025-09-24 13:42:50,719 - INFO - Initializing BedrockProcessor...
2025-09-24 13:42:50,724 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/321813fe_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:42:50,724 - INFO - Processing PDF from S3...
2025-09-24 13:42:50,724 - INFO - Downloading PDF from S3 to /tmp/tmpn1w3x71_/321813fe_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:42:51,015 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:42:51,015 - INFO - Splitting PDF into individual pages...
2025-09-24 13:42:51,016 - INFO - Splitting PDF ada4121c_FYQQGIW8Z9DSAPCL0S9G into 1 pages
2025-09-24 13:42:51,017 - INFO - Split PDF into 1 pages
2025-09-24 13:42:51,017 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:42:51,017 - INFO - Expected pages: [1]
2025-09-24 13:42:51,292 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf -> s3://document-extraction-logistically/temp/08b80cda_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:42:51,293 - INFO - 🔍 [13:42:51] Starting classification: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:42:51,293 - INFO - ⬆️ [13:42:51] Uploading: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:42:51,295 - INFO - Initializing TextractProcessor...
2025-09-24 13:42:51,305 - INFO - Initializing BedrockProcessor...
2025-09-24 13:42:51,307 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/08b80cda_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:42:51,307 - INFO - Processing PDF from S3...
2025-09-24 13:42:51,307 - INFO - Downloading PDF from S3 to /tmp/tmp6ou1o9ym/08b80cda_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:42:52,258 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf -> s3://document-extraction-logistically/temp/0fe3cb3d_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:42:52,258 - INFO - 🔍 [13:42:52] Starting classification: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:42:52,259 - INFO - ⬆️ [13:42:52] Uploading: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:42:52,259 - INFO - Initializing TextractProcessor...
2025-09-24 13:42:52,268 - INFO - Initializing BedrockProcessor...
2025-09-24 13:42:52,271 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0fe3cb3d_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:42:52,271 - INFO - Processing PDF from S3...
2025-09-24 13:42:52,271 - INFO - Downloading PDF from S3 to /tmp/tmp595m9xal/0fe3cb3d_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:42:52,741 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 13:42:52,742 - INFO - Splitting PDF into individual pages...
2025-09-24 13:42:52,743 - INFO - Splitting PDF 321813fe_IZTBXFPGXBFH3DV900G4 into 1 pages
2025-09-24 13:42:52,744 - INFO - Split PDF into 1 pages
2025-09-24 13:42:52,745 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:42:52,745 - INFO - Expected pages: [1]
2025-09-24 13:42:52,864 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf -> s3://document-extraction-logistically/temp/f95e5e5f_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:42:52,864 - INFO - 🔍 [13:42:52] Starting classification: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:42:52,865 - INFO - ⬆️ [13:42:52] Uploading: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:42:52,867 - INFO - Initializing TextractProcessor...
2025-09-24 13:42:52,918 - INFO - Initializing BedrockProcessor...
2025-09-24 13:42:52,920 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f95e5e5f_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:42:52,920 - INFO - Processing PDF from S3...
2025-09-24 13:42:52,920 - INFO - Downloading PDF from S3 to /tmp/tmpsk2hs6hr/f95e5e5f_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:42:52,981 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:42:52,981 - INFO - Splitting PDF into individual pages...
2025-09-24 13:42:52,983 - INFO - Splitting PDF 08b80cda_MR6ONA8GK6HN1LCZHEX3 into 1 pages
2025-09-24 13:42:52,984 - INFO - Split PDF into 1 pages
2025-09-24 13:42:52,984 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:42:52,984 - INFO - Expected pages: [1]
2025-09-24 13:42:53,448 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf -> s3://document-extraction-logistically/temp/677b4a5d_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:42:53,448 - INFO - 🔍 [13:42:53] Starting classification: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:42:53,449 - INFO - Initializing TextractProcessor...
2025-09-24 13:42:53,449 - INFO - ⬆️ [13:42:53] Uploading: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:42:53,460 - INFO - Initializing BedrockProcessor...
2025-09-24 13:42:53,468 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/677b4a5d_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:42:53,468 - INFO - Processing PDF from S3...
2025-09-24 13:42:53,469 - INFO - Downloading PDF from S3 to /tmp/tmpvbl15tpi/677b4a5d_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:42:53,957 - INFO - Page 1: Extracted 1255 characters, 79 lines from 53ff2fde_CUF54EHGMLQ57HR93DRB_e0dd5a68_page_001.pdf
2025-09-24 13:42:53,957 - INFO - Successfully processed page 1
2025-09-24 13:42:53,957 - INFO - Combined 1 pages into final text
2025-09-24 13:42:53,958 - INFO - Text validation for 53ff2fde_CUF54EHGMLQ57HR93DRB: 1272 characters, 1 pages
2025-09-24 13:42:53,958 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:42:53,958 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:42:54,062 - INFO - Page 1: Extracted 1454 characters, 85 lines from 8821fd27_B3SIRREC9IAVZOJVDQSN_985e0237_page_001.pdf
2025-09-24 13:42:54,062 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf -> s3://document-extraction-logistically/temp/8980afd1_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:42:54,062 - INFO - 🔍 [13:42:54] Starting classification: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:42:54,062 - INFO - Successfully processed page 1
2025-09-24 13:42:54,063 - INFO - Combined 1 pages into final text
2025-09-24 13:42:54,063 - INFO - Text validation for 8821fd27_B3SIRREC9IAVZOJVDQSN: 1471 characters, 1 pages
2025-09-24 13:42:54,064 - INFO - Initializing TextractProcessor...
2025-09-24 13:42:54,066 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:42:54,070 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:42:54,075 - INFO - Initializing BedrockProcessor...
2025-09-24 13:42:54,079 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8980afd1_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:42:54,079 - INFO - Processing PDF from S3...
2025-09-24 13:42:54,079 - INFO - Downloading PDF from S3 to /tmp/tmp3yoe0m00/8980afd1_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:42:54,595 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 13:42:54,595 - INFO - Splitting PDF into individual pages...
2025-09-24 13:42:54,596 - INFO - Splitting PDF 0fe3cb3d_PB67IAPSJB1DZWMDIE1H into 2 pages
2025-09-24 13:42:54,601 - INFO - Split PDF into 2 pages
2025-09-24 13:42:54,601 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:42:54,601 - INFO - Expected pages: [1, 2]
2025-09-24 13:42:54,655 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:42:54,655 - INFO - Splitting PDF into individual pages...
2025-09-24 13:42:54,656 - INFO - Splitting PDF f95e5e5f_PEE2ZFMV7X0A0FL35G4G into 1 pages
2025-09-24 13:42:54,656 - INFO - Split PDF into 1 pages
2025-09-24 13:42:54,656 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:42:54,656 - INFO - Expected pages: [1]
2025-09-24 13:42:54,661 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:42:54,661 - INFO - Splitting PDF into individual pages...
2025-09-24 13:42:54,662 - INFO - Splitting PDF 677b4a5d_U7BB1XSF3ASMIAE1MQ5I into 1 pages
2025-09-24 13:42:54,662 - INFO - Split PDF into 1 pages
2025-09-24 13:42:54,662 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:42:54,662 - INFO - Expected pages: [1]
2025-09-24 13:42:55,631 - INFO - Page 1: Extracted 1463 characters, 95 lines from ada4121c_FYQQGIW8Z9DSAPCL0S9G_61d052cb_page_001.pdf
2025-09-24 13:42:55,631 - INFO - Successfully processed page 1
2025-09-24 13:42:55,631 - INFO - Combined 1 pages into final text
2025-09-24 13:42:55,631 - INFO - Text validation for ada4121c_FYQQGIW8Z9DSAPCL0S9G: 1480 characters, 1 pages
2025-09-24 13:42:55,631 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:42:55,631 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:42:56,265 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 13:42:56,266 - INFO - Splitting PDF into individual pages...
2025-09-24 13:42:56,267 - INFO - Splitting PDF 8980afd1_YL4HZPZH7D3B03FZZ5QK into 1 pages
2025-09-24 13:42:56,285 - INFO - Split PDF into 1 pages
2025-09-24 13:42:56,286 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:42:56,286 - INFO - Expected pages: [1]
2025-09-24 13:42:56,519 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '91f6bea7-24ac-4fb3-8641-d720edaccdb3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:12:56 GMT', 'content-type': 'application/json', 'content-length': '1522', 'connection': 'keep-alive', 'x-amzn-requestid': '91f6bea7-24ac-4fb3-8641-d720edaccdb3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify this single page. It seems a weight and research program document. The header: "Weight and Research Program" "Weight and Research Section". It contains weight, declared value, "As found upon Examination". It does not seem like a commercial invoice (though declared value present). But the doc type list includes weight_and_inspection_cert. Keywords: "Weight and Research Section", "We certify that this shipment has been inspected and/or reweighed". That matches weight_and_inspection_cert. Also "As found upon Examination" indicates rechecked. But note rule: If weight_and_inspection_cert has keywords that are in nmfc_cert, classify as nmfc_cert else keep weight_and_inspection_cert. Does it have nmfc-cert keywords: "Correction", "Original", "Correction", "As found upon Examination" could be rechecked. The note says: rechecked or reweighted or reexamined certificate is also considered as nmfc_cert. The doc says "As found upon Examination". So likely nmfc_cert. But also indicates "Original Correction". The page shows "Original Correction". That indicates a correction. So likely nmfc_cert.\n\nThus page1 doc_type: nmfc_cert.\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2923, 'outputTokens': 269, 'totalTokens': 3192}, 'metrics': {'latencyMs': 1402}}
2025-09-24 13:42:56,520 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/53ff2fde_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:42:56,544 - INFO - 

CUF54EHGMLQ57HR93DRB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:42:56,544 - INFO - 

✓ Saved result: output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:42:56,821 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/53ff2fde_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:42:57,346 - INFO - Page 1: Extracted 500 characters, 76 lines from 08b80cda_MR6ONA8GK6HN1LCZHEX3_c0aeb0f7_page_001.pdf
2025-09-24 13:42:57,346 - INFO - Successfully processed page 1
2025-09-24 13:42:57,346 - INFO - Combined 1 pages into final text
2025-09-24 13:42:57,346 - INFO - Text validation for 08b80cda_MR6ONA8GK6HN1LCZHEX3: 517 characters, 1 pages
2025-09-24 13:42:57,347 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:42:57,347 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:42:57,378 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'df65e4be-ce50-4394-b4b2-fc1161e5f1d2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:12:57 GMT', 'content-type': 'application/json', 'content-length': '2520', 'connection': 'keep-alive', 'x-amzn-requestid': 'df65e4be-ce50-4394-b4b2-fc1161e5f1d2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. We need to detect doc type. This page has heading: "Weight and Research Program" and "Weight and Research Section". It seems like a weight and inspection certificate? It has "As found upon Examination" and "We certify that this shipment has been inspected and/or reweighed". Also "UPD CLASS PER NMFC 56840 ON BOL". There\'s mention of NMFC. The doc appears to be a "Weight and Inspection Certificate" or "nmfc_cert" maybe. According to note: weight_and_inspection_cert vs nmfc_cert: If the document has weight and inspection and has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else weight_and_inspection_cert. The note says: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert, else keep it as weight_and_inspection_cert." nmfc_cert keywords: "As described and As found or Original and inspection or Corrected class or Correction information" and optional "NMFC Code, class #". In this doc, there is "As found upon Examination" which is close to "As found". Also "UPD CLASS PER NMFC 56840 ON BOL". There\'s no correction notice though. But "As found upon Examination" might be considered as the keywords for nmfc_cert. The doc seems to be a weight and inspection certificate that includes NMFC correction. The note: "rechecked or reweighted or reexamined certificate is also considered as nmfc_cert, not weight_and_inspection_cert." This doc says "We certify that this shipment has been inspected and/or reweighed" and "As found upon Examination". That indicates a recheck. So likely nmfc_cert.\n\nAlso the heading "Weight and Research Program" could be a program for weight measurement. But the presence of NMFC code and "As found" indicates a correction? The note says if weight_and_inspection_cert has nmfc_cert keywords, classify as nmfc_cert.\n\nThus page1 doc_type: nmfc_cert.\n\nWe need to output JSON with array of documents. There\'s only one page, so array with one object: page_no 1, doc_type "nmfc_cert".\n\nWe must call the tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_Zr0bCwfSQ3qSg6QL5ilAqQ', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2985, 'outputTokens': 495, 'totalTokens': 3480}, 'metrics': {'latencyMs': 2141}}
2025-09-24 13:42:57,378 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8821fd27_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:42:57,413 - INFO - 

B3SIRREC9IAVZOJVDQSN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:42:57,414 - INFO - 

✓ Saved result: output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:42:57,631 - INFO - Page 1: Extracted 465 characters, 37 lines from 321813fe_IZTBXFPGXBFH3DV900G4_8b88c270_page_001.pdf
2025-09-24 13:42:57,631 - INFO - Successfully processed page 1
2025-09-24 13:42:57,632 - INFO - Combined 1 pages into final text
2025-09-24 13:42:57,632 - INFO - Text validation for 321813fe_IZTBXFPGXBFH3DV900G4: 482 characters, 1 pages
2025-09-24 13:42:57,632 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:42:57,632 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:42:57,699 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8821fd27_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:42:57,808 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd041c6a4-3b4b-4cc6-86d2-d49926b51835', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:12:57 GMT', 'content-type': 'application/json', 'content-length': '1215', 'connection': 'keep-alive', 'x-amzn-requestid': 'd041c6a4-3b4b-4cc6-86d2-d49926b51835'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page 1. The content: NMFC Classification Certificate. This looks like a certification for NMFC (National Motor Freight Classification). So doc_type likely is nmfc_cert. We need to see if it\'s weight_and_inspection_cert or nmfc_cert. The instructions: For weight_and_inspection_cert: if it has weight keywords and is a certificate of analysis etc. But this page says "NMFC Classification Certificate" and includes NMFC# 133300 S-3, class, description, weight, etc. It\'s not a weight-and-inspection certificate? The definition: nmfc_cert — NMFC Classification Certificate or Correction notice. So we classify as nmfc_cert. There\'s no mention of "rechecked" etc. So nmfc_cert is correct. Also it\'s a single page. Output: array of objects. Each object: page_no: 1, doc_type: "nmfc_cert". We must use the tool classify_logistics_doc_type. Provide JSON with array of documents.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3040, 'outputTokens': 223, 'totalTokens': 3263}, 'metrics': {'latencyMs': 1062}}
2025-09-24 13:42:57,808 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ada4121c_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:42:57,835 - INFO - 

FYQQGIW8Z9DSAPCL0S9G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:42:57,835 - INFO - 

✓ Saved result: output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:42:58,122 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ada4121c_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:42:58,520 - INFO - Page 1: Extracted 1119 characters, 62 lines from 677b4a5d_U7BB1XSF3ASMIAE1MQ5I_c40bb1e6_page_001.pdf
2025-09-24 13:42:58,520 - INFO - Successfully processed page 1
2025-09-24 13:42:58,520 - INFO - Combined 1 pages into final text
2025-09-24 13:42:58,520 - INFO - Text validation for 677b4a5d_U7BB1XSF3ASMIAE1MQ5I: 1136 characters, 1 pages
2025-09-24 13:42:58,520 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:42:58,520 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:42:59,055 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2684d4cc-f5be-4dff-9182-7f08e242b7f1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:12:58 GMT', 'content-type': 'application/json', 'content-length': '630', 'connection': 'keep-alive', 'x-amzn-requestid': '2684d4cc-f5be-4dff-9182-7f08e242b7f1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The content shows "NMFC CLASSIFICATION CERTIFICATE" and later "NMFCC". So this matches the definition of nmfc_cert. Also note that there\'s no weight and inspection cert? It is a classification certificate. So the doc_type is "nmfc_cert". There\'s no continuation. So output one object.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2729, 'outputTokens': 88, 'totalTokens': 2817}, 'metrics': {'latencyMs': 571}}
2025-09-24 13:42:59,056 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/08b80cda_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:42:59,083 - INFO - 

MR6ONA8GK6HN1LCZHEX3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:42:59,083 - INFO - 

✓ Saved result: output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:42:59,384 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/08b80cda_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:42:59,596 - INFO - Page 2: Extracted 832 characters, 40 lines from 0fe3cb3d_PB67IAPSJB1DZWMDIE1H_6c60a68f_page_002.pdf
2025-09-24 13:42:59,597 - INFO - Successfully processed page 2
2025-09-24 13:42:59,791 - INFO - Page 1: Extracted 477 characters, 75 lines from f95e5e5f_PEE2ZFMV7X0A0FL35G4G_27e5dedd_page_001.pdf
2025-09-24 13:42:59,791 - INFO - Successfully processed page 1
2025-09-24 13:42:59,791 - INFO - Combined 1 pages into final text
2025-09-24 13:42:59,791 - INFO - Text validation for f95e5e5f_PEE2ZFMV7X0A0FL35G4G: 494 characters, 1 pages
2025-09-24 13:42:59,792 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:42:59,792 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:43:00,000 - INFO - Page 1: Extracted 1225 characters, 82 lines from 0fe3cb3d_PB67IAPSJB1DZWMDIE1H_6c60a68f_page_001.pdf
2025-09-24 13:43:00,000 - INFO - Successfully processed page 1
2025-09-24 13:43:00,001 - INFO - Combined 2 pages into final text
2025-09-24 13:43:00,001 - INFO - Text validation for 0fe3cb3d_PB67IAPSJB1DZWMDIE1H: 2093 characters, 2 pages
2025-09-24 13:43:00,001 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:43:00,001 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:43:01,413 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6c920a91-ed25-403b-950c-e1151e356313', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:13:01 GMT', 'content-type': 'application/json', 'content-length': '2107', 'connection': 'keep-alive', 'x-amzn-requestid': '6c920a91-ed25-403b-950c-e1151e356313'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page, page1. Need to classify. The page content: "Inspection Certificate" header. Looks like a NMFC Inspection Certificate? It includes NMFC 87560, corrected bill information, etc. The guidelines: weight_and_inspection_cert vs inspection_cert vs nmfc_cert. We need to see if it\'s weight and inspection certificate or nmfc cert. The content includes NMFC code, corrected bill, etc. The guidelines say:\n\n- weight_and_inspection_cert: certificate/Report issued by authority confirming weight possibly with length width height. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Note: rechecked or reexamined certificate is nmfc_cert. Also, if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. So we need to check nmfc_cert keywords: "As described" or "As found" or "Original" etc. Let\'s check the page: It has "Corrected Bill Information" indicating a correction. It says "An inspection at an ABF terminal resulted in a correction". That suggests correction. Also includes NMFC code. So likely it\'s an NMFC Certification, as it\'s a corrected inspection certificate. The guidelines: "If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert". The keywords for nmfc_cert: "As described or As found or Original and inspection or Corrected class or Correction information". We see "Corrected Bill Information" and "An inspection ... resulted in a correction". So yes, it\'s a corrected inspection certificate. So classification: nmfc_cert. The doc_type should be "nmfc_cert". The page number 1. So output JSON array with one object.\n\nReturn using tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2837, 'outputTokens': 392, 'totalTokens': 3229}, 'metrics': {'latencyMs': 1746}}
2025-09-24 13:43:01,413 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/677b4a5d_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:43:01,428 - INFO - 

U7BB1XSF3ASMIAE1MQ5I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:43:01,428 - INFO - 

✓ Saved result: output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:43:01,594 - INFO - Page 1: Extracted 1248 characters, 88 lines from 8980afd1_YL4HZPZH7D3B03FZZ5QK_0d5280f0_page_001.pdf
2025-09-24 13:43:01,594 - INFO - Successfully processed page 1
2025-09-24 13:43:01,594 - INFO - Combined 1 pages into final text
2025-09-24 13:43:01,595 - INFO - Text validation for 8980afd1_YL4HZPZH7D3B03FZZ5QK: 1265 characters, 1 pages
2025-09-24 13:43:01,595 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:43:01,595 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:43:01,709 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/677b4a5d_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:43:01,755 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ab2a35c1-7038-43e9-bda9-895da4f8cbca', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:13:01 GMT', 'content-type': 'application/json', 'content-length': '924', 'connection': 'keep-alive', 'x-amzn-requestid': 'ab2a35c1-7038-43e9-bda9-895da4f8cbca'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. It shows "NMFC CLASSIFICATION CERTIFICATE". This is a certification. According to enum definitions, it\'s "nmfc_cert" (NMFC Classification Certificate or Correction notice). So doc_type: "nmfc_cert". We need to output via tool. The request: must return exactly one object describing that page. Output via tool call. Let\'s produce JSON with array of documents? The function expects an object with documents array of objects each with page_no and doc_type. So we should call classify_logistics_doc_type with documents: [{page_no:1, doc_type:"nmfc_cert"}].'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2720, 'outputTokens': 150, 'totalTokens': 2870}, 'metrics': {'latencyMs': 783}}
2025-09-24 13:43:01,755 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f95e5e5f_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:43:01,784 - INFO - 

PEE2ZFMV7X0A0FL35G4G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:43:01,784 - INFO - 

✓ Saved result: output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:43:01,958 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4a2aa3c2-ec07-461c-9272-4ef4bbbf9dab', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:13:01 GMT', 'content-type': 'application/json', 'content-length': '3658', 'connection': 'keep-alive', 'x-amzn-requestid': '4a2aa3c2-ec07-461c-9272-4ef4bbbf9dab'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided? It\'s <page1> but contains "Page 3/3". So maybe it\'s page 3 of 3. But we only have page 1 content. But there might be preceding pages not provided. According to instruction: "If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page). If document is not from any categories, classify as other. But before that check if it is continuation of previous page. If it is continuation then assign the same doc_type as previous page."\n\nWe only have one page. We have to output a list of objects for pages. The page content has "Page 3/3" indicating it\'s page 3 of 3. We don\'t know pages 1 and 2. But we need to classify page 3. The page content looks like a weight and inspection certificate? Actually it\'s "Weight & Inspection Freight Class Certificate", "CORRECT TO CLASS: 250", "Density PCF: 3.41", "CORRECT CLASS PER DEFAULT DENSITY RATING & DIMENSIONS OBTAINED AT PICK UP". That suggests it\'s a Weight and Inspection Certificate. The doc type "weight_and_inspection_cert" defined earlier. It says key fields: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I. It has weight, dimensions, cubic ft, correct class. So likely weight_and_inspection_cert.\n\nHowever, we need to check if it\'s nmfc_cert? There is note: If weight_and_inspection_cert has keywords that are in nmfc_cert, classify as nmfc_cert. Need to see if any nmfc_cert keywords: "NMFC Code, class #, As described, As found or Original and inspection, Corrected class or Correction information". There\'s no such. So weight_and_inspection_cert is correct.\n\nAlso, "Page 3/3" indicates continuation. But we only have page 3. We have no previous page. But we need to output doc_type for this page. There\'s no prior page. So assign doc_type weight_and_inspection_cert. There\'s only one page. We must output an array with one object: {page_no: 1, doc_type: ...}. Wait, the tool expects an object with "documents" array containing items with page_no and doc_type. But we only have one page. The page number in the input is ambiguous: it\'s page 3 of 3. But we should output page_no 1 for the first page? In the instruction, "For every page in the input PDF you MUST return exactly one object describing that page." So if the PDF has N pages, we output an array of N objects. The PDF pages are presumably 3 pages; we only see page 3. But we don\'t know page 1 and 2 content. But we must output all pages? The instruction: "If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page)." But we don\'t have pages 1 and 2 content. The user only gave page1 content but labelled as <page1> but inside there is "Page 3/3". So maybe the user mistakenly put page 3 content inside page1 block. We\'ll treat this as page 1? The tool expects page numbers ascending. We only have one page. We\'ll output page_no: 1 doc_type: weight_and_inspection_cert.\n\nAlso check for continuation: The page starts with "Central Transport" etc. Not a continuation indicator. No "Continued". So it\'s a new page. So we classify as weight_and_inspection_cert.\n\nThus output: documents: [{page_no: 1, doc_type: "weight_and_inspection_cert"}].'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2634, 'outputTokens': 805, 'totalTokens': 3439}, 'metrics': {'latencyMs': 3236}}
2025-09-24 13:43:01,958 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/321813fe_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:43:02,057 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f95e5e5f_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:43:02,065 - INFO - 

IZTBXFPGXBFH3DV900G4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:43:02,065 - INFO - 

✓ Saved result: output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:43:02,351 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/321813fe_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:43:04,821 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '033727cd-6160-4284-a5b8-a5a12bd0df13', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:13:04 GMT', 'content-type': 'application/json', 'content-length': '2338', 'connection': 'keep-alive', 'x-amzn-requestid': '033727cd-6160-4284-a5b8-a5a12bd0df13'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page of a document, labeled "<page1>". The content is a "Weights & Inspection Certificate" from Daylight Transport. It seems to be a Weight and Inspection Certificate as per the definition. The header says "WEIGHTS & INSPECTION CERTIFICATE". Also contains "Correction Date" etc. The keywords for weight_and_inspection_cert: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I. Also note we must check if it should be nmfc_cert. For nmfc_cert, keywords: "As described and As found or Original and inspection or Corrected class or Correction information", "NMFC Code", "class #". This page has "Correction Date..." and "Type: RECLASS REWEIGH". It also has NMFC codes. But does it have "Corrected class" or "Correction information"? It says "Correction Date" and "Type: RECLASS REWEIGH". The note says nmfc_cert: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. It has "Correction Date". Does it have "Corrected class"? It has "Type: RECLASS REWEIGH". Might still be weight_and_inspection_cert. But let\'s see classification guidelines: "strictly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So we need to detect if nmfc_cert keywords are present. Let\'s search for "Corrected class" or "Correction information" or "Original and inspection" or "As described" etc. The content does not contain "Corrected class" nor "Correction information" nor "Original and inspection" nor "As described". It has "Correction Date:" but not "Correction information". So likely weight_and_inspection_cert. It also contains NMFC codes, but that is normal for weight_and_inspection_cert.\n\nTherefore classify page 1 as weight_and_inspection_cert. Use tool classify_logistics_doc_type. Output must be JSON with documents array: page_no 1, doc_type weight_and_inspection_cert.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2882, 'outputTokens': 455, 'totalTokens': 3337}, 'metrics': {'latencyMs': 2117}}
2025-09-24 13:43:04,822 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8980afd1_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:43:04,846 - INFO - 

YL4HZPZH7D3B03FZZ5QK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:43:04,846 - INFO - 

✓ Saved result: output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:43:05,125 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8980afd1_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:43:05,162 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '76744685-5e89-43b8-a238-ef1fa7ec2665', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:13:05 GMT', 'content-type': 'application/json', 'content-length': '4058', 'connection': 'keep-alive', 'x-amzn-requestid': '76744685-5e89-43b8-a238-ef1fa7ec2665'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have two pages. Need to classify each page\'s doc_type. They appear to be a Weight and Research Certificate (also spelled Weight & Research Certificate). The description "WEIGHT & RESEARCH CERTIFICATE" is shown. The first page includes "WEIGHT & RESEARCH CERTIFICATE" header. The second page also same. So it\'s a weight and inspection certificate? The enum is weight_and_inspection_cert. It says "Weight and Inspection Certificate" definition. So doc_type is weight_and_inspection_cert.\n\nCheck if it might be nmfc_cert? The second page has "NMFC: 73260 /SUB NMFC: 09 CLASS: 70.0" and "W&R APPROVED". Also "C W&R ISSUED". The first page had "NMFC 73260-9". But the guidelines: weight_and_inspection_cert: if having nmfc_cert keywords, classify as nmfc_cert. Let\'s check the note: "Strictly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert, else keep it as weight_and_inspection_cert." nmfc_cert keywords: "As described or As found or Original and inspection or Corrected class or Correction information" and optional "NMFC Code, class #". The page has "NMFC: 73260 /SUB NMFC: 09 CLASS: 70.0". That might be considered nmfc_cert? But nmfc_cert is a correction notice issued when correction is made in weight_and_inspection_cert. The page shows "W&R APPROVED" and "C W&R ISSUED". But the wording: "C W&R ISSUED" maybe indicates the certificate was issued. The page also includes "W&R APPROVED". The second page says "W&R APPROVED". There\'s no mention of "Corrected". The first page has "AS CORRECTED". Wait first page: "BILL TOTALS 3824 AS CORRECTED". Hmm that indicates correction. The first page has "AS CORRECTED" in the totals. The second page says "W&R APPROVED". So maybe this is a weight and inspection certificate with a correction? But nmfc_cert is used when correction is made. The first page says "AS CORRECTED" which indicates a correction. But the second page is the corrected version? The second page includes the corrected weight 3850. Actually first page totals 3824; second page totals 3850. The second page says "SHIPMENT WAS WEIGHED AT CGO TML; SCALE SHOWED 3850 INSTEAD OF 3824". So the second page is a corrected certificate. It says "W&R APPROVED" and "C W&R ISSUED". So this might be a corrected weight and inspection certificate, which may be nmfc_cert. But the enum: nmfc_cert is "NMFC Classification Certificate or Correction notice". The second page includes NMFC 73260 and class 70.0. That fits. The guidelines: if weight_and_inspection_cert has nmfc_cert keywords, classify as nmfc_cert. So likely both pages are nmfc_cert? But the first page may be the original weight_and_inspection_cert? But the content is the same header "WEIGHT & RESEARCH CERTIFICATE". The second page also same. But second page appears to be the corrected version, referencing the correction. The first page may be the original weight_and_inspection_cert. But the doc type likely same across pages. So we should classify all pages as nmfc_cert? Let\'s see the definition: nmfc_cert classification: when correction is made in weight_and_inspection_cert. The page shows "W&R APPROVED" and "C W&R ISSUED". That indicates that a new certificate was issued? The note: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." The second page indicates correction. So this is nmfc_cert. But the first page also likely part of the same corrected certificate? Might be part of a multi-page single document, so both pages same type. So output both pages as nmfc_cert.\n\nThus we return two objects: page_no 1 nmfc_cert, page_no 2 nmfc_cert.\n\nLet\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3328, 'outputTokens': 913, 'totalTokens': 4241}, 'metrics': {'latencyMs': 3999}}
2025-09-24 13:43:05,163 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0fe3cb3d_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:43:05,193 - INFO - 

PB67IAPSJB1DZWMDIE1H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:43:05,193 - INFO - 

✓ Saved result: output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:43:05,471 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0fe3cb3d_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:43:05,472 - INFO - 
📊 Processing Summary:
2025-09-24 13:43:05,472 - INFO -    Total files: 9
2025-09-24 13:43:05,473 - INFO -    Successful: 9
2025-09-24 13:43:05,473 - INFO -    Failed: 0
2025-09-24 13:43:05,473 - INFO -    Duration: 18.83 seconds
2025-09-24 13:43:05,473 - INFO -    Output directory: output
2025-09-24 13:43:05,473 - INFO - 
📋 Successfully Processed Files:
2025-09-24 13:43:05,473 - INFO -    📄 B3SIRREC9IAVZOJVDQSN.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:43:05,473 - INFO -    📄 CUF54EHGMLQ57HR93DRB.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:43:05,473 - INFO -    📄 FYQQGIW8Z9DSAPCL0S9G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:43:05,473 - INFO -    📄 IZTBXFPGXBFH3DV900G4.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:43:05,473 - INFO -    📄 MR6ONA8GK6HN1LCZHEX3.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:43:05,473 - INFO -    📄 PB67IAPSJB1DZWMDIE1H.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}
2025-09-24 13:43:05,474 - INFO -    📄 PEE2ZFMV7X0A0FL35G4G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:43:05,474 - INFO -    📄 U7BB1XSF3ASMIAE1MQ5I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:43:05,474 - INFO -    📄 YL4HZPZH7D3B03FZZ5QK.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:43:05,474 - INFO - 
============================================================================================================================================
2025-09-24 13:43:05,474 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 13:43:05,474 - INFO - ============================================================================================================================================
2025-09-24 13:43:05,475 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 13:43:05,475 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:43:05,475 - INFO - B3SIRREC9IAVZOJVDQSN.pdf                           1      nmfc_cert            run1_B3SIRREC9IAVZOJVDQSN.json                    
2025-09-24 13:43:05,475 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:43:05,475 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:43:05,475 - INFO - 
2025-09-24 13:43:05,475 - INFO - CUF54EHGMLQ57HR93DRB.pdf                           1      nmfc_cert            run1_CUF54EHGMLQ57HR93DRB.json                    
2025-09-24 13:43:05,475 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:43:05,475 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:43:05,475 - INFO - 
2025-09-24 13:43:05,475 - INFO - FYQQGIW8Z9DSAPCL0S9G.pdf                           1      nmfc_cert            run1_FYQQGIW8Z9DSAPCL0S9G.json                    
2025-09-24 13:43:05,475 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:43:05,475 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:43:05,475 - INFO - 
2025-09-24 13:43:05,475 - INFO - IZTBXFPGXBFH3DV900G4.pdf                           1      weight_and_inspect... run1_IZTBXFPGXBFH3DV900G4.json                    
2025-09-24 13:43:05,475 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:43:05,476 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:43:05,476 - INFO - 
2025-09-24 13:43:05,476 - INFO - MR6ONA8GK6HN1LCZHEX3.pdf                           1      nmfc_cert            run1_MR6ONA8GK6HN1LCZHEX3.json                    
2025-09-24 13:43:05,476 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:43:05,476 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:43:05,476 - INFO - 
2025-09-24 13:43:05,476 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           1      nmfc_cert            run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:43:05,476 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:43:05,476 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:43:05,476 - INFO - 
2025-09-24 13:43:05,476 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           2      nmfc_cert            run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:43:05,476 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:43:05,476 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:43:05,476 - INFO - 
2025-09-24 13:43:05,476 - INFO - PEE2ZFMV7X0A0FL35G4G.pdf                           1      nmfc_cert            run1_PEE2ZFMV7X0A0FL35G4G.json                    
2025-09-24 13:43:05,476 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:43:05,476 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:43:05,476 - INFO - 
2025-09-24 13:43:05,476 - INFO - U7BB1XSF3ASMIAE1MQ5I.pdf                           1      nmfc_cert            run1_U7BB1XSF3ASMIAE1MQ5I.json                    
2025-09-24 13:43:05,476 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:43:05,476 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:43:05,476 - INFO - 
2025-09-24 13:43:05,476 - INFO - YL4HZPZH7D3B03FZZ5QK.pdf                           1      weight_and_inspect... run1_YL4HZPZH7D3B03FZZ5QK.json                    
2025-09-24 13:43:05,476 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:43:05,476 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:43:05,476 - INFO - 
2025-09-24 13:43:05,476 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:43:05,476 - INFO - Total entries: 10
2025-09-24 13:43:05,476 - INFO - ============================================================================================================================================
2025-09-24 13:43:05,476 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 13:43:05,476 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:43:05,476 - INFO -   1. B3SIRREC9IAVZOJVDQSN.pdf            Page 1   → nmfc_cert       | run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:43:05,476 - INFO -   2. CUF54EHGMLQ57HR93DRB.pdf            Page 1   → nmfc_cert       | run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:43:05,476 - INFO -   3. FYQQGIW8Z9DSAPCL0S9G.pdf            Page 1   → nmfc_cert       | run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:43:05,476 - INFO -   4. IZTBXFPGXBFH3DV900G4.pdf            Page 1   → weight_and_inspection_cert | run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:43:05,476 - INFO -   5. MR6ONA8GK6HN1LCZHEX3.pdf            Page 1   → nmfc_cert       | run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:43:05,476 - INFO -   6. PB67IAPSJB1DZWMDIE1H.pdf            Page 1   → nmfc_cert       | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:43:05,476 - INFO -   7. PB67IAPSJB1DZWMDIE1H.pdf            Page 2   → nmfc_cert       | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:43:05,476 - INFO -   8. PEE2ZFMV7X0A0FL35G4G.pdf            Page 1   → nmfc_cert       | run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:43:05,477 - INFO -   9. U7BB1XSF3ASMIAE1MQ5I.pdf            Page 1   → nmfc_cert       | run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:43:05,477 - INFO -  10. YL4HZPZH7D3B03FZZ5QK.pdf            Page 1   → weight_and_inspection_cert | run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:43:05,477 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:43:05,477 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 18.83438, 'processed_files': [{'filename': 'B3SIRREC9IAVZOJVDQSN.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf'}, {'filename': 'CUF54EHGMLQ57HR93DRB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf'}, {'filename': 'FYQQGIW8Z9DSAPCL0S9G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf'}, {'filename': 'IZTBXFPGXBFH3DV900G4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf'}, {'filename': 'MR6ONA8GK6HN1LCZHEX3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf'}, {'filename': 'PB67IAPSJB1DZWMDIE1H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf'}, {'filename': 'PEE2ZFMV7X0A0FL35G4G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf'}, {'filename': 'U7BB1XSF3ASMIAE1MQ5I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf'}, {'filename': 'YL4HZPZH7D3B03FZZ5QK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf'}]}
