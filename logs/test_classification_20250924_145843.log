2025-09-24 14:58:43,114 - INFO - Logging initialized. Log file: logs/test_classification_20250924_145843.log
2025-09-24 14:58:43,114 - INFO - 📁 Found 1 files to process
2025-09-24 14:58:43,114 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:58:43,114 - INFO - 🚀 Processing 1 files in FORCED PARALLEL MODE...
2025-09-24 14:58:43,114 - INFO - 🚀 Creating 1 parallel tasks...
2025-09-24 14:58:43,114 - INFO - 🚀 All 1 tasks created - executing in parallel...
2025-09-24 14:58:43,114 - INFO - ⬆️ [14:58:43] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:58:46,472 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/fefd1b05_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:58:46,472 - INFO - 🔍 [14:58:46] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:58:46,473 - INFO - Initializing TextractProcessor...
2025-09-24 14:58:46,493 - INFO - Initializing BedrockProcessor...
2025-09-24 14:58:46,502 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fefd1b05_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:58:46,502 - INFO - Processing PDF from S3...
2025-09-24 14:58:46,505 - INFO - Downloading PDF from S3 to /tmp/tmp3v_b5xu0/fefd1b05_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:58:48,909 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 14:58:48,909 - INFO - Splitting PDF into individual pages...
2025-09-24 14:58:48,910 - INFO - Splitting PDF fefd1b05_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 14:58:48,913 - INFO - Split PDF into 1 pages
2025-09-24 14:58:48,913 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:58:48,913 - INFO - Expected pages: [1]
2025-09-24 14:58:56,835 - INFO - Page 1: Extracted 939 characters, 64 lines from fefd1b05_A34CDFDJ66EDOZEKZWJL_407729a8_page_001.pdf
2025-09-24 14:58:56,835 - INFO - Successfully processed page 1
2025-09-24 14:58:56,835 - INFO - Combined 1 pages into final text
2025-09-24 14:58:56,836 - INFO - Text validation for fefd1b05_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 14:58:56,836 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:58:56,836 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:58:59,766 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '21a26c36-f9d6-4c56-bdaf-e6ef26251ab7', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:28:59 GMT', 'content-type': 'application/json', 'content-length': '2057', 'connection': 'keep-alive', 'x-amzn-requestid': '21a26c36-f9d6-4c56-bdaf-e6ef26251ab7'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There is only page1. Let\'s analyze. The page includes "Inspection Certificate" header. Also "ROADRUNNER TRANSPORTATION SERVICES" etc. Contains weight, dimensions, etc. The description says "Inspection Certificate". The tool says we have categories: inspection_cert, weight_and_inspection_cert, nmfc_cert. We need to determine which fits.\n\nWe need to check whether it\'s an Inspection Certificate or Weight and Inspection Certificate. There\'s a mention of weight (lbs). And "Dimensions and weights determined and certified by use of Mettler-Toledo ... NCWM Certificate No. 09-076PA1." Also "Weight(Lbs)". The description says "Inspection Certificate" in header. That indicates inspection certificate. But we need to differentiate.\n\nWe need to check if the inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert. The rule: "Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert." So because it includes weight, weight_and_inspection_cert is appropriate. Also includes dimension measurement, weight. So it is a weight and inspection certificate. Also check nmfc_cert: if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. But we don\'t see NMFC codes or correction info. So weight_and_inspection_cert.\n\nThus output should be page 1: doc_type weight_and_inspection_cert.\n\nWe must use tool call with array of objects. Only one page. So return: [{"page_no":1,"doc_type":"weight_and_inspection_cert"}].\n\nLet\'s produce.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2826, 'outputTokens': 378, 'totalTokens': 3204}, 'metrics': {'latencyMs': 1624}}
2025-09-24 14:58:59,766 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fefd1b05_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:58:59,778 - INFO - 

A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:58:59,778 - INFO - 

✓ Saved result: output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:59:00,885 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fefd1b05_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:59:00,886 - INFO - 
📊 Processing Summary:
2025-09-24 14:59:00,886 - INFO -    Total files: 1
2025-09-24 14:59:00,886 - INFO -    Successful: 1
2025-09-24 14:59:00,887 - INFO -    Failed: 0
2025-09-24 14:59:00,887 - INFO -    Duration: 17.77 seconds
2025-09-24 14:59:00,887 - INFO -    Output directory: output
2025-09-24 14:59:00,887 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:59:00,887 - INFO -    📄 A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:59:00,887 - INFO - 
============================================================================================================================================
2025-09-24 14:59:00,888 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:59:00,888 - INFO - ============================================================================================================================================
2025-09-24 14:59:00,888 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:59:00,888 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:59:00,888 - INFO - A34CDFDJ66EDOZEKZWJL.pdf                           1      weight_and_inspect... run1_A34CDFDJ66EDOZEKZWJL.json                    
2025-09-24 14:59:00,888 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:59:00,888 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:59:00,888 - INFO - 
2025-09-24 14:59:00,888 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:59:00,889 - INFO - Total entries: 1
2025-09-24 14:59:00,889 - INFO - ============================================================================================================================================
2025-09-24 14:59:00,889 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:59:00,889 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:59:00,889 - INFO -   1. A34CDFDJ66EDOZEKZWJL.pdf            Page 1   → weight_and_inspection_cert | run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:59:00,889 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:59:00,889 - INFO - 
✅ Test completed: {'total_files': 1, 'processed': 1, 'failed': 0, 'errors': [], 'duration_seconds': 17.771844, 'processed_files': [{'filename': 'A34CDFDJ66EDOZEKZWJL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf'}]}
