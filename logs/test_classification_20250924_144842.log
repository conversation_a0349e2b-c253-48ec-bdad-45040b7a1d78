2025-09-24 14:48:42,824 - INFO - Logging initialized. Log file: logs/test_classification_20250924_144842.log
2025-09-24 14:48:42,824 - INFO - 📁 Found 15 files to process
2025-09-24 14:48:42,824 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:48:42,824 - INFO - 🚀 Processing 15 files in FORCED PARALLEL MODE...
2025-09-24 14:48:42,824 - INFO - 🚀 Creating 15 parallel tasks...
2025-09-24 14:48:42,824 - INFO - 🚀 All 15 tasks created - executing in parallel...
2025-09-24 14:48:42,824 - INFO - ⬆️ [14:48:42] Uploading: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:48:45,772 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/3ff49c87_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:48:45,772 - INFO - 🔍 [14:48:45] Starting classification: A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:48:45,773 - INFO - ⬆️ [14:48:45] Uploading: D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 14:48:45,774 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:45,798 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:45,803 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3ff49c87_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:48:45,803 - INFO - Processing PDF from S3...
2025-09-24 14:48:45,803 - INFO - Downloading PDF from S3 to /tmp/tmpdfuyhdy9/3ff49c87_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:48:46,345 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/D5KCCMIJXGREC9Q81E0H.pdf -> s3://document-extraction-logistically/temp/e1ef4f40_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 14:48:46,346 - INFO - 🔍 [14:48:46] Starting classification: D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 14:48:46,346 - INFO - ⬆️ [14:48:46] Uploading: DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:48:46,349 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:46,356 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:46,359 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e1ef4f40_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 14:48:46,359 - INFO - Processing PDF from S3...
2025-09-24 14:48:46,359 - INFO - Downloading PDF from S3 to /tmp/tmpe9x58tj2/e1ef4f40_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 14:48:47,000 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/b6c351ed_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:48:47,001 - INFO - 🔍 [14:48:47] Starting classification: DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:48:47,002 - INFO - ⬆️ [14:48:47] Uploading: DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:48:47,004 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:47,024 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:47,027 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b6c351ed_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:48:47,027 - INFO - Processing PDF from S3...
2025-09-24 14:48:47,027 - INFO - Downloading PDF from S3 to /tmp/tmpvpuyyhy2/b6c351ed_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:48:47,614 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/DFY1VDZWR7NBDLJV02G2.pdf -> s3://document-extraction-logistically/temp/a7cd079e_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:48:47,615 - INFO - 🔍 [14:48:47] Starting classification: DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:48:47,616 - INFO - ⬆️ [14:48:47] Uploading: DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:48:47,618 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:47,642 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:47,646 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a7cd079e_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:48:47,647 - INFO - Processing PDF from S3...
2025-09-24 14:48:47,647 - INFO - Downloading PDF from S3 to /tmp/tmpo9fyk1r8/a7cd079e_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:48:47,968 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:48:47,969 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:47,971 - INFO - Splitting PDF e1ef4f40_D5KCCMIJXGREC9Q81E0H into 1 pages
2025-09-24 14:48:47,978 - INFO - Split PDF into 1 pages
2025-09-24 14:48:47,979 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:47,979 - INFO - Expected pages: [1]
2025-09-24 14:48:48,211 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/DH0JZ2JWDGRHD26BX74C.pdf -> s3://document-extraction-logistically/temp/e5e99069_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:48:48,211 - INFO - 🔍 [14:48:48] Starting classification: DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:48:48,212 - INFO - ⬆️ [14:48:48] Uploading: HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:48:48,213 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:48,239 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:48,244 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e5e99069_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:48:48,245 - INFO - Processing PDF from S3...
2025-09-24 14:48:48,245 - INFO - Downloading PDF from S3 to /tmp/tmpnlveum2t/e5e99069_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:48:48,315 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 14:48:48,316 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:48,317 - INFO - Splitting PDF 3ff49c87_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 14:48:48,321 - INFO - Split PDF into 1 pages
2025-09-24 14:48:48,321 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:48,321 - INFO - Expected pages: [1]
2025-09-24 14:48:48,800 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/HFPAXYL947DH59AB12FL.pdf -> s3://document-extraction-logistically/temp/be3a402c_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:48:48,800 - INFO - 🔍 [14:48:48] Starting classification: HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:48:48,802 - INFO - ⬆️ [14:48:48] Uploading: K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:48:48,804 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:48,832 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:48,838 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/be3a402c_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:48:48,839 - INFO - Processing PDF from S3...
2025-09-24 14:48:48,839 - INFO - Downloading PDF from S3 to /tmp/tmpre4u6mz8/be3a402c_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:48:48,857 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:48:48,857 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:48,858 - INFO - Splitting PDF b6c351ed_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 14:48:48,860 - INFO - Split PDF into 1 pages
2025-09-24 14:48:48,861 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:48,861 - INFO - Expected pages: [1]
2025-09-24 14:48:49,292 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:48:49,292 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:49,293 - INFO - Splitting PDF a7cd079e_DFY1VDZWR7NBDLJV02G2 into 1 pages
2025-09-24 14:48:49,295 - INFO - Split PDF into 1 pages
2025-09-24 14:48:49,295 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:49,295 - INFO - Expected pages: [1]
2025-09-24 14:48:49,376 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/K9VSARJOKAIZHNJ5RBDT.pdf -> s3://document-extraction-logistically/temp/50587b12_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:48:49,376 - INFO - 🔍 [14:48:49] Starting classification: K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:48:49,377 - INFO - ⬆️ [14:48:49] Uploading: NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:48:49,378 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:49,396 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:49,429 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/50587b12_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:48:49,429 - INFO - Processing PDF from S3...
2025-09-24 14:48:49,429 - INFO - Downloading PDF from S3 to /tmp/tmpmyc8g_n6/50587b12_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:48:49,763 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:48:49,763 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:49,764 - INFO - Splitting PDF e5e99069_DH0JZ2JWDGRHD26BX74C into 2 pages
2025-09-24 14:48:49,766 - INFO - Split PDF into 2 pages
2025-09-24 14:48:49,767 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:49,767 - INFO - Expected pages: [1, 2]
2025-09-24 14:48:49,968 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/NJ4WSZ8BUQAW48V6403N.pdf -> s3://document-extraction-logistically/temp/e059ab60_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:48:49,969 - INFO - 🔍 [14:48:49] Starting classification: NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:48:49,969 - INFO - ⬆️ [14:48:49] Uploading: OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:48:49,969 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:49,982 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:49,984 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e059ab60_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:48:49,984 - INFO - Processing PDF from S3...
2025-09-24 14:48:49,984 - INFO - Downloading PDF from S3 to /tmp/tmpopmbxahj/e059ab60_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:48:50,404 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:48:50,404 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:50,405 - INFO - Splitting PDF be3a402c_HFPAXYL947DH59AB12FL into 1 pages
2025-09-24 14:48:50,406 - INFO - Split PDF into 1 pages
2025-09-24 14:48:50,406 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:50,406 - INFO - Expected pages: [1]
2025-09-24 14:48:50,900 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/OR9EL08KIKNQPZ3UV3HH.pdf -> s3://document-extraction-logistically/temp/5038c27f_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:48:50,901 - INFO - 🔍 [14:48:50] Starting classification: OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:48:50,902 - INFO - ⬆️ [14:48:50] Uploading: QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:48:50,904 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:50,904 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:48:50,917 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:50,924 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:50,929 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5038c27f_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:48:50,930 - INFO - Processing PDF from S3...
2025-09-24 14:48:50,930 - INFO - Splitting PDF 50587b12_K9VSARJOKAIZHNJ5RBDT into 1 pages
2025-09-24 14:48:50,931 - INFO - Downloading PDF from S3 to /tmp/tmpny8doh93/5038c27f_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:48:50,938 - INFO - Split PDF into 1 pages
2025-09-24 14:48:50,938 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:50,938 - INFO - Expected pages: [1]
2025-09-24 14:48:51,466 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/QHD3LC0DU6S8O2YVVS60.pdf -> s3://document-extraction-logistically/temp/5069cec4_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:48:51,466 - INFO - 🔍 [14:48:51] Starting classification: QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:48:51,467 - INFO - ⬆️ [14:48:51] Uploading: R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:48:51,468 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:51,486 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:51,490 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5069cec4_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:48:51,490 - INFO - Processing PDF from S3...
2025-09-24 14:48:51,490 - INFO - Downloading PDF from S3 to /tmp/tmpkh_drpox/5069cec4_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:48:52,026 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:48:52,026 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:52,028 - INFO - Splitting PDF e059ab60_NJ4WSZ8BUQAW48V6403N into 3 pages
2025-09-24 14:48:52,031 - INFO - Split PDF into 3 pages
2025-09-24 14:48:52,032 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:52,032 - INFO - Expected pages: [1, 2, 3]
2025-09-24 14:48:52,045 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/R1V0MO844PBLWNEAUETU.pdf -> s3://document-extraction-logistically/temp/e8da5693_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:48:52,047 - INFO - 🔍 [14:48:52] Starting classification: R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:48:52,047 - INFO - ⬆️ [14:48:52] Uploading: RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:48:52,054 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:52,069 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:52,073 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e8da5693_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:48:52,073 - INFO - Processing PDF from S3...
2025-09-24 14:48:52,073 - INFO - Downloading PDF from S3 to /tmp/tmp2w2b2vos/e8da5693_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:48:52,643 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/RUDVGETVRZO7XX6YNW7I.pdf -> s3://document-extraction-logistically/temp/a32149d0_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:48:52,644 - INFO - 🔍 [14:48:52] Starting classification: RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:48:52,645 - INFO - ⬆️ [14:48:52] Uploading: WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:48:52,647 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:52,670 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:52,676 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a32149d0_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:48:52,676 - INFO - Processing PDF from S3...
2025-09-24 14:48:52,677 - INFO - Downloading PDF from S3 to /tmp/tmpl77muavs/a32149d0_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:48:52,929 - INFO - Page 1: Extracted 840 characters, 77 lines from e1ef4f40_D5KCCMIJXGREC9Q81E0H_b54e8a9c_page_001.pdf
2025-09-24 14:48:52,930 - INFO - Successfully processed page 1
2025-09-24 14:48:52,931 - INFO - Combined 1 pages into final text
2025-09-24 14:48:52,931 - INFO - Text validation for e1ef4f40_D5KCCMIJXGREC9Q81E0H: 857 characters, 1 pages
2025-09-24 14:48:52,931 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:48:52,931 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:48:53,008 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:48:53,009 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:53,018 - INFO - Splitting PDF 5069cec4_QHD3LC0DU6S8O2YVVS60 into 1 pages
2025-09-24 14:48:53,019 - INFO - Split PDF into 1 pages
2025-09-24 14:48:53,019 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:53,019 - INFO - Expected pages: [1]
2025-09-24 14:48:53,242 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/WRKSHW76B3QUG47QWR75.pdf -> s3://document-extraction-logistically/temp/53896267_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:48:53,242 - INFO - 🔍 [14:48:53] Starting classification: WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:48:53,243 - INFO - ⬆️ [14:48:53] Uploading: XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:48:53,245 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:53,263 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:53,266 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/53896267_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:48:53,266 - INFO - Processing PDF from S3...
2025-09-24 14:48:53,267 - INFO - Downloading PDF from S3 to /tmp/tmpcnu47ggx/53896267_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:48:53,310 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:48:53,311 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:53,313 - INFO - Splitting PDF e8da5693_R1V0MO844PBLWNEAUETU into 2 pages
2025-09-24 14:48:53,320 - INFO - Split PDF into 2 pages
2025-09-24 14:48:53,320 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:53,320 - INFO - Expected pages: [1, 2]
2025-09-24 14:48:53,542 - INFO - Page 1: Extracted 626 characters, 49 lines from a7cd079e_DFY1VDZWR7NBDLJV02G2_e7c0e8d1_page_001.pdf
2025-09-24 14:48:53,542 - INFO - Successfully processed page 1
2025-09-24 14:48:53,543 - INFO - Combined 1 pages into final text
2025-09-24 14:48:53,543 - INFO - Text validation for a7cd079e_DFY1VDZWR7NBDLJV02G2: 643 characters, 1 pages
2025-09-24 14:48:53,543 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:48:53,543 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:48:53,633 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 14:48:53,633 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:53,634 - WARNING - Multiple definitions in dictionary at byte 0x9e9f6 for key /OpenAction
2025-09-24 14:48:53,634 - INFO - Splitting PDF 5038c27f_OR9EL08KIKNQPZ3UV3HH into 2 pages
2025-09-24 14:48:53,663 - INFO - Split PDF into 2 pages
2025-09-24 14:48:53,663 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:53,663 - INFO - Expected pages: [1, 2]
2025-09-24 14:48:53,809 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/XCJLXZK140FUS8020ZAG.pdf -> s3://document-extraction-logistically/temp/fff5d879_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:48:53,809 - INFO - 🔍 [14:48:53] Starting classification: XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:48:53,810 - INFO - ⬆️ [14:48:53] Uploading: Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 14:48:53,810 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:53,831 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:53,834 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/fff5d879_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:48:53,834 - INFO - Processing PDF from S3...
2025-09-24 14:48:53,835 - INFO - Downloading PDF from S3 to /tmp/tmpfmcqpgoi/fff5d879_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:48:54,129 - INFO - Page 1: Extracted 854 characters, 69 lines from e5e99069_DH0JZ2JWDGRHD26BX74C_612cfde7_page_001.pdf
2025-09-24 14:48:54,130 - INFO - Successfully processed page 1
2025-09-24 14:48:54,458 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/Y82AJRDQU1FCXFHREDEA.pdf -> s3://document-extraction-logistically/temp/a940d968_Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 14:48:54,458 - INFO - 🔍 [14:48:54] Starting classification: Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 14:48:54,460 - INFO - Initializing TextractProcessor...
2025-09-24 14:48:54,475 - INFO - Initializing BedrockProcessor...
2025-09-24 14:48:54,480 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a940d968_Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 14:48:54,481 - INFO - Processing PDF from S3...
2025-09-24 14:48:54,481 - INFO - Downloading PDF from S3 to /tmp/tmp_43fmvjq/a940d968_Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 14:48:54,481 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:48:54,488 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:54,489 - INFO - Splitting PDF 53896267_WRKSHW76B3QUG47QWR75 into 1 pages
2025-09-24 14:48:54,492 - INFO - Split PDF into 1 pages
2025-09-24 14:48:54,492 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:54,493 - INFO - Expected pages: [1]
2025-09-24 14:48:54,547 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:48:54,547 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:54,550 - INFO - Splitting PDF a32149d0_RUDVGETVRZO7XX6YNW7I into 1 pages
2025-09-24 14:48:54,551 - INFO - Split PDF into 1 pages
2025-09-24 14:48:54,551 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:54,551 - INFO - Expected pages: [1]
2025-09-24 14:48:54,635 - INFO - Page 2: Extracted 764 characters, 54 lines from e5e99069_DH0JZ2JWDGRHD26BX74C_612cfde7_page_002.pdf
2025-09-24 14:48:54,635 - INFO - Successfully processed page 2
2025-09-24 14:48:54,636 - INFO - Combined 2 pages into final text
2025-09-24 14:48:54,636 - INFO - Text validation for e5e99069_DH0JZ2JWDGRHD26BX74C: 1654 characters, 2 pages
2025-09-24 14:48:54,636 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:48:54,637 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:48:54,760 - INFO - Page 1: Extracted 802 characters, 30 lines from 50587b12_K9VSARJOKAIZHNJ5RBDT_d9dcdc3c_page_001.pdf
2025-09-24 14:48:54,761 - INFO - Successfully processed page 1
2025-09-24 14:48:54,761 - INFO - Combined 1 pages into final text
2025-09-24 14:48:54,761 - INFO - Text validation for 50587b12_K9VSARJOKAIZHNJ5RBDT: 819 characters, 1 pages
2025-09-24 14:48:54,761 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:48:54,761 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:48:55,056 - INFO - Page 1: Extracted 939 characters, 64 lines from 3ff49c87_A34CDFDJ66EDOZEKZWJL_09571a84_page_001.pdf
2025-09-24 14:48:55,056 - INFO - Successfully processed page 1
2025-09-24 14:48:55,056 - INFO - Combined 1 pages into final text
2025-09-24 14:48:55,057 - INFO - Text validation for 3ff49c87_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 14:48:55,057 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:48:55,057 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:48:55,135 - INFO - Page 1: Extracted 732 characters, 59 lines from b6c351ed_DCY7SLNMWUXIENOREHQF_9f89f85a_page_001.pdf
2025-09-24 14:48:55,135 - INFO - Successfully processed page 1
2025-09-24 14:48:55,136 - INFO - Combined 1 pages into final text
2025-09-24 14:48:55,136 - INFO - Text validation for b6c351ed_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 14:48:55,136 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:48:55,136 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:48:55,616 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:48:55,616 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:55,618 - INFO - Splitting PDF fff5d879_XCJLXZK140FUS8020ZAG into 1 pages
2025-09-24 14:48:55,624 - INFO - Split PDF into 1 pages
2025-09-24 14:48:55,624 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:55,624 - INFO - Expected pages: [1]
2025-09-24 14:48:55,829 - INFO - Page 1: Extracted 517 characters, 31 lines from be3a402c_HFPAXYL947DH59AB12FL_86cd5547_page_001.pdf
2025-09-24 14:48:55,830 - INFO - Successfully processed page 1
2025-09-24 14:48:55,830 - INFO - Combined 1 pages into final text
2025-09-24 14:48:55,830 - INFO - Text validation for be3a402c_HFPAXYL947DH59AB12FL: 534 characters, 1 pages
2025-09-24 14:48:55,831 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:48:55,831 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:48:55,854 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'aa941bf6-de2f-46d8-bef4-37dc236fe8f2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:18:55 GMT', 'content-type': 'application/json', 'content-length': '981', 'connection': 'keep-alive', 'x-amzn-requestid': 'aa941bf6-de2f-46d8-bef4-37dc236fe8f2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page content. Determine document type. Keywords: "Weight & Inspection", "CERTIFIED WEIGHT CERTIFICATE", "Weight Certificate", "Declared Weight", "Corrected Weight", "Pro Number", "Bill of Lading", "Scan Date", "Time", "Weight Terminal". This indicates a weight and inspection certificate. According to enum: weight_and_inspection_cert. Check if it has "nmfc_cert" keywords: no NMFC, class, etc. So classify as weight_and_inspection_cert.\n\nWe need output via tool classify_logistics_doc_type with array of objects per page. There\'s only one page. Provide page_no 1, doc_type "weight_and_inspection_cert".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2685, 'outputTokens': 164, 'totalTokens': 2849}, 'metrics': {'latencyMs': 865}}
2025-09-24 14:48:55,855 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a7cd079e_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:48:55,871 - INFO - 

DFY1VDZWR7NBDLJV02G2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:48:55,871 - INFO - 

✓ Saved result: output/run1_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 14:48:56,031 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:48:56,031 - INFO - Splitting PDF into individual pages...
2025-09-24 14:48:56,032 - INFO - Splitting PDF a940d968_Y82AJRDQU1FCXFHREDEA into 1 pages
2025-09-24 14:48:56,034 - INFO - Split PDF into 1 pages
2025-09-24 14:48:56,034 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:48:56,034 - INFO - Expected pages: [1]
2025-09-24 14:48:56,095 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'dfbb4534-35fa-482d-abe8-693ae7da1465', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:18:55 GMT', 'content-type': 'application/json', 'content-length': '1173', 'connection': 'keep-alive', 'x-amzn-requestid': 'dfbb4534-35fa-482d-abe8-693ae7da1465'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided: page1. We must use tool classify_logistics_doc_type. Document text includes "NMFC Classification Certificate" at top. So doc_type likely nmfc_cert. Must confirm that it includes keywords: "NMFC Classification Certificate" obviously. Also includes "Corrected" or "Original Detail" but not "Corrected". But nmfc_cert definition: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." But this page shows "ORIGINAL DETAIL" and "INSPECTION DETAIL" but not "Corrected" or "Correction". But nmfc_cert keywords: "As described or As found or Original and inspection or Corrected class or Correction information". There\'s "ORIGINAL DETAIL" present. That might qualify. It\'s probably nmfc_cert. So we output page 1 with doc_type: nmfc_cert.\n\nWe must call tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2797, 'outputTokens': 207, 'totalTokens': 3004}, 'metrics': {'latencyMs': 1112}}
2025-09-24 14:48:56,096 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e1ef4f40_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 14:48:56,150 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a7cd079e_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:48:56,178 - INFO - 

D5KCCMIJXGREC9Q81E0H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:48:56,178 - INFO - 

✓ Saved result: output/run1_D5KCCMIJXGREC9Q81E0H.json
2025-09-24 14:48:56,432 - INFO - Page 3: Extracted 850 characters, 59 lines from e059ab60_NJ4WSZ8BUQAW48V6403N_bca8cced_page_003.pdf
2025-09-24 14:48:56,433 - INFO - Successfully processed page 3
2025-09-24 14:48:56,462 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e1ef4f40_D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 14:48:56,668 - INFO - Page 2: Extracted 850 characters, 59 lines from e059ab60_NJ4WSZ8BUQAW48V6403N_bca8cced_page_002.pdf
2025-09-24 14:48:56,669 - INFO - Successfully processed page 2
2025-09-24 14:48:56,853 - INFO - Page 1: Extracted 980 characters, 76 lines from e059ab60_NJ4WSZ8BUQAW48V6403N_bca8cced_page_001.pdf
2025-09-24 14:48:56,853 - INFO - Successfully processed page 1
2025-09-24 14:48:56,854 - INFO - Combined 3 pages into final text
2025-09-24 14:48:56,854 - INFO - Text validation for e059ab60_NJ4WSZ8BUQAW48V6403N: 2735 characters, 3 pages
2025-09-24 14:48:56,855 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:48:56,855 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:48:57,043 - INFO - Page 1: Extracted 589 characters, 36 lines from 5069cec4_QHD3LC0DU6S8O2YVVS60_fdd99abe_page_001.pdf
2025-09-24 14:48:57,044 - INFO - Successfully processed page 1
2025-09-24 14:48:57,044 - INFO - Combined 1 pages into final text
2025-09-24 14:48:57,045 - INFO - Text validation for 5069cec4_QHD3LC0DU6S8O2YVVS60: 606 characters, 1 pages
2025-09-24 14:48:57,046 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:48:57,046 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:48:57,219 - INFO - Page 2: Extracted 913 characters, 56 lines from e8da5693_R1V0MO844PBLWNEAUETU_8d8d28ec_page_002.pdf
2025-09-24 14:48:57,220 - INFO - Successfully processed page 2
2025-09-24 14:48:57,543 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e5086544-2f2d-4d9f-9962-22a0935e709e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:18:57 GMT', 'content-type': 'application/json', 'content-length': '920', 'connection': 'keep-alive', 'x-amzn-requestid': 'e5086544-2f2d-4d9f-9962-22a0935e709e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have page1 content. Need to classify. Keywords: "Inspection Certificate", "Weight", "Dimensions", "Mettler-Toledo" etc. Likely weight_and_inspection_cert or inspection_cert. In definitions: weight_and_inspection_cert: certificate confirming weight with measurement, maybe weight mentioned. It says "inspection certificate" and weight numbers. Also note: "Inspection Certificate" header. So doc_type: weight_and_inspection_cert. Must output a JSON array of objects: one for each page. Just one page. Provide {page_no:1, doc_type:"weight_and_inspection_cert"}. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2826, 'outputTokens': 148, 'totalTokens': 2974}, 'metrics': {'latencyMs': 847}}
2025-09-24 14:48:57,543 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3ff49c87_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:48:57,567 - INFO - 

A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:48:57,567 - INFO - 

✓ Saved result: output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:48:57,660 - INFO - Page 1: Extracted 1511 characters, 86 lines from e8da5693_R1V0MO844PBLWNEAUETU_8d8d28ec_page_001.pdf
2025-09-24 14:48:57,661 - INFO - Successfully processed page 1
2025-09-24 14:48:57,661 - INFO - Combined 2 pages into final text
2025-09-24 14:48:57,661 - INFO - Text validation for e8da5693_R1V0MO844PBLWNEAUETU: 2460 characters, 2 pages
2025-09-24 14:48:57,662 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:48:57,662 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:48:57,762 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '54f283d2-b6ee-4c21-b5f2-047d41044e1f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:18:57 GMT', 'content-type': 'application/json', 'content-length': '1296', 'connection': 'keep-alive', 'x-amzn-requestid': '54f283d2-b6ee-4c21-b5f2-047d41044e1f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a page with "WEIGHT VALIDATION CERTIFICATE" and weight details. That matches weight_and_inspection_cert? The page has "WEIGHT VALIDATION CERTIFICATE" and "Total Actual Verified Weight" etc. This is likely a weight validation certificate, which is similar to weight_and_inspection_cert. The description says "Weight Validation Certificate" used for freight. The definitions: weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. That seems to match. It includes weight. So doc_type: weight_and_inspection_cert.\n\nWe also need to check if it\'s nmfc_cert? That requires keywords like "Inspected against original" or "Corrected against Actual" etc. Not present. So weight_and_inspection_cert.\n\nWe need to output tool call classify_logistics_doc_type with documents array: single object with page_no 1, doc_type weight_and_inspection_cert.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2718, 'outputTokens': 218, 'totalTokens': 2936}, 'metrics': {'latencyMs': 1045}}
2025-09-24 14:48:57,762 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/b6c351ed_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:48:57,853 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3ff49c87_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:48:57,869 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '22247a7b-aa80-4e82-88a3-c28c87c267a6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:18:57 GMT', 'content-type': 'application/json', 'content-length': '1246', 'connection': 'keep-alive', 'x-amzn-requestid': '22247a7b-aa80-4e82-88a3-c28c87c267a6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Here only page1. Identify type. The page is a weight certificate. Keywords: WEIGHT CERTIFICATE, Freight Bill Number, Weighed Date, scales, weight. It is a weight certificate. According to enum, we have weight_and_inspection_cert, inspection_cert, nmfc_cert. The definition: weight_and_inspection_cert: certificate/report confirming weight possibly with length, width, height but not rechecked. nmfc_cert: NMFC classification certificate or correction notice. The page includes weight, and references NMFC 100 series items etc. It\'s a weight certificate. The keywords: WEIGHT CERTIFICATE, "WEIGHT CERTIFICATION". It has weight. So classify as weight_and_inspection_cert? Or weight_and_inspection_cert is for weight certificate. The nmfc_cert is for correction notice. This seems just weight certificate. So doc_type = weight_and_inspection_cert. No continuation. So output JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2672, 'outputTokens': 214, 'totalTokens': 2886}, 'metrics': {'latencyMs': 1160}}
2025-09-24 14:48:57,878 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/50587b12_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:48:57,878 - INFO - 

DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:48:57,880 - INFO - 

✓ Saved result: output/run1_DCY7SLNMWUXIENOREHQF.json
2025-09-24 14:48:58,174 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/b6c351ed_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:48:58,193 - INFO - 

K9VSARJOKAIZHNJ5RBDT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:48:58,193 - INFO - 

✓ Saved result: output/run1_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 14:48:58,196 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'be3b21c8-29cb-4ae8-aeb2-fd532c0f7cce', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:18:58 GMT', 'content-type': 'application/json', 'content-length': '1460', 'connection': 'keep-alive', 'x-amzn-requestid': 'be3b21c8-29cb-4ae8-aeb2-fd532c0f7cce'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. It\'s a "Freight Certificate" with dimensions, weight, etc. The keywords: "CERTIFICATE", "Total Weight", "Total Rated Dimensions", "Total Mileage", "Total Rated Cube". This looks like a "weight_and_inspection_cert" or "inspection_cert" or maybe "weight_and_inspection_cert" or "inspection_cert" or "certificate". Let\'s see. The definition: weight_and_inspection_cert is a certificate/report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. There is no mention of weight certificate or inspection certificate but it\'s a "Freight Certificate" and includes weight and dimensions. So likely weight_and_inspection_cert.\n\nAlso check nmfc_cert: includes keywords like "Original", "Corrected", "Inspected against original", "NMFC Code". Not present. So weight_and_inspection_cert.\n\nThus classification: page 1 doc_type: weight_and_inspection_cert.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2606, 'outputTokens': 246, 'totalTokens': 2852}, 'metrics': {'latencyMs': 1157}}
2025-09-24 14:48:58,196 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/be3a402c_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:48:58,304 - INFO - Page 1: Extracted 580 characters, 48 lines from 53896267_WRKSHW76B3QUG47QWR75_ad4d5df5_page_001.pdf
2025-09-24 14:48:58,304 - INFO - Successfully processed page 1
2025-09-24 14:48:58,304 - INFO - Combined 1 pages into final text
2025-09-24 14:48:58,304 - INFO - Text validation for 53896267_WRKSHW76B3QUG47QWR75: 597 characters, 1 pages
2025-09-24 14:48:58,305 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:48:58,305 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:48:58,509 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/50587b12_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:48:58,514 - INFO - 

HFPAXYL947DH59AB12FL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:48:58,514 - INFO - 

✓ Saved result: output/run1_HFPAXYL947DH59AB12FL.json
2025-09-24 14:48:58,861 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/be3a402c_HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:48:59,136 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4846b585-2a0f-4619-9ea7-08701b3b7a40', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:18:59 GMT', 'content-type': 'application/json', 'content-length': '975', 'connection': 'keep-alive', 'x-amzn-requestid': '4846b585-2a0f-4619-9ea7-08701b3b7a40'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It\'s a CUBE MEASUREMENT CERTIFICATE. That likely matches "inspection_cert" or "weight_and_inspection_cert"? It says CUBE MEASUREMENT CERTIFICATE. That is a cube measurement certificate, which is a type of inspection certificate? The definition says inspection_cert is Inspection Certificate, Cube Measurement certificate. So classify as inspection_cert.\n\nCheck weight_and_inspection_cert: requires weight mentioned in lbs or tons. This doc doesn\'t have weight. So it\'s inspection_cert.\n\nThus output JSON array with one object: page_no 1, doc_type "inspection_cert".\n\nReturn via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2618, 'outputTokens': 149, 'totalTokens': 2767}, 'metrics': {'latencyMs': 829}}
2025-09-24 14:48:59,137 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5069cec4_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:48:59,169 - INFO - 

QHD3LC0DU6S8O2YVVS60.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "inspection_cert"
        }
    ]
}
2025-09-24 14:48:59,169 - INFO - 

✓ Saved result: output/run1_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 14:48:59,455 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5069cec4_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:48:59,480 - INFO - Page 2: Extracted 540 characters, 29 lines from 5038c27f_OR9EL08KIKNQPZ3UV3HH_65122e92_page_002.pdf
2025-09-24 14:48:59,481 - INFO - Successfully processed page 2
2025-09-24 14:48:59,542 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '24fe94a7-41bd-4657-8c43-91020fe68456', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:18:59 GMT', 'content-type': 'application/json', 'content-length': '3128', 'connection': 'keep-alive', 'x-amzn-requestid': '24fe94a7-41bd-4657-8c43-91020fe68456'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Two pages. The pages appear to be inspection correction notice. It includes "Inspection Correction Notice". There\'s mention NMFC, weights, etc. Need to determine correct doc_type. Looking at enum, we have "inspection_cert" and "weight_and_inspection_cert" and "nmfc_cert". The doc seems to be an "Inspection Correction Notice" that has corrections to weight and NMFC. It might be a "weight_and_inspection_cert" or "nmfc_cert". Let\'s examine the description: "Inspection Correction Notice" includes NMFC, weight, before and after. It also says "This will certify that this shipment has been (Inspected/Dimensioned/Weighed) and the descriptions have been found to be as indicated above." So it\'s a certificate of inspection that includes weight and NMFC. The definition: weight_and_inspection_cert - "Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". It says: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." And "Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert."\n\nSo if it has NMFC and weight, it might be nmfc_cert. The keywords for nmfc_cert: "As described and As found or Original and inspection or Corrected class or Correction information" and optional NMFC Code, class #. But this doc includes "Correction Details" and "NMFC". Not clear if it qualifies as nmfc_cert. The instruction: "If weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So we need to see if weight_and_inspection_cert has keywords from nmfc_cert. Let\'s check nmfc_cert keywords: "As described and As found or Original and inspection or Corrected class or Correction information" and optional NMFC Code, class #. Our doc has "Correction Details" and "NMFC". That might match "Correction information" and "NMFC". So likely nmfc_cert. Additionally, "Inspection Correction Notice" is a correction notice. So I think nmfc_cert is the right classification.\n\nThus both pages are part of same doc type, nmfc_cert. Also check continuation: page2 has same content but no "continued" header. But they seem same. But the instructions: If a page is part of a multi-page single document each page gets same doc_type. So page1 and page2 both nmfc_cert.\n\nThus we output two objects: page_no 1, doc_type "nmfc_cert"; page_no 2, same.\n\nWe must use tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2966, 'outputTokens': 635, 'totalTokens': 3601}, 'metrics': {'latencyMs': 2879}}
2025-09-24 14:48:59,543 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e5e99069_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:48:59,571 - INFO - 

DH0JZ2JWDGRHD26BX74C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:48:59,571 - INFO - 

✓ Saved result: output/run1_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 14:48:59,856 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e5e99069_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:49:00,044 - INFO - Page 1: Extracted 1120 characters, 87 lines from 5038c27f_OR9EL08KIKNQPZ3UV3HH_65122e92_page_001.pdf
2025-09-24 14:49:00,044 - INFO - Successfully processed page 1
2025-09-24 14:49:00,045 - INFO - Combined 2 pages into final text
2025-09-24 14:49:00,045 - INFO - Text validation for 5038c27f_OR9EL08KIKNQPZ3UV3HH: 1696 characters, 2 pages
2025-09-24 14:49:00,046 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:49:00,046 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:49:00,070 - INFO - Page 1: Extracted 528 characters, 31 lines from fff5d879_XCJLXZK140FUS8020ZAG_c97d8a0b_page_001.pdf
2025-09-24 14:49:00,071 - INFO - Successfully processed page 1
2025-09-24 14:49:00,071 - INFO - Combined 1 pages into final text
2025-09-24 14:49:00,072 - INFO - Text validation for fff5d879_XCJLXZK140FUS8020ZAG: 545 characters, 1 pages
2025-09-24 14:49:00,072 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:49:00,072 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:49:00,208 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c47603f9-6981-4451-8bba-35376715b9c9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:19:00 GMT', 'content-type': 'application/json', 'content-length': '2628', 'connection': 'keep-alive', 'x-amzn-requestid': 'c47603f9-6981-4451-8bba-35376715b9c9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page as a doc type. The pages are three pages of a document titled "Inspection Correction Notice". That looks like a customs or inspection certificate type. The description says: "Inspection Correction Notice" with PRO number, date, etc. It includes NMFC codes, weight, etc. This seems like a "weight_and_inspection_cert" or "inspection_cert" or "nmfc_cert". Let\'s see the doc types.\n\n- weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width, height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also if weight_and_inspection_cert has nmfc_cert keywords, classify as nmfc_cert. Also if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise inspection_cert.\n\n- nmfc_cert: NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual. Keywords: As described and As found or Original and inspection or Corrected class or Correction information. Also NMFC Code, class #.\n\n- inspection_cert: Inspection certificate other than weight and inspection certificate which doesn\'t have weight mentioned.\n\nOur document is "Inspection Correction Notice" with NMFC codes, weight, etc. It includes "NMFC to 189800-4" etc. It has weight data. It says "Inspection Correction Notice" and has "NMFC to 189800-4". So it\'s a correction notice for NMFC. That looks like nmfc_cert. The description: "This will certify that this shipment has been (Inspected/Dimensioned/Weighed) and the descriptions have been found to be as indicated above." So it\'s a certificate of correction. So likely nmfc_cert.\n\nThus each page is nmfc_cert. All pages same doc type.\n\nWe need to output JSON array of objects? The tool signature expects a single JSON with "documents" array containing objects with page_no and doc_type. So we need to call tool classify_logistics_doc_type with documents array.\n\nWe need to output exactly that. Let\'s do it.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"},{"page_no":3,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3326, 'outputTokens': 495, 'totalTokens': 3821}, 'metrics': {'latencyMs': 2107}}
2025-09-24 14:49:00,213 - INFO - Page 1: Extracted 443 characters, 72 lines from a32149d0_RUDVGETVRZO7XX6YNW7I_83dccab5_page_001.pdf
2025-09-24 14:49:00,213 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e059ab60_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:49:00,214 - INFO - Successfully processed page 1
2025-09-24 14:49:00,214 - INFO - Combined 1 pages into final text
2025-09-24 14:49:00,215 - INFO - Text validation for a32149d0_RUDVGETVRZO7XX6YNW7I: 460 characters, 1 pages
2025-09-24 14:49:00,218 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:49:00,219 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:49:00,272 - INFO - 

NJ4WSZ8BUQAW48V6403N.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 3,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:49:00,272 - INFO - 

✓ Saved result: output/run1_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:49:00,580 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e26c18c9-ac85-4b02-92b6-374af7c26f64', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:19:00 GMT', 'content-type': 'application/json', 'content-length': '1263', 'connection': 'keep-alive', 'x-amzn-requestid': 'e26c18c9-ac85-4b02-92b6-374af7c26f64'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. It says "Weight and Research Linear Certificate", "CERTIFIED ORIGINAL", "Linear Certification", "We certify that the above shipment has been measured and that the linear feet as stated is the true and accurate length of the shipment". Also "Weight and Research" maybe weight and inspection certificate? There\'s a "Weight and Research Linear Certificate". It contains weight, linear feet, dimensions, density. This looks like a weight and inspection certificate. The keywords: "Linear Certificate", "Weight and Research". "Linear Certification". It has "Linear Feet", "Description: SKIDS", "Length, Width, Height". This might be a weight and inspection certificate. The doc type: weight_and_inspection_cert. We see no mention of nmfc. It\'s a certificate of measurement. So weight_and_inspection_cert. We should output JSON with page_no 1 and doc_type weight_and_inspection_cert.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2640, 'outputTokens': 207, 'totalTokens': 2847}, 'metrics': {'latencyMs': 996}}
2025-09-24 14:49:00,580 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/53896267_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:49:00,581 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e059ab60_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:49:00,600 - INFO - 

WRKSHW76B3QUG47QWR75.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:49:00,600 - INFO - 

✓ Saved result: output/run1_WRKSHW76B3QUG47QWR75.json
2025-09-24 14:49:00,839 - INFO - Page 1: Extracted 992 characters, 60 lines from a940d968_Y82AJRDQU1FCXFHREDEA_a78c6c75_page_001.pdf
2025-09-24 14:49:00,840 - INFO - Successfully processed page 1
2025-09-24 14:49:00,840 - INFO - Combined 1 pages into final text
2025-09-24 14:49:00,841 - INFO - Text validation for a940d968_Y82AJRDQU1FCXFHREDEA: 1009 characters, 1 pages
2025-09-24 14:49:00,841 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:49:00,841 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:49:00,895 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/53896267_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:49:02,462 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'effbffdb-b34b-4f68-9c14-ef0535c60dc9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:19:02 GMT', 'content-type': 'application/json', 'content-length': '978', 'connection': 'keep-alive', 'x-amzn-requestid': 'effbffdb-b34b-4f68-9c14-ef0535c60dc9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a page with text: "NMFC CLASSIFICATION CERTIFICATE" and "NMFCC". That matches the nmfc_cert. It says "NMFC CLASSIFICATION CERTIFICATE" and "NMFCC". So doc_type: nmfc_cert. Also note the page has "4 of 6" indicating continuation. Since it\'s page 1 of 6? Actually page says "PAGE: 1" and "4 of 6" at bottom? Wait: It says "PAGE: 1" near top. Then at bottom: "4 of 6". That might be watermark. But anyway this is page 1 of a multi-page certificate. So doc_type nmfc_cert. We must output array of objects each with page_no and doc_type. Only one page. So output:\n\n[{page_no: 1, doc_type: "nmfc_cert"}]\n\nWe need to use the tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2713, 'outputTokens': 206, 'totalTokens': 2919}, 'metrics': {'latencyMs': 996}}
2025-09-24 14:49:02,463 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a32149d0_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:49:02,482 - INFO - 

RUDVGETVRZO7XX6YNW7I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:49:02,482 - INFO - 

✓ Saved result: output/run1_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 14:49:02,765 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a32149d0_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:49:02,899 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'a2cc6533-5990-45cf-853f-cf56cb5d2c2f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:19:02 GMT', 'content-type': 'application/json', 'content-length': '2096', 'connection': 'keep-alive', 'x-amzn-requestid': 'a2cc6533-5990-45cf-853f-cf56cb5d2c2f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only page1 provided. Need to determine doc_type. The page shows a "Freight Certificate" with dimensions, weight, etc. This seems like a "weight_and_inspection_cert" or "inspection_cert" or "nmfc_cert"? The description: "Freight Certificate" with weight and dimensions. It says "Dimensions outlined above are actual dimensions and may be subject to Item 110 paragraph 14 for calculating effective occupancy." It lists Total Weight, Total Rated Dimensions, etc. Does it include weight? Yes 280 lbs. It is a certificate of dimensions. It might be a weight and inspection certificate? Let\'s check the definitions: weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also note: if it has nmfc_cert keywords, classify nmfc_cert instead. But which keywords for nmfc_cert? It says "As described or As found or Original and inspection or Corrected class or Correction information". Not here.\n\nInspection_cert: Inspection certificate other than weight and inspection certificate which doesn\'t have weight mentioned. But this one has weight mentioned. So weight_and_inspection_cert seems appropriate.\n\nCheck if it\'s "inspection_cert" vs "weight_and_inspection_cert". It has weight, so weight_and_inspection_cert. Does it mention "Certificate" maybe "Freight Certificate". That matches weight_and_inspection_cert. So doc_type: weight_and_inspection_cert. There\'s no continuation. So output page 1 with doc_type weight_and_inspection_cert. Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2613, 'outputTokens': 370, 'totalTokens': 2983}, 'metrics': {'latencyMs': 1729}}
2025-09-24 14:49:02,899 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/fff5d879_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:49:02,914 - INFO - 

XCJLXZK140FUS8020ZAG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:49:02,914 - INFO - 

✓ Saved result: output/run1_XCJLXZK140FUS8020ZAG.json
2025-09-24 14:49:03,150 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '384c8b9c-3eaa-48e7-aa88-ebf469976d78', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:19:03 GMT', 'content-type': 'application/json', 'content-length': '4974', 'connection': 'keep-alive', 'x-amzn-requestid': '384c8b9c-3eaa-48e7-aa88-ebf469976d78'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a PDF with two pages. Need to classify each page.\n\nThe content looks like a weight and research program (maybe weight and inspection certificate). The header: "Weight and Research Program" and terms like "We certify that this shipment has been inspected and/or reweighed" and "Weight and Research Section". Also includes "As found upon Examination". This likely is a "weight_and_inspection_cert" or "inspection_cert" or "nmfc_cert" if correction. The description includes "Original" and "Correction". It says "Original Correction". It shows "Original Correction" date. It shows "We certify that this shipment has been inspected and/or reweighed". That matches weight and inspection certificate. Does it mention any correction? The header says "Original Correction". But does it have "Original" or "Correction"? It says "Original Correction" maybe means original or corrected. But no explicit "Original" vs "Correction" section. The text: "Original Correction 9/04/25". It might be a corrected certificate. The instruction for weight_and_inspection_cert: If it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert otherwise keep as weight_and_inspection_cert. Also check if inspection certificate is related to weight and has weight mentioned in lbs or tons classify as weight_and_inspection_cert; else inspection_cert.\n\nWe see weight details: "Weight and Research Program". The weight is indicated: "Weights", "Gross", "Tare" maybe? It lists "Weights" but not explicit weight numbers. It does show "CUBE 22.84 UNITS 1" and "51.00x43.00x18.00" and "UPD PER ITEM 111460 DENS14.97 PCF". It mentions "WEIGHT" maybe? It says "Weight and Research Section". It doesn\'t show explicit weight numbers like "Gross: 500 lbs". But might still be weight and inspection certificate. The presence of "Original Correction" suggests a corrected certificate. For nmfc_cert, keywords: "Original and inspection" or "Corrected class" etc. The text says: "As found upon Examination" and "We certify that this shipment has been inspected and/or reweighed". This seems like a certificate of inspection. It doesn\'t explicitly mention "Original and inspection" or "Corrected". But it says "Original Correction". That might be a combined phrase. But the actual header says "Weight and Research Program". The doc type likely "weight_and_inspection_cert".\n\nBut check if any nmfc_cert keywords present: "As found upon Examination" is part of weight_and_inspection_cert description. But nmfc_cert keywords: "As found or Original and inspection or Corrected class or Correction information". The phrase "Original Correction" might be a keyword? But the guidelines say "If it has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep as weight_and_inspection_cert." The provided keywords for nmfc_cert: "As found or Original and inspection or Corrected class or Correction information". It says "As found upon Examination" appears. That phrase "As found" is a keyword. That might lead to nmfc_cert classification. But also weight_and_inspection_cert also has "As found upon Examination" as part of its description. Actually weight_and_inspection_cert description: "If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise inspection_cert." But it also says "If it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert." That means if we see nmfc_cert keywords, we choose nmfc_cert. The phrase "As found upon Examination" is listed under weight_and_inspection_cert too? Let\'s re-read: In weight_and_inspection_cert description: "If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify as inspection_cert." Then the note: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." The nmfc_cert keywords: "As found or Original and inspection or Corrected class or Correction information". So "As found" appears in weight_and_inspection_cert as well? Actually weight_and_inspection_cert description didn\'t mention "As found". But the note says if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, then classify as nmfc_cert. So if we see "As found" on the page, we might classify as nmfc_cert. The page has "As found upon Examination". That matches "As found" keyword. So likely nmfc_cert.\n\nBut the page also includes "Original'}}}]}}, 'stopReason': 'max_tokens', 'usage': {'inputTokens': 3302, 'outputTokens': 997, 'totalTokens': 4299}, 'metrics': {'latencyMs': 4283}}
2025-09-24 14:49:03,150 - ERROR - Max tokens reached
2025-09-24 14:49:03,150 - ERROR - Failed to extract tool response: Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file
2025-09-24 14:49:03,151 - ERROR - Processing failed for s3://document-extraction-logistically/temp/e8da5693_R1V0MO844PBLWNEAUETU.pdf: Unexpected tool response format from Bedrock
2025-09-24 14:49:03,151 - ERROR - ❌ Classification failed for s3://document-extraction-logistically/temp/e8da5693_R1V0MO844PBLWNEAUETU.pdf: Unexpected tool response format from Bedrock
2025-09-24 14:49:03,155 - ERROR - ❌ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 132, in extract_tool_response
    raise Exception("Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file")
Exception: Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 149, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 14:49:03,155 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 14:49:03,196 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/fff5d879_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:49:03,197 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/R1V0MO844PBLWNEAUETU.pdf: Unexpected tool response format from Bedrock
2025-09-24 14:49:03,199 - ERROR - ✗ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 132, in extract_tool_response
    raise Exception("Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file")
Exception: Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 149, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 14:49:03,200 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 14:49:03,475 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e8da5693_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 14:49:03,671 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '7f566051-5304-47ec-a55f-fdcb1b582d0f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:19:03 GMT', 'content-type': 'application/json', 'content-length': '2899', 'connection': 'keep-alive', 'x-amzn-requestid': '7f566051-5304-47ec-a55f-fdcb1b582d0f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a PDF with two pages. They look like a weight and research certificate (W&R). The document type is "weight_and_inspection_cert" or "inspection_cert"? Let\'s examine.\n\nThe content includes "WEIGHT & RESEARCH CERTIFICATE" header. The definitions: weight_and_inspection_cert: certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Check if weight_and_inspection_cert is having keywords mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert. Also check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise, inspection_cert.\n\nThis document is weight & research certificate. That seems a weight certificate. So doc_type: weight_and_inspection_cert.\n\nBut we need to check if nmfc_cert? The nmfc_cert keywords: "As described and As found or Original and inspection or Corrected class or Correction information" etc. Not present. So weight_and_inspection_cert.\n\nNow, check for continuation: Page2 might be a continuation of page1? The text says "WEIGHT & RESEARCH CERTIFICATE" same header. Are there continuation markers? In page1, there is no continuation and page2 is same header, but page2 may be continuation? The instruction: If the page starts with any of the following: "continued", "continued on next page", "continued on next", etc., or indicates pagination like "page 2 of 3", or any other signal indicating continuation of previous page/document. This page doesn\'t start with those. But page2 is likely the second page of the same document. The document type is weight_and_inspection_cert and we should assign the same doc_type to each page. The instruction: If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page). So we need to assign weight_and_inspection_cert to both page1 and page2. There\'s no explicit "continued" but it\'s still part of the same document. We don\'t need to detect continuation for classification; we just repeat same doc_type.\n\nThus output: for page 1: doc_type weight_and_inspection_cert. For page 2: same.\n\nWe need to output via tool call classify_logistics_doc_type with documents array of objects with page_no and doc_type.\n\nReturn tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3124, 'outputTokens': 554, 'totalTokens': 3678}, 'metrics': {'latencyMs': 2492}}
2025-09-24 14:49:03,671 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5038c27f_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:49:03,711 - INFO - 

OR9EL08KIKNQPZ3UV3HH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:49:03,711 - INFO - 

✓ Saved result: output/run1_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 14:49:03,993 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5038c27f_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:49:04,258 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1df9e5dd-f77e-4629-a476-97c0aa533f3d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:19:04 GMT', 'content-type': 'application/json', 'content-length': '2782', 'connection': 'keep-alive', 'x-amzn-requestid': '1df9e5dd-f77e-4629-a476-97c0aa533f3d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. We must determine doc_type. The page shows inspection certificate. Keywords: INSPECTION CERTIFICATE, "THIS IS NOT AN INVOICE/FREIGHT BILL", "DATE OF CORRECTION", "ISSUING TERMINAL", "PRO NUMBER", "SHIPMENT DATE", "BILLING TERMS", "ORIGINAL DENSITY", "REVISED DENSITY", "CHANGE TYPE", "WEIGHT", "FREIGHT DESCRIBED AS", "AS FOUND UPON EXAMINATION", "DIMENSIONS", "CLASS", "SUPPLIES", "TOTAL PIECES", "TOTAL WEIGHT", "IDENTIFYING REFERENCE NUMBERS", "SHIPMENT ID", "P.O. NUMBER", "BOL NUMBER", "SHIPPER\'S NUMBER", "NS", "KOBA6613837", "REMARKS". This is clearly an inspection certificate. According to definitions: "inspection_cert" - Inspection certificate, Cube Measurement certificate. Not weight_and_inspection_cert unless weight mentioned and weight? It mentions weight, but weight_and_inspection_cert requires weight mention and specific conditions. Also, "inspection_cert" is for inspection certificate other than weight and inspection certificate which doesn\'t have weight mentioned. But here weight is mentioned. Wait: There\'s nuance: weight_and_inspection_cert is for weight and inspection certificate with weight mentioned in lbs or tons. But they say to check if weight_and_inspection_cert has keywords mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert. There\'s mention of weight. So maybe weight_and_inspection_cert. Let\'s examine definitions: Under "weight_and_inspection_cert" description: "Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also note: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So if it has nmfc keywords, then nmfc. Here no NMFC. So likely weight_and_inspection_cert.\n\nBut also "inspection_cert" definition: "Inspection certificate, Cube Measurement certificate" and description: "Inspection certificate other than weight and inspection certificate which doesn\'t have weight mentioned." So this page has weight. So it\'s weight_and_inspection_cert.\n\nThus doc_type: weight_and_inspection_cert.\n\nReturn JSON via function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2805, 'outputTokens': 543, 'totalTokens': 3348}, 'metrics': {'latencyMs': 2239}}
2025-09-24 14:49:04,258 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a940d968_Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 14:49:04,283 - INFO - 

Y82AJRDQU1FCXFHREDEA.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:49:04,283 - INFO - 

✓ Saved result: output/run1_Y82AJRDQU1FCXFHREDEA.json
2025-09-24 14:49:04,612 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a940d968_Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 14:49:04,613 - INFO - 
📊 Processing Summary:
2025-09-24 14:49:04,613 - INFO -    Total files: 15
2025-09-24 14:49:04,613 - INFO -    Successful: 14
2025-09-24 14:49:04,613 - INFO -    Failed: 1
2025-09-24 14:49:04,613 - INFO -    Duration: 21.79 seconds
2025-09-24 14:49:04,613 - INFO -    Output directory: output
2025-09-24 14:49:04,613 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:49:04,613 - INFO -    📄 A34CDFDJ66EDOZEKZWJL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:49:04,613 - INFO -    📄 D5KCCMIJXGREC9Q81E0H.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:49:04,613 - INFO -    📄 DCY7SLNMWUXIENOREHQF.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:49:04,613 - INFO -    📄 DFY1VDZWR7NBDLJV02G2.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:49:04,613 - INFO -    📄 DH0JZ2JWDGRHD26BX74C.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}
2025-09-24 14:49:04,613 - INFO -    📄 HFPAXYL947DH59AB12FL.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:49:04,614 - INFO -    📄 K9VSARJOKAIZHNJ5RBDT.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:49:04,614 - INFO -    📄 NJ4WSZ8BUQAW48V6403N.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"},{"page_no":3,"doc_type":"nmfc_cert"}]}
2025-09-24 14:49:04,614 - INFO -    📄 OR9EL08KIKNQPZ3UV3HH.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:49:04,614 - INFO -    📄 QHD3LC0DU6S8O2YVVS60.pdf: {"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}
2025-09-24 14:49:04,614 - INFO -    📄 RUDVGETVRZO7XX6YNW7I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:49:04,614 - INFO -    📄 WRKSHW76B3QUG47QWR75.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:49:04,614 - INFO -    📄 XCJLXZK140FUS8020ZAG.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:49:04,614 - INFO -    📄 Y82AJRDQU1FCXFHREDEA.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:49:04,614 - ERROR - 
❌ Errors:
2025-09-24 14:49:04,614 - ERROR -    Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/R1V0MO844PBLWNEAUETU.pdf: Unexpected tool response format from Bedrock
2025-09-24 14:49:04,615 - INFO - 
============================================================================================================================================
2025-09-24 14:49:04,615 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:49:04,615 - INFO - ============================================================================================================================================
2025-09-24 14:49:04,615 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:49:04,615 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:49:04,615 - INFO - A34CDFDJ66EDOZEKZWJL.pdf                           1      weight_and_inspect... run1_A34CDFDJ66EDOZEKZWJL.json                    
2025-09-24 14:49:04,615 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 14:49:04,615 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:49:04,615 - INFO - 
2025-09-24 14:49:04,615 - INFO - D5KCCMIJXGREC9Q81E0H.pdf                           1      nmfc_cert            run1_D5KCCMIJXGREC9Q81E0H.json                    
2025-09-24 14:49:04,615 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/D5KCCMIJXGREC9Q81E0H.pdf
2025-09-24 14:49:04,615 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_D5KCCMIJXGREC9Q81E0H.json
2025-09-24 14:49:04,615 - INFO - 
2025-09-24 14:49:04,615 - INFO - DCY7SLNMWUXIENOREHQF.pdf                           1      weight_and_inspect... run1_DCY7SLNMWUXIENOREHQF.json                    
2025-09-24 14:49:04,615 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 14:49:04,616 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DCY7SLNMWUXIENOREHQF.json
2025-09-24 14:49:04,616 - INFO - 
2025-09-24 14:49:04,616 - INFO - DFY1VDZWR7NBDLJV02G2.pdf                           1      weight_and_inspect... run1_DFY1VDZWR7NBDLJV02G2.json                    
2025-09-24 14:49:04,616 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 14:49:04,616 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 14:49:04,616 - INFO - 
2025-09-24 14:49:04,616 - INFO - DH0JZ2JWDGRHD26BX74C.pdf                           1      nmfc_cert            run1_DH0JZ2JWDGRHD26BX74C.json                    
2025-09-24 14:49:04,616 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:49:04,616 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 14:49:04,616 - INFO - 
2025-09-24 14:49:04,616 - INFO - DH0JZ2JWDGRHD26BX74C.pdf                           2      nmfc_cert            run1_DH0JZ2JWDGRHD26BX74C.json                    
2025-09-24 14:49:04,616 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 14:49:04,616 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 14:49:04,616 - INFO - 
2025-09-24 14:49:04,616 - INFO - HFPAXYL947DH59AB12FL.pdf                           1      weight_and_inspect... run1_HFPAXYL947DH59AB12FL.json                    
2025-09-24 14:49:04,616 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/HFPAXYL947DH59AB12FL.pdf
2025-09-24 14:49:04,616 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HFPAXYL947DH59AB12FL.json
2025-09-24 14:49:04,616 - INFO - 
2025-09-24 14:49:04,616 - INFO - K9VSARJOKAIZHNJ5RBDT.pdf                           1      weight_and_inspect... run1_K9VSARJOKAIZHNJ5RBDT.json                    
2025-09-24 14:49:04,616 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 14:49:04,617 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 14:49:04,617 - INFO - 
2025-09-24 14:49:04,617 - INFO - NJ4WSZ8BUQAW48V6403N.pdf                           1      nmfc_cert            run1_NJ4WSZ8BUQAW48V6403N.json                    
2025-09-24 14:49:04,617 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:49:04,617 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:49:04,617 - INFO - 
2025-09-24 14:49:04,617 - INFO - NJ4WSZ8BUQAW48V6403N.pdf                           2      nmfc_cert            run1_NJ4WSZ8BUQAW48V6403N.json                    
2025-09-24 14:49:04,617 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:49:04,617 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:49:04,617 - INFO - 
2025-09-24 14:49:04,617 - INFO - NJ4WSZ8BUQAW48V6403N.pdf                           3      nmfc_cert            run1_NJ4WSZ8BUQAW48V6403N.json                    
2025-09-24 14:49:04,617 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 14:49:04,617 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:49:04,617 - INFO - 
2025-09-24 14:49:04,617 - INFO - OR9EL08KIKNQPZ3UV3HH.pdf                           1      weight_and_inspect... run1_OR9EL08KIKNQPZ3UV3HH.json                    
2025-09-24 14:49:04,617 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:49:04,617 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 14:49:04,617 - INFO - 
2025-09-24 14:49:04,617 - INFO - OR9EL08KIKNQPZ3UV3HH.pdf                           2      weight_and_inspect... run1_OR9EL08KIKNQPZ3UV3HH.json                    
2025-09-24 14:49:04,617 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 14:49:04,617 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 14:49:04,617 - INFO - 
2025-09-24 14:49:04,618 - INFO - QHD3LC0DU6S8O2YVVS60.pdf                           1      inspection_cert      run1_QHD3LC0DU6S8O2YVVS60.json                    
2025-09-24 14:49:04,618 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 14:49:04,618 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 14:49:04,618 - INFO - 
2025-09-24 14:49:04,618 - INFO - RUDVGETVRZO7XX6YNW7I.pdf                           1      nmfc_cert            run1_RUDVGETVRZO7XX6YNW7I.json                    
2025-09-24 14:49:04,618 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 14:49:04,618 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 14:49:04,618 - INFO - 
2025-09-24 14:49:04,618 - INFO - WRKSHW76B3QUG47QWR75.pdf                           1      weight_and_inspect... run1_WRKSHW76B3QUG47QWR75.json                    
2025-09-24 14:49:04,618 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/WRKSHW76B3QUG47QWR75.pdf
2025-09-24 14:49:04,618 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WRKSHW76B3QUG47QWR75.json
2025-09-24 14:49:04,618 - INFO - 
2025-09-24 14:49:04,618 - INFO - XCJLXZK140FUS8020ZAG.pdf                           1      weight_and_inspect... run1_XCJLXZK140FUS8020ZAG.json                    
2025-09-24 14:49:04,618 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/XCJLXZK140FUS8020ZAG.pdf
2025-09-24 14:49:04,618 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_XCJLXZK140FUS8020ZAG.json
2025-09-24 14:49:04,618 - INFO - 
2025-09-24 14:49:04,618 - INFO - Y82AJRDQU1FCXFHREDEA.pdf                           1      weight_and_inspect... run1_Y82AJRDQU1FCXFHREDEA.json                    
2025-09-24 14:49:04,618 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/Y82AJRDQU1FCXFHREDEA.pdf
2025-09-24 14:49:04,618 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y82AJRDQU1FCXFHREDEA.json
2025-09-24 14:49:04,618 - INFO - 
2025-09-24 14:49:04,618 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:49:04,618 - INFO - Total entries: 18
2025-09-24 14:49:04,618 - INFO - ============================================================================================================================================
2025-09-24 14:49:04,618 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:49:04,618 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:49:04,619 - INFO -   1. A34CDFDJ66EDOZEKZWJL.pdf            Page 1   → weight_and_inspection_cert | run1_A34CDFDJ66EDOZEKZWJL.json
2025-09-24 14:49:04,619 - INFO -   2. D5KCCMIJXGREC9Q81E0H.pdf            Page 1   → nmfc_cert       | run1_D5KCCMIJXGREC9Q81E0H.json
2025-09-24 14:49:04,619 - INFO -   3. DCY7SLNMWUXIENOREHQF.pdf            Page 1   → weight_and_inspection_cert | run1_DCY7SLNMWUXIENOREHQF.json
2025-09-24 14:49:04,619 - INFO -   4. DFY1VDZWR7NBDLJV02G2.pdf            Page 1   → weight_and_inspection_cert | run1_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 14:49:04,619 - INFO -   5. DH0JZ2JWDGRHD26BX74C.pdf            Page 1   → nmfc_cert       | run1_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 14:49:04,619 - INFO -   6. DH0JZ2JWDGRHD26BX74C.pdf            Page 2   → nmfc_cert       | run1_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 14:49:04,619 - INFO -   7. HFPAXYL947DH59AB12FL.pdf            Page 1   → weight_and_inspection_cert | run1_HFPAXYL947DH59AB12FL.json
2025-09-24 14:49:04,619 - INFO -   8. K9VSARJOKAIZHNJ5RBDT.pdf            Page 1   → weight_and_inspection_cert | run1_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 14:49:04,619 - INFO -   9. NJ4WSZ8BUQAW48V6403N.pdf            Page 1   → nmfc_cert       | run1_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:49:04,619 - INFO -  10. NJ4WSZ8BUQAW48V6403N.pdf            Page 2   → nmfc_cert       | run1_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:49:04,619 - INFO -  11. NJ4WSZ8BUQAW48V6403N.pdf            Page 3   → nmfc_cert       | run1_NJ4WSZ8BUQAW48V6403N.json
2025-09-24 14:49:04,619 - INFO -  12. OR9EL08KIKNQPZ3UV3HH.pdf            Page 1   → weight_and_inspection_cert | run1_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 14:49:04,619 - INFO -  13. OR9EL08KIKNQPZ3UV3HH.pdf            Page 2   → weight_and_inspection_cert | run1_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 14:49:04,619 - INFO -  14. QHD3LC0DU6S8O2YVVS60.pdf            Page 1   → inspection_cert | run1_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 14:49:04,619 - INFO -  15. RUDVGETVRZO7XX6YNW7I.pdf            Page 1   → nmfc_cert       | run1_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 14:49:04,619 - INFO -  16. WRKSHW76B3QUG47QWR75.pdf            Page 1   → weight_and_inspection_cert | run1_WRKSHW76B3QUG47QWR75.json
2025-09-24 14:49:04,619 - INFO -  17. XCJLXZK140FUS8020ZAG.pdf            Page 1   → weight_and_inspection_cert | run1_XCJLXZK140FUS8020ZAG.json
2025-09-24 14:49:04,619 - INFO -  18. Y82AJRDQU1FCXFHREDEA.pdf            Page 1   → weight_and_inspection_cert | run1_Y82AJRDQU1FCXFHREDEA.json
2025-09-24 14:49:04,619 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:49:04,620 - ERROR - 🐛 Error occurred during processing - entering debugger...
2025-09-24 14:49:04,620 - ERROR - 🐛 Debug info: {'exception': Exception('Unexpected tool response format from Bedrock'), 'traceback': 'Traceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 132, in extract_tool_response\n    raise Exception("Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file")\nException: Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file\n    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)\n  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync\n    result = loop.run_until_complete(llm_classification(s3_uri))\n  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete\n    return future.result()\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main\n    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock\n    content = bedrock_processor.extract_tool_response(response)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 149, in extract_tool_response\n    raise Exception("Unexpected tool response format from Bedrock")\nException: Unexpected tool response format from Bedrock\n', 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert/R1V0MO844PBLWNEAUETU.pdf', 's3_uri': 's3://document-extraction-logistically/temp/e8da5693_R1V0MO844PBLWNEAUETU.pdf', 'location': 'process_single_file'}
2025-09-24 14:49:04,620 - ERROR - 🐛 Location: process_single_file
2025-09-24 14:49:04,620 - ERROR - 🐛 Exception: Unexpected tool response format from Bedrock
2025-09-24 14:49:04,620 - ERROR - 🐛 Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 132, in extract_tool_response
    raise Exception("Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file")
Exception: Max tokens reached. Please increase MAX_TOKENS_CLASSIFICATION in .env file

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 149, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

