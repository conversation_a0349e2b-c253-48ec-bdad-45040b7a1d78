2025-09-24 14:18:25,536 - INFO - Logging initialized. Log file: logs/test_classification_20250924_141825.log
2025-09-24 14:18:25,536 - INFO - 📁 Found 9 files to process
2025-09-24 14:18:25,536 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:18:25,536 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 14:18:25,536 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 14:18:25,536 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 14:18:25,536 - INFO - ⬆️ [14:18:25] Uploading: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:18:27,011 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf -> s3://document-extraction-logistically/temp/0943358a_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:18:27,012 - INFO - 🔍 [14:18:27] Starting classification: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:18:27,013 - INFO - ⬆️ [14:18:27] Uploading: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:18:27,013 - INFO - Initializing TextractProcessor...
2025-09-24 14:18:27,038 - INFO - Initializing BedrockProcessor...
2025-09-24 14:18:27,044 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0943358a_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:18:27,044 - INFO - Processing PDF from S3...
2025-09-24 14:18:27,044 - INFO - Downloading PDF from S3 to /tmp/tmpgzu2qjk6/0943358a_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:18:27,725 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf -> s3://document-extraction-logistically/temp/68109037_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:18:27,726 - INFO - 🔍 [14:18:27] Starting classification: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:18:27,726 - INFO - ⬆️ [14:18:27] Uploading: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:18:27,726 - INFO - Initializing TextractProcessor...
2025-09-24 14:18:27,734 - INFO - Initializing BedrockProcessor...
2025-09-24 14:18:27,736 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/68109037_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:18:27,736 - INFO - Processing PDF from S3...
2025-09-24 14:18:27,736 - INFO - Downloading PDF from S3 to /tmp/tmp5rp3ysnh/68109037_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:18:28,403 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:18:28,403 - INFO - Splitting PDF into individual pages...
2025-09-24 14:18:28,404 - INFO - Splitting PDF 0943358a_B3SIRREC9IAVZOJVDQSN into 1 pages
2025-09-24 14:18:28,409 - INFO - Split PDF into 1 pages
2025-09-24 14:18:28,409 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:18:28,409 - INFO - Expected pages: [1]
2025-09-24 14:18:28,572 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf -> s3://document-extraction-logistically/temp/53f5db0f_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:18:28,572 - INFO - 🔍 [14:18:28] Starting classification: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:18:28,574 - INFO - ⬆️ [14:18:28] Uploading: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:18:28,576 - INFO - Initializing TextractProcessor...
2025-09-24 14:18:28,603 - INFO - Initializing BedrockProcessor...
2025-09-24 14:18:28,612 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/53f5db0f_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:18:28,613 - INFO - Processing PDF from S3...
2025-09-24 14:18:28,613 - INFO - Downloading PDF from S3 to /tmp/tmptq7etf3k/53f5db0f_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:18:29,053 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:18:29,053 - INFO - Splitting PDF into individual pages...
2025-09-24 14:18:29,053 - INFO - Splitting PDF 68109037_CUF54EHGMLQ57HR93DRB into 1 pages
2025-09-24 14:18:29,056 - INFO - Split PDF into 1 pages
2025-09-24 14:18:29,056 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:18:29,056 - INFO - Expected pages: [1]
2025-09-24 14:18:29,744 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf -> s3://document-extraction-logistically/temp/20b9b7ea_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:18:29,744 - INFO - 🔍 [14:18:29] Starting classification: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:18:29,745 - INFO - ⬆️ [14:18:29] Uploading: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:18:29,746 - INFO - Initializing TextractProcessor...
2025-09-24 14:18:29,754 - INFO - Initializing BedrockProcessor...
2025-09-24 14:18:29,757 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/20b9b7ea_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:18:29,757 - INFO - Processing PDF from S3...
2025-09-24 14:18:29,757 - INFO - Downloading PDF from S3 to /tmp/tmp623lq7ky/20b9b7ea_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:18:30,196 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:18:30,196 - INFO - Splitting PDF into individual pages...
2025-09-24 14:18:30,196 - INFO - Splitting PDF 53f5db0f_FYQQGIW8Z9DSAPCL0S9G into 1 pages
2025-09-24 14:18:30,197 - INFO - Split PDF into 1 pages
2025-09-24 14:18:30,197 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:18:30,197 - INFO - Expected pages: [1]
2025-09-24 14:18:30,390 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf -> s3://document-extraction-logistically/temp/eeacef43_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:18:30,390 - INFO - 🔍 [14:18:30] Starting classification: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:18:30,391 - INFO - Initializing TextractProcessor...
2025-09-24 14:18:30,391 - INFO - ⬆️ [14:18:30] Uploading: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:18:30,409 - INFO - Initializing BedrockProcessor...
2025-09-24 14:18:30,415 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/eeacef43_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:18:30,416 - INFO - Processing PDF from S3...
2025-09-24 14:18:30,416 - INFO - Downloading PDF from S3 to /tmp/tmp31xrpag4/eeacef43_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:18:31,311 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf -> s3://document-extraction-logistically/temp/e583cf2e_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:18:31,311 - INFO - 🔍 [14:18:31] Starting classification: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:18:31,311 - INFO - ⬆️ [14:18:31] Uploading: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:18:31,312 - INFO - Initializing TextractProcessor...
2025-09-24 14:18:31,331 - INFO - Initializing BedrockProcessor...
2025-09-24 14:18:31,335 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e583cf2e_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:18:31,335 - INFO - Processing PDF from S3...
2025-09-24 14:18:31,335 - INFO - Downloading PDF from S3 to /tmp/tmpph7tnli_/e583cf2e_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:18:31,911 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 14:18:31,912 - INFO - Splitting PDF into individual pages...
2025-09-24 14:18:31,914 - INFO - Splitting PDF 20b9b7ea_IZTBXFPGXBFH3DV900G4 into 1 pages
2025-09-24 14:18:31,917 - INFO - Split PDF into 1 pages
2025-09-24 14:18:31,917 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:18:31,917 - INFO - Expected pages: [1]
2025-09-24 14:18:31,925 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf -> s3://document-extraction-logistically/temp/74c76960_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:18:31,926 - INFO - 🔍 [14:18:31] Starting classification: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:18:31,926 - INFO - ⬆️ [14:18:31] Uploading: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:18:31,928 - INFO - Initializing TextractProcessor...
2025-09-24 14:18:31,976 - INFO - Initializing BedrockProcessor...
2025-09-24 14:18:31,978 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/74c76960_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:18:31,979 - INFO - Processing PDF from S3...
2025-09-24 14:18:31,979 - INFO - Downloading PDF from S3 to /tmp/tmpq72xbmb6/74c76960_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:18:32,242 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:18:32,242 - INFO - Splitting PDF into individual pages...
2025-09-24 14:18:32,243 - INFO - Splitting PDF eeacef43_MR6ONA8GK6HN1LCZHEX3 into 1 pages
2025-09-24 14:18:32,244 - INFO - Split PDF into 1 pages
2025-09-24 14:18:32,244 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:18:32,244 - INFO - Expected pages: [1]
2025-09-24 14:18:32,305 - INFO - Page 1: Extracted 1454 characters, 85 lines from 0943358a_B3SIRREC9IAVZOJVDQSN_7a4e394e_page_001.pdf
2025-09-24 14:18:32,305 - INFO - Successfully processed page 1
2025-09-24 14:18:32,305 - INFO - Combined 1 pages into final text
2025-09-24 14:18:32,305 - INFO - Text validation for 0943358a_B3SIRREC9IAVZOJVDQSN: 1471 characters, 1 pages
2025-09-24 14:18:32,305 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:18:32,306 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:18:32,540 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf -> s3://document-extraction-logistically/temp/d41b134c_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:18:32,540 - INFO - 🔍 [14:18:32] Starting classification: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:18:32,541 - INFO - ⬆️ [14:18:32] Uploading: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:18:32,547 - INFO - Initializing TextractProcessor...
2025-09-24 14:18:32,559 - INFO - Initializing BedrockProcessor...
2025-09-24 14:18:32,563 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d41b134c_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:18:32,563 - INFO - Processing PDF from S3...
2025-09-24 14:18:32,563 - INFO - Downloading PDF from S3 to /tmp/tmpowc81v55/d41b134c_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:18:32,776 - INFO - Page 1: Extracted 1255 characters, 79 lines from 68109037_CUF54EHGMLQ57HR93DRB_4360de02_page_001.pdf
2025-09-24 14:18:32,777 - INFO - Successfully processed page 1
2025-09-24 14:18:32,777 - INFO - Combined 1 pages into final text
2025-09-24 14:18:32,777 - INFO - Text validation for 68109037_CUF54EHGMLQ57HR93DRB: 1272 characters, 1 pages
2025-09-24 14:18:32,777 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:18:32,777 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:18:33,132 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf -> s3://document-extraction-logistically/temp/703b09ea_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:18:33,132 - INFO - 🔍 [14:18:33] Starting classification: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:18:33,134 - INFO - Initializing TextractProcessor...
2025-09-24 14:18:33,144 - INFO - Initializing BedrockProcessor...
2025-09-24 14:18:33,148 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/703b09ea_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:18:33,148 - INFO - Processing PDF from S3...
2025-09-24 14:18:33,148 - INFO - Downloading PDF from S3 to /tmp/tmp3h1pcudk/703b09ea_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:18:33,690 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 14:18:33,691 - INFO - Splitting PDF into individual pages...
2025-09-24 14:18:33,693 - INFO - Splitting PDF e583cf2e_PB67IAPSJB1DZWMDIE1H into 2 pages
2025-09-24 14:18:33,699 - INFO - Split PDF into 2 pages
2025-09-24 14:18:33,700 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:18:33,700 - INFO - Expected pages: [1, 2]
2025-09-24 14:18:33,808 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:18:33,808 - INFO - Splitting PDF into individual pages...
2025-09-24 14:18:33,810 - INFO - Splitting PDF d41b134c_U7BB1XSF3ASMIAE1MQ5I into 1 pages
2025-09-24 14:18:33,812 - INFO - Split PDF into 1 pages
2025-09-24 14:18:33,812 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:18:33,812 - INFO - Expected pages: [1]
2025-09-24 14:18:33,904 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:18:33,904 - INFO - Splitting PDF into individual pages...
2025-09-24 14:18:33,905 - INFO - Splitting PDF 74c76960_PEE2ZFMV7X0A0FL35G4G into 1 pages
2025-09-24 14:18:33,907 - INFO - Split PDF into 1 pages
2025-09-24 14:18:33,907 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:18:33,907 - INFO - Expected pages: [1]
2025-09-24 14:18:34,978 - INFO - Page 1: Extracted 1463 characters, 95 lines from 53f5db0f_FYQQGIW8Z9DSAPCL0S9G_36092531_page_001.pdf
2025-09-24 14:18:34,978 - INFO - Successfully processed page 1
2025-09-24 14:18:34,979 - INFO - Combined 1 pages into final text
2025-09-24 14:18:34,979 - INFO - Text validation for 53f5db0f_FYQQGIW8Z9DSAPCL0S9G: 1480 characters, 1 pages
2025-09-24 14:18:34,979 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:18:34,980 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:18:35,093 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6ca924eb-7c14-44ee-9d49-ed5e37dfeb21', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:48:34 GMT', 'content-type': 'application/json', 'content-length': '1278', 'connection': 'keep-alive', 'x-amzn-requestid': '6ca924eb-7c14-44ee-9d49-ed5e37dfeb21'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. We need to determine doc_type. The page appears to be a weight and research program certificate, with weight and inspection certificate. The header shows "Weight and Research Program" and "Weight and Research Section". There\'s "We certify that this shipment has been inspected and/or reweighed". It has declared value, but no HS codes. It has "Weight and Research". Also includes "As found upon Examination". This seems like a Weight and Inspection Certificate. According to enum, weight_and_inspection_cert: needs weight mention. It has weight details: density 3.8 PCF, maybe weight. The page includes "We certify that this shipment has been inspected and/or reweighed". So classify as weight_and_inspection_cert. There\'s no mention of NMFC or correction. So weight_and_inspection_cert.\n\nThus output: array with one object, page_no 1, doc_type weight_and_inspection_cert. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2920, 'outputTokens': 220, 'totalTokens': 3140}, 'metrics': {'latencyMs': 1049}}
2025-09-24 14:18:35,093 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/68109037_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:18:35,120 - INFO - 

CUF54EHGMLQ57HR93DRB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:18:35,120 - INFO - 

✓ Saved result: output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 14:18:35,397 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/68109037_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:18:35,526 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 14:18:35,526 - INFO - Splitting PDF into individual pages...
2025-09-24 14:18:35,526 - INFO - Splitting PDF 703b09ea_YL4HZPZH7D3B03FZZ5QK into 1 pages
2025-09-24 14:18:35,535 - INFO - Split PDF into 1 pages
2025-09-24 14:18:35,535 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:18:35,535 - INFO - Expected pages: [1]
2025-09-24 14:18:35,694 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd3dbdde7-5f69-4d9b-8f80-5d436a4ba6ae', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:48:35 GMT', 'content-type': 'application/json', 'content-length': '2798', 'connection': 'keep-alive', 'x-amzn-requestid': 'd3dbdde7-5f69-4d9b-8f80-5d436a4ba6ae'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to analyze the content for keywords.\n\nWe see "We certify that this shipment has been inspected and/or reweighed". Also "Weight and Research Program". There\'s mention of "As found upon Examination". It looks like a weight and inspection certificate. Keywords: "Weight and Research Program", "certify that shipment has been inspected". Could be weight_and_inspection_cert. Also "Weight and Research Section". So this is a weight and inspection certificate. But note the enum "weight_and_inspection_cert" earlier: Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also check if weight_and_inspection_cert has weight mention in lbs or tons: yes. There\'s weight details. Also check nmfc_cert: if it has words like "As found upon Examination", "Original Correction" etc. But the description for nmfc_cert: "NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual". The text includes "Original Correction", "As found upon Examination". That suggests nmfc_cert.\n\nBut let\'s check the spec: weight_and_inspection_cert: "Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". Also note: "If weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So we need to check if the text includes keywords of nmfc_cert. The nmfc_cert keywords: "Inspected against original or Corrected against Actual", "Original Correction". The text contains "Original" and "Correction". Also "As found upon Examination". That might be nmfc_cert. Let\'s read the nmfc_cert definition: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." The text indeed has "Original" and "Correction". So likely nmfc_cert.\n\nAlso the text says "As found upon Examination". That aligns with nmfc_cert. So we classify as nmfc_cert.\n\nThus output JSON with one object: page_no:1, doc_type:"nmfc_cert".\n\nWe must use the tool classify_logistics_doc_type.\n\nLet\'s produce tool call.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2982, 'outputTokens': 518, 'totalTokens': 3500}, 'metrics': {'latencyMs': 2190}}
2025-09-24 14:18:35,694 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0943358a_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:18:35,717 - INFO - 

B3SIRREC9IAVZOJVDQSN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:18:35,717 - INFO - 

✓ Saved result: output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 14:18:36,020 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0943358a_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:18:36,656 - INFO - Page 1: Extracted 465 characters, 37 lines from 20b9b7ea_IZTBXFPGXBFH3DV900G4_cfc3b619_page_001.pdf
2025-09-24 14:18:36,656 - INFO - Successfully processed page 1
2025-09-24 14:18:36,657 - INFO - Combined 1 pages into final text
2025-09-24 14:18:36,658 - INFO - Text validation for 20b9b7ea_IZTBXFPGXBFH3DV900G4: 482 characters, 1 pages
2025-09-24 14:18:36,658 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:18:36,658 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:18:36,738 - INFO - Page 1: Extracted 500 characters, 76 lines from eeacef43_MR6ONA8GK6HN1LCZHEX3_baf8597e_page_001.pdf
2025-09-24 14:18:36,738 - INFO - Successfully processed page 1
2025-09-24 14:18:36,738 - INFO - Combined 1 pages into final text
2025-09-24 14:18:36,739 - INFO - Text validation for eeacef43_MR6ONA8GK6HN1LCZHEX3: 517 characters, 1 pages
2025-09-24 14:18:36,739 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:18:36,739 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:18:37,098 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c4f667c0-86b2-4310-862e-21157f9119d1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:48:37 GMT', 'content-type': 'application/json', 'content-length': '876', 'connection': 'keep-alive', 'x-amzn-requestid': 'c4f667c0-86b2-4310-862e-21157f9119d1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. We need to output using tool classify_logistics_doc_type.\n\nWe need to examine content.\n\nWe see "NMFC Classification Certificate" at top. Also "NMFC# 133300 S-3". This seems to be an NMFC classification certificate. According to enum list, \'nmfc_cert\' is a category. The page also contains weight, density, etc. The description says it\'s NMFC Classification Certificate. So doc_type: nmfc_cert.\n\nWe need to output an array of documents with page_no and doc_type. Only page 1.\n\nThus call tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3037, 'outputTokens': 147, 'totalTokens': 3184}, 'metrics': {'latencyMs': 785}}
2025-09-24 14:18:37,098 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/53f5db0f_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:18:37,119 - INFO - 

FYQQGIW8Z9DSAPCL0S9G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:18:37,119 - INFO - 

✓ Saved result: output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 14:18:37,400 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/53f5db0f_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:18:37,937 - INFO - Page 1: Extracted 1119 characters, 62 lines from d41b134c_U7BB1XSF3ASMIAE1MQ5I_a2f1831a_page_001.pdf
2025-09-24 14:18:37,937 - INFO - Successfully processed page 1
2025-09-24 14:18:37,937 - INFO - Combined 1 pages into final text
2025-09-24 14:18:37,937 - INFO - Text validation for d41b134c_U7BB1XSF3ASMIAE1MQ5I: 1136 characters, 1 pages
2025-09-24 14:18:37,937 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:18:37,937 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:18:38,320 - INFO - Page 1: Extracted 477 characters, 75 lines from 74c76960_PEE2ZFMV7X0A0FL35G4G_8e168c3b_page_001.pdf
2025-09-24 14:18:38,320 - INFO - Successfully processed page 1
2025-09-24 14:18:38,320 - INFO - Combined 1 pages into final text
2025-09-24 14:18:38,321 - INFO - Text validation for 74c76960_PEE2ZFMV7X0A0FL35G4G: 494 characters, 1 pages
2025-09-24 14:18:38,321 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:18:38,321 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:18:38,618 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '2598f88f-304f-487a-9d42-52609b0d56fb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:48:38 GMT', 'content-type': 'application/json', 'content-length': '797', 'connection': 'keep-alive', 'x-amzn-requestid': '2598f88f-304f-487a-9d42-52609b0d56fb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. It appears to be an NMFC Classification Certificate. According to spec, doc_type can be nmfc_cert. So we output one object for page 1 with doc_type "nmfc_cert". Use tool classify_logistics_doc_type.\n\nWe need to output JSON array of objects? The tool expects input: { documents: [{page_no, doc_type}] }. So we need to return that via tool call. So call classify_logistics_doc_type with documents: [{page_no:1, doc_type:"nmfc_cert"}].\n\nLet\'s do that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2726, 'outputTokens': 135, 'totalTokens': 2861}, 'metrics': {'latencyMs': 752}}
2025-09-24 14:18:38,618 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/eeacef43_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:18:38,640 - INFO - 

MR6ONA8GK6HN1LCZHEX3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:18:38,640 - INFO - 

✓ Saved result: output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 14:18:38,686 - INFO - Page 2: Extracted 832 characters, 40 lines from e583cf2e_PB67IAPSJB1DZWMDIE1H_86e97aef_page_002.pdf
2025-09-24 14:18:38,686 - INFO - Successfully processed page 2
2025-09-24 14:18:38,690 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'bcf94145-a431-488f-a21d-c2e13b379715', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:48:38 GMT', 'content-type': 'application/json', 'content-length': '1055', 'connection': 'keep-alive', 'x-amzn-requestid': 'bcf94145-a431-488f-a21d-c2e13b379715'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. Determine doc type: looks like "Weight & Inspection Freight Class Certificate" with weight, dimensions, class. This is a weight and inspection certificate. The keywords: "Weight & Inspection", "Freight Class Certificate", "CORRECT CLASS", "Density PCF". According to definitions: weight_and_inspection_cert: "Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". Also note for nmfc_cert: if has keywords like "Inspected against original" etc. Not present. So classify as weight_and_inspection_cert. No continuation. So output: page_no 1, doc_type weight_and_inspection_cert. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2631, 'outputTokens': 172, 'totalTokens': 2803}, 'metrics': {'latencyMs': 865}}
2025-09-24 14:18:38,690 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/20b9b7ea_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:18:38,949 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/eeacef43_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:18:38,958 - INFO - 

IZTBXFPGXBFH3DV900G4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:18:38,958 - INFO - 

✓ Saved result: output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 14:18:39,002 - INFO - Page 1: Extracted 1225 characters, 82 lines from e583cf2e_PB67IAPSJB1DZWMDIE1H_86e97aef_page_001.pdf
2025-09-24 14:18:39,003 - INFO - Successfully processed page 1
2025-09-24 14:18:39,003 - INFO - Combined 2 pages into final text
2025-09-24 14:18:39,004 - INFO - Text validation for e583cf2e_PB67IAPSJB1DZWMDIE1H: 2093 characters, 2 pages
2025-09-24 14:18:39,004 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:18:39,004 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:18:39,271 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/20b9b7ea_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:18:40,109 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c5c806c7-31f7-4889-a848-bf7e30a27703', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:48:39 GMT', 'content-type': 'application/json', 'content-length': '651', 'connection': 'keep-alive', 'x-amzn-requestid': 'c5c806c7-31f7-4889-a848-bf7e30a27703'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify the single page. It\'s an NMFC Classification Certificate. Keywords: "NMFC CLASSIFICATION CERTIFICATE" appears. So doc_type likely nmfc_cert. According to enum details, nmfc_cert is a separate type defined. We should output single object: page_no: 1, doc_type: "nmfc_cert". Use tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2717, 'outputTokens': 96, 'totalTokens': 2813}, 'metrics': {'latencyMs': 572}}
2025-09-24 14:18:40,109 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/74c76960_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:18:40,135 - INFO - 

PEE2ZFMV7X0A0FL35G4G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:18:40,135 - INFO - 

✓ Saved result: output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 14:18:40,415 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/74c76960_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:18:41,348 - INFO - Page 1: Extracted 1248 characters, 88 lines from 703b09ea_YL4HZPZH7D3B03FZZ5QK_4eed5974_page_001.pdf
2025-09-24 14:18:41,349 - INFO - Successfully processed page 1
2025-09-24 14:18:41,350 - INFO - Combined 1 pages into final text
2025-09-24 14:18:41,350 - INFO - Text validation for 703b09ea_YL4HZPZH7D3B03FZZ5QK: 1265 characters, 1 pages
2025-09-24 14:18:41,351 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:18:41,351 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:18:41,551 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3d6a52fa-7da0-402d-876f-6182a0b183bf', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:48:41 GMT', 'content-type': 'application/json', 'content-length': '3041', 'connection': 'keep-alive', 'x-amzn-requestid': '3d6a52fa-7da0-402d-876f-6182a0b183bf'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. Determine if it\'s a weight_and_inspection_cert or inspection_cert or nmfc_cert, etc. The page description: "Inspection Certificate" header. It includes NMFC codes, corrected bill info, corrected weight, cube, density, etc. It describes a correction to a "Bill" (original bill info). Contains "CERTIFICATE INFORMATION" and "CORRECTED BILL INFORMATION". It also indicates "DOCUMENTATION HAS INVOICE PACKING LIST PHOTO AVAILABLE OTHER DESCRIPTION". So it\'s an inspection certificate. The classification: weight_and_inspection_cert vs inspection_cert vs nmfc_cert. Let\'s review definitions:\n\n- weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Note: If it has weight_and_inspection_cert keyword with weight in lbs or tons, classify as weight_and_inspection_cert; else inspection_cert.\n\n- nmfc_cert: NMFC classification certificate or correction notice or weight & inspection certificate but strictly with Inspected against original or Corrected against Actual. Keywords: As described... As found or Original and inspection or Corrected class or Correction information. Also optional: NMFC Code, class #.\n\nIn this page, it\'s "Inspection Certificate" and it\'s a correction of the original bill. It includes "Original Bill Information" and "Corrected Bill Information". That might match the description of nmfc_cert: "Correction notice" for NMFC classification. It says "CORRECTED BILL INFORMATION" and "CUBIC FT: 77.81 DENSITY: 11.18 TOTAL WT: 870". It also says "AN INSPECTION AT AN ABF TERMINAL RESULTED IN A CORRECTION". That indicates it\'s a correction of NMFC classification. That seems to be an NMFC correction notice.\n\nBut also the classification says weight_and_inspection_cert vs nmfc_cert. The note says: check if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep it as weight_and_inspection_cert. So if the document has nmfc_cert keywords, we classify as nmfc_cert. The document indeed includes NMFC 87560 and 87552. It also says "Corrected Bill Information". That likely qualifies as nmfc_cert. Let\'s see the key: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." So this is a correction. So label should be nmfc_cert.\n\nThus page1 -> doc_type: nmfc_cert.\n\nReturn via tool call: classify_logistics_doc_type with documents array that has one entry: page_no: 1, doc_type: "nmfc_cert".\n\nLet\'s produce that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2834, 'outputTokens': 599, 'totalTokens': 3433}, 'metrics': {'latencyMs': 2440}}
2025-09-24 14:18:41,551 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d41b134c_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:18:41,582 - INFO - 

U7BB1XSF3ASMIAE1MQ5I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:18:41,582 - INFO - 

✓ Saved result: output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 14:18:41,882 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d41b134c_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:18:42,473 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '8c58cca1-9ac5-4559-aa4f-35021bd78736', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:48:42 GMT', 'content-type': 'application/json', 'content-length': '2542', 'connection': 'keep-alive', 'x-amzn-requestid': '8c58cca1-9ac5-4559-aa4f-35021bd78736'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a two-page document. The content looks like a Weight & Research Certificate (W&R) from SAIA Motor Freight. The title says "WEIGHT & RESEARCH CERTIFICATE". So likely the type is weight_and_inspection_cert? The description says "WEIGHT & RESEARCH CERTIFICATE" - that\'s a type of certificate that includes weight and inspection. The classification list includes weight_and_inspection_cert. We should check if it contains NMFC codes; yes, NMFC 73260-9. The doc says "NMFC: 73260 /SUB NMFC: 09 CLASS: 70.0". That seems to be a NMFC certificate? The instructions: For weight_and_inspection_cert: "Strickly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." Also: "Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert." So we have weight mentioned in lbs. Also NMFC keyword present. So per rule, if weight_and_inspection_cert has NMFC keywords, classify as nmfc_cert. So likely the doc_type is nmfc_cert. But we need to check if it\'s an NMFC certificate or a Weight & Research Certificate. The definition for nmfc_cert: "NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual". The doc says "WEIGHT & RESEARCH CERTIFICATE" and "W&R APPROVED". It seems like a W&R certificate. It also says "C W&R ISSUED". That could be a W&R certificate. The presence of NMFC code suggests it\'s an NMFC certificate. But the title explicitly says "WEIGHT & RESEARCH CERTIFICATE". The rule says if weight_and_inspection_cert has NMFC keywords, classify it as nmfc_cert. So we should classify as nmfc_cert.\n\nThus both pages are part of same document. So output two objects, page_no 1 and 2, each with doc_type "nmfc_cert".\n\nWe must use the tool classify_logistics_doc_type with documents array. Provide both pages. The tool expects array of objects with page_no and doc_type. Output exactly that.\n\nLet\'s produce JSON array.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3325, 'outputTokens': 525, 'totalTokens': 3850}, 'metrics': {'latencyMs': 2183}}
2025-09-24 14:18:42,473 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e583cf2e_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:18:42,511 - INFO - 

PB67IAPSJB1DZWMDIE1H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 14:18:42,511 - INFO - 

✓ Saved result: output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 14:18:42,790 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e583cf2e_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:18:44,172 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '61fd1ca5-571b-45f5-90c5-d2183b441324', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:48:44 GMT', 'content-type': 'application/json', 'content-length': '1609', 'connection': 'keep-alive', 'x-amzn-requestid': '61fd1ca5-571b-45f5-90c5-d2183b441324'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page given. It says "WEIGHTS & INSPECTION CERTIFICATE". Keywords: Weight and Inspection Certificate. In enum: weight_and_inspection_cert. Also mention "Correction Date" and "Reclass Reweigh". This is a weight certificate. So doc_type = weight_and_inspection_cert. We should check if any nmfc_cert? The description: "Inspection certificate ... weight ... NMFC". No "Corrected" or "Correction". There\'s \'Correction Date\' but maybe that is just a correction of the weight. But the doc header says WEIGHTS & INSPECTION CERTIFICATE. That\'s weight_and_inspection_cert. We also consider "nmfc_cert" rule: "if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep it as weight_and_inspection_cert." nmfc_cert keywords: "As described and As found or Original and inspection or Corrected class or Correction information" etc. Also "NMFC Code, class #". The page includes NMFC. But those are normal. Does it mention "Corrected"? It says "Reclass Reweigh" but not "Corrected". It says Correction Date. Not sure. But likely weight_and_inspection_cert.\n\nWe must return JSON with one object: {page_no: 1, doc_type: "weight_and_inspection_cert"} Using tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2879, 'outputTokens': 313, 'totalTokens': 3192}, 'metrics': {'latencyMs': 1544}}
2025-09-24 14:18:44,173 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/703b09ea_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:18:44,188 - INFO - 

YL4HZPZH7D3B03FZZ5QK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 14:18:44,188 - INFO - 

✓ Saved result: output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 14:18:44,483 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/703b09ea_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:18:44,484 - INFO - 
📊 Processing Summary:
2025-09-24 14:18:44,485 - INFO -    Total files: 9
2025-09-24 14:18:44,485 - INFO -    Successful: 9
2025-09-24 14:18:44,485 - INFO -    Failed: 0
2025-09-24 14:18:44,485 - INFO -    Duration: 18.95 seconds
2025-09-24 14:18:44,485 - INFO -    Output directory: output
2025-09-24 14:18:44,485 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:18:44,485 - INFO -    📄 B3SIRREC9IAVZOJVDQSN.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:18:44,485 - INFO -    📄 CUF54EHGMLQ57HR93DRB.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:18:44,485 - INFO -    📄 FYQQGIW8Z9DSAPCL0S9G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:18:44,485 - INFO -    📄 IZTBXFPGXBFH3DV900G4.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:18:44,486 - INFO -    📄 MR6ONA8GK6HN1LCZHEX3.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:18:44,486 - INFO -    📄 PB67IAPSJB1DZWMDIE1H.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}
2025-09-24 14:18:44,486 - INFO -    📄 PEE2ZFMV7X0A0FL35G4G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:18:44,486 - INFO -    📄 U7BB1XSF3ASMIAE1MQ5I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 14:18:44,486 - INFO -    📄 YL4HZPZH7D3B03FZZ5QK.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 14:18:44,487 - INFO - 
============================================================================================================================================
2025-09-24 14:18:44,487 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:18:44,487 - INFO - ============================================================================================================================================
2025-09-24 14:18:44,487 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:18:44,487 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:18:44,487 - INFO - B3SIRREC9IAVZOJVDQSN.pdf                           1      nmfc_cert            run1_B3SIRREC9IAVZOJVDQSN.json                    
2025-09-24 14:18:44,487 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 14:18:44,487 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 14:18:44,487 - INFO - 
2025-09-24 14:18:44,487 - INFO - CUF54EHGMLQ57HR93DRB.pdf                           1      weight_and_inspect... run1_CUF54EHGMLQ57HR93DRB.json                    
2025-09-24 14:18:44,487 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 14:18:44,487 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 14:18:44,487 - INFO - 
2025-09-24 14:18:44,487 - INFO - FYQQGIW8Z9DSAPCL0S9G.pdf                           1      nmfc_cert            run1_FYQQGIW8Z9DSAPCL0S9G.json                    
2025-09-24 14:18:44,487 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 14:18:44,488 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 14:18:44,488 - INFO - 
2025-09-24 14:18:44,488 - INFO - IZTBXFPGXBFH3DV900G4.pdf                           1      weight_and_inspect... run1_IZTBXFPGXBFH3DV900G4.json                    
2025-09-24 14:18:44,488 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 14:18:44,488 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 14:18:44,488 - INFO - 
2025-09-24 14:18:44,488 - INFO - MR6ONA8GK6HN1LCZHEX3.pdf                           1      nmfc_cert            run1_MR6ONA8GK6HN1LCZHEX3.json                    
2025-09-24 14:18:44,488 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 14:18:44,488 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 14:18:44,488 - INFO - 
2025-09-24 14:18:44,488 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           1      nmfc_cert            run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 14:18:44,488 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:18:44,488 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 14:18:44,488 - INFO - 
2025-09-24 14:18:44,488 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           2      nmfc_cert            run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 14:18:44,488 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 14:18:44,488 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 14:18:44,488 - INFO - 
2025-09-24 14:18:44,489 - INFO - PEE2ZFMV7X0A0FL35G4G.pdf                           1      nmfc_cert            run1_PEE2ZFMV7X0A0FL35G4G.json                    
2025-09-24 14:18:44,489 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 14:18:44,489 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 14:18:44,489 - INFO - 
2025-09-24 14:18:44,489 - INFO - U7BB1XSF3ASMIAE1MQ5I.pdf                           1      nmfc_cert            run1_U7BB1XSF3ASMIAE1MQ5I.json                    
2025-09-24 14:18:44,489 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 14:18:44,489 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 14:18:44,489 - INFO - 
2025-09-24 14:18:44,489 - INFO - YL4HZPZH7D3B03FZZ5QK.pdf                           1      weight_and_inspect... run1_YL4HZPZH7D3B03FZZ5QK.json                    
2025-09-24 14:18:44,489 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 14:18:44,489 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 14:18:44,489 - INFO - 
2025-09-24 14:18:44,489 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:18:44,489 - INFO - Total entries: 10
2025-09-24 14:18:44,489 - INFO - ============================================================================================================================================
2025-09-24 14:18:44,489 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:18:44,489 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:18:44,489 - INFO -   1. B3SIRREC9IAVZOJVDQSN.pdf            Page 1   → nmfc_cert       | run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 14:18:44,489 - INFO -   2. CUF54EHGMLQ57HR93DRB.pdf            Page 1   → weight_and_inspection_cert | run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 14:18:44,490 - INFO -   3. FYQQGIW8Z9DSAPCL0S9G.pdf            Page 1   → nmfc_cert       | run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 14:18:44,490 - INFO -   4. IZTBXFPGXBFH3DV900G4.pdf            Page 1   → weight_and_inspection_cert | run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 14:18:44,490 - INFO -   5. MR6ONA8GK6HN1LCZHEX3.pdf            Page 1   → nmfc_cert       | run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 14:18:44,490 - INFO -   6. PB67IAPSJB1DZWMDIE1H.pdf            Page 1   → nmfc_cert       | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 14:18:44,490 - INFO -   7. PB67IAPSJB1DZWMDIE1H.pdf            Page 2   → nmfc_cert       | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 14:18:44,490 - INFO -   8. PEE2ZFMV7X0A0FL35G4G.pdf            Page 1   → nmfc_cert       | run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 14:18:44,490 - INFO -   9. U7BB1XSF3ASMIAE1MQ5I.pdf            Page 1   → nmfc_cert       | run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 14:18:44,490 - INFO -  10. YL4HZPZH7D3B03FZZ5QK.pdf            Page 1   → weight_and_inspection_cert | run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 14:18:44,490 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:18:44,490 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 18.948474, 'processed_files': [{'filename': 'B3SIRREC9IAVZOJVDQSN.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf'}, {'filename': 'CUF54EHGMLQ57HR93DRB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf'}, {'filename': 'FYQQGIW8Z9DSAPCL0S9G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf'}, {'filename': 'IZTBXFPGXBFH3DV900G4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf'}, {'filename': 'MR6ONA8GK6HN1LCZHEX3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf'}, {'filename': 'PB67IAPSJB1DZWMDIE1H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf'}, {'filename': 'PEE2ZFMV7X0A0FL35G4G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf'}, {'filename': 'U7BB1XSF3ASMIAE1MQ5I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf'}, {'filename': 'YL4HZPZH7D3B03FZZ5QK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf'}]}
