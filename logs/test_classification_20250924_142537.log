2025-09-24 14:25:37,102 - INFO - Logging initialized. Log file: logs/test_classification_20250924_142537.log
2025-09-24 14:25:37,103 - INFO - 📁 Found 10 files to process
2025-09-24 14:25:37,103 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:25:37,103 - INFO - 🚀 Processing 10 files in FORCED PARALLEL MODE...
2025-09-24 14:25:37,103 - INFO - 🚀 Creating 10 parallel tasks...
2025-09-24 14:25:37,103 - INFO - 🚀 All 10 tasks created - executing in parallel...
2025-09-24 14:25:37,103 - INFO - ⬆️ [14:25:37] Uploading: AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:25:38,547 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/AF0EUFN20TKQSN94KZCH.PDF -> s3://document-extraction-logistically/temp/41cfcd72_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:25:38,547 - INFO - 🔍 [14:25:38] Starting classification: AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:25:38,548 - INFO - ⬆️ [14:25:38] Uploading: FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:25:38,548 - INFO - Initializing TextractProcessor...
2025-09-24 14:25:38,559 - INFO - Initializing BedrockProcessor...
2025-09-24 14:25:38,565 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/41cfcd72_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:25:38,566 - INFO - Processing PDF from S3...
2025-09-24 14:25:38,566 - INFO - Downloading PDF from S3 to /tmp/tmp5q97vvru/41cfcd72_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:25:39,338 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/FFJ2USKKEFCH3U0FO1S5.PDF -> s3://document-extraction-logistically/temp/d81789b3_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:25:39,338 - INFO - 🔍 [14:25:39] Starting classification: FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:25:39,339 - INFO - ⬆️ [14:25:39] Uploading: HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:25:39,340 - INFO - Initializing TextractProcessor...
2025-09-24 14:25:39,360 - INFO - Initializing BedrockProcessor...
2025-09-24 14:25:39,365 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d81789b3_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:25:39,366 - INFO - Processing PDF from S3...
2025-09-24 14:25:39,366 - INFO - Downloading PDF from S3 to /tmp/tmpm62b_hdt/d81789b3_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:25:40,235 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:25:40,235 - INFO - Splitting PDF into individual pages...
2025-09-24 14:25:40,236 - INFO - Splitting PDF 41cfcd72_AF0EUFN20TKQSN94KZCH into 1 pages
2025-09-24 14:25:40,240 - INFO - Split PDF into 1 pages
2025-09-24 14:25:40,241 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:25:40,241 - INFO - Expected pages: [1]
2025-09-24 14:25:40,596 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HBV9LT6SK8HIOJ5DI4P2.pdf -> s3://document-extraction-logistically/temp/4b6288fa_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:25:40,597 - INFO - 🔍 [14:25:40] Starting classification: HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:25:40,598 - INFO - ⬆️ [14:25:40] Uploading: HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:25:40,599 - INFO - Initializing TextractProcessor...
2025-09-24 14:25:40,613 - INFO - Initializing BedrockProcessor...
2025-09-24 14:25:40,615 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4b6288fa_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:25:40,616 - INFO - Processing PDF from S3...
2025-09-24 14:25:40,616 - INFO - Downloading PDF from S3 to /tmp/tmpkl_d927t/4b6288fa_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:25:40,770 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:25:40,770 - INFO - Splitting PDF into individual pages...
2025-09-24 14:25:40,771 - INFO - Splitting PDF d81789b3_FFJ2USKKEFCH3U0FO1S5 into 1 pages
2025-09-24 14:25:40,774 - INFO - Split PDF into 1 pages
2025-09-24 14:25:40,774 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:25:40,774 - INFO - Expected pages: [1]
2025-09-24 14:25:42,188 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HL4SSNKOC8T141SEURCG.pdf -> s3://document-extraction-logistically/temp/2d797ad8_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:25:42,188 - INFO - 🔍 [14:25:42] Starting classification: HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:25:42,189 - INFO - ⬆️ [14:25:42] Uploading: Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:25:42,190 - INFO - Initializing TextractProcessor...
2025-09-24 14:25:42,205 - INFO - Initializing BedrockProcessor...
2025-09-24 14:25:42,209 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/2d797ad8_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:25:42,210 - INFO - Processing PDF from S3...
2025-09-24 14:25:42,210 - INFO - Downloading PDF from S3 to /tmp/tmprkzsj9ea/2d797ad8_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:25:42,423 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:25:42,423 - INFO - Splitting PDF into individual pages...
2025-09-24 14:25:42,425 - INFO - Splitting PDF 4b6288fa_HBV9LT6SK8HIOJ5DI4P2 into 1 pages
2025-09-24 14:25:42,430 - INFO - Split PDF into 1 pages
2025-09-24 14:25:42,430 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:25:42,430 - INFO - Expected pages: [1]
2025-09-24 14:25:42,819 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Q9MLAQNGOP70MYYKOYFJ.pdf -> s3://document-extraction-logistically/temp/1d8c44be_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:25:42,819 - INFO - 🔍 [14:25:42] Starting classification: Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:25:42,820 - INFO - ⬆️ [14:25:42] Uploading: RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:25:42,820 - INFO - Initializing TextractProcessor...
2025-09-24 14:25:42,837 - INFO - Initializing BedrockProcessor...
2025-09-24 14:25:42,840 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1d8c44be_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:25:42,840 - INFO - Processing PDF from S3...
2025-09-24 14:25:42,840 - INFO - Downloading PDF from S3 to /tmp/tmp_fy7ffjo/1d8c44be_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:25:44,088 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:25:44,089 - INFO - Splitting PDF into individual pages...
2025-09-24 14:25:44,090 - INFO - Splitting PDF 1d8c44be_Q9MLAQNGOP70MYYKOYFJ into 1 pages
2025-09-24 14:25:44,091 - INFO - Split PDF into 1 pages
2025-09-24 14:25:44,091 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:25:44,091 - INFO - Expected pages: [1]
2025-09-24 14:25:44,273 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 14:25:44,273 - INFO - Splitting PDF into individual pages...
2025-09-24 14:25:44,274 - INFO - Splitting PDF 2d797ad8_HL4SSNKOC8T141SEURCG into 1 pages
2025-09-24 14:25:44,276 - INFO - Split PDF into 1 pages
2025-09-24 14:25:44,277 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:25:44,277 - INFO - Expected pages: [1]
2025-09-24 14:25:44,425 - INFO - Page 1: Extracted 717 characters, 43 lines from 41cfcd72_AF0EUFN20TKQSN94KZCH_87049382_page_001.pdf
2025-09-24 14:25:44,425 - INFO - Successfully processed page 1
2025-09-24 14:25:44,426 - INFO - Combined 1 pages into final text
2025-09-24 14:25:44,426 - INFO - Text validation for 41cfcd72_AF0EUFN20TKQSN94KZCH: 734 characters, 1 pages
2025-09-24 14:25:44,426 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:25:44,426 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:25:44,600 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/RBKKSRU2KS6IJRASO4SB.pdf -> s3://document-extraction-logistically/temp/8357fd1c_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:25:44,600 - INFO - 🔍 [14:25:44] Starting classification: RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:25:44,601 - INFO - ⬆️ [14:25:44] Uploading: SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:25:44,602 - INFO - Initializing TextractProcessor...
2025-09-24 14:25:44,617 - INFO - Initializing BedrockProcessor...
2025-09-24 14:25:44,621 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8357fd1c_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:25:44,622 - INFO - Processing PDF from S3...
2025-09-24 14:25:44,622 - INFO - Downloading PDF from S3 to /tmp/tmp8t05ubrq/8357fd1c_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:25:45,236 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/SV5IN68S36F6PA0633RU.pdf -> s3://document-extraction-logistically/temp/37c42faf_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:25:45,237 - INFO - 🔍 [14:25:45] Starting classification: SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:25:45,238 - INFO - ⬆️ [14:25:45] Uploading: WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:25:45,240 - INFO - Initializing TextractProcessor...
2025-09-24 14:25:45,302 - INFO - Initializing BedrockProcessor...
2025-09-24 14:25:45,304 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/37c42faf_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:25:45,304 - INFO - Processing PDF from S3...
2025-09-24 14:25:45,305 - INFO - Downloading PDF from S3 to /tmp/tmptyltbrht/37c42faf_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:25:45,311 - INFO - Page 1: Extracted 654 characters, 40 lines from d81789b3_FFJ2USKKEFCH3U0FO1S5_2aefe691_page_001.pdf
2025-09-24 14:25:45,312 - INFO - Successfully processed page 1
2025-09-24 14:25:45,312 - INFO - Combined 1 pages into final text
2025-09-24 14:25:45,312 - INFO - Text validation for d81789b3_FFJ2USKKEFCH3U0FO1S5: 671 characters, 1 pages
2025-09-24 14:25:45,312 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:25:45,312 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:25:45,813 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/WDPSPQTC87MJOF3B6952.pdf -> s3://document-extraction-logistically/temp/084eb543_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:25:45,813 - INFO - 🔍 [14:25:45] Starting classification: WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:25:45,813 - INFO - ⬆️ [14:25:45] Uploading: XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:25:45,813 - INFO - Initializing TextractProcessor...
2025-09-24 14:25:45,822 - INFO - Initializing BedrockProcessor...
2025-09-24 14:25:45,823 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/084eb543_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:25:45,824 - INFO - Processing PDF from S3...
2025-09-24 14:25:45,824 - INFO - Downloading PDF from S3 to /tmp/tmpz68mxr2c/084eb543_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:25:46,395 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/XVTBS5BQOTRUDPEUC4CC.pdf -> s3://document-extraction-logistically/temp/3d3015a2_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:25:46,395 - INFO - 🔍 [14:25:46] Starting classification: XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:25:46,396 - INFO - ⬆️ [14:25:46] Uploading: Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:25:46,396 - INFO - Initializing TextractProcessor...
2025-09-24 14:25:46,407 - INFO - Initializing BedrockProcessor...
2025-09-24 14:25:46,409 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/3d3015a2_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:25:46,409 - INFO - Processing PDF from S3...
2025-09-24 14:25:46,409 - INFO - Downloading PDF from S3 to /tmp/tmpft9h66r4/3d3015a2_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:25:46,594 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:25:46,595 - INFO - Splitting PDF into individual pages...
2025-09-24 14:25:46,597 - INFO - Splitting PDF 37c42faf_SV5IN68S36F6PA0633RU into 2 pages
2025-09-24 14:25:46,600 - INFO - Split PDF into 2 pages
2025-09-24 14:25:46,600 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:25:46,600 - INFO - Expected pages: [1, 2]
2025-09-24 14:25:46,966 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '42c2bc55-cd53-4492-b8c6-735b8a5d6210', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:55:46 GMT', 'content-type': 'application/json', 'content-length': '1594', 'connection': 'keep-alive', 'x-amzn-requestid': '42c2bc55-cd53-4492-b8c6-735b8a5d6210'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page: "ORDER CONFIRMATION". Contains order number, ship via, shipping instructions, ship-to, items, quantities, weight, pallets. This looks like a purchase order or order confirmation. Among categories: po, order confirmation likely corresponds to purchase order? But purchase order typically from buyer to seller. Order confirmation could be a sales order confirmation (so_confirmation). The description says: "ORDER CONFIRMATION" with shipping details, items, quantities. In context of logistics, a sales order confirmation from seller to buyer? The page is an "ORDER CONFIRMATION" likely by the seller to the buyer confirming the order. The labels: "Our order#", "Winery order#", "Our order date", "Requested ship date". This is typical of a sales order confirmation. So doc_type should be "so_confirmation". Let\'s confirm: so_confirmation definition: "Sales Order Confirmation: Seller\'s confirmation of a sales order (acknowledges order details). Keywords: Sales Order Confirmation, SO#, Order Confirmation." This page is "ORDER CONFIRMATION". Yes. So classify as so_confirmation.\n\nWe need to output via tool call classify_logistics_doc_type. Provide documents array with one object: page_no: 1, doc_type: "so_confirmation".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2747, 'outputTokens': 276, 'totalTokens': 3023}, 'metrics': {'latencyMs': 1272}}
2025-09-24 14:25:46,967 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/41cfcd72_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:25:47,017 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Y6MOA7KJV1OU7NHZ4P4U.pdf -> s3://document-extraction-logistically/temp/5ee8c57b_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:25:47,017 - INFO - 🔍 [14:25:47] Starting classification: Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:25:47,017 - INFO - Initializing TextractProcessor...
2025-09-24 14:25:47,024 - INFO - Initializing BedrockProcessor...
2025-09-24 14:25:47,027 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5ee8c57b_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:25:47,028 - INFO - Processing PDF from S3...
2025-09-24 14:25:47,028 - INFO - Downloading PDF from S3 to /tmp/tmphbexfu9m/5ee8c57b_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:25:47,037 - INFO - 

AF0EUFN20TKQSN94KZCH.PDF

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:25:47,038 - INFO - 

✓ Saved result: output/run1_AF0EUFN20TKQSN94KZCH.json
2025-09-24 14:25:47,082 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 14:25:47,082 - INFO - Splitting PDF into individual pages...
2025-09-24 14:25:47,085 - INFO - Splitting PDF 8357fd1c_RBKKSRU2KS6IJRASO4SB into 1 pages
2025-09-24 14:25:47,090 - INFO - Split PDF into 1 pages
2025-09-24 14:25:47,090 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:25:47,090 - INFO - Expected pages: [1]
2025-09-24 14:25:47,143 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:25:47,143 - INFO - Splitting PDF into individual pages...
2025-09-24 14:25:47,145 - INFO - Splitting PDF 084eb543_WDPSPQTC87MJOF3B6952 into 2 pages
2025-09-24 14:25:47,147 - INFO - Split PDF into 2 pages
2025-09-24 14:25:47,147 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:25:47,148 - INFO - Expected pages: [1, 2]
2025-09-24 14:25:47,342 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/41cfcd72_AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:25:47,679 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:25:47,679 - INFO - Splitting PDF into individual pages...
2025-09-24 14:25:47,682 - INFO - Splitting PDF 3d3015a2_XVTBS5BQOTRUDPEUC4CC into 2 pages
2025-09-24 14:25:47,686 - INFO - Split PDF into 2 pages
2025-09-24 14:25:47,686 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:25:47,686 - INFO - Expected pages: [1, 2]
2025-09-24 14:25:47,708 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3f03b8d5-7b49-45bb-b4a4-f9549bc55873', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:55:47 GMT', 'content-type': 'application/json', 'content-length': '1262', 'connection': 'keep-alive', 'x-amzn-requestid': '3f03b8d5-7b49-45bb-b4a4-f9549bc55873'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It looks like an "Order Confirmation" from a warehouse. It includes order numbers, shipment details, item list, quantities. This matches "so_confirmation" (Sales Order Confirmation). The doc type "so_confirmation" definition: Seller\'s confirmation of a sales order. It includes "Sales Order Confirmation" header. Keywords: Sales Order Confirmation, SO#, Order Confirmation. This page has "ORDER CONFIRMATION". So it\'s likely so_confirmation.\n\nCheck if any other type: "po" Purchase Order? It is an order confirmation from seller to buyer, not the buyer\'s purchase order. The doc type "so_confirmation" is likely correct. Let\'s ensure no other type matches. "po" would have PO#, but this has Our order#, but not purchase order. So "so_confirmation" is correct.\n\nThus output: page_no 1, doc_type "so_confirmation".\n\nWe must call tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2725, 'outputTokens': 215, 'totalTokens': 2940}, 'metrics': {'latencyMs': 1036}}
2025-09-24 14:25:47,709 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d81789b3_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:25:47,730 - INFO - 

FFJ2USKKEFCH3U0FO1S5.PDF

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:25:47,730 - INFO - 

✓ Saved result: output/run1_FFJ2USKKEFCH3U0FO1S5.json
2025-09-24 14:25:48,034 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d81789b3_FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:25:48,258 - INFO - Page 1: Extracted 1170 characters, 73 lines from 1d8c44be_Q9MLAQNGOP70MYYKOYFJ_285bea5c_page_001.pdf
2025-09-24 14:25:48,258 - INFO - Successfully processed page 1
2025-09-24 14:25:48,259 - INFO - Combined 1 pages into final text
2025-09-24 14:25:48,259 - INFO - Text validation for 1d8c44be_Q9MLAQNGOP70MYYKOYFJ: 1187 characters, 1 pages
2025-09-24 14:25:48,259 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:25:48,259 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:25:48,386 - INFO - Page 1: Extracted 1438 characters, 62 lines from 4b6288fa_HBV9LT6SK8HIOJ5DI4P2_d5c2d607_page_001.pdf
2025-09-24 14:25:48,386 - INFO - Successfully processed page 1
2025-09-24 14:25:48,387 - INFO - Combined 1 pages into final text
2025-09-24 14:25:48,387 - INFO - Text validation for 4b6288fa_HBV9LT6SK8HIOJ5DI4P2: 1455 characters, 1 pages
2025-09-24 14:25:48,387 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:25:48,388 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:25:48,420 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:25:48,421 - INFO - Splitting PDF into individual pages...
2025-09-24 14:25:48,424 - INFO - Splitting PDF 5ee8c57b_Y6MOA7KJV1OU7NHZ4P4U into 2 pages
2025-09-24 14:25:48,428 - INFO - Split PDF into 2 pages
2025-09-24 14:25:48,428 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:25:48,428 - INFO - Expected pages: [1, 2]
2025-09-24 14:25:49,551 - INFO - Page 1: Extracted 1443 characters, 97 lines from 2d797ad8_HL4SSNKOC8T141SEURCG_fed7bac3_page_001.pdf
2025-09-24 14:25:49,551 - INFO - Successfully processed page 1
2025-09-24 14:25:49,552 - INFO - Combined 1 pages into final text
2025-09-24 14:25:49,552 - INFO - Text validation for 2d797ad8_HL4SSNKOC8T141SEURCG: 1460 characters, 1 pages
2025-09-24 14:25:49,553 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:25:49,553 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:25:50,263 - INFO - Page 2: Extracted 456 characters, 26 lines from 37c42faf_SV5IN68S36F6PA0633RU_801c4368_page_002.pdf
2025-09-24 14:25:50,263 - INFO - Successfully processed page 2
2025-09-24 14:25:50,294 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'c340ea8b-7b80-4f5d-9e0f-47f45d36f333', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:55:50 GMT', 'content-type': 'application/json', 'content-length': '937', 'connection': 'keep-alive', 'x-amzn-requestid': 'c340ea8b-7b80-4f5d-9e0f-47f45d36f333'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page: looks like a sales acknowledgment / sales order confirmation. Keywords: "SALES ACKNOWLEDGMENT", "Order Totals", "SALES ACKNOWLEDGMENT". There\'s no invoice, no shipping docs etc. This is likely a sales order confirmation. The doc_type "so_confirmation" fits: "Sales Order Confirmation". The page shows "SALES ACKNOWLEDGMENT" and an order number. It seems more like a sales acknowledgment rather than a commercial invoice. So classify as "so_confirmation". There\'s only page 1, no continuation. So output array with one object: page_no 1, doc_type "so_confirmation". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2928, 'outputTokens': 157, 'totalTokens': 3085}, 'metrics': {'latencyMs': 879}}
2025-09-24 14:25:50,294 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1d8c44be_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:25:50,315 - INFO - 

Q9MLAQNGOP70MYYKOYFJ.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:25:50,315 - INFO - 

✓ Saved result: output/run1_Q9MLAQNGOP70MYYKOYFJ.json
2025-09-24 14:25:50,499 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '388655e4-2343-49f0-8ca8-27dce80b0ccb', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:55:50 GMT', 'content-type': 'application/json', 'content-length': '870', 'connection': 'keep-alive', 'x-amzn-requestid': '388655e4-2343-49f0-8ca8-27dce80b0ccb'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. Determine doc_type. The page is an Order Confirmation page, with "Order Confirmation" header, "Order Number: SO-00173736", "Customer PO", etc. It\'s a sales order confirmation. In the enum list, "so_confirmation" is Sales Order Confirmation. So classify as so_confirmation. Output JSON with array of documents: single object with page_no:1, doc_type:"so_confirmation". Use tool classify_logistics_doc_type.'}}}, {'toolUse': {'toolUseId': 'tooluse_93inkkZLTWuUcykcwnAwnw', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2905, 'outputTokens': 119, 'totalTokens': 3024}, 'metrics': {'latencyMs': 811}}
2025-09-24 14:25:50,499 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4b6288fa_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:25:50,606 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1d8c44be_Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:25:50,637 - INFO - Page 1: Extracted 1305 characters, 67 lines from 37c42faf_SV5IN68S36F6PA0633RU_801c4368_page_001.pdf
2025-09-24 14:25:50,639 - INFO - Successfully processed page 1
2025-09-24 14:25:50,641 - INFO - Combined 2 pages into final text
2025-09-24 14:25:50,642 - INFO - Text validation for 37c42faf_SV5IN68S36F6PA0633RU: 1797 characters, 2 pages
2025-09-24 14:25:50,645 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:25:50,646 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:25:50,649 - INFO - 

HBV9LT6SK8HIOJ5DI4P2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:25:50,649 - INFO - 

✓ Saved result: output/run1_HBV9LT6SK8HIOJ5DI4P2.json
2025-09-24 14:25:50,977 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4b6288fa_HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:25:51,146 - INFO - Page 2: Extracted 457 characters, 26 lines from 3d3015a2_XVTBS5BQOTRUDPEUC4CC_9cf15ad6_page_002.pdf
2025-09-24 14:25:51,150 - INFO - Page 2: Extracted 614 characters, 29 lines from 084eb543_WDPSPQTC87MJOF3B6952_bfa13c5d_page_002.pdf
2025-09-24 14:25:51,151 - INFO - Successfully processed page 2
2025-09-24 14:25:51,151 - INFO - Successfully processed page 2
2025-09-24 14:25:51,476 - INFO - Page 1: Extracted 1364 characters, 74 lines from 084eb543_WDPSPQTC87MJOF3B6952_bfa13c5d_page_001.pdf
2025-09-24 14:25:51,476 - INFO - Successfully processed page 1
2025-09-24 14:25:51,476 - INFO - Combined 2 pages into final text
2025-09-24 14:25:51,476 - INFO - Text validation for 084eb543_WDPSPQTC87MJOF3B6952: 2014 characters, 2 pages
2025-09-24 14:25:51,477 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:25:51,477 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:25:51,552 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e6affe9b-b3e9-4b44-9978-6110910cc7b4', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:55:51 GMT', 'content-type': 'application/json', 'content-length': '581', 'connection': 'keep-alive', 'x-amzn-requestid': 'e6affe9b-b3e9-4b44-9978-6110910cc7b4'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It\'s a Sales Order confirmation. Keywords: "Sales Order", "SO-", "Item No.", "Unit Price", etc. It matches so_confirmation. So output single object: page_no:1, doc_type:"so_confirmation". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3008, 'outputTokens': 79, 'totalTokens': 3087}, 'metrics': {'latencyMs': 779}}
2025-09-24 14:25:51,552 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/2d797ad8_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:25:51,581 - INFO - 

HL4SSNKOC8T141SEURCG.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:25:51,581 - INFO - 

✓ Saved result: output/run1_HL4SSNKOC8T141SEURCG.json
2025-09-24 14:25:51,631 - INFO - Page 2: Extracted 457 characters, 26 lines from 5ee8c57b_Y6MOA7KJV1OU7NHZ4P4U_92a4d71f_page_002.pdf
2025-09-24 14:25:51,631 - INFO - Successfully processed page 2
2025-09-24 14:25:51,678 - INFO - Page 1: Extracted 1308 characters, 67 lines from 3d3015a2_XVTBS5BQOTRUDPEUC4CC_9cf15ad6_page_001.pdf
2025-09-24 14:25:51,678 - INFO - Successfully processed page 1
2025-09-24 14:25:51,678 - INFO - Combined 2 pages into final text
2025-09-24 14:25:51,678 - INFO - Text validation for 3d3015a2_XVTBS5BQOTRUDPEUC4CC: 1801 characters, 2 pages
2025-09-24 14:25:51,679 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:25:51,679 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:25:51,900 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/2d797ad8_HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:25:52,643 - INFO - Page 1: Extracted 1306 characters, 69 lines from 5ee8c57b_Y6MOA7KJV1OU7NHZ4P4U_92a4d71f_page_001.pdf
2025-09-24 14:25:52,643 - INFO - Successfully processed page 1
2025-09-24 14:25:52,644 - INFO - Combined 2 pages into final text
2025-09-24 14:25:52,644 - INFO - Text validation for 5ee8c57b_Y6MOA7KJV1OU7NHZ4P4U: 1799 characters, 2 pages
2025-09-24 14:25:52,644 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:25:52,644 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:25:52,916 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '59d87a3e-0f09-47a4-8f69-dd094784e1b3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:55:52 GMT', 'content-type': 'application/json', 'content-length': '978', 'connection': 'keep-alive', 'x-amzn-requestid': '59d87a3e-0f09-47a4-8f69-dd094784e1b3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. This appears to be a Plant Order, essentially a Purchase Order from Calumet Refining. This is a PO. The pages show PO Number, Order Number, etc. So doc_type = po.\n\nCheck for continuation: page2 starts with "CALUMET ... Page: 2/2" but no "continued" but it\'s a continuation of same document. The instruction: if page is continuation of previous page, assign same doc_type. We can just classify page2 as same type. So output two objects.\n\nReturn via tool call classify_logistics_doc_type with array of objects: page_no 1, doc_type "po"; page_no 2, doc_type "po".\n\nLet\'s produce tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3124, 'outputTokens': 178, 'totalTokens': 3302}, 'metrics': {'latencyMs': 979}}
2025-09-24 14:25:52,916 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/37c42faf_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:25:52,943 - INFO - 

SV5IN68S36F6PA0633RU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:25:52,943 - INFO - 

✓ Saved result: output/run1_SV5IN68S36F6PA0633RU.json
2025-09-24 14:25:53,233 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/37c42faf_SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:25:53,483 - INFO - Page 1: Extracted 1479 characters, 67 lines from 8357fd1c_RBKKSRU2KS6IJRASO4SB_acd193c8_page_001.pdf
2025-09-24 14:25:53,484 - INFO - Successfully processed page 1
2025-09-24 14:25:53,484 - INFO - Combined 1 pages into final text
2025-09-24 14:25:53,484 - INFO - Text validation for 8357fd1c_RBKKSRU2KS6IJRASO4SB: 1496 characters, 1 pages
2025-09-24 14:25:53,485 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:25:53,485 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:25:53,918 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '234ab5ba-1ee9-4f26-80b1-0fc2994940df', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:55:53 GMT', 'content-type': 'application/json', 'content-length': '1039', 'connection': 'keep-alive', 'x-amzn-requestid': '234ab5ba-1ee9-4f26-80b1-0fc2994940df'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The document appears to be a plant order from Calumet Refining. It\'s basically a purchase order? It says "PO Number: wax091925", "Order Number", "Ship To", "Bill To", etc. It\'s a Plant Order form. It\'s probably a purchase order, but the doc type definitions: "po" is a purchase order. The doc type should be "po". The pages are two pages of the same doc. We need to check if page2 is a continuation. It starts with "CALUMET..." same header, no "continued" but page indicator "Page: 2/2". It\'s a continuation of the same doc. So both pages should be "po". Output objects: page_no 1, doc_type "po"; page_no 2, doc_type "po". Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3208, 'outputTokens': 203, 'totalTokens': 3411}, 'metrics': {'latencyMs': 1102}}
2025-09-24 14:25:53,918 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/084eb543_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:25:53,921 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5cae0433-b294-4b62-8b1b-691e2056f6c2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:55:53 GMT', 'content-type': 'application/json', 'content-length': '1087', 'connection': 'keep-alive', 'x-amzn-requestid': '5cae0433-b294-4b62-8b1b-691e2056f6c2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The content appears to be a Plant Order from Calumet Refining. It\'s a purchase order type, specifically a "Plant Order" maybe from a supplier to a customer. The structure includes PO Number, Order Number, Ship To, Bill To, etc. This looks like a purchase order (PO). The doc type classification is "po". The instruction: For each page output an object with page_no and doc_type. Both pages are continuations. Page 2 starts with "Page: 2/2" and maybe no header but it\'s a continuation. Should classify both as po. Use only tool call.\n\nReturn JSON array of objects for each page.\n\nWe\'ll produce:\n\n[\n {page_no: 1, doc_type: "po"},\n {page_no: 2, doc_type: "po"}\n]\n\nWe must use tool call.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3110, 'outputTokens': 204, 'totalTokens': 3314}, 'metrics': {'latencyMs': 1000}}
2025-09-24 14:25:53,921 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/3d3015a2_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:25:53,940 - INFO - 

WDPSPQTC87MJOF3B6952.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:25:53,940 - INFO - 

✓ Saved result: output/run1_WDPSPQTC87MJOF3B6952.json
2025-09-24 14:25:54,230 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/084eb543_WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:25:54,252 - INFO - 

XVTBS5BQOTRUDPEUC4CC.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:25:54,252 - INFO - 

✓ Saved result: output/run1_XVTBS5BQOTRUDPEUC4CC.json
2025-09-24 14:25:54,544 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/3d3015a2_XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:25:55,477 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'be69b222-f9bd-4d01-9bd3-97422635876d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:55:55 GMT', 'content-type': 'application/json', 'content-length': '765', 'connection': 'keep-alive', 'x-amzn-requestid': 'be69b222-f9bd-4d01-9bd3-97422635876d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have one page. It looks like an Order Acknowledgment, likely a Sales Order Confirmation? The header says "ORDER ACKNOWLEDGMENT" and mentions "Detailed Sales Order". There\'s no explicit doc type. The doc likely is a sales order confirmation. So classify as so_confirmation. The page includes terms, etc. No invoice, no BOL, no POD. So so_confirmation. No continuation. So output: page_no 1, doc_type "so_confirmation". Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2919, 'outputTokens': 119, 'totalTokens': 3038}, 'metrics': {'latencyMs': 684}}
2025-09-24 14:25:55,478 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8357fd1c_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:25:55,516 - INFO - 

RBKKSRU2KS6IJRASO4SB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "so_confirmation"
        }
    ]
}
2025-09-24 14:25:55,516 - INFO - 

✓ Saved result: output/run1_RBKKSRU2KS6IJRASO4SB.json
2025-09-24 14:25:55,799 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9883e1b6-ce2e-412a-887d-be1120e92fa7', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:55:55 GMT', 'content-type': 'application/json', 'content-length': '1694', 'connection': 'keep-alive', 'x-amzn-requestid': '9883e1b6-ce2e-412a-887d-be1120e92fa7'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. The pages are part of a plant order document, not a logistic document. It looks like a purchase order or a sales order? It\'s a plant order (invoice to produce). It\'s basically a sales order? The content: PO Number, Order Number, Order ZOR1, etc. It\'s a purchase order? Actually it\'s a plant order from Calumet Refining to produce wax. The doc type: This is likely a Purchase Order or Sales Order Confirmation? Let\'s analyze.\n\nThe doc has "PO Number:" but the PO number is \'lilwax081325\'. The header says "Plant Order". Usually plant order is a purchase order from a customer to the plant. So the document is like a purchase order from the customer to the plant. The doc type list includes \'po\' for purchase order: "Buyer\'s order to a seller specifying items". This matches. There is also \'so_confirmation\' for Sales Order Confirmation, but that is seller\'s confirmation. This doc is basically the buyer\'s order. So classify as \'po\'.\n\nBut we must check if any other types fit: invoice, comm_invoice etc. No. It\'s not a bill or invoice. It\'s a purchase order. So each page gets doc_type \'po\'. Also check continuation: Page 2 has "Page: 2/2" and no new header. It\'s continuation. So both pages are \'po\'.\n\nSo output: array with two objects: page_no:1 doc_type:"po", page_no:2 doc_type:"po". Use tool.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3123, 'outputTokens': 341, 'totalTokens': 3464}, 'metrics': {'latencyMs': 1925}}
2025-09-24 14:25:55,800 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5ee8c57b_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:25:55,824 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8357fd1c_RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:25:55,847 - INFO - 

Y6MOA7KJV1OU7NHZ4P4U.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "po"
        },
        {
            "page_no": 2,
            "doc_type": "po"
        }
    ]
}
2025-09-24 14:25:55,847 - INFO - 

✓ Saved result: output/run1_Y6MOA7KJV1OU7NHZ4P4U.json
2025-09-24 14:25:56,263 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5ee8c57b_Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:25:56,263 - INFO - 
📊 Processing Summary:
2025-09-24 14:25:56,264 - INFO -    Total files: 10
2025-09-24 14:25:56,264 - INFO -    Successful: 10
2025-09-24 14:25:56,264 - INFO -    Failed: 0
2025-09-24 14:25:56,264 - INFO -    Duration: 19.16 seconds
2025-09-24 14:25:56,264 - INFO -    Output directory: output
2025-09-24 14:25:56,264 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:25:56,264 - INFO -    📄 AF0EUFN20TKQSN94KZCH.PDF: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-24 14:25:56,264 - INFO -    📄 FFJ2USKKEFCH3U0FO1S5.PDF: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-24 14:25:56,264 - INFO -    📄 HBV9LT6SK8HIOJ5DI4P2.pdf: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-24 14:25:56,264 - INFO -    📄 HL4SSNKOC8T141SEURCG.pdf: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-24 14:25:56,264 - INFO -    📄 Q9MLAQNGOP70MYYKOYFJ.pdf: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-24 14:25:56,264 - INFO -    📄 RBKKSRU2KS6IJRASO4SB.pdf: {"documents":[{"page_no":1,"doc_type":"so_confirmation"}]}
2025-09-24 14:25:56,264 - INFO -    📄 SV5IN68S36F6PA0633RU.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-24 14:25:56,264 - INFO -    📄 WDPSPQTC87MJOF3B6952.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-24 14:25:56,264 - INFO -    📄 XVTBS5BQOTRUDPEUC4CC.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-24 14:25:56,264 - INFO -    📄 Y6MOA7KJV1OU7NHZ4P4U.pdf: {"documents":[{"page_no":1,"doc_type":"po"},{"page_no":2,"doc_type":"po"}]}
2025-09-24 14:25:56,265 - INFO - 
============================================================================================================================================
2025-09-24 14:25:56,265 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:25:56,265 - INFO - ============================================================================================================================================
2025-09-24 14:25:56,265 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:25:56,265 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:25:56,265 - INFO - AF0EUFN20TKQSN94KZCH.PDF                           1      so_confirmation      run1_AF0EUFN20TKQSN94KZCH.json                    
2025-09-24 14:25:56,265 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/AF0EUFN20TKQSN94KZCH.PDF
2025-09-24 14:25:56,265 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_AF0EUFN20TKQSN94KZCH.json
2025-09-24 14:25:56,265 - INFO - 
2025-09-24 14:25:56,265 - INFO - FFJ2USKKEFCH3U0FO1S5.PDF                           1      so_confirmation      run1_FFJ2USKKEFCH3U0FO1S5.json                    
2025-09-24 14:25:56,265 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/FFJ2USKKEFCH3U0FO1S5.PDF
2025-09-24 14:25:56,266 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FFJ2USKKEFCH3U0FO1S5.json
2025-09-24 14:25:56,266 - INFO - 
2025-09-24 14:25:56,266 - INFO - HBV9LT6SK8HIOJ5DI4P2.pdf                           1      so_confirmation      run1_HBV9LT6SK8HIOJ5DI4P2.json                    
2025-09-24 14:25:56,266 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HBV9LT6SK8HIOJ5DI4P2.pdf
2025-09-24 14:25:56,266 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HBV9LT6SK8HIOJ5DI4P2.json
2025-09-24 14:25:56,266 - INFO - 
2025-09-24 14:25:56,266 - INFO - HL4SSNKOC8T141SEURCG.pdf                           1      so_confirmation      run1_HL4SSNKOC8T141SEURCG.json                    
2025-09-24 14:25:56,266 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HL4SSNKOC8T141SEURCG.pdf
2025-09-24 14:25:56,266 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_HL4SSNKOC8T141SEURCG.json
2025-09-24 14:25:56,266 - INFO - 
2025-09-24 14:25:56,266 - INFO - Q9MLAQNGOP70MYYKOYFJ.pdf                           1      so_confirmation      run1_Q9MLAQNGOP70MYYKOYFJ.json                    
2025-09-24 14:25:56,266 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Q9MLAQNGOP70MYYKOYFJ.pdf
2025-09-24 14:25:56,266 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Q9MLAQNGOP70MYYKOYFJ.json
2025-09-24 14:25:56,266 - INFO - 
2025-09-24 14:25:56,266 - INFO - RBKKSRU2KS6IJRASO4SB.pdf                           1      so_confirmation      run1_RBKKSRU2KS6IJRASO4SB.json                    
2025-09-24 14:25:56,266 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/RBKKSRU2KS6IJRASO4SB.pdf
2025-09-24 14:25:56,266 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RBKKSRU2KS6IJRASO4SB.json
2025-09-24 14:25:56,266 - INFO - 
2025-09-24 14:25:56,266 - INFO - SV5IN68S36F6PA0633RU.pdf                           1      po                   run1_SV5IN68S36F6PA0633RU.json                    
2025-09-24 14:25:56,266 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:25:56,266 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SV5IN68S36F6PA0633RU.json
2025-09-24 14:25:56,266 - INFO - 
2025-09-24 14:25:56,266 - INFO - SV5IN68S36F6PA0633RU.pdf                           2      po                   run1_SV5IN68S36F6PA0633RU.json                    
2025-09-24 14:25:56,267 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/SV5IN68S36F6PA0633RU.pdf
2025-09-24 14:25:56,267 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_SV5IN68S36F6PA0633RU.json
2025-09-24 14:25:56,267 - INFO - 
2025-09-24 14:25:56,267 - INFO - WDPSPQTC87MJOF3B6952.pdf                           1      po                   run1_WDPSPQTC87MJOF3B6952.json                    
2025-09-24 14:25:56,267 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:25:56,267 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WDPSPQTC87MJOF3B6952.json
2025-09-24 14:25:56,267 - INFO - 
2025-09-24 14:25:56,267 - INFO - WDPSPQTC87MJOF3B6952.pdf                           2      po                   run1_WDPSPQTC87MJOF3B6952.json                    
2025-09-24 14:25:56,267 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/WDPSPQTC87MJOF3B6952.pdf
2025-09-24 14:25:56,267 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WDPSPQTC87MJOF3B6952.json
2025-09-24 14:25:56,267 - INFO - 
2025-09-24 14:25:56,267 - INFO - XVTBS5BQOTRUDPEUC4CC.pdf                           1      po                   run1_XVTBS5BQOTRUDPEUC4CC.json                    
2025-09-24 14:25:56,267 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:25:56,267 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_XVTBS5BQOTRUDPEUC4CC.json
2025-09-24 14:25:56,267 - INFO - 
2025-09-24 14:25:56,267 - INFO - XVTBS5BQOTRUDPEUC4CC.pdf                           2      po                   run1_XVTBS5BQOTRUDPEUC4CC.json                    
2025-09-24 14:25:56,267 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/XVTBS5BQOTRUDPEUC4CC.pdf
2025-09-24 14:25:56,267 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_XVTBS5BQOTRUDPEUC4CC.json
2025-09-24 14:25:56,267 - INFO - 
2025-09-24 14:25:56,267 - INFO - Y6MOA7KJV1OU7NHZ4P4U.pdf                           1      po                   run1_Y6MOA7KJV1OU7NHZ4P4U.json                    
2025-09-24 14:25:56,267 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:25:56,267 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y6MOA7KJV1OU7NHZ4P4U.json
2025-09-24 14:25:56,267 - INFO - 
2025-09-24 14:25:56,267 - INFO - Y6MOA7KJV1OU7NHZ4P4U.pdf                           2      po                   run1_Y6MOA7KJV1OU7NHZ4P4U.json                    
2025-09-24 14:25:56,268 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Y6MOA7KJV1OU7NHZ4P4U.pdf
2025-09-24 14:25:56,268 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Y6MOA7KJV1OU7NHZ4P4U.json
2025-09-24 14:25:56,268 - INFO - 
2025-09-24 14:25:56,268 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:25:56,268 - INFO - Total entries: 14
2025-09-24 14:25:56,268 - INFO - ============================================================================================================================================
2025-09-24 14:25:56,268 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:25:56,268 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:25:56,268 - INFO -   1. AF0EUFN20TKQSN94KZCH.PDF            Page 1   → so_confirmation | run1_AF0EUFN20TKQSN94KZCH.json
2025-09-24 14:25:56,268 - INFO -   2. FFJ2USKKEFCH3U0FO1S5.PDF            Page 1   → so_confirmation | run1_FFJ2USKKEFCH3U0FO1S5.json
2025-09-24 14:25:56,268 - INFO -   3. HBV9LT6SK8HIOJ5DI4P2.pdf            Page 1   → so_confirmation | run1_HBV9LT6SK8HIOJ5DI4P2.json
2025-09-24 14:25:56,268 - INFO -   4. HL4SSNKOC8T141SEURCG.pdf            Page 1   → so_confirmation | run1_HL4SSNKOC8T141SEURCG.json
2025-09-24 14:25:56,268 - INFO -   5. Q9MLAQNGOP70MYYKOYFJ.pdf            Page 1   → so_confirmation | run1_Q9MLAQNGOP70MYYKOYFJ.json
2025-09-24 14:25:56,268 - INFO -   6. RBKKSRU2KS6IJRASO4SB.pdf            Page 1   → so_confirmation | run1_RBKKSRU2KS6IJRASO4SB.json
2025-09-24 14:25:56,268 - INFO -   7. SV5IN68S36F6PA0633RU.pdf            Page 1   → po              | run1_SV5IN68S36F6PA0633RU.json
2025-09-24 14:25:56,268 - INFO -   8. SV5IN68S36F6PA0633RU.pdf            Page 2   → po              | run1_SV5IN68S36F6PA0633RU.json
2025-09-24 14:25:56,268 - INFO -   9. WDPSPQTC87MJOF3B6952.pdf            Page 1   → po              | run1_WDPSPQTC87MJOF3B6952.json
2025-09-24 14:25:56,268 - INFO -  10. WDPSPQTC87MJOF3B6952.pdf            Page 2   → po              | run1_WDPSPQTC87MJOF3B6952.json
2025-09-24 14:25:56,268 - INFO -  11. XVTBS5BQOTRUDPEUC4CC.pdf            Page 1   → po              | run1_XVTBS5BQOTRUDPEUC4CC.json
2025-09-24 14:25:56,268 - INFO -  12. XVTBS5BQOTRUDPEUC4CC.pdf            Page 2   → po              | run1_XVTBS5BQOTRUDPEUC4CC.json
2025-09-24 14:25:56,268 - INFO -  13. Y6MOA7KJV1OU7NHZ4P4U.pdf            Page 1   → po              | run1_Y6MOA7KJV1OU7NHZ4P4U.json
2025-09-24 14:25:56,269 - INFO -  14. Y6MOA7KJV1OU7NHZ4P4U.pdf            Page 2   → po              | run1_Y6MOA7KJV1OU7NHZ4P4U.json
2025-09-24 14:25:56,269 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:25:56,269 - INFO - 
✅ Test completed: {'total_files': 10, 'processed': 10, 'failed': 0, 'errors': [], 'duration_seconds': 19.160527, 'processed_files': [{'filename': 'AF0EUFN20TKQSN94KZCH.PDF', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/AF0EUFN20TKQSN94KZCH.PDF'}, {'filename': 'FFJ2USKKEFCH3U0FO1S5.PDF', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/FFJ2USKKEFCH3U0FO1S5.PDF'}, {'filename': 'HBV9LT6SK8HIOJ5DI4P2.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HBV9LT6SK8HIOJ5DI4P2.pdf'}, {'filename': 'HL4SSNKOC8T141SEURCG.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/HL4SSNKOC8T141SEURCG.pdf'}, {'filename': 'Q9MLAQNGOP70MYYKOYFJ.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Q9MLAQNGOP70MYYKOYFJ.pdf'}, {'filename': 'RBKKSRU2KS6IJRASO4SB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'so_confirmation'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/RBKKSRU2KS6IJRASO4SB.pdf'}, {'filename': 'SV5IN68S36F6PA0633RU.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/SV5IN68S36F6PA0633RU.pdf'}, {'filename': 'WDPSPQTC87MJOF3B6952.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/WDPSPQTC87MJOF3B6952.pdf'}, {'filename': 'XVTBS5BQOTRUDPEUC4CC.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/XVTBS5BQOTRUDPEUC4CC.pdf'}, {'filename': 'Y6MOA7KJV1OU7NHZ4P4U.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'po'}, {'page_no': 2, 'doc_type': 'po'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/so_confirmatin/Y6MOA7KJV1OU7NHZ4P4U.pdf'}]}
