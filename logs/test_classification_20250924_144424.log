2025-09-24 14:44:24,883 - INFO - Logging initialized. Log file: logs/test_classification_20250924_144424.log
2025-09-24 14:44:24,883 - INFO - 📁 Found 9 files to process
2025-09-24 14:44:24,884 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:44:24,884 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 14:44:24,884 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 14:44:24,884 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 14:44:24,884 - INFO - ⬆️ [14:44:24] Uploading: CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 14:44:26,421 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf -> s3://document-extraction-logistically/temp/05c30a02_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 14:44:26,421 - INFO - 🔍 [14:44:26] Starting classification: CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 14:44:26,422 - INFO - ⬆️ [14:44:26] Uploading: DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 14:44:26,423 - INFO - Initializing TextractProcessor...
2025-09-24 14:44:26,452 - INFO - Initializing BedrockProcessor...
2025-09-24 14:44:26,461 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/05c30a02_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 14:44:26,461 - INFO - Processing PDF from S3...
2025-09-24 14:44:26,462 - INFO - Downloading PDF from S3 to /tmp/tmpw7aq5ke4/05c30a02_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 14:44:27,711 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf -> s3://document-extraction-logistically/temp/8a9145ea_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 14:44:27,712 - INFO - 🔍 [14:44:27] Starting classification: DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 14:44:27,713 - INFO - ⬆️ [14:44:27] Uploading: ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 14:44:27,715 - INFO - Initializing TextractProcessor...
2025-09-24 14:44:27,736 - INFO - Initializing BedrockProcessor...
2025-09-24 14:44:27,739 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/8a9145ea_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 14:44:27,740 - INFO - Processing PDF from S3...
2025-09-24 14:44:27,740 - INFO - Downloading PDF from S3 to /tmp/tmph0w48mzz/8a9145ea_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 14:44:27,923 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:44:27,923 - INFO - Splitting PDF into individual pages...
2025-09-24 14:44:27,927 - INFO - Splitting PDF 05c30a02_CZ7K9JE7PH88JUBC19JF into 1 pages
2025-09-24 14:44:27,961 - INFO - Split PDF into 1 pages
2025-09-24 14:44:27,961 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:44:27,961 - INFO - Expected pages: [1]
2025-09-24 14:44:28,619 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf -> s3://document-extraction-logistically/temp/92b938fe_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 14:44:28,620 - INFO - 🔍 [14:44:28] Starting classification: ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 14:44:28,620 - INFO - ⬆️ [14:44:28] Uploading: EDYM381JJDTUB87YAAPI.pdf
2025-09-24 14:44:28,622 - INFO - Initializing TextractProcessor...
2025-09-24 14:44:28,641 - INFO - Initializing BedrockProcessor...
2025-09-24 14:44:28,647 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/92b938fe_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 14:44:28,648 - INFO - Processing PDF from S3...
2025-09-24 14:44:28,648 - INFO - Downloading PDF from S3 to /tmp/tmpq0t0r6cj/92b938fe_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 14:44:29,320 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/EDYM381JJDTUB87YAAPI.pdf -> s3://document-extraction-logistically/temp/445c82e5_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 14:44:29,321 - INFO - 🔍 [14:44:29] Starting classification: EDYM381JJDTUB87YAAPI.pdf
2025-09-24 14:44:29,321 - INFO - ⬆️ [14:44:29] Uploading: GMCKX2ERTX05S300COLL.pdf
2025-09-24 14:44:29,322 - INFO - Initializing TextractProcessor...
2025-09-24 14:44:29,343 - INFO - Initializing BedrockProcessor...
2025-09-24 14:44:29,349 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/445c82e5_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 14:44:29,349 - INFO - Processing PDF from S3...
2025-09-24 14:44:29,349 - INFO - Downloading PDF from S3 to /tmp/tmp12znryiy/445c82e5_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 14:44:29,820 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 14:44:29,821 - INFO - Splitting PDF into individual pages...
2025-09-24 14:44:29,823 - INFO - Splitting PDF 8a9145ea_DGAKPGYVH59IXR7KRKZS into 4 pages
2025-09-24 14:44:29,842 - INFO - Split PDF into 4 pages
2025-09-24 14:44:29,842 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:44:29,842 - INFO - Expected pages: [1, 2, 3, 4]
2025-09-24 14:44:29,963 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/GMCKX2ERTX05S300COLL.pdf -> s3://document-extraction-logistically/temp/190e51b6_GMCKX2ERTX05S300COLL.pdf
2025-09-24 14:44:29,963 - INFO - 🔍 [14:44:29] Starting classification: GMCKX2ERTX05S300COLL.pdf
2025-09-24 14:44:29,964 - INFO - Initializing TextractProcessor...
2025-09-24 14:44:29,964 - INFO - ⬆️ [14:44:29] Uploading: T51X0UC3WJL168AL2PGB.pdf
2025-09-24 14:44:29,982 - INFO - Initializing BedrockProcessor...
2025-09-24 14:44:29,988 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/190e51b6_GMCKX2ERTX05S300COLL.pdf
2025-09-24 14:44:29,988 - INFO - Processing PDF from S3...
2025-09-24 14:44:29,988 - INFO - Downloading PDF from S3 to /tmp/tmpgapwutjm/190e51b6_GMCKX2ERTX05S300COLL.pdf
2025-09-24 14:44:30,617 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/T51X0UC3WJL168AL2PGB.pdf -> s3://document-extraction-logistically/temp/f7000952_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 14:44:30,617 - INFO - 🔍 [14:44:30] Starting classification: T51X0UC3WJL168AL2PGB.pdf
2025-09-24 14:44:30,618 - INFO - ⬆️ [14:44:30] Uploading: TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 14:44:30,621 - INFO - Initializing TextractProcessor...
2025-09-24 14:44:30,638 - INFO - Initializing BedrockProcessor...
2025-09-24 14:44:30,643 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f7000952_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 14:44:30,644 - INFO - Processing PDF from S3...
2025-09-24 14:44:30,644 - INFO - Downloading PDF from S3 to /tmp/tmpxyzg5v9m/f7000952_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 14:44:31,123 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 14:44:31,123 - INFO - Splitting PDF into individual pages...
2025-09-24 14:44:31,125 - INFO - Splitting PDF 92b938fe_ECY73YCNA7IPQSM8NAKW into 1 pages
2025-09-24 14:44:31,126 - INFO - Split PDF into 1 pages
2025-09-24 14:44:31,127 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:44:31,127 - INFO - Expected pages: [1]
2025-09-24 14:44:31,289 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf -> s3://document-extraction-logistically/temp/69a21c68_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 14:44:31,289 - INFO - 🔍 [14:44:31] Starting classification: TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 14:44:31,289 - INFO - ⬆️ [14:44:31] Uploading: V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 14:44:31,290 - INFO - Initializing TextractProcessor...
2025-09-24 14:44:31,299 - INFO - Initializing BedrockProcessor...
2025-09-24 14:44:31,302 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/69a21c68_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 14:44:31,302 - INFO - Processing PDF from S3...
2025-09-24 14:44:31,302 - INFO - Downloading PDF from S3 to /tmp/tmpgloyxe5p/69a21c68_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 14:44:31,506 - INFO - Downloaded PDF size: 0.3 MB
2025-09-24 14:44:31,506 - INFO - Splitting PDF into individual pages...
2025-09-24 14:44:31,507 - INFO - Splitting PDF 445c82e5_EDYM381JJDTUB87YAAPI into 1 pages
2025-09-24 14:44:31,512 - INFO - Split PDF into 1 pages
2025-09-24 14:44:31,512 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:44:31,512 - INFO - Expected pages: [1]
2025-09-24 14:44:31,553 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:44:31,554 - INFO - Splitting PDF into individual pages...
2025-09-24 14:44:31,555 - INFO - Splitting PDF 190e51b6_GMCKX2ERTX05S300COLL into 3 pages
2025-09-24 14:44:31,571 - INFO - Split PDF into 3 pages
2025-09-24 14:44:31,571 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:44:31,572 - INFO - Expected pages: [1, 2, 3]
2025-09-24 14:44:31,943 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf -> s3://document-extraction-logistically/temp/f15eba8c_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 14:44:31,943 - INFO - 🔍 [14:44:31] Starting classification: V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 14:44:31,944 - INFO - Initializing TextractProcessor...
2025-09-24 14:44:31,944 - INFO - ⬆️ [14:44:31] Uploading: Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 14:44:31,950 - INFO - Initializing BedrockProcessor...
2025-09-24 14:44:31,953 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f15eba8c_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 14:44:31,953 - INFO - Processing PDF from S3...
2025-09-24 14:44:31,955 - INFO - Downloading PDF from S3 to /tmp/tmpznkczwk0/f15eba8c_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 14:44:32,154 - INFO - Page 1: Extracted 1384 characters, 71 lines from 05c30a02_CZ7K9JE7PH88JUBC19JF_a541b48d_page_001.pdf
2025-09-24 14:44:32,154 - INFO - Successfully processed page 1
2025-09-24 14:44:32,155 - INFO - Combined 1 pages into final text
2025-09-24 14:44:32,155 - INFO - Text validation for 05c30a02_CZ7K9JE7PH88JUBC19JF: 1401 characters, 1 pages
2025-09-24 14:44:32,155 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:44:32,155 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:44:32,487 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:44:32,487 - INFO - Splitting PDF into individual pages...
2025-09-24 14:44:32,490 - INFO - Splitting PDF f7000952_T51X0UC3WJL168AL2PGB into 1 pages
2025-09-24 14:44:32,499 - INFO - Split PDF into 1 pages
2025-09-24 14:44:32,499 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:44:32,499 - INFO - Expected pages: [1]
2025-09-24 14:44:32,661 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf -> s3://document-extraction-logistically/temp/145a98bf_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 14:44:32,662 - INFO - 🔍 [14:44:32] Starting classification: Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 14:44:32,664 - INFO - Initializing TextractProcessor...
2025-09-24 14:44:32,677 - INFO - Initializing BedrockProcessor...
2025-09-24 14:44:32,681 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/145a98bf_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 14:44:32,682 - INFO - Processing PDF from S3...
2025-09-24 14:44:32,682 - INFO - Downloading PDF from S3 to /tmp/tmpyvto9txe/145a98bf_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 14:44:33,573 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 14:44:33,573 - INFO - Splitting PDF into individual pages...
2025-09-24 14:44:33,574 - INFO - Splitting PDF 69a21c68_TCM8BE9P052RZLZGHQIW into 1 pages
2025-09-24 14:44:33,576 - INFO - Split PDF into 1 pages
2025-09-24 14:44:33,576 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:44:33,576 - INFO - Expected pages: [1]
2025-09-24 14:44:33,765 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:44:33,765 - INFO - Splitting PDF into individual pages...
2025-09-24 14:44:33,767 - INFO - Splitting PDF f15eba8c_V44T1WF2N7RT33JSFU5F into 1 pages
2025-09-24 14:44:33,774 - INFO - Split PDF into 1 pages
2025-09-24 14:44:33,774 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:44:33,774 - INFO - Expected pages: [1]
2025-09-24 14:44:33,870 - INFO - Page 3: Extracted 541 characters, 39 lines from 8a9145ea_DGAKPGYVH59IXR7KRKZS_a66d96ca_page_003.pdf
2025-09-24 14:44:33,871 - INFO - Successfully processed page 3
2025-09-24 14:44:34,085 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '75bf48c5-1da7-481f-be1d-861d32196546', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:14:33 GMT', 'content-type': 'application/json', 'content-length': '752', 'connection': 'keep-alive', 'x-amzn-requestid': '75bf48c5-1da7-481f-be1d-861d32196546'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We must classify each page. Only page1 provided. We need to examine keywords. The header says "COMMERCIAL INVOICE". That matches doc_type comm_invoice. Also contains HS number, country of origin, terms of sale, etc. So classify as comm_invoice. Provide output via tool call classify_logistics_doc_type. The input expects an array of documents objects with page_no and doc_type. Must output exactly one object for page 1. So call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2940, 'outputTokens': 111, 'totalTokens': 3051}, 'metrics': {'latencyMs': 760}}
2025-09-24 14:44:34,085 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/05c30a02_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 14:44:34,102 - INFO - 

CZ7K9JE7PH88JUBC19JF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 14:44:34,102 - INFO - 

✓ Saved result: output/run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 14:44:34,211 - INFO - Page 2: Extracted 579 characters, 42 lines from 8a9145ea_DGAKPGYVH59IXR7KRKZS_a66d96ca_page_002.pdf
2025-09-24 14:44:34,212 - INFO - Successfully processed page 2
2025-09-24 14:44:34,424 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/05c30a02_CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 14:44:35,010 - INFO - Page 3: Extracted 526 characters, 49 lines from 190e51b6_GMCKX2ERTX05S300COLL_0d9ce540_page_003.pdf
2025-09-24 14:44:35,011 - INFO - Successfully processed page 3
2025-09-24 14:44:35,307 - INFO - Downloaded PDF size: 0.6 MB
2025-09-24 14:44:35,307 - INFO - Splitting PDF into individual pages...
2025-09-24 14:44:35,316 - INFO - Splitting PDF 145a98bf_Z106V9IKGLMUAGC3VOK8 into 2 pages
2025-09-24 14:44:35,328 - INFO - Page 4: Extracted 3255 characters, 170 lines from 8a9145ea_DGAKPGYVH59IXR7KRKZS_a66d96ca_page_004.pdf
2025-09-24 14:44:35,328 - INFO - Successfully processed page 4
2025-09-24 14:44:35,331 - INFO - Split PDF into 2 pages
2025-09-24 14:44:35,331 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:44:35,331 - INFO - Expected pages: [1, 2]
2025-09-24 14:44:35,576 - INFO - Page 1: Extracted 2957 characters, 137 lines from 8a9145ea_DGAKPGYVH59IXR7KRKZS_a66d96ca_page_001.pdf
2025-09-24 14:44:35,577 - INFO - Successfully processed page 1
2025-09-24 14:44:35,577 - INFO - Combined 4 pages into final text
2025-09-24 14:44:35,577 - INFO - Text validation for 8a9145ea_DGAKPGYVH59IXR7KRKZS: 7406 characters, 4 pages
2025-09-24 14:44:35,578 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:44:35,578 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:44:35,840 - INFO - Page 2: Extracted 1411 characters, 134 lines from 190e51b6_GMCKX2ERTX05S300COLL_0d9ce540_page_002.pdf
2025-09-24 14:44:35,840 - INFO - Successfully processed page 2
2025-09-24 14:44:35,978 - INFO - Page 1: Extracted 1271 characters, 100 lines from 190e51b6_GMCKX2ERTX05S300COLL_0d9ce540_page_001.pdf
2025-09-24 14:44:35,978 - INFO - Successfully processed page 1
2025-09-24 14:44:35,978 - INFO - Combined 3 pages into final text
2025-09-24 14:44:35,979 - INFO - Text validation for 190e51b6_GMCKX2ERTX05S300COLL: 3263 characters, 3 pages
2025-09-24 14:44:35,979 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:44:35,979 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:44:37,122 - INFO - Page 1: Extracted 1163 characters, 75 lines from 445c82e5_EDYM381JJDTUB87YAAPI_e25d3427_page_001.pdf
2025-09-24 14:44:37,123 - INFO - Successfully processed page 1
2025-09-24 14:44:37,123 - INFO - Combined 1 pages into final text
2025-09-24 14:44:37,124 - INFO - Text validation for 445c82e5_EDYM381JJDTUB87YAAPI: 1180 characters, 1 pages
2025-09-24 14:44:37,124 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:44:37,124 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:44:37,873 - INFO - Page 1: Extracted 2010 characters, 210 lines from f7000952_T51X0UC3WJL168AL2PGB_72cd4e1c_page_001.pdf
2025-09-24 14:44:37,873 - INFO - Successfully processed page 1
2025-09-24 14:44:37,884 - INFO - Page 1: Extracted 1545 characters, 72 lines from 92b938fe_ECY73YCNA7IPQSM8NAKW_51c360b0_page_001.pdf
2025-09-24 14:44:37,884 - INFO - Combined 1 pages into final text
2025-09-24 14:44:37,885 - INFO - Successfully processed page 1
2025-09-24 14:44:37,885 - INFO - Text validation for f7000952_T51X0UC3WJL168AL2PGB: 2027 characters, 1 pages
2025-09-24 14:44:37,885 - INFO - Combined 1 pages into final text
2025-09-24 14:44:37,885 - INFO - Text validation for 92b938fe_ECY73YCNA7IPQSM8NAKW: 1562 characters, 1 pages
2025-09-24 14:44:37,886 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:44:37,886 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:44:37,886 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:44:37,886 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:44:38,277 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '82aa5789-a472-45c7-9082-0aea4b8fb602', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:14:38 GMT', 'content-type': 'application/json', 'content-length': '1195', 'connection': 'keep-alive', 'x-amzn-requestid': '82aa5789-a472-45c7-9082-0aea4b8fb602'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have three pages. All appear to be an invoice, specifically a "NORTHBOUND INVOICE" with "INVOICE: TR2025FE3272". It\'s a commercial invoice (international shipment) because includes HS codes, country origins, incoterm, etc. So doc_type is "comm_invoice". All pages part of same invoice.\n\nWe must output via tool. Each object: page_no and doc_type. Should we include the array of documents as per function signature. The function expects an array of objects with page_no and doc_type. Each object described per page. All three pages same type. So we produce:\n\n{\n  documents: [\n    {page_no: 1, doc_type: "comm_invoice"},\n    {page_no: 2, doc_type: "comm_invoice"},\n    {page_no: 3, doc_type: "comm_invoice"}\n  ]\n}\n\nNeed to call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"comm_invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4014, 'outputTokens': 229, 'totalTokens': 4243}, 'metrics': {'latencyMs': 1121}}
2025-09-24 14:44:38,277 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/190e51b6_GMCKX2ERTX05S300COLL.pdf
2025-09-24 14:44:38,311 - INFO - 

GMCKX2ERTX05S300COLL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 3,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 14:44:38,311 - INFO - 

✓ Saved result: output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 14:44:38,584 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '16be638d-ace0-4170-8723-cc997d1316cf', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:14:38 GMT', 'content-type': 'application/json', 'content-length': '1774', 'connection': 'keep-alive', 'x-amzn-requestid': '16be638d-ace0-4170-8723-cc997d1316cf'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have 4 pages. Need to classify each page. Let\'s analyze each page.\n\nPage1: Looks like a "COMBINATION INVOICE - DECLARATION BY FOREIGN SHIPPER". It contains invoice details, shipping info, etc. Could be a commercial invoice? It contains HS codes, country of origin, terms of sale, etc. So that\'s a commercial invoice. The doc_type "comm_invoice" is for commercial invoice. The page also has "COMBINATION INVOICE - DECLARATION BY FOREIGN SHIPPER". It contains invoice details and a customs declaration. It is a commercial invoice. So doc_type = comm_invoice.\n\nCheck if page is continuation? It starts with header "COMBINATION INVOICE - DECLARATION BY FOREIGN SHIPPER". Not continuation. So page1 = comm_invoice.\n\nPage2: It is an "INVOICE". Contains "INVOICE #" and "INVOICE DATE". It\'s a carrier invoice? It has shipping details, line items, etc. No mention of HS codes, customs. It\'s a simple invoice. So doc_type = invoice. Not commercial invoice because no HS codes etc. So page2 = invoice.\n\nPage3: Similar to page2, an invoice. So page3 = invoice.\n\nPage4: "USMCA Certification of Origin". That is a customs document, specifically a certification of origin. It\'s a customs doc. So doc_type = customs_doc.\n\nThat\'s it. We have 4 pages. Output each with page_no and doc_type.\n\nReturn tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"},{"page_no":4,"doc_type":"customs_doc"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 4891, 'outputTokens': 360, 'totalTokens': 5251}, 'metrics': {'latencyMs': 1778}}
2025-09-24 14:44:38,585 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/8a9145ea_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 14:44:38,598 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/190e51b6_GMCKX2ERTX05S300COLL.pdf
2025-09-24 14:44:38,666 - INFO - 

DGAKPGYVH59IXR7KRKZS.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 2,
            "doc_type": "invoice"
        },
        {
            "page_no": 3,
            "doc_type": "invoice"
        },
        {
            "page_no": 4,
            "doc_type": "customs_doc"
        }
    ]
}
2025-09-24 14:44:38,666 - INFO - 

✓ Saved result: output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 14:44:38,932 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'ec475407-d360-420c-862b-2d84d95ca25f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:14:38 GMT', 'content-type': 'application/json', 'content-length': '641', 'connection': 'keep-alive', 'x-amzn-requestid': 'ec475407-d360-420c-862b-2d84d95ca25f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It contains "COMMERCIAL INVOICE" header. That is clearly a commercial invoice. Should classify as "comm_invoice". Ensure no other header like invoice? It\'s commercial invoice. So we output one JSON array with one object page_no 1 doc_type "comm_invoice". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2843, 'outputTokens': 87, 'totalTokens': 2930}, 'metrics': {'latencyMs': 590}}
2025-09-24 14:44:38,932 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/445c82e5_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 14:44:38,980 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/8a9145ea_DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 14:44:39,002 - INFO - 

EDYM381JJDTUB87YAAPI.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 14:44:39,002 - INFO - 

✓ Saved result: output/run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 14:44:39,391 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/445c82e5_EDYM381JJDTUB87YAAPI.pdf
2025-09-24 14:44:39,690 - INFO - Page 1: Extracted 4288 characters, 110 lines from f15eba8c_V44T1WF2N7RT33JSFU5F_115f0d45_page_001.pdf
2025-09-24 14:44:39,691 - INFO - Successfully processed page 1
2025-09-24 14:44:39,691 - INFO - Combined 1 pages into final text
2025-09-24 14:44:39,691 - INFO - Text validation for f15eba8c_V44T1WF2N7RT33JSFU5F: 4305 characters, 1 pages
2025-09-24 14:44:39,691 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:44:39,691 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:44:39,694 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '1c8a1a18-c1c5-4b56-8b70-34dd52a0c76b', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:14:39 GMT', 'content-type': 'application/json', 'content-length': '610', 'connection': 'keep-alive', 'x-amzn-requestid': '1c8a1a18-c1c5-4b56-8b70-34dd52a0c76b'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have to classify each page. There\'s only one page. It\'s an export commercial invoice with HS codes etc. So it\'s a commercial invoice. The doc_type: "comm_invoice". It is page 1 of 1. Not continuation. So output single object with page_no 1 and doc_type "comm_invoice". Use tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3430, 'outputTokens': 86, 'totalTokens': 3516}, 'metrics': {'latencyMs': 561}}
2025-09-24 14:44:39,694 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f7000952_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 14:44:39,715 - INFO - Page 1: Extracted 1341 characters, 86 lines from 69a21c68_TCM8BE9P052RZLZGHQIW_94fc81b5_page_001.pdf
2025-09-24 14:44:39,715 - INFO - Successfully processed page 1
2025-09-24 14:44:39,717 - INFO - Combined 1 pages into final text
2025-09-24 14:44:39,718 - INFO - Text validation for 69a21c68_TCM8BE9P052RZLZGHQIW: 1358 characters, 1 pages
2025-09-24 14:44:39,721 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:44:39,722 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:44:39,732 - INFO - 

T51X0UC3WJL168AL2PGB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 14:44:39,732 - INFO - 

✓ Saved result: output/run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 14:44:39,795 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '290b8ccd-742f-4d04-821b-78ed43c081ff', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:14:39 GMT', 'content-type': 'application/json', 'content-length': '701', 'connection': 'keep-alive', 'x-amzn-requestid': '290b8ccd-742f-4d04-821b-78ed43c081ff'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. We have only page1. It appears to be a commercial invoice: includes Invoice No, PO No, Terms of Sale, HS codes, Country of Origin, Declaration Statement, etc. So doc_type: comm_invoice. It\'s a single page. So output must call the function classify_logistics_doc_type with documents array containing one object: page_no 1, doc_type: "comm_invoice".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2928, 'outputTokens': 104, 'totalTokens': 3032}, 'metrics': {'latencyMs': 676}}
2025-09-24 14:44:39,796 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/92b938fe_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 14:44:40,051 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f7000952_T51X0UC3WJL168AL2PGB.pdf
2025-09-24 14:44:40,066 - INFO - Page 2: Extracted 727 characters, 48 lines from 145a98bf_Z106V9IKGLMUAGC3VOK8_cc0bb0c3_page_002.pdf
2025-09-24 14:44:40,067 - INFO - Successfully processed page 2
2025-09-24 14:44:40,070 - INFO - 

ECY73YCNA7IPQSM8NAKW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 14:44:40,071 - INFO - 

✓ Saved result: output/run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 14:44:40,366 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/92b938fe_ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 14:44:41,436 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd62e9571-14d7-443d-9292-13d7bee3bc9f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:14:41 GMT', 'content-type': 'application/json', 'content-length': '516', 'connection': 'keep-alive', 'x-amzn-requestid': 'd62e9571-14d7-443d-9292-13d7bee3bc9f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. There\'s only page1. It\'s a commercial invoice. Keywords: "COMMERCIAL INVOICE", HS codes, country of origin, etc. So doc_type = "comm_invoice". Output via tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2889, 'outputTokens': 64, 'totalTokens': 2953}, 'metrics': {'latencyMs': 488}}
2025-09-24 14:44:41,437 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/69a21c68_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 14:44:41,491 - INFO - 

TCM8BE9P052RZLZGHQIW.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 14:44:41,491 - INFO - 

✓ Saved result: output/run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 14:44:41,733 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'd801c3bd-3485-4c8c-afbc-6c927b113a62', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:14:41 GMT', 'content-type': 'application/json', 'content-length': '1008', 'connection': 'keep-alive', 'x-amzn-requestid': 'd801c3bd-3485-4c8c-afbc-6c927b113a62'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify. The content looks like a Proforma Invoice. It includes "Proforma Invoice". It has invoice-like structure, but also includes HS codes, terms, etc. It\'s a commercial invoice? The definitions: invoice is carrier invoice. This is a Proforma Invoice from a vendor to a customer. That would be a commercial invoice (comm_invoice). Criteria: commercial invoice if HS, Country of Origin, Incoterms etc. Yes it has HS code, incoterms. So doc_type: comm_invoice. Output: use tool classify_logistics_doc_type with array of objects for each page. Only page 1. So output JSON with documents array: {page_no:1, doc_type:"comm_invoice"}.\n\nReturn via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3506, 'outputTokens': 175, 'totalTokens': 3681}, 'metrics': {'latencyMs': 914}}
2025-09-24 14:44:41,734 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f15eba8c_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 14:44:41,781 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/69a21c68_TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 14:44:41,832 - INFO - 

V44T1WF2N7RT33JSFU5F.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 14:44:41,832 - INFO - 

✓ Saved result: output/run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 14:44:42,020 - INFO - Page 1: Extracted 1789 characters, 98 lines from 145a98bf_Z106V9IKGLMUAGC3VOK8_cc0bb0c3_page_001.pdf
2025-09-24 14:44:42,020 - INFO - Successfully processed page 1
2025-09-24 14:44:42,020 - INFO - Combined 2 pages into final text
2025-09-24 14:44:42,020 - INFO - Text validation for 145a98bf_Z106V9IKGLMUAGC3VOK8: 2552 characters, 2 pages
2025-09-24 14:44:42,021 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:44:42,021 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:44:42,125 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f15eba8c_V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 14:44:44,503 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f11b5517-8333-48fc-8381-a0df112363d7', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:14:44 GMT', 'content-type': 'application/json', 'content-length': '1305', 'connection': 'keep-alive', 'x-amzn-requestid': 'f11b5517-8333-48fc-8381-a0df112363d7'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The content is a Proforma Invoice with many details like "Proforma Invoice", "Proforma Invoice Number", "PO Box", etc. Terms: Incoterm, Country of Origin, Commodity Code, etc. This is a commercial invoice type. The category \'comm_invoice\' is defined as Commercial Invoice with HS/HTS codes, Country of Origin, terms of sale. Here there is Country of Origin: FR, Commodity Code (HS code). So this is a commercial invoice. Not a standard carrier invoice. It\'s a proforma invoice from supplier to buyer. The doc type: comm_invoice. So both pages are same doc type. There is no continuation indicator like "continued". The second page has "Page 2 of 2". So it\'s a continuation. The first page is page 1. So page1 doc_type comm_invoice, page2 doc_type comm_invoice.\n\nReturn JSON list of objects: each with page_no, doc_type. Use tool classify_logistics_doc_type.\n\nWe must output via function call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3387, 'outputTokens': 243, 'totalTokens': 3630}, 'metrics': {'latencyMs': 1247}}
2025-09-24 14:44:44,504 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/145a98bf_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 14:44:44,544 - INFO - 

Z106V9IKGLMUAGC3VOK8.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "comm_invoice"
        },
        {
            "page_no": 2,
            "doc_type": "comm_invoice"
        }
    ]
}
2025-09-24 14:44:44,544 - INFO - 

✓ Saved result: output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 14:44:44,920 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/145a98bf_Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 14:44:44,921 - INFO - 
📊 Processing Summary:
2025-09-24 14:44:44,921 - INFO -    Total files: 9
2025-09-24 14:44:44,921 - INFO -    Successful: 9
2025-09-24 14:44:44,921 - INFO -    Failed: 0
2025-09-24 14:44:44,921 - INFO -    Duration: 20.04 seconds
2025-09-24 14:44:44,921 - INFO -    Output directory: output
2025-09-24 14:44:44,921 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:44:44,922 - INFO -    📄 CZ7K9JE7PH88JUBC19JF.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 14:44:44,922 - INFO -    📄 DGAKPGYVH59IXR7KRKZS.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"invoice"},{"page_no":3,"doc_type":"invoice"},{"page_no":4,"doc_type":"customs_doc"}]}
2025-09-24 14:44:44,922 - INFO -    📄 ECY73YCNA7IPQSM8NAKW.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 14:44:44,922 - INFO -    📄 EDYM381JJDTUB87YAAPI.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 14:44:44,922 - INFO -    📄 GMCKX2ERTX05S300COLL.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"},{"page_no":3,"doc_type":"comm_invoice"}]}
2025-09-24 14:44:44,922 - INFO -    📄 T51X0UC3WJL168AL2PGB.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 14:44:44,922 - INFO -    📄 TCM8BE9P052RZLZGHQIW.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 14:44:44,922 - INFO -    📄 V44T1WF2N7RT33JSFU5F.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"}]}
2025-09-24 14:44:44,922 - INFO -    📄 Z106V9IKGLMUAGC3VOK8.pdf: {"documents":[{"page_no":1,"doc_type":"comm_invoice"},{"page_no":2,"doc_type":"comm_invoice"}]}
2025-09-24 14:44:44,922 - INFO - 
============================================================================================================================================
2025-09-24 14:44:44,922 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:44:44,922 - INFO - ============================================================================================================================================
2025-09-24 14:44:44,922 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:44:44,922 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:44:44,922 - INFO - CZ7K9JE7PH88JUBC19JF.pdf                           1      comm_invoice         run1_CZ7K9JE7PH88JUBC19JF.json                    
2025-09-24 14:44:44,922 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf
2025-09-24 14:44:44,922 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 14:44:44,922 - INFO - 
2025-09-24 14:44:44,922 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           1      comm_invoice         run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 14:44:44,922 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 14:44:44,922 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 14:44:44,922 - INFO - 
2025-09-24 14:44:44,922 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           2      invoice              run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 14:44:44,922 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 14:44:44,922 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 14:44:44,922 - INFO - 
2025-09-24 14:44:44,922 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           3      invoice              run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 14:44:44,922 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 14:44:44,922 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 14:44:44,922 - INFO - 
2025-09-24 14:44:44,923 - INFO - DGAKPGYVH59IXR7KRKZS.pdf                           4      customs_doc          run1_DGAKPGYVH59IXR7KRKZS.json                    
2025-09-24 14:44:44,923 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf
2025-09-24 14:44:44,923 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 14:44:44,923 - INFO - 
2025-09-24 14:44:44,923 - INFO - ECY73YCNA7IPQSM8NAKW.pdf                           1      comm_invoice         run1_ECY73YCNA7IPQSM8NAKW.json                    
2025-09-24 14:44:44,923 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf
2025-09-24 14:44:44,923 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 14:44:44,923 - INFO - 
2025-09-24 14:44:44,923 - INFO - EDYM381JJDTUB87YAAPI.pdf                           1      comm_invoice         run1_EDYM381JJDTUB87YAAPI.json                    
2025-09-24 14:44:44,923 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/EDYM381JJDTUB87YAAPI.pdf
2025-09-24 14:44:44,923 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 14:44:44,923 - INFO - 
2025-09-24 14:44:44,923 - INFO - GMCKX2ERTX05S300COLL.pdf                           1      comm_invoice         run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 14:44:44,923 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 14:44:44,923 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 14:44:44,923 - INFO - 
2025-09-24 14:44:44,923 - INFO - GMCKX2ERTX05S300COLL.pdf                           2      comm_invoice         run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 14:44:44,923 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 14:44:44,923 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 14:44:44,923 - INFO - 
2025-09-24 14:44:44,923 - INFO - GMCKX2ERTX05S300COLL.pdf                           3      comm_invoice         run1_GMCKX2ERTX05S300COLL.json                    
2025-09-24 14:44:44,923 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/GMCKX2ERTX05S300COLL.pdf
2025-09-24 14:44:44,923 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_GMCKX2ERTX05S300COLL.json
2025-09-24 14:44:44,923 - INFO - 
2025-09-24 14:44:44,923 - INFO - T51X0UC3WJL168AL2PGB.pdf                           1      comm_invoice         run1_T51X0UC3WJL168AL2PGB.json                    
2025-09-24 14:44:44,923 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/T51X0UC3WJL168AL2PGB.pdf
2025-09-24 14:44:44,923 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 14:44:44,923 - INFO - 
2025-09-24 14:44:44,923 - INFO - TCM8BE9P052RZLZGHQIW.pdf                           1      comm_invoice         run1_TCM8BE9P052RZLZGHQIW.json                    
2025-09-24 14:44:44,923 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf
2025-09-24 14:44:44,923 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 14:44:44,923 - INFO - 
2025-09-24 14:44:44,923 - INFO - V44T1WF2N7RT33JSFU5F.pdf                           1      comm_invoice         run1_V44T1WF2N7RT33JSFU5F.json                    
2025-09-24 14:44:44,924 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf
2025-09-24 14:44:44,924 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 14:44:44,924 - INFO - 
2025-09-24 14:44:44,924 - INFO - Z106V9IKGLMUAGC3VOK8.pdf                           1      comm_invoice         run1_Z106V9IKGLMUAGC3VOK8.json                    
2025-09-24 14:44:44,924 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 14:44:44,924 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 14:44:44,924 - INFO - 
2025-09-24 14:44:44,924 - INFO - Z106V9IKGLMUAGC3VOK8.pdf                           2      comm_invoice         run1_Z106V9IKGLMUAGC3VOK8.json                    
2025-09-24 14:44:44,924 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf
2025-09-24 14:44:44,924 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 14:44:44,924 - INFO - 
2025-09-24 14:44:44,924 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:44:44,924 - INFO - Total entries: 15
2025-09-24 14:44:44,924 - INFO - ============================================================================================================================================
2025-09-24 14:44:44,924 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:44:44,924 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:44:44,924 - INFO -   1. CZ7K9JE7PH88JUBC19JF.pdf            Page 1   → comm_invoice    | run1_CZ7K9JE7PH88JUBC19JF.json
2025-09-24 14:44:44,924 - INFO -   2. DGAKPGYVH59IXR7KRKZS.pdf            Page 1   → comm_invoice    | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 14:44:44,925 - INFO -   3. DGAKPGYVH59IXR7KRKZS.pdf            Page 2   → invoice         | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 14:44:44,925 - INFO -   4. DGAKPGYVH59IXR7KRKZS.pdf            Page 3   → invoice         | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 14:44:44,925 - INFO -   5. DGAKPGYVH59IXR7KRKZS.pdf            Page 4   → customs_doc     | run1_DGAKPGYVH59IXR7KRKZS.json
2025-09-24 14:44:44,925 - INFO -   6. ECY73YCNA7IPQSM8NAKW.pdf            Page 1   → comm_invoice    | run1_ECY73YCNA7IPQSM8NAKW.json
2025-09-24 14:44:44,925 - INFO -   7. EDYM381JJDTUB87YAAPI.pdf            Page 1   → comm_invoice    | run1_EDYM381JJDTUB87YAAPI.json
2025-09-24 14:44:44,925 - INFO -   8. GMCKX2ERTX05S300COLL.pdf            Page 1   → comm_invoice    | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 14:44:44,925 - INFO -   9. GMCKX2ERTX05S300COLL.pdf            Page 2   → comm_invoice    | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 14:44:44,925 - INFO -  10. GMCKX2ERTX05S300COLL.pdf            Page 3   → comm_invoice    | run1_GMCKX2ERTX05S300COLL.json
2025-09-24 14:44:44,925 - INFO -  11. T51X0UC3WJL168AL2PGB.pdf            Page 1   → comm_invoice    | run1_T51X0UC3WJL168AL2PGB.json
2025-09-24 14:44:44,925 - INFO -  12. TCM8BE9P052RZLZGHQIW.pdf            Page 1   → comm_invoice    | run1_TCM8BE9P052RZLZGHQIW.json
2025-09-24 14:44:44,925 - INFO -  13. V44T1WF2N7RT33JSFU5F.pdf            Page 1   → comm_invoice    | run1_V44T1WF2N7RT33JSFU5F.json
2025-09-24 14:44:44,925 - INFO -  14. Z106V9IKGLMUAGC3VOK8.pdf            Page 1   → comm_invoice    | run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 14:44:44,925 - INFO -  15. Z106V9IKGLMUAGC3VOK8.pdf            Page 2   → comm_invoice    | run1_Z106V9IKGLMUAGC3VOK8.json
2025-09-24 14:44:44,925 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:44:44,925 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 20.037709, 'processed_files': [{'filename': 'CZ7K9JE7PH88JUBC19JF.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/CZ7K9JE7PH88JUBC19JF.pdf'}, {'filename': 'DGAKPGYVH59IXR7KRKZS.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}, {'page_no': 2, 'doc_type': 'invoice'}, {'page_no': 3, 'doc_type': 'invoice'}, {'page_no': 4, 'doc_type': 'customs_doc'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/DGAKPGYVH59IXR7KRKZS.pdf'}, {'filename': 'ECY73YCNA7IPQSM8NAKW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/ECY73YCNA7IPQSM8NAKW.pdf'}, {'filename': 'EDYM381JJDTUB87YAAPI.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/EDYM381JJDTUB87YAAPI.pdf'}, {'filename': 'GMCKX2ERTX05S300COLL.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}, {'page_no': 2, 'doc_type': 'comm_invoice'}, {'page_no': 3, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/GMCKX2ERTX05S300COLL.pdf'}, {'filename': 'T51X0UC3WJL168AL2PGB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/T51X0UC3WJL168AL2PGB.pdf'}, {'filename': 'TCM8BE9P052RZLZGHQIW.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/TCM8BE9P052RZLZGHQIW.pdf'}, {'filename': 'V44T1WF2N7RT33JSFU5F.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/V44T1WF2N7RT33JSFU5F.pdf'}, {'filename': 'Z106V9IKGLMUAGC3VOK8.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'comm_invoice'}, {'page_no': 2, 'doc_type': 'comm_invoice'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/comm_invoice/Z106V9IKGLMUAGC3VOK8.pdf'}]}
