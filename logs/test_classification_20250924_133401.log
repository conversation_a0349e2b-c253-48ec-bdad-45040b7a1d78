2025-09-24 13:34:01,885 - INFO - Logging initialized. Log file: logs/test_classification_20250924_133401.log
2025-09-24 13:34:01,886 - INFO - 📁 Found 9 files to process
2025-09-24 13:34:01,886 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 13:34:01,886 - INFO - 🚀 Processing 9 files in FORCED PARALLEL MODE...
2025-09-24 13:34:01,886 - INFO - 🚀 Creating 9 parallel tasks...
2025-09-24 13:34:01,886 - INFO - 🚀 All 9 tasks created - executing in parallel...
2025-09-24 13:34:01,886 - INFO - ⬆️ [13:34:01] Uploading: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:34:03,413 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf -> s3://document-extraction-logistically/temp/226d0eec_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:34:03,413 - INFO - 🔍 [13:34:03] Starting classification: B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:34:03,414 - INFO - ⬆️ [13:34:03] Uploading: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:34:03,415 - INFO - Initializing TextractProcessor...
2025-09-24 13:34:03,426 - INFO - Initializing BedrockProcessor...
2025-09-24 13:34:03,431 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/226d0eec_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:34:03,431 - INFO - Processing PDF from S3...
2025-09-24 13:34:03,432 - INFO - Downloading PDF from S3 to /tmp/tmp3mj9rr15/226d0eec_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:34:03,994 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf -> s3://document-extraction-logistically/temp/f2600872_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:34:03,995 - INFO - 🔍 [13:34:03] Starting classification: CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:34:03,995 - INFO - ⬆️ [13:34:03] Uploading: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:34:03,997 - INFO - Initializing TextractProcessor...
2025-09-24 13:34:04,010 - INFO - Initializing BedrockProcessor...
2025-09-24 13:34:04,012 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f2600872_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:34:04,012 - INFO - Processing PDF from S3...
2025-09-24 13:34:04,013 - INFO - Downloading PDF from S3 to /tmp/tmpv0x8c7em/f2600872_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:34:05,153 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf -> s3://document-extraction-logistically/temp/1394367b_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:34:05,154 - INFO - 🔍 [13:34:05] Starting classification: FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:34:05,155 - INFO - ⬆️ [13:34:05] Uploading: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:34:05,156 - INFO - Initializing TextractProcessor...
2025-09-24 13:34:05,156 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:34:05,165 - INFO - Splitting PDF into individual pages...
2025-09-24 13:34:05,166 - INFO - Splitting PDF 226d0eec_B3SIRREC9IAVZOJVDQSN into 1 pages
2025-09-24 13:34:05,180 - INFO - Initializing BedrockProcessor...
2025-09-24 13:34:05,187 - INFO - Split PDF into 1 pages
2025-09-24 13:34:05,187 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:34:05,187 - INFO - Expected pages: [1]
2025-09-24 13:34:05,189 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/1394367b_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:34:05,190 - INFO - Processing PDF from S3...
2025-09-24 13:34:05,194 - INFO - Downloading PDF from S3 to /tmp/tmp4gs2ht3z/1394367b_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:34:05,767 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:34:05,767 - INFO - Splitting PDF into individual pages...
2025-09-24 13:34:05,768 - INFO - Splitting PDF f2600872_CUF54EHGMLQ57HR93DRB into 1 pages
2025-09-24 13:34:05,776 - INFO - Split PDF into 1 pages
2025-09-24 13:34:05,776 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:34:05,776 - INFO - Expected pages: [1]
2025-09-24 13:34:06,601 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf -> s3://document-extraction-logistically/temp/5b59b97e_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:34:06,601 - INFO - 🔍 [13:34:06] Starting classification: IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:34:06,602 - INFO - ⬆️ [13:34:06] Uploading: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:34:06,604 - INFO - Initializing TextractProcessor...
2025-09-24 13:34:06,626 - INFO - Initializing BedrockProcessor...
2025-09-24 13:34:06,628 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/5b59b97e_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:34:06,628 - INFO - Processing PDF from S3...
2025-09-24 13:34:06,628 - INFO - Downloading PDF from S3 to /tmp/tmpc09tgp_0/5b59b97e_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:34:06,855 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:34:06,856 - INFO - Splitting PDF into individual pages...
2025-09-24 13:34:06,859 - INFO - Splitting PDF 1394367b_FYQQGIW8Z9DSAPCL0S9G into 1 pages
2025-09-24 13:34:06,860 - INFO - Split PDF into 1 pages
2025-09-24 13:34:06,860 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:34:06,861 - INFO - Expected pages: [1]
2025-09-24 13:34:07,264 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf -> s3://document-extraction-logistically/temp/18daa9a9_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:34:07,264 - INFO - 🔍 [13:34:07] Starting classification: MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:34:07,264 - INFO - ⬆️ [13:34:07] Uploading: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:34:07,265 - INFO - Initializing TextractProcessor...
2025-09-24 13:34:07,276 - INFO - Initializing BedrockProcessor...
2025-09-24 13:34:07,279 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/18daa9a9_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:34:07,279 - INFO - Processing PDF from S3...
2025-09-24 13:34:07,280 - INFO - Downloading PDF from S3 to /tmp/tmpxeoyl8qq/18daa9a9_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:34:08,647 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 13:34:08,647 - INFO - Splitting PDF into individual pages...
2025-09-24 13:34:08,649 - INFO - Splitting PDF 5b59b97e_IZTBXFPGXBFH3DV900G4 into 1 pages
2025-09-24 13:34:08,650 - INFO - Split PDF into 1 pages
2025-09-24 13:34:08,651 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:34:08,651 - INFO - Expected pages: [1]
2025-09-24 13:34:09,009 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:34:09,010 - INFO - Splitting PDF into individual pages...
2025-09-24 13:34:09,010 - INFO - Splitting PDF 18daa9a9_MR6ONA8GK6HN1LCZHEX3 into 1 pages
2025-09-24 13:34:09,011 - INFO - Split PDF into 1 pages
2025-09-24 13:34:09,012 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:34:09,012 - INFO - Expected pages: [1]
2025-09-24 13:34:09,221 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf -> s3://document-extraction-logistically/temp/08fd7abf_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:34:09,221 - INFO - 🔍 [13:34:09] Starting classification: PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:34:09,222 - INFO - ⬆️ [13:34:09] Uploading: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:34:09,224 - INFO - Initializing TextractProcessor...
2025-09-24 13:34:09,244 - INFO - Initializing BedrockProcessor...
2025-09-24 13:34:09,248 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/08fd7abf_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:34:09,248 - INFO - Processing PDF from S3...
2025-09-24 13:34:09,249 - INFO - Downloading PDF from S3 to /tmp/tmpqccdlixa/08fd7abf_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:34:09,612 - INFO - Page 1: Extracted 1255 characters, 79 lines from f2600872_CUF54EHGMLQ57HR93DRB_a03db254_page_001.pdf
2025-09-24 13:34:09,612 - INFO - Successfully processed page 1
2025-09-24 13:34:09,612 - INFO - Combined 1 pages into final text
2025-09-24 13:34:09,612 - INFO - Text validation for f2600872_CUF54EHGMLQ57HR93DRB: 1272 characters, 1 pages
2025-09-24 13:34:09,612 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:34:09,612 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:34:09,881 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf -> s3://document-extraction-logistically/temp/ddb39482_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:34:09,882 - INFO - 🔍 [13:34:09] Starting classification: PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:34:09,883 - INFO - ⬆️ [13:34:09] Uploading: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:34:09,883 - INFO - Initializing TextractProcessor...
2025-09-24 13:34:09,901 - INFO - Initializing BedrockProcessor...
2025-09-24 13:34:09,906 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ddb39482_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:34:09,907 - INFO - Processing PDF from S3...
2025-09-24 13:34:09,907 - INFO - Downloading PDF from S3 to /tmp/tmpcmyasapa/ddb39482_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:34:10,279 - INFO - Page 1: Extracted 1454 characters, 85 lines from 226d0eec_B3SIRREC9IAVZOJVDQSN_7403469c_page_001.pdf
2025-09-24 13:34:10,280 - INFO - Successfully processed page 1
2025-09-24 13:34:10,280 - INFO - Combined 1 pages into final text
2025-09-24 13:34:10,280 - INFO - Text validation for 226d0eec_B3SIRREC9IAVZOJVDQSN: 1471 characters, 1 pages
2025-09-24 13:34:10,280 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:34:10,280 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:34:10,446 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf -> s3://document-extraction-logistically/temp/46a3d972_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:34:10,447 - INFO - 🔍 [13:34:10] Starting classification: U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:34:10,448 - INFO - ⬆️ [13:34:10] Uploading: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:34:10,453 - INFO - Initializing TextractProcessor...
2025-09-24 13:34:10,469 - INFO - Initializing BedrockProcessor...
2025-09-24 13:34:10,474 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/46a3d972_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:34:10,475 - INFO - Processing PDF from S3...
2025-09-24 13:34:10,475 - INFO - Downloading PDF from S3 to /tmp/tmp2rl5b5kg/46a3d972_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:34:11,349 - INFO - Page 1: Extracted 1463 characters, 95 lines from 1394367b_FYQQGIW8Z9DSAPCL0S9G_013092de_page_001.pdf
2025-09-24 13:34:11,349 - INFO - Successfully processed page 1
2025-09-24 13:34:11,350 - INFO - Combined 1 pages into final text
2025-09-24 13:34:11,350 - INFO - Text validation for 1394367b_FYQQGIW8Z9DSAPCL0S9G: 1480 characters, 1 pages
2025-09-24 13:34:11,351 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:34:11,351 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:34:11,565 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 13:34:11,565 - INFO - Splitting PDF into individual pages...
2025-09-24 13:34:11,567 - INFO - Splitting PDF 08fd7abf_PB67IAPSJB1DZWMDIE1H into 2 pages
2025-09-24 13:34:11,570 - INFO - Split PDF into 2 pages
2025-09-24 13:34:11,570 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:34:11,570 - INFO - Expected pages: [1, 2]
2025-09-24 13:34:11,617 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:34:11,617 - INFO - Splitting PDF into individual pages...
2025-09-24 13:34:11,618 - INFO - Splitting PDF ddb39482_PEE2ZFMV7X0A0FL35G4G into 1 pages
2025-09-24 13:34:11,620 - INFO - Split PDF into 1 pages
2025-09-24 13:34:11,620 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:34:11,620 - INFO - Expected pages: [1]
2025-09-24 13:34:11,699 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:34:11,699 - INFO - Splitting PDF into individual pages...
2025-09-24 13:34:11,701 - INFO - Splitting PDF 46a3d972_U7BB1XSF3ASMIAE1MQ5I into 1 pages
2025-09-24 13:34:11,704 - INFO - Split PDF into 1 pages
2025-09-24 13:34:11,705 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:34:11,705 - INFO - Expected pages: [1]
2025-09-24 13:34:11,859 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf -> s3://document-extraction-logistically/temp/0e4c8f18_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:34:11,860 - INFO - 🔍 [13:34:11] Starting classification: YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:34:11,861 - INFO - Initializing TextractProcessor...
2025-09-24 13:34:11,874 - INFO - Initializing BedrockProcessor...
2025-09-24 13:34:11,879 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/0e4c8f18_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:34:11,880 - INFO - Processing PDF from S3...
2025-09-24 13:34:11,880 - INFO - Downloading PDF from S3 to /tmp/tmpbawuqnce/0e4c8f18_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:34:12,369 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '16734c73-4f51-4cd6-9ed0-221a82aeea26', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:04:12 GMT', 'content-type': 'application/json', 'content-length': '1796', 'connection': 'keep-alive', 'x-amzn-requestid': '16734c73-4f51-4cd6-9ed0-221a82aeea26'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. The content: It seems like a Weight and Research Program document, with weight and research section, declared value, etc. Keywords: "Weight and Research Program", "Weight and Research Section", "Weight and Research Program", "Weight and Research Section". Also contains "Declared Value". It\'s likely a Weight and Inspection Certificate? Actually the description says "Weight and Research Program" seems like a weight certificate for a freight. There\'s a "Weight and Research Section" - maybe it\'s a "Weight and Inspection Certificate". The definition for weight_and_inspection_cert: "Certificate/Report issued by authority confirming weight possibly with length, width and height". It says "If the weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." Check nmfc cert: keywords like NMFC Code, class#, Correction information. This page doesn\'t mention NMFC. So it\'s weight_and_inspection_cert likely.\n\nCheck also "inspection_cert" vs weight_and_inspection_cert. It says if weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise classification is inspection_cert. The page includes weight? There is "Weights Per CWT." and "CWT". It shows weight? Yes weights. So weight mentioned. So weight_and_inspection_cert.\n\nThus doc_type = weight_and_inspection_cert. Page 1. Return JSON.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2896, 'outputTokens': 316, 'totalTokens': 3212}, 'metrics': {'latencyMs': 1538}}
2025-09-24 13:34:12,370 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f2600872_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:34:12,393 - INFO - 

CUF54EHGMLQ57HR93DRB.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:34:12,394 - INFO - 

✓ Saved result: output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:34:12,677 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f2600872_CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:34:12,882 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '861013c7-8895-455f-b73d-a09ac8e45fa6', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:04:12 GMT', 'content-type': 'application/json', 'content-length': '1693', 'connection': 'keep-alive', 'x-amzn-requestid': '861013c7-8895-455f-b73d-a09ac8e45fa6'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page.\n\nWe have only page1. We need to output using the tool classify_logistics_doc_type with documents array containing one object? Actually specification: For every page in the input PDF you MUST return exactly one object describing that page. Output pages in ascending page order. For each page, the object should include page_no and doc_type. So we need to call tool classify_logistics_doc_type with documents: [{page_no: 1, doc_type: ...}]. Must use the tool.\n\nNow determine doc_type. The content seems to be a weight and research program sheet, possibly a weight and inspection certificate. Keywords: "Weight and Research Program", "Weight", "As found upon Examination", "We certify that this shipment has been inspected and/or reweighed". Also "UPD CLASS PER NMFC 56840 ON BOL". This seems like a weight and inspection certificate. The type "weight_and_inspection_cert" is for certificates confirming weight. This page seems to be a weight certificate from a research program; likely a weight and inspection certificate. There\'s no mention of "certificate", but "We certify that this shipment has been inspected and/or reweighed". Also "Weight and Research Program". This matches definition of weight_and_inspection_cert. So doc_type = weight_and_inspection_cert.\n\nWe need to output.\n\nWe\'ll use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2958, 'outputTokens': 300, 'totalTokens': 3258}, 'metrics': {'latencyMs': 1422}}
2025-09-24 13:34:12,882 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/226d0eec_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:34:12,904 - INFO - 

B3SIRREC9IAVZOJVDQSN.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:34:12,904 - INFO - 

✓ Saved result: output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:34:13,187 - INFO - Page 1: Extracted 500 characters, 76 lines from 18daa9a9_MR6ONA8GK6HN1LCZHEX3_dd3b0a0c_page_001.pdf
2025-09-24 13:34:13,188 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/226d0eec_B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:34:13,188 - INFO - Successfully processed page 1
2025-09-24 13:34:13,189 - INFO - Combined 1 pages into final text
2025-09-24 13:34:13,189 - INFO - Text validation for 18daa9a9_MR6ONA8GK6HN1LCZHEX3: 517 characters, 1 pages
2025-09-24 13:34:13,189 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:34:13,189 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:34:13,202 - INFO - Page 1: Extracted 465 characters, 37 lines from 5b59b97e_IZTBXFPGXBFH3DV900G4_5568e4d8_page_001.pdf
2025-09-24 13:34:13,202 - INFO - Successfully processed page 1
2025-09-24 13:34:13,203 - INFO - Combined 1 pages into final text
2025-09-24 13:34:13,203 - INFO - Text validation for 5b59b97e_IZTBXFPGXBFH3DV900G4: 482 characters, 1 pages
2025-09-24 13:34:13,204 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:34:13,204 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:34:13,293 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '00276974-d126-4e41-85f2-068c7c80ee43', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:04:13 GMT', 'content-type': 'application/json', 'content-length': '816', 'connection': 'keep-alive', 'x-amzn-requestid': '00276974-d126-4e41-85f2-068c7c80ee43'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. Keywords? The header shows "NMFC Classification Certificate". That matches nmfc_cert. So doc_type: "nmfc_cert". Should we check if weight_and_inspection_cert? It has weight and inspection details. But nmfc_cert has higher priority as per note: "If weight_and_inspection_cert has key words that are mentioned in nmfc_cert, classify as nmfc_cert." So nmfc_cert. So output JSON array with one object: page_no:1 doc_type:"nmfc_cert". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3013, 'outputTokens': 136, 'totalTokens': 3149}, 'metrics': {'latencyMs': 745}}
2025-09-24 13:34:13,294 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/1394367b_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:34:13,313 - INFO - 

FYQQGIW8Z9DSAPCL0S9G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:34:13,313 - INFO - 

✓ Saved result: output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:34:13,592 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/1394367b_FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:34:14,173 - INFO - Downloaded PDF size: 0.4 MB
2025-09-24 13:34:14,174 - INFO - Splitting PDF into individual pages...
2025-09-24 13:34:14,175 - INFO - Splitting PDF 0e4c8f18_YL4HZPZH7D3B03FZZ5QK into 1 pages
2025-09-24 13:34:14,196 - INFO - Split PDF into 1 pages
2025-09-24 13:34:14,196 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:34:14,196 - INFO - Expected pages: [1]
2025-09-24 13:34:14,943 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0474588c-8c45-4d1c-bb45-5801c6242812', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:04:14 GMT', 'content-type': 'application/json', 'content-length': '714', 'connection': 'keep-alive', 'x-amzn-requestid': '0474588c-8c45-4d1c-bb45-5801c6242812'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page. Check if it is continuation of previous? No previous. Determine doc_type. The page contains "NMFC CLASSIFICATION CERTIFICATE" header. So doc_type likely nmfc_cert. According to keywords, yes. So output array with object page_no 1, doc_type "nmfc_cert". Use tool.'}}}, {'toolUse': {'toolUseId': 'tooluse_oFPdaGrdQXCej-1r56N4IQ', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2702, 'outputTokens': 92, 'totalTokens': 2794}, 'metrics': {'latencyMs': 593}}
2025-09-24 13:34:14,944 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/18daa9a9_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:34:14,969 - INFO - 

MR6ONA8GK6HN1LCZHEX3.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:34:14,969 - INFO - 

✓ Saved result: output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:34:15,265 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/18daa9a9_MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:34:15,895 - INFO - Page 1: Extracted 1119 characters, 62 lines from 46a3d972_U7BB1XSF3ASMIAE1MQ5I_85d46252_page_001.pdf
2025-09-24 13:34:15,896 - INFO - Successfully processed page 1
2025-09-24 13:34:15,897 - INFO - Combined 1 pages into final text
2025-09-24 13:34:15,897 - INFO - Text validation for 46a3d972_U7BB1XSF3ASMIAE1MQ5I: 1136 characters, 1 pages
2025-09-24 13:34:15,898 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:34:15,898 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:34:16,273 - INFO - Page 1: Extracted 477 characters, 75 lines from ddb39482_PEE2ZFMV7X0A0FL35G4G_db0392e9_page_001.pdf
2025-09-24 13:34:16,273 - INFO - Successfully processed page 1
2025-09-24 13:34:16,274 - INFO - Combined 1 pages into final text
2025-09-24 13:34:16,274 - INFO - Text validation for ddb39482_PEE2ZFMV7X0A0FL35G4G: 494 characters, 1 pages
2025-09-24 13:34:16,275 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:34:16,275 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:34:16,742 - INFO - Page 2: Extracted 832 characters, 40 lines from 08fd7abf_PB67IAPSJB1DZWMDIE1H_8f2ed96d_page_002.pdf
2025-09-24 13:34:16,742 - INFO - Successfully processed page 2
2025-09-24 13:34:16,747 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '9d9cab6a-68ba-4adb-9c47-827053df073f', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:04:16 GMT', 'content-type': 'application/json', 'content-length': '2700', 'connection': 'keep-alive', 'x-amzn-requestid': '9d9cab6a-68ba-4adb-9c47-827053df073f'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to identify doc_type. The text shows "Weight & Inspection Freight Class Certificate" and "CORRECT CLASS PER DEFAULT DENSITY RATING & DIMENSIONS OBTAINED AT PICK UP" and "CORRECT TO CLASS: 250". The doc type "weight_and_inspection_cert" is defined as Certificate/Report issued by authority confirming weight possibly with length, width and height. Weight and Inspection Certificate. Also note that we must check if nmfc_cert applies: weight_and_inspection_cert has weight and possibly NMFC related. The rule: "Strictly check if weight_and_inspection_cert is having keywwords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." We have "Freight Class Certificate" and "CORRECT TO CLASS: 250" and "Density PCF" and "CORRECT CLASS PER DEFAULT DENSITY RATING & DIMENSIONS OBTAINED AT PICK UP". Does this include NMFC Code, class #, etc? There\'s no explicit NMFC code. "CORRECT TO CLASS: 250" maybe class number. But NMFC cert keywords: "NMFC Code", "class #", "As described and As found or Original and inspection or Corrected class or Correction information". We see "CORRECT TO CLASS: 250" and "CORRECT CLASS PER DEFAULT DENSITY RATING". That could be a corrected class. This might be nmfc_cert. But the doc type is a Freight Class Certificate, which is a type of NMFC classification certificate. And nmfc_cert is specifically "NMFC Classification Certificate or Correction notice" with keywords like "Corrected class". Here we have "Corrected class" phrase? It says "CORRECT CLASS PER DEFAULT DENSITY RATING". That seems like a corrected class. So I think nmfc_cert is appropriate. Let\'s check: weight_and_inspection_cert is used when weight is mentioned. But nmfc_cert is also about weight and class. The instruction: "Strictly check if weight_and_inspection_cert is having keywwords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." So we see "CORRECTED CLASS"? The phrase "CORRECT CLASS PER DEFAULT DENSITY RATING & DIMENSIONS OBTAINED AT PICK UP" indicates a correction. Also "CORRECT TO CLASS: 250". So yes, we should classify as nmfc_cert. So output a JSON array with one object: page_no:1, doc_type:"nmfc_cert".\n\nWe must use tool call classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2607, 'outputTokens': 575, 'totalTokens': 3182}, 'metrics': {'latencyMs': 2348}}
2025-09-24 13:34:16,747 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/5b59b97e_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:34:16,763 - INFO - 

IZTBXFPGXBFH3DV900G4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:34:16,763 - INFO - 

✓ Saved result: output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:34:17,046 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/5b59b97e_IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:34:17,323 - INFO - Page 1: Extracted 1225 characters, 82 lines from 08fd7abf_PB67IAPSJB1DZWMDIE1H_8f2ed96d_page_001.pdf
2025-09-24 13:34:17,323 - INFO - Successfully processed page 1
2025-09-24 13:34:17,324 - INFO - Combined 2 pages into final text
2025-09-24 13:34:17,324 - INFO - Text validation for 08fd7abf_PB67IAPSJB1DZWMDIE1H: 2093 characters, 2 pages
2025-09-24 13:34:17,325 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:34:17,325 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:34:18,735 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0cf2db60-3818-4306-be2b-6e6f84b648e9', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:04:18 GMT', 'content-type': 'application/json', 'content-length': '1806', 'connection': 'keep-alive', 'x-amzn-requestid': '0cf2db60-3818-4306-be2b-6e6f84b648e9'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. The text includes "Inspection Certificate" header. It contains details like NMFC, weight, cube. Under "CERTIFICATE INFORMATION" includes cube, weight, density. Also includes "CORRECTED BILL INFORMATION". The doc type likely "inspection_cert" or "weight_and_inspection_cert" or "nmfc_cert". Need to decide.\n\nThe definition: weight_and_inspection_cert includes weight, length, width, height, certificate of analysis, measurement certificate, weight certificate, inspection certificate, W&I. The note: if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. That means if the certificate includes NMFC code and maybe class or correction. It says "Strickly check if weight_and_inspection_cert is having keywwords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert."\n\nLet\'s see nmfc_cert keywords: "As described and As found or Original and inspection or Corrected class or Correction information", "NMFC Code, class #". Also "corrected" appears. The page shows "CORRECTED BILL INFORMATION" and "Corrected" appears. It has NMFC 87560 and 87552. So maybe nmfc_cert. Also note: It might be "inspection_cert" (non-weight) but this has weight. So weight_and_inspection_cert is a candidate. But since it has NMFC code and "Corrected" indicates correction notice, we should classify as nmfc_cert.\n\nThus doc_type: nmfc_cert. Page 1. Return JSON.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2810, 'outputTokens': 355, 'totalTokens': 3165}, 'metrics': {'latencyMs': 1684}}
2025-09-24 13:34:18,736 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/46a3d972_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:34:18,757 - INFO - 

U7BB1XSF3ASMIAE1MQ5I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:34:18,757 - INFO - 

✓ Saved result: output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:34:19,037 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/46a3d972_U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:34:20,089 - INFO - Page 1: Extracted 1248 characters, 88 lines from 0e4c8f18_YL4HZPZH7D3B03FZZ5QK_9320a574_page_001.pdf
2025-09-24 13:34:20,089 - INFO - Successfully processed page 1
2025-09-24 13:34:20,090 - INFO - Combined 1 pages into final text
2025-09-24 13:34:20,090 - INFO - Text validation for 0e4c8f18_YL4HZPZH7D3B03FZZ5QK: 1265 characters, 1 pages
2025-09-24 13:34:20,090 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:34:20,090 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:34:21,616 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4eaed9d6-48d2-4ff1-a1fa-cb5d3fa949df', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:04:21 GMT', 'content-type': 'application/json', 'content-length': '3149', 'connection': 'keep-alive', 'x-amzn-requestid': '4eaed9d6-48d2-4ff1-a1fa-cb5d3fa949df'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Determine type: This looks like a Weight and Inspection Certificate (W&R Certificate). Keywords: "WEIGHT & RESEARCH CERTIFICATE", "W&R APPROVED", "DIMENSIONAL SCAN INFO", "NMFC", "CUBE", "WEIGHT". This matches weight_and_inspection_cert. There is also mention of "W&R CODES" and "W&R APPROVED". So likely weight_and_inspection_cert. However we need to check if it\'s nmfc_cert. The rule: If weight_and_inspection_cert has key words that are mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert. Let\'s see nmfc_cert keywords: "As described ... As found or Original and inspection or Corrected class or Correction information" and optional "NMFC Code, class #". In the page, we have "NMFC: 73260 /SUB NMFC: 09 CLASS: 70.0" and "Corrected". There\'s mention: "C W&R ISSUED" but not "Correction information". The header says "WEIGHT & RESEARCH CERTIFICATE". There\'s "W&R CODES" and "W&R APPROVED". The note says "C W&R ISSUED" maybe indicates issuance. The question is whether the presence of "NMFC" alone qualifies for nmfc_cert? The rule: "If weight_and_inspection_cert is having keywwords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." The key words for nmfc_cert include "NMFC Code" etc. The doc has "NMFC: 73260 /SUB NMFC: 09 CLASS: 70.0". That includes NMFC code. So might classify as nmfc_cert. But we must also consider if it\'s a correction certificate. The doc says "W&R APPROVED" and "C W&R ISSUED". No explicit "Correction" but may be a standard W&R certificate. The presence of NMFC code does not necessarily mean it\'s a correction. However the rule says if the weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. But the nmfc_cert keywords include "NMFC Code, class #". Those are present. So likely nmfc_cert.\n\nBut we should also consider that the doc is not a correction notice but a weight and inspection certificate possibly with NMFC info. But the rule is explicit: "If weight_and_inspection_cert is having keywwords that are mentioend in nmfc_cert, classify it as nmfc_cert". So we should classify as nmfc_cert.\n\nThus both pages are nmfc_cert. But we must check if page2 has "C W&R ISSUED" and "W&R APPROVED". That indicates issuance, not correction. But rule might override. However maybe it\'s still weight_and_inspection_cert but includes NMFC info. But the rule says if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. There\'s an optional "NMFC Code" mention. The doc has NMFC code. So likely nmfc_cert.\n\nThus output two objects: page_no 1 and 2 both doc_type: "nmfc_cert".\n\nReturn via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3301, 'outputTokens': 718, 'totalTokens': 4019}, 'metrics': {'latencyMs': 3095}}
2025-09-24 13:34:21,617 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/08fd7abf_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:34:21,649 - INFO - 

PB67IAPSJB1DZWMDIE1H.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:34:21,649 - INFO - 

✓ Saved result: output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:34:21,970 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/08fd7abf_PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:34:22,197 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'dae561d1-5d9c-4549-88fe-3121c4308ec2', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:04:22 GMT', 'content-type': 'application/json', 'content-length': '577', 'connection': 'keep-alive', 'x-amzn-requestid': 'dae561d1-5d9c-4549-88fe-3121c4308ec2'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify the page. The page shows NMFC CLASSIFICATION CERTIFICATE. So doc_type likely "nmfc_cert". The doc_type list includes nmfc_cert. So we should output object: page_no: 1, doc_type: "nmfc_cert". Return via tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2693, 'outputTokens': 83, 'totalTokens': 2776}, 'metrics': {'latencyMs': 4760}}
2025-09-24 13:34:22,198 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/ddb39482_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:34:22,218 - INFO - 

PEE2ZFMV7X0A0FL35G4G.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:34:22,218 - INFO - 

✓ Saved result: output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:34:22,505 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ddb39482_PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:34:23,506 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '80a40d48-23db-4a8a-8e57-1ec6fd2eba46', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:04:23 GMT', 'content-type': 'application/json', 'content-length': '2522', 'connection': 'keep-alive', 'x-amzn-requestid': '80a40d48-23db-4a8a-8e57-1ec6fd2eba46'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It looks like a Weight and Inspection Certificate. The header says "WEIGHTS & INSPECTION CERTIFICATE". Also includes "Correction Date", "Pro #", "Type: RECLASS REWEIGH". There is mention of NMFC, class, pieces, weight etc. So it\'s weight_and_inspection_cert. Need to check if it also qualifies as nmfc_cert. The note: "Strickly check if weight_and_inspection_cert is having keywwords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." nmfc_cert keywords: "As described and As found or Original and inspection or Corrected class or Correction information" plus NMFC Code, class #. The document says "Correction Date" and "Reclass reweigh". It doesn\'t mention "Corrected class" explicitly but has "Reclass S". Also has "Correction Date". But is it a correction? Might be a reweigh certificate. It might still be weight_and_inspection_cert. The nmfc_cert is for correction of weight_and_inspection_cert. Since it\'s a correction, maybe nmfc_cert. But the doc is a Weight and Inspection Certificate. The presence of "Correction Date" suggests it\'s a correction to a previous certificate. The guidelines: If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. Keywords: "As described and As found or Original and inspection or Corrected class or Correction information". The text has "Correction Date" but not "Corrected class". However, "Reclass reweigh" might be a correction. Also "Reclass S:" appears.\n\nGiven these, I\'d lean weight_and_inspection_cert. But maybe nmfc_cert. The guidelines ambiguous. The key difference is nmfc_cert is a correction notice. This has a correction date. I\'d classify as nmfc_cert. But decide: The doc type is "weight_and_inspection_cert". The presence of "Correction Date" might be part of the certificate. But nmfc_cert is specifically for correction of weight_and_inspection_cert. The doc isn\'t showing a correction notice like "Corrected Class". I\'ll choose weight_and_inspection_cert.\n\nThus output:\n\n[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2855, 'outputTokens': 506, 'totalTokens': 3361}, 'metrics': {'latencyMs': 2286}}
2025-09-24 13:34:23,507 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/0e4c8f18_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:34:23,522 - INFO - 

YL4HZPZH7D3B03FZZ5QK.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:34:23,522 - INFO - 

✓ Saved result: output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:34:23,802 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/0e4c8f18_YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:34:23,804 - INFO - 
📊 Processing Summary:
2025-09-24 13:34:23,804 - INFO -    Total files: 9
2025-09-24 13:34:23,804 - INFO -    Successful: 9
2025-09-24 13:34:23,804 - INFO -    Failed: 0
2025-09-24 13:34:23,804 - INFO -    Duration: 21.92 seconds
2025-09-24 13:34:23,804 - INFO -    Output directory: output
2025-09-24 13:34:23,804 - INFO - 
📋 Successfully Processed Files:
2025-09-24 13:34:23,805 - INFO -    📄 B3SIRREC9IAVZOJVDQSN.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:34:23,805 - INFO -    📄 CUF54EHGMLQ57HR93DRB.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:34:23,805 - INFO -    📄 FYQQGIW8Z9DSAPCL0S9G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:34:23,805 - INFO -    📄 IZTBXFPGXBFH3DV900G4.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:34:23,805 - INFO -    📄 MR6ONA8GK6HN1LCZHEX3.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:34:23,805 - INFO -    📄 PB67IAPSJB1DZWMDIE1H.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}
2025-09-24 13:34:23,805 - INFO -    📄 PEE2ZFMV7X0A0FL35G4G.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:34:23,805 - INFO -    📄 U7BB1XSF3ASMIAE1MQ5I.pdf: {"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}
2025-09-24 13:34:23,806 - INFO -    📄 YL4HZPZH7D3B03FZZ5QK.pdf: {"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}
2025-09-24 13:34:23,806 - INFO - 
============================================================================================================================================
2025-09-24 13:34:23,806 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 13:34:23,806 - INFO - ============================================================================================================================================
2025-09-24 13:34:23,807 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 13:34:23,807 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:34:23,807 - INFO - B3SIRREC9IAVZOJVDQSN.pdf                           1      weight_and_inspect... run1_B3SIRREC9IAVZOJVDQSN.json                    
2025-09-24 13:34:23,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf
2025-09-24 13:34:23,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:34:23,807 - INFO - 
2025-09-24 13:34:23,807 - INFO - CUF54EHGMLQ57HR93DRB.pdf                           1      weight_and_inspect... run1_CUF54EHGMLQ57HR93DRB.json                    
2025-09-24 13:34:23,807 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf
2025-09-24 13:34:23,807 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:34:23,808 - INFO - 
2025-09-24 13:34:23,808 - INFO - FYQQGIW8Z9DSAPCL0S9G.pdf                           1      nmfc_cert            run1_FYQQGIW8Z9DSAPCL0S9G.json                    
2025-09-24 13:34:23,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf
2025-09-24 13:34:23,808 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:34:23,808 - INFO - 
2025-09-24 13:34:23,808 - INFO - IZTBXFPGXBFH3DV900G4.pdf                           1      nmfc_cert            run1_IZTBXFPGXBFH3DV900G4.json                    
2025-09-24 13:34:23,808 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf
2025-09-24 13:34:23,808 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:34:23,808 - INFO - 
2025-09-24 13:34:23,808 - INFO - MR6ONA8GK6HN1LCZHEX3.pdf                           1      nmfc_cert            run1_MR6ONA8GK6HN1LCZHEX3.json                    
2025-09-24 13:34:23,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf
2025-09-24 13:34:23,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:34:23,809 - INFO - 
2025-09-24 13:34:23,809 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           1      nmfc_cert            run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:34:23,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:34:23,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:34:23,809 - INFO - 
2025-09-24 13:34:23,809 - INFO - PB67IAPSJB1DZWMDIE1H.pdf                           2      nmfc_cert            run1_PB67IAPSJB1DZWMDIE1H.json                    
2025-09-24 13:34:23,809 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf
2025-09-24 13:34:23,809 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:34:23,810 - INFO - 
2025-09-24 13:34:23,810 - INFO - PEE2ZFMV7X0A0FL35G4G.pdf                           1      nmfc_cert            run1_PEE2ZFMV7X0A0FL35G4G.json                    
2025-09-24 13:34:23,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf
2025-09-24 13:34:23,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:34:23,810 - INFO - 
2025-09-24 13:34:23,810 - INFO - U7BB1XSF3ASMIAE1MQ5I.pdf                           1      nmfc_cert            run1_U7BB1XSF3ASMIAE1MQ5I.json                    
2025-09-24 13:34:23,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf
2025-09-24 13:34:23,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:34:23,810 - INFO - 
2025-09-24 13:34:23,810 - INFO - YL4HZPZH7D3B03FZZ5QK.pdf                           1      weight_and_inspect... run1_YL4HZPZH7D3B03FZZ5QK.json                    
2025-09-24 13:34:23,810 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf
2025-09-24 13:34:23,810 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:34:23,810 - INFO - 
2025-09-24 13:34:23,810 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 13:34:23,810 - INFO - Total entries: 10
2025-09-24 13:34:23,810 - INFO - ============================================================================================================================================
2025-09-24 13:34:23,810 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 13:34:23,810 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:34:23,810 - INFO -   1. B3SIRREC9IAVZOJVDQSN.pdf            Page 1   → weight_and_inspection_cert | run1_B3SIRREC9IAVZOJVDQSN.json
2025-09-24 13:34:23,810 - INFO -   2. CUF54EHGMLQ57HR93DRB.pdf            Page 1   → weight_and_inspection_cert | run1_CUF54EHGMLQ57HR93DRB.json
2025-09-24 13:34:23,811 - INFO -   3. FYQQGIW8Z9DSAPCL0S9G.pdf            Page 1   → nmfc_cert       | run1_FYQQGIW8Z9DSAPCL0S9G.json
2025-09-24 13:34:23,811 - INFO -   4. IZTBXFPGXBFH3DV900G4.pdf            Page 1   → nmfc_cert       | run1_IZTBXFPGXBFH3DV900G4.json
2025-09-24 13:34:23,811 - INFO -   5. MR6ONA8GK6HN1LCZHEX3.pdf            Page 1   → nmfc_cert       | run1_MR6ONA8GK6HN1LCZHEX3.json
2025-09-24 13:34:23,811 - INFO -   6. PB67IAPSJB1DZWMDIE1H.pdf            Page 1   → nmfc_cert       | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:34:23,811 - INFO -   7. PB67IAPSJB1DZWMDIE1H.pdf            Page 2   → nmfc_cert       | run1_PB67IAPSJB1DZWMDIE1H.json
2025-09-24 13:34:23,811 - INFO -   8. PEE2ZFMV7X0A0FL35G4G.pdf            Page 1   → nmfc_cert       | run1_PEE2ZFMV7X0A0FL35G4G.json
2025-09-24 13:34:23,811 - INFO -   9. U7BB1XSF3ASMIAE1MQ5I.pdf            Page 1   → nmfc_cert       | run1_U7BB1XSF3ASMIAE1MQ5I.json
2025-09-24 13:34:23,811 - INFO -  10. YL4HZPZH7D3B03FZZ5QK.pdf            Page 1   → weight_and_inspection_cert | run1_YL4HZPZH7D3B03FZZ5QK.json
2025-09-24 13:34:23,811 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 13:34:23,811 - INFO - 
✅ Test completed: {'total_files': 9, 'processed': 9, 'failed': 0, 'errors': [], 'duration_seconds': 21.917837, 'processed_files': [{'filename': 'B3SIRREC9IAVZOJVDQSN.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/B3SIRREC9IAVZOJVDQSN.pdf'}, {'filename': 'CUF54EHGMLQ57HR93DRB.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/CUF54EHGMLQ57HR93DRB.pdf'}, {'filename': 'FYQQGIW8Z9DSAPCL0S9G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/FYQQGIW8Z9DSAPCL0S9G.pdf'}, {'filename': 'IZTBXFPGXBFH3DV900G4.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/IZTBXFPGXBFH3DV900G4.pdf'}, {'filename': 'MR6ONA8GK6HN1LCZHEX3.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/MR6ONA8GK6HN1LCZHEX3.pdf'}, {'filename': 'PB67IAPSJB1DZWMDIE1H.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}, {'page_no': 2, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PB67IAPSJB1DZWMDIE1H.pdf'}, {'filename': 'PEE2ZFMV7X0A0FL35G4G.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/PEE2ZFMV7X0A0FL35G4G.pdf'}, {'filename': 'U7BB1XSF3ASMIAE1MQ5I.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'nmfc_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/U7BB1XSF3ASMIAE1MQ5I.pdf'}, {'filename': 'YL4HZPZH7D3B03FZZ5QK.pdf', 'classification': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}, 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs/nmfc_cert/YL4HZPZH7D3B03FZZ5QK.pdf'}]}
