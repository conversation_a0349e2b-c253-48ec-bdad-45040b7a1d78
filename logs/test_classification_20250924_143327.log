2025-09-24 14:33:27,756 - INFO - Logging initialized. Log file: logs/test_classification_20250924_143327.log
2025-09-24 14:33:27,756 - INFO - 📁 Found 8 files to process
2025-09-24 14:33:27,756 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 14:33:27,756 - INFO - 🚀 Processing 8 files in FORCED PARALLEL MODE...
2025-09-24 14:33:27,756 - INFO - 🚀 Creating 8 parallel tasks...
2025-09-24 14:33:27,756 - INFO - 🚀 All 8 tasks created - executing in parallel...
2025-09-24 14:33:27,756 - INFO - ⬆️ [14:33:27] Uploading: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:33:29,202 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/BQJUG5URFR2GH9ECWFV4.pdf -> s3://document-extraction-logistically/temp/01ba901e_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:33:29,202 - INFO - 🔍 [14:33:29] Starting classification: BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:33:29,203 - INFO - ⬆️ [14:33:29] Uploading: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:33:29,205 - INFO - Initializing TextractProcessor...
2025-09-24 14:33:29,234 - INFO - Initializing BedrockProcessor...
2025-09-24 14:33:29,239 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/01ba901e_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:33:29,240 - INFO - Processing PDF from S3...
2025-09-24 14:33:29,240 - INFO - Downloading PDF from S3 to /tmp/tmpv0wo4ik5/01ba901e_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:33:30,494 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 14:33:30,494 - INFO - Splitting PDF into individual pages...
2025-09-24 14:33:30,495 - INFO - Splitting PDF 01ba901e_BQJUG5URFR2GH9ECWFV4 into 1 pages
2025-09-24 14:33:30,497 - INFO - Split PDF into 1 pages
2025-09-24 14:33:30,497 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:33:30,497 - INFO - Expected pages: [1]
2025-09-24 14:33:33,824 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/DTA5S55B66Z5U1H1GDK5.jpeg -> s3://document-extraction-logistically/temp/40dc2c04_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:33:33,825 - INFO - 🔍 [14:33:33] Starting classification: DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:33:33,825 - INFO - Initializing TextractProcessor...
2025-09-24 14:33:33,828 - INFO - ⬆️ [14:33:33] Uploading: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:33:33,840 - INFO - Initializing BedrockProcessor...
2025-09-24 14:33:33,845 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/40dc2c04_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:33:33,845 - INFO - Processing image from S3...
2025-09-24 14:33:34,787 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/KE7TCH9TPQZFVA5CZ3HT.pdf -> s3://document-extraction-logistically/temp/9716666d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:33:34,787 - INFO - 🔍 [14:33:34] Starting classification: KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:33:34,788 - INFO - ⬆️ [14:33:34] Uploading: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:33:34,788 - INFO - Initializing TextractProcessor...
2025-09-24 14:33:34,795 - INFO - Initializing BedrockProcessor...
2025-09-24 14:33:34,797 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/9716666d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:33:34,797 - INFO - Processing PDF from S3...
2025-09-24 14:33:34,797 - INFO - Downloading PDF from S3 to /tmp/tmppv7720ur/9716666d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:33:35,506 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf -> s3://document-extraction-logistically/temp/ba1719ef_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:33:35,506 - INFO - 🔍 [14:33:35] Starting classification: O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:33:35,507 - INFO - ⬆️ [14:33:35] Uploading: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:33:35,508 - INFO - Initializing TextractProcessor...
2025-09-24 14:33:35,529 - INFO - Initializing BedrockProcessor...
2025-09-24 14:33:35,534 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/ba1719ef_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:33:35,535 - INFO - Processing PDF from S3...
2025-09-24 14:33:35,535 - INFO - Downloading PDF from S3 to /tmp/tmpl3i2r4o7/ba1719ef_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:33:35,876 - INFO - Page 1: Extracted 1260 characters, 84 lines from 01ba901e_BQJUG5URFR2GH9ECWFV4_170802d9_page_001.pdf
2025-09-24 14:33:35,876 - INFO - Successfully processed page 1
2025-09-24 14:33:35,876 - INFO - Combined 1 pages into final text
2025-09-24 14:33:35,876 - INFO - Text validation for 01ba901e_BQJUG5URFR2GH9ECWFV4: 1277 characters, 1 pages
2025-09-24 14:33:35,876 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:33:35,877 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:33:36,839 - INFO - Downloaded PDF size: 0.2 MB
2025-09-24 14:33:36,839 - INFO - Splitting PDF into individual pages...
2025-09-24 14:33:36,840 - INFO - Splitting PDF 9716666d_KE7TCH9TPQZFVA5CZ3HT into 1 pages
2025-09-24 14:33:36,846 - INFO - Split PDF into 1 pages
2025-09-24 14:33:36,846 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:33:36,846 - INFO - Expected pages: [1]
2025-09-24 14:33:37,302 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 14:33:37,302 - INFO - Splitting PDF into individual pages...
2025-09-24 14:33:37,305 - INFO - Splitting PDF ba1719ef_O2IU5G77LYNTYE0RP1TI into 7 pages
2025-09-24 14:33:37,319 - INFO - Split PDF into 7 pages
2025-09-24 14:33:37,319 - INFO - Processing pages with Textract in parallel...
2025-09-24 14:33:37,319 - INFO - Expected pages: [1, 2, 3, 4, 5, 6, 7]
2025-09-24 14:33:37,430 - INFO - S3 Image temp/40dc2c04_DTA5S55B66Z5U1H1GDK5.jpeg: Extracted 359 characters, 37 lines
2025-09-24 14:33:37,430 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:33:37,430 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:33:38,053 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f3455ba1-0d25-454a-8e25-c4e93a3ac9c8', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:03:37 GMT', 'content-type': 'application/json', 'content-length': '816', 'connection': 'keep-alive', 'x-amzn-requestid': 'f3455ba1-0d25-454a-8e25-c4e93a3ac9c8'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page provided. This page shows ELD Records, which is Electronic Logging Device records. That likely falls under "log". The doc type "log" covers driver logs, activity logs, etc. So classify as log. Check keywords: "ELD Records", "Driver", "Time", "Location", "Event / Status". Yes. So doc_type log.\n\nReturn JSON array? The tool expects a single call with documents array. We must produce an array with one object: page_no 1, doc_type log. Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2928, 'outputTokens': 134, 'totalTokens': 3062}, 'metrics': {'latencyMs': 908}}
2025-09-24 14:33:38,054 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/01ba901e_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:33:40,114 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/PEI6APOK17E5E1C2H2TN.jpg -> s3://document-extraction-logistically/temp/4b1eb9a6_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:33:40,114 - INFO - 🔍 [14:33:40] Starting classification: PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:33:40,115 - INFO - Initializing TextractProcessor...
2025-09-24 14:33:40,115 - INFO - ⬆️ [14:33:40] Uploading: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:33:40,129 - INFO - Initializing BedrockProcessor...
2025-09-24 14:33:40,134 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4b1eb9a6_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:33:40,134 - INFO - Processing image from S3...
2025-09-24 14:33:40,317 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '6c37f363-0468-4497-8672-b843b006d399', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:03:40 GMT', 'content-type': 'application/json', 'content-length': '1853', 'connection': 'keep-alive', 'x-amzn-requestid': '6c37f363-0468-4497-8672-b843b006d399'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page content. Need to classify. Let\'s parse. The page seems like some kind of record, maybe a log or a "gate" document? It has "Truck Ready Time", "Truck Departure", "DEPARTURE TIME: 5am". "CUSTOMER WORK ORDER #" etc. There\'s a list of items like "2 1sm Box Automation & Modular X X ***********", "3 Williams Gunsight X X ***********". It looks like a gate log of trucks loading at a facility, maybe a "log" (driver log, activity record). Or could be "ingate" document. But "ingate" is for record of vehicle entering a facility. This has departure times and truck ready times, but also "Truck Ready Time:" and "Truck Departure:" but not explicitly "Ingate". Could be a "log" (activity record). The presence of date/time, truck departure, items loaded suggests a "log". So likely "log". Let\'s check the categories: log has keywords like Driver Log, Logbook, Hours of Service, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock. This page includes "Truck Ready Time", "Truck Departure", "Dock Time", "CUSTOMER WORK ORDER #". So yes, log. Also "DATE: 16-Jun Metal Finishing" maybe a header. There\'s no "Proof of Delivery" or "Bill of Lading". So I\'m leaning to "log". It\'s not a purchase order because it\'s not about buying goods. It\'s not a "po" or "so_confirmation". It\'s not a "combined_carrier_documents". So "log". There\'s only one page. So we produce JSON array with one object: page_no: 1, doc_type: "log". Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2598, 'outputTokens': 392, 'totalTokens': 2990}, 'metrics': {'latencyMs': 1713}}
2025-09-24 14:33:40,318 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/40dc2c04_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:33:41,570 - INFO - Page 1: Extracted 1731 characters, 110 lines from ba1719ef_O2IU5G77LYNTYE0RP1TI_3203fa61_page_001.pdf
2025-09-24 14:33:41,570 - INFO - Successfully processed page 1
2025-09-24 14:33:41,622 - INFO - Page 1: Extracted 519 characters, 34 lines from 9716666d_KE7TCH9TPQZFVA5CZ3HT_fe0f79ec_page_001.pdf
2025-09-24 14:33:41,622 - INFO - Successfully processed page 1
2025-09-24 14:33:41,623 - INFO - Combined 1 pages into final text
2025-09-24 14:33:41,623 - INFO - Text validation for 9716666d_KE7TCH9TPQZFVA5CZ3HT: 536 characters, 1 pages
2025-09-24 14:33:41,624 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:33:41,624 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:33:42,103 - INFO - Page 4: Extracted 2242 characters, 148 lines from ba1719ef_O2IU5G77LYNTYE0RP1TI_3203fa61_page_004.pdf
2025-09-24 14:33:42,111 - INFO - Page 5: Extracted 2059 characters, 131 lines from ba1719ef_O2IU5G77LYNTYE0RP1TI_3203fa61_page_005.pdf
2025-09-24 14:33:42,111 - INFO - Successfully processed page 4
2025-09-24 14:33:42,111 - INFO - Successfully processed page 5
2025-09-24 14:33:42,275 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/QRYUPA9PAG4E9QPW5OT2.jpeg -> s3://document-extraction-logistically/temp/968e6d16_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:33:42,275 - INFO - 🔍 [14:33:42] Starting classification: QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:33:42,275 - INFO - ⬆️ [14:33:42] Uploading: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:33:42,276 - INFO - Initializing TextractProcessor...
2025-09-24 14:33:42,286 - INFO - Initializing BedrockProcessor...
2025-09-24 14:33:42,288 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/968e6d16_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:33:42,288 - INFO - Processing image from S3...
2025-09-24 14:33:42,409 - INFO - Page 6: Extracted 1973 characters, 129 lines from ba1719ef_O2IU5G77LYNTYE0RP1TI_3203fa61_page_006.pdf
2025-09-24 14:33:42,409 - INFO - Successfully processed page 6
2025-09-24 14:33:42,497 - INFO - Page 3: Extracted 2265 characters, 147 lines from ba1719ef_O2IU5G77LYNTYE0RP1TI_3203fa61_page_003.pdf
2025-09-24 14:33:42,497 - INFO - Successfully processed page 3
2025-09-24 14:33:42,514 - INFO - Page 2: Extracted 1821 characters, 105 lines from ba1719ef_O2IU5G77LYNTYE0RP1TI_3203fa61_page_002.pdf
2025-09-24 14:33:42,514 - INFO - Successfully processed page 2
2025-09-24 14:33:42,905 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/RCXF8W06HPYJQHAQ0N3S.jpg -> s3://document-extraction-logistically/temp/6b998510_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:33:42,905 - INFO - 🔍 [14:33:42] Starting classification: RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:33:42,906 - INFO - ⬆️ [14:33:42] Uploading: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:33:42,907 - INFO - Initializing TextractProcessor...
2025-09-24 14:33:42,928 - INFO - Initializing BedrockProcessor...
2025-09-24 14:33:42,932 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6b998510_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:33:42,932 - INFO - Processing image from S3...
2025-09-24 14:33:43,343 - ERROR - Processing failed for s3://document-extraction-logistically/temp/9716666d_KE7TCH9TPQZFVA5CZ3HT.pdf: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 14:33:43,343 - ERROR - ❌ Classification failed for s3://document-extraction-logistically/temp/9716666d_KE7TCH9TPQZFVA5CZ3HT.pdf: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 14:33:43,345 - ERROR - ❌ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 93, in get_document_types_with_bedrock
    response = bedrock_processor.call_bedrock_converse(
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 87, in call_bedrock_converse
    return self.bedrock_client.converse(**call_params)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 602, in _api_call
    return self._make_api_call(operation_name, kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 1078, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.errorfactory.ValidationException: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}

2025-09-24 14:33:43,345 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 14:33:44,211 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/WKJ8LEUD5SSNRVRB1YYW.jpg -> s3://document-extraction-logistically/temp/7b94c8e9_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:33:44,211 - INFO - 🔍 [14:33:44] Starting classification: WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:33:44,213 - INFO - Initializing TextractProcessor...
2025-09-24 14:33:44,235 - INFO - Initializing BedrockProcessor...
2025-09-24 14:33:44,242 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7b94c8e9_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:33:44,245 - INFO - Processing image from S3...
2025-09-24 14:33:44,268 - INFO - 

BQJUG5URFR2GH9ECWFV4.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:33:44,268 - INFO - 

✓ Saved result: output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 14:33:44,618 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/01ba901e_BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:33:44,623 - INFO - 

DTA5S55B66Z5U1H1GDK5.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:33:44,623 - INFO - 

✓ Saved result: output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 14:33:44,919 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/40dc2c04_DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:33:44,920 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/KE7TCH9TPQZFVA5CZ3HT.pdf: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 14:33:44,921 - ERROR - ✗ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 93, in get_document_types_with_bedrock
    response = bedrock_processor.call_bedrock_converse(
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 87, in call_bedrock_converse
    return self.bedrock_client.converse(**call_params)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 602, in _api_call
    return self._make_api_call(operation_name, kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 1078, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.errorfactory.ValidationException: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}

2025-09-24 14:33:44,921 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 14:33:44,993 - INFO - Page 7: Extracted 417 characters, 27 lines from ba1719ef_O2IU5G77LYNTYE0RP1TI_3203fa61_page_007.pdf
2025-09-24 14:33:44,994 - INFO - Successfully processed page 7
2025-09-24 14:33:44,995 - INFO - Combined 7 pages into final text
2025-09-24 14:33:44,996 - INFO - Text validation for ba1719ef_O2IU5G77LYNTYE0RP1TI: 12639 characters, 7 pages
2025-09-24 14:33:44,996 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:33:44,996 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:33:45,217 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/9716666d_KE7TCH9TPQZFVA5CZ3HT.pdf
2025-09-24 14:33:45,555 - INFO - S3 Image temp/6b998510_RCXF8W06HPYJQHAQ0N3S.jpg: Extracted 25 characters, 5 lines
2025-09-24 14:33:45,555 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:33:45,555 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:33:45,893 - INFO - S3 Image temp/4b1eb9a6_PEI6APOK17E5E1C2H2TN.jpg: Extracted 3256 characters, 250 lines
2025-09-24 14:33:45,893 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:33:45,893 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:33:46,505 - INFO - S3 Image temp/7b94c8e9_WKJ8LEUD5SSNRVRB1YYW.jpg: Extracted 17 characters, 3 lines
2025-09-24 14:33:46,505 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:33:46,505 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:33:47,500 - INFO - S3 Image temp/968e6d16_QRYUPA9PAG4E9QPW5OT2.jpeg: Extracted 648 characters, 56 lines
2025-09-24 14:33:47,500 - INFO - Analyzing document types with Bedrock...
2025-09-24 14:33:47,500 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 14:33:47,508 - ERROR - Processing failed for s3://document-extraction-logistically/temp/ba1719ef_O2IU5G77LYNTYE0RP1TI.pdf: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type\\\", \\\"@assistant<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 14:33:47,508 - ERROR - ❌ Classification failed for s3://document-extraction-logistically/temp/ba1719ef_O2IU5G77LYNTYE0RP1TI.pdf: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type\\\", \\\"@assistant<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 14:33:47,508 - ERROR - ❌ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 93, in get_document_types_with_bedrock
    response = bedrock_processor.call_bedrock_converse(
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 87, in call_bedrock_converse
    return self.bedrock_client.converse(**call_params)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 602, in _api_call
    return self._make_api_call(operation_name, kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 1078, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.errorfactory.ValidationException: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type\\\", \\\"@assistant<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}

2025-09-24 14:33:47,508 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 14:33:47,509 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type\\\", \\\"@assistant<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 14:33:47,509 - ERROR - ✗ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 93, in get_document_types_with_bedrock
    response = bedrock_processor.call_bedrock_converse(
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 87, in call_bedrock_converse
    return self.bedrock_client.converse(**call_params)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 602, in _api_call
    return self._make_api_call(operation_name, kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 1078, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.errorfactory.ValidationException: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type\\\", \\\"@assistant<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}

2025-09-24 14:33:47,510 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 14:33:47,629 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '4851d073-5420-4ff5-ada5-81d39da31aac', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:03:47 GMT', 'content-type': 'application/json', 'content-length': '559', 'connection': 'keep-alive', 'x-amzn-requestid': '4851d073-5420-4ff5-ada5-81d39da31aac'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. It\'s a driver\'s daily log. Keywords: DRIVER\'S DAILY LOG, On-duty hours, etc. So doc_type: log. It\'s a driver log. So output: an array with one object: page_no 1, doc_type "log". Use the function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3574, 'outputTokens': 82, 'totalTokens': 3656}, 'metrics': {'latencyMs': 561}}
2025-09-24 14:33:47,629 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4b1eb9a6_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:33:47,690 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '5f130e69-e8f5-4ea8-b84c-43c84ceb2d84', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:03:47 GMT', 'content-type': 'application/json', 'content-length': '981', 'connection': 'keep-alive', 'x-amzn-requestid': '5f130e69-e8f5-4ea8-b84c-43c84ceb2d84'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have only one page. It\'s just a snippet: "HOLD 8 UCO U.U MAX 45.8 F". Likely a temperature log? This looks like a temperature log: "HOLD 8 UCO" maybe UCO stands for unsatisfactory? "MAX 45.8 F". It\'s probably a temperature reading of a container. The description says: log — Log: Activity record (driver log, tracking log) with dock time for operational/regulatory use or Temperature log/reading or Driver detention certificate. Keywords: Temperature, degree Fahrenheit, etc. This page has "45.8 F". So it\'s a temperature log. So doc_type = log. There\'s no continuity. So page 1 doc_type log. Output: array with object page_no:1 doc_type:"log". Use tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2475, 'outputTokens': 184, 'totalTokens': 2659}, 'metrics': {'latencyMs': 960}}
2025-09-24 14:33:47,690 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6b998510_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:33:47,831 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/ba1719ef_O2IU5G77LYNTYE0RP1TI.pdf
2025-09-24 14:33:47,866 - INFO - 

PEI6APOK17E5E1C2H2TN.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:33:47,866 - INFO - 

✓ Saved result: output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 14:33:48,152 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4b1eb9a6_PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:33:48,155 - INFO - 

RCXF8W06HPYJQHAQ0N3S.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:33:48,155 - INFO - 

✓ Saved result: output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 14:33:48,446 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6b998510_RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:33:49,150 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e871fbfa-67f1-4eaf-b5ee-28e7c9afb48a', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:03:49 GMT', 'content-type': 'application/json', 'content-length': '1685', 'connection': 'keep-alive', 'x-amzn-requestid': 'e871fbfa-67f1-4eaf-b5ee-28e7c9afb48a'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page: "<page1> THERMO KING +1 5h </page1>". This looks like a simple text, could be a log? Maybe a driver log or temperature log. "THERMO KING" could be a temperature log? The content: "THERMO KING +1 5h". Not clear.\n\nWe need to classify this page. Let\'s think: The text "THERMO KING" could be a brand name of a temperature measuring device? The "+1 5h" might indicate temperature reading? Or maybe it\'s a waste? It could be a log of driver hours? "5h" could be hours. "THERMO KING" could be a driver name? But no.\n\nGiven limited info, we should check categories. The page does not show any header indicating known types. It might be a log: "Driver Log" or "Temperature Log". The keywords for log: "Driver Log", "Logbook", "Hours of Service", "HOS", "Activity", "Timestamp", "driver/vehicle IDs", "mileage". Not present. Temperature log: "Temperature", "degree", "fehrenheit", "celsius", "humidity". Not present. "THERMO KING" could be a brand name for a temperature device, but no "Temperature".\n\nMaybe it\'s a "fuel receipt"? No.\n\nCould be "scale ticket"? No weight.\n\nCould be "other". The guidelines say if not any category, classify as "other". But check if it\'s continuation of previous page. There\'s only one page.\n\nThus answer: page 1: doc_type: "other". Provide tool call.\n\n'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"other"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2467, 'outputTokens': 351, 'totalTokens': 2818}, 'metrics': {'latencyMs': 1545}}
2025-09-24 14:33:49,150 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7b94c8e9_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:33:49,153 - INFO - 

WKJ8LEUD5SSNRVRB1YYW.jpg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "other"
        }
    ]
}
2025-09-24 14:33:49,153 - INFO - 

✓ Saved result: output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 14:33:49,534 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7b94c8e9_WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:33:50,179 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'bd6e410b-6c13-41c6-8109-f47e103d9963', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:03:50 GMT', 'content-type': 'application/json', 'content-length': '1688', 'connection': 'keep-alive', 'x-amzn-requestid': 'bd6e410b-6c13-41c6-8109-f47e103d9963'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. Need to classify document type. Let\'s read content. It contains date, driver, truck ready time, departure time, dock time, etc. It lists "WORK ORDER #", "**********" etc. It\'s likely a load sheet or driver log? Keywords: "Truck Ready Time", "Truck Departure", "Dock Time", "CUSTOMER UP DEL". Might be a driver log or gate log. There\'s no explicit header like "Driver Log", "Ingate", "Outgate". But there are times, truck departure, driver. Could be an "ingate" or "outgate" document? Ingate documents have "Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time". Outgate: "Outgate, Departed, Gate Out, Time Out". This page lists departure times, dock times. It might be a "log" type, activity record. The definition of log includes driver log, hours, activity. This page seems to be a load schedule or driver log with times. So classify as "log".\n\nAlso note the content includes "WORK ORDER #", "**********" etc. Could be a "Packing List" or "Pick/Delivery Schedule"? But no packing list items. It\'s more like a "driver log" or "gate log". Since it includes "Truck Ready Time", "Truck Departure", "Dock Time" and driver name, it\'s likely a "log" record. So doc_type = log.\n\nWe must return JSON with documents array containing page_no: 1, doc_type: "log".\n\nLet\'s produce tool call.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"log"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2752, 'outputTokens': 349, 'totalTokens': 3101}, 'metrics': {'latencyMs': 1525}}
2025-09-24 14:33:50,179 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/968e6d16_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:33:50,195 - INFO - 

QRYUPA9PAG4E9QPW5OT2.jpeg

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "log"
        }
    ]
}
2025-09-24 14:33:50,195 - INFO - 

✓ Saved result: output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 14:33:50,564 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/968e6d16_QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:33:50,565 - INFO - 
📊 Processing Summary:
2025-09-24 14:33:50,565 - INFO -    Total files: 8
2025-09-24 14:33:50,565 - INFO -    Successful: 6
2025-09-24 14:33:50,565 - INFO -    Failed: 2
2025-09-24 14:33:50,565 - INFO -    Duration: 22.81 seconds
2025-09-24 14:33:50,565 - INFO -    Output directory: output
2025-09-24 14:33:50,565 - INFO - 
📋 Successfully Processed Files:
2025-09-24 14:33:50,565 - INFO -    📄 BQJUG5URFR2GH9ECWFV4.pdf: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:33:50,565 - INFO -    📄 DTA5S55B66Z5U1H1GDK5.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:33:50,565 - INFO -    📄 PEI6APOK17E5E1C2H2TN.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:33:50,565 - INFO -    📄 QRYUPA9PAG4E9QPW5OT2.jpeg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:33:50,565 - INFO -    📄 RCXF8W06HPYJQHAQ0N3S.jpg: {"documents":[{"page_no":1,"doc_type":"log"}]}
2025-09-24 14:33:50,565 - INFO -    📄 WKJ8LEUD5SSNRVRB1YYW.jpg: {"documents":[{"page_no":1,"doc_type":"other"}]}
2025-09-24 14:33:50,565 - ERROR - 
❌ Errors:
2025-09-24 14:33:50,565 - ERROR -    Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/KE7TCH9TPQZFVA5CZ3HT.pdf: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 14:33:50,565 - ERROR -    Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type\\\", \\\"@assistant<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 14:33:50,566 - INFO - 
============================================================================================================================================
2025-09-24 14:33:50,566 - INFO - 📋 CLASSIFICATION RESULTS TABLE
2025-09-24 14:33:50,566 - INFO - ============================================================================================================================================
2025-09-24 14:33:50,566 - INFO - PDF FILE LINK                                      PAGE   CATEGORY             JSON OUTPUT LINK                                  
2025-09-24 14:33:50,566 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:33:50,566 - INFO - BQJUG5URFR2GH9ECWFV4.pdf                           1      log                  run1_BQJUG5URFR2GH9ECWFV4.json                    
2025-09-24 14:33:50,566 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/BQJUG5URFR2GH9ECWFV4.pdf
2025-09-24 14:33:50,566 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 14:33:50,566 - INFO - 
2025-09-24 14:33:50,566 - INFO - DTA5S55B66Z5U1H1GDK5.jpeg                          1      log                  run1_DTA5S55B66Z5U1H1GDK5.json                    
2025-09-24 14:33:50,566 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/DTA5S55B66Z5U1H1GDK5.jpeg
2025-09-24 14:33:50,566 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 14:33:50,566 - INFO - 
2025-09-24 14:33:50,566 - INFO - PEI6APOK17E5E1C2H2TN.jpg                           1      log                  run1_PEI6APOK17E5E1C2H2TN.json                    
2025-09-24 14:33:50,566 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/PEI6APOK17E5E1C2H2TN.jpg
2025-09-24 14:33:50,566 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 14:33:50,566 - INFO - 
2025-09-24 14:33:50,567 - INFO - QRYUPA9PAG4E9QPW5OT2.jpeg                          1      log                  run1_QRYUPA9PAG4E9QPW5OT2.json                    
2025-09-24 14:33:50,567 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/QRYUPA9PAG4E9QPW5OT2.jpeg
2025-09-24 14:33:50,567 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 14:33:50,567 - INFO - 
2025-09-24 14:33:50,567 - INFO - RCXF8W06HPYJQHAQ0N3S.jpg                           1      log                  run1_RCXF8W06HPYJQHAQ0N3S.json                    
2025-09-24 14:33:50,567 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/RCXF8W06HPYJQHAQ0N3S.jpg
2025-09-24 14:33:50,567 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 14:33:50,567 - INFO - 
2025-09-24 14:33:50,567 - INFO - WKJ8LEUD5SSNRVRB1YYW.jpg                           1      other                run1_WKJ8LEUD5SSNRVRB1YYW.json                    
2025-09-24 14:33:50,567 - INFO -   PDF → file:///home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/WKJ8LEUD5SSNRVRB1YYW.jpg
2025-09-24 14:33:50,567 - INFO -   JSON→ file:///home/<USER>/Documents/repositories/test_logistically/output/run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 14:33:50,567 - INFO - 
2025-09-24 14:33:50,567 - INFO - --------------------------------------------------------------------------------------------------------------------------------------------
2025-09-24 14:33:50,567 - INFO - Total entries: 6
2025-09-24 14:33:50,567 - INFO - ============================================================================================================================================
2025-09-24 14:33:50,567 - INFO - 
📋 COMPACT TABLE VIEW:
2025-09-24 14:33:50,567 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:33:50,567 - INFO -   1. BQJUG5URFR2GH9ECWFV4.pdf            Page 1   → log             | run1_BQJUG5URFR2GH9ECWFV4.json
2025-09-24 14:33:50,567 - INFO -   2. DTA5S55B66Z5U1H1GDK5.jpeg           Page 1   → log             | run1_DTA5S55B66Z5U1H1GDK5.json
2025-09-24 14:33:50,567 - INFO -   3. PEI6APOK17E5E1C2H2TN.jpg            Page 1   → log             | run1_PEI6APOK17E5E1C2H2TN.json
2025-09-24 14:33:50,567 - INFO -   4. QRYUPA9PAG4E9QPW5OT2.jpeg           Page 1   → log             | run1_QRYUPA9PAG4E9QPW5OT2.json
2025-09-24 14:33:50,567 - INFO -   5. RCXF8W06HPYJQHAQ0N3S.jpg            Page 1   → log             | run1_RCXF8W06HPYJQHAQ0N3S.json
2025-09-24 14:33:50,567 - INFO -   6. WKJ8LEUD5SSNRVRB1YYW.jpg            Page 1   → other           | run1_WKJ8LEUD5SSNRVRB1YYW.json
2025-09-24 14:33:50,567 - INFO - ----------------------------------------------------------------------------------------------------
2025-09-24 14:33:50,568 - ERROR - 🐛 Error occurred during processing - entering debugger...
2025-09-24 14:33:50,568 - ERROR - 🐛 Debug info: {'exception': ValidationException('An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \\"BadRequestError\\", code: Some(400), message: \\"unexpected tokens remaining in message header: [\\\\\\"<|constrain|>functions.classify_logistics_doc_type\\\\\\", \\\\\\"@assistant<|channel|>analysis\\\\\\"]\\", param: None } }","type":"invalid_request_error"}}'), 'traceback': 'Traceback (most recent call last):\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file\n    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)\n  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run\n    result = self.fn(*self.args, **self.kwargs)\n  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync\n    result = loop.run_until_complete(llm_classification(s3_uri))\n  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete\n    return future.result()\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main\n    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 93, in get_document_types_with_bedrock\n    response = bedrock_processor.call_bedrock_converse(\n  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 87, in call_bedrock_converse\n    return self.bedrock_client.converse(**call_params)\n  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 602, in _api_call\n    return self._make_api_call(operation_name, kwargs)\n  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/context.py", line 123, in wrapper\n    return func(*args, **kwargs)\n  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 1078, in _make_api_call\n    raise error_class(parsed_response, operation_name)\nbotocore.errorfactory.ValidationException: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \\"BadRequestError\\", code: Some(400), message: \\"unexpected tokens remaining in message header: [\\\\\\"<|constrain|>functions.classify_logistics_doc_type\\\\\\", \\\\\\"@assistant<|channel|>analysis\\\\\\"]\\", param: None } }","type":"invalid_request_error"}}\n', 'file_path': '/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/log/O2IU5G77LYNTYE0RP1TI.pdf', 's3_uri': 's3://document-extraction-logistically/temp/ba1719ef_O2IU5G77LYNTYE0RP1TI.pdf', 'location': 'process_single_file'}
2025-09-24 14:33:50,568 - ERROR - 🐛 Location: process_single_file
2025-09-24 14:33:50,568 - ERROR - 🐛 Exception: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type\\\", \\\"@assistant<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}
2025-09-24 14:33:50,568 - ERROR - 🐛 Traceback:
Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 93, in get_document_types_with_bedrock
    response = bedrock_processor.call_bedrock_converse(
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 87, in call_bedrock_converse
    return self.bedrock_client.converse(**call_params)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 602, in _api_call
    return self._make_api_call(operation_name, kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/context.py", line 123, in wrapper
    return func(*args, **kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/venv/lib/python3.10/site-packages/botocore/client.py", line 1078, in _make_api_call
    raise error_class(parsed_response, operation_name)
botocore.errorfactory.ValidationException: An error occurred (ValidationException) when calling the Converse operation: The model returned the following errors: {"error":{"message":"ErrorEvent { error: APIError { type: \"BadRequestError\", code: Some(400), message: \"unexpected tokens remaining in message header: [\\\"<|constrain|>functions.classify_logistics_doc_type\\\", \\\"@assistant<|channel|>analysis\\\"]\", param: None } }","type":"invalid_request_error"}}

