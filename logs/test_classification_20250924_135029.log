2025-09-24 13:50:29,902 - INFO - Logging initialized. Log file: logs/test_classification_20250924_135029.log
2025-09-24 13:50:29,902 - INFO - 📁 Found 13 files to process
2025-09-24 13:50:29,903 - INFO - 🚀 Starting processing with run number: 1
2025-09-24 13:50:29,903 - INFO - 🚀 Processing 13 files in FORCED PARALLEL MODE...
2025-09-24 13:50:29,903 - INFO - 🚀 Creating 13 parallel tasks...
2025-09-24 13:50:29,903 - INFO - 🚀 All 13 tasks created - executing in parallel...
2025-09-24 13:50:29,903 - INFO - ⬆️ [13:50:29] Uploading: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:50:31,798 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/I_QHD3LC0DU6S8O2YVVS60.pdf -> s3://document-extraction-logistically/temp/6040339b_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:50:31,798 - INFO - 🔍 [13:50:31] Starting classification: I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:50:31,799 - INFO - ⬆️ [13:50:31] Uploading: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:50:31,800 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:31,826 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:31,832 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/6040339b_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:50:31,833 - INFO - Processing PDF from S3...
2025-09-24 13:50:31,833 - INFO - Downloading PDF from S3 to /tmp/tmpzkbbfl66/6040339b_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:50:32,669 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_DH0JZ2JWDGRHD26BX74C.pdf -> s3://document-extraction-logistically/temp/e0d63d92_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:50:32,669 - INFO - 🔍 [13:50:32] Starting classification: NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:50:32,670 - INFO - ⬆️ [13:50:32] Uploading: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:50:32,670 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:32,685 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:32,690 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e0d63d92_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:50:32,691 - INFO - Processing PDF from S3...
2025-09-24 13:50:32,691 - INFO - Downloading PDF from S3 to /tmp/tmp1a2sh79u/e0d63d92_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:50:33,142 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:50:33,143 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:33,147 - INFO - Splitting PDF 6040339b_I_QHD3LC0DU6S8O2YVVS60 into 1 pages
2025-09-24 13:50:33,149 - INFO - Split PDF into 1 pages
2025-09-24 13:50:33,149 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:33,149 - INFO - Expected pages: [1]
2025-09-24 13:50:33,626 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf -> s3://document-extraction-logistically/temp/e3d7a014_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:50:33,626 - INFO - 🔍 [13:50:33] Starting classification: NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:50:33,627 - INFO - ⬆️ [13:50:33] Uploading: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:50:33,630 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:33,635 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:33,637 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/e3d7a014_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:50:33,637 - INFO - Processing PDF from S3...
2025-09-24 13:50:33,637 - INFO - Downloading PDF from S3 to /tmp/tmp762o7ja8/e3d7a014_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:50:34,256 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:50:34,256 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:34,257 - INFO - Splitting PDF e0d63d92_NMFC_DH0JZ2JWDGRHD26BX74C into 2 pages
2025-09-24 13:50:34,260 - INFO - Split PDF into 2 pages
2025-09-24 13:50:34,260 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:34,260 - INFO - Expected pages: [1, 2]
2025-09-24 13:50:34,850 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_OR9EL08KIKNQPZ3UV3HH.pdf -> s3://document-extraction-logistically/temp/4332abd1_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:50:34,851 - INFO - 🔍 [13:50:34] Starting classification: NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:50:34,852 - INFO - ⬆️ [13:50:34] Uploading: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:50:34,853 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:34,869 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:34,873 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4332abd1_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:50:34,873 - INFO - Processing PDF from S3...
2025-09-24 13:50:34,873 - INFO - Downloading PDF from S3 to /tmp/tmpopjouz3n/4332abd1_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:50:35,431 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_R1V0MO844PBLWNEAUETU.pdf -> s3://document-extraction-logistically/temp/a0e05db6_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:50:35,432 - INFO - 🔍 [13:50:35] Starting classification: NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:50:35,433 - INFO - ⬆️ [13:50:35] Uploading: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:50:35,435 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:35,450 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:35,455 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/a0e05db6_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:50:35,455 - INFO - Processing PDF from S3...
2025-09-24 13:50:35,455 - INFO - Downloading PDF from S3 to /tmp/tmpthuig49z/a0e05db6_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:50:35,710 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:50:35,710 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:35,712 - INFO - Splitting PDF e3d7a014_NMFC_NJ4WSZ8BUQAW48V6403N into 3 pages
2025-09-24 13:50:35,715 - INFO - Split PDF into 3 pages
2025-09-24 13:50:35,716 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:35,716 - INFO - Expected pages: [1, 2, 3]
2025-09-24 13:50:36,043 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_RUDVGETVRZO7XX6YNW7I.pdf -> s3://document-extraction-logistically/temp/14a4779b_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:50:36,043 - INFO - 🔍 [13:50:36] Starting classification: NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:50:36,044 - INFO - ⬆️ [13:50:36] Uploading: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:50:36,046 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:36,062 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:36,067 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/14a4779b_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:50:36,067 - INFO - Processing PDF from S3...
2025-09-24 13:50:36,067 - INFO - Downloading PDF from S3 to /tmp/tmpf0ptykq8/14a4779b_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:50:36,705 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:50:36,705 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:36,708 - INFO - Splitting PDF a0e05db6_NMFC_R1V0MO844PBLWNEAUETU into 2 pages
2025-09-24 13:50:36,714 - INFO - Split PDF into 2 pages
2025-09-24 13:50:36,714 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:36,714 - INFO - Expected pages: [1, 2]
2025-09-24 13:50:36,738 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_A34CDFDJ66EDOZEKZWJL.pdf -> s3://document-extraction-logistically/temp/40a93b8e_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:50:36,738 - INFO - 🔍 [13:50:36] Starting classification: W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:50:36,739 - INFO - ⬆️ [13:50:36] Uploading: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:50:36,739 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:36,806 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:36,810 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/40a93b8e_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:50:36,810 - INFO - Processing PDF from S3...
2025-09-24 13:50:36,812 - INFO - Downloading PDF from S3 to /tmp/tmp01my2gff/40a93b8e_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:50:36,842 - INFO - Page 1: Extracted 589 characters, 36 lines from 6040339b_I_QHD3LC0DU6S8O2YVVS60_dfb19e42_page_001.pdf
2025-09-24 13:50:36,843 - INFO - Successfully processed page 1
2025-09-24 13:50:36,843 - INFO - Combined 1 pages into final text
2025-09-24 13:50:36,843 - INFO - Text validation for 6040339b_I_QHD3LC0DU6S8O2YVVS60: 606 characters, 1 pages
2025-09-24 13:50:36,843 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:36,844 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:37,331 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DCY7SLNMWUXIENOREHQF.pdf -> s3://document-extraction-logistically/temp/7aa950b2_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:50:37,331 - INFO - 🔍 [13:50:37] Starting classification: W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:50:37,332 - INFO - ⬆️ [13:50:37] Uploading: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:50:37,334 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:37,347 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:37,350 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/7aa950b2_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:50:37,350 - INFO - Processing PDF from S3...
2025-09-24 13:50:37,350 - INFO - Downloading PDF from S3 to /tmp/tmppuiawrps/7aa950b2_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:50:37,429 - INFO - Downloaded PDF size: 0.7 MB
2025-09-24 13:50:37,429 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:37,432 - WARNING - Multiple definitions in dictionary at byte 0x9e9f6 for key /OpenAction
2025-09-24 13:50:37,432 - INFO - Splitting PDF 4332abd1_NMFC_OR9EL08KIKNQPZ3UV3HH into 2 pages
2025-09-24 13:50:37,465 - INFO - Split PDF into 2 pages
2025-09-24 13:50:37,465 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:37,465 - INFO - Expected pages: [1, 2]
2025-09-24 13:50:37,907 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_DFY1VDZWR7NBDLJV02G2.pdf -> s3://document-extraction-logistically/temp/333c8840_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:50:37,908 - INFO - 🔍 [13:50:37] Starting classification: W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:50:37,909 - INFO - ⬆️ [13:50:37] Uploading: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:50:37,911 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:37,938 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:37,946 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/333c8840_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:50:37,946 - INFO - Processing PDF from S3...
2025-09-24 13:50:37,947 - INFO - Downloading PDF from S3 to /tmp/tmp8k0mgijo/333c8840_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:50:38,138 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:50:38,138 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:38,140 - INFO - Splitting PDF 14a4779b_NMFC_RUDVGETVRZO7XX6YNW7I into 1 pages
2025-09-24 13:50:38,141 - INFO - Split PDF into 1 pages
2025-09-24 13:50:38,141 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:38,141 - INFO - Expected pages: [1]
2025-09-24 13:50:38,351 - INFO - Page 1: Extracted 854 characters, 69 lines from e0d63d92_NMFC_DH0JZ2JWDGRHD26BX74C_40a57d29_page_001.pdf
2025-09-24 13:50:38,351 - INFO - Successfully processed page 1
2025-09-24 13:50:38,504 - INFO - Page 2: Extracted 764 characters, 54 lines from e0d63d92_NMFC_DH0JZ2JWDGRHD26BX74C_40a57d29_page_002.pdf
2025-09-24 13:50:38,504 - INFO - Successfully processed page 2
2025-09-24 13:50:38,504 - INFO - Combined 2 pages into final text
2025-09-24 13:50:38,505 - INFO - Text validation for e0d63d92_NMFC_DH0JZ2JWDGRHD26BX74C: 1654 characters, 2 pages
2025-09-24 13:50:38,505 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:38,505 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:38,512 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_HFPAXYL947DH59AB12FL.pdf -> s3://document-extraction-logistically/temp/f5e8dd55_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:50:38,512 - INFO - 🔍 [13:50:38] Starting classification: W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:50:38,513 - INFO - ⬆️ [13:50:38] Uploading: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:50:38,514 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:38,523 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:38,524 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/f5e8dd55_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:50:38,524 - INFO - Processing PDF from S3...
2025-09-24 13:50:38,524 - INFO - Downloading PDF from S3 to /tmp/tmp602p39r6/f5e8dd55_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:50:38,906 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:50:38,906 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:38,907 - INFO - Splitting PDF 7aa950b2_W_DCY7SLNMWUXIENOREHQF into 1 pages
2025-09-24 13:50:38,908 - INFO - Split PDF into 1 pages
2025-09-24 13:50:38,908 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:38,908 - INFO - Expected pages: [1]
2025-09-24 13:50:39,139 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_K9VSARJOKAIZHNJ5RBDT.pdf -> s3://document-extraction-logistically/temp/d610f135_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:50:39,139 - INFO - 🔍 [13:50:39] Starting classification: W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:50:39,140 - INFO - ⬆️ [13:50:39] Uploading: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:50:39,140 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:39,153 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:39,157 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/d610f135_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:50:39,157 - INFO - Processing PDF from S3...
2025-09-24 13:50:39,158 - INFO - Downloading PDF from S3 to /tmp/tmpjkk39306/d610f135_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:50:39,343 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'e3523d22-545d-4763-9b75-e2fd276683ce', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:39 GMT', 'content-type': 'application/json', 'content-length': '1492', 'connection': 'keep-alive', 'x-amzn-requestid': 'e3523d22-545d-4763-9b75-e2fd276683ce'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need classify each page. Page1 has "CUBE MEASUREMENT CERTIFICATE" header. That likely is nmfc_cert? Actually nmfc_cert is NMFC classification certificate or correction notice or weight & inspection certificate. But this is a cube measurement certificate. The description says "Cube Measurement Certificate". Also earlier in enum, there is "inspection_cert" and "weight_and_inspection_cert" but also "nmfc_cert". The nmfc_cert description: NMFC Classification Certificate or Correction notice or Weight & inspection certificate but with inspected against original or Corrected against Actual. This page is a cube measurement certificate, not a weight and inspection, nor NMFC? But likely "inspection_cert" or maybe "weight_and_inspection_cert"? It says "Cube Measurement Certificate". That might be a type of inspection certificate. The earlier definition for inspection_cert: "Inspection Certificate, Cube Measurement certificate". So indeed this page is inspection_cert. So doc_type: inspection_cert.\n\nWe must output via tool call classify_logistics_doc_type with documents array containing one object: page_no:1, doc_type:"inspection_cert".'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2615, 'outputTokens': 242, 'totalTokens': 2857}, 'metrics': {'latencyMs': 1227}}
2025-09-24 13:50:39,343 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/6040339b_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:50:39,345 - INFO - Downloaded PDF size: 0.5 MB
2025-09-24 13:50:39,346 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:39,346 - INFO - Splitting PDF 40a93b8e_W_A34CDFDJ66EDOZEKZWJL into 1 pages
2025-09-24 13:50:39,347 - INFO - Split PDF into 1 pages
2025-09-24 13:50:39,347 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:39,347 - INFO - Expected pages: [1]
2025-09-24 13:50:39,518 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:50:39,518 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:39,519 - INFO - Splitting PDF 333c8840_W_DFY1VDZWR7NBDLJV02G2 into 1 pages
2025-09-24 13:50:39,522 - INFO - Split PDF into 1 pages
2025-09-24 13:50:39,522 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:39,522 - INFO - Expected pages: [1]
2025-09-24 13:50:39,721 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_WRKSHW76B3QUG47QWR75.pdf -> s3://document-extraction-logistically/temp/4e450538_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:50:39,722 - INFO - 🔍 [13:50:39] Starting classification: W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:50:39,722 - INFO - ⬆️ [13:50:39] Uploading: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:50:39,722 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:39,729 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:39,732 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/4e450538_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:50:39,732 - INFO - Processing PDF from S3...
2025-09-24 13:50:39,732 - INFO - Downloading PDF from S3 to /tmp/tmp18enmjjw/4e450538_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:50:40,059 - INFO - Page 3: Extracted 850 characters, 59 lines from e3d7a014_NMFC_NJ4WSZ8BUQAW48V6403N_5d72423f_page_003.pdf
2025-09-24 13:50:40,059 - INFO - Successfully processed page 3
2025-09-24 13:50:40,279 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:50:40,279 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:40,281 - INFO - Splitting PDF f5e8dd55_W_HFPAXYL947DH59AB12FL into 1 pages
2025-09-24 13:50:40,284 - INFO - Split PDF into 1 pages
2025-09-24 13:50:40,284 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:40,284 - INFO - Expected pages: [1]
2025-09-24 13:50:40,297 - INFO - Page 2: Extracted 850 characters, 59 lines from e3d7a014_NMFC_NJ4WSZ8BUQAW48V6403N_5d72423f_page_002.pdf
2025-09-24 13:50:40,298 - INFO - Successfully processed page 2
2025-09-24 13:50:40,321 - INFO - ✓ Uploaded: /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/W_XCJLXZK140FUS8020ZAG.pdf -> s3://document-extraction-logistically/temp/b1c8c81d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:50:40,321 - INFO - 🔍 [13:50:40] Starting classification: W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:50:40,322 - INFO - Initializing TextractProcessor...
2025-09-24 13:50:40,334 - INFO - Initializing BedrockProcessor...
2025-09-24 13:50:40,336 - INFO - Processing document from S3: s3://document-extraction-logistically/temp/b1c8c81d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:50:40,336 - INFO - Processing PDF from S3...
2025-09-24 13:50:40,337 - INFO - Downloading PDF from S3 to /tmp/tmp744_55ck/b1c8c81d_W_XCJLXZK140FUS8020ZAG.pdf
2025-09-24 13:50:40,345 - INFO - 

I_QHD3LC0DU6S8O2YVVS60.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "inspection_cert"
        }
    ]
}
2025-09-24 13:50:40,345 - INFO - 

✓ Saved result: output/run1_I_QHD3LC0DU6S8O2YVVS60.json
2025-09-24 13:50:40,367 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:50:40,367 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:40,368 - INFO - Splitting PDF d610f135_W_K9VSARJOKAIZHNJ5RBDT into 1 pages
2025-09-24 13:50:40,369 - INFO - Split PDF into 1 pages
2025-09-24 13:50:40,369 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:40,370 - INFO - Expected pages: [1]
2025-09-24 13:50:40,570 - INFO - Page 1: Extracted 1511 characters, 86 lines from a0e05db6_NMFC_R1V0MO844PBLWNEAUETU_be689d75_page_001.pdf
2025-09-24 13:50:40,570 - INFO - Successfully processed page 1
2025-09-24 13:50:40,637 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/6040339b_I_QHD3LC0DU6S8O2YVVS60.pdf
2025-09-24 13:50:41,117 - INFO - Page 1: Extracted 980 characters, 76 lines from e3d7a014_NMFC_NJ4WSZ8BUQAW48V6403N_5d72423f_page_001.pdf
2025-09-24 13:50:41,117 - INFO - Successfully processed page 1
2025-09-24 13:50:41,118 - INFO - Combined 3 pages into final text
2025-09-24 13:50:41,118 - INFO - Text validation for e3d7a014_NMFC_NJ4WSZ8BUQAW48V6403N: 2735 characters, 3 pages
2025-09-24 13:50:41,118 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:41,118 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:41,261 - INFO - Downloaded PDF size: 0.0 MB
2025-09-24 13:50:41,261 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:41,263 - INFO - Splitting PDF 4e450538_W_WRKSHW76B3QUG47QWR75 into 1 pages
2025-09-24 13:50:41,264 - INFO - Split PDF into 1 pages
2025-09-24 13:50:41,264 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:41,264 - INFO - Expected pages: [1]
2025-09-24 13:50:41,440 - INFO - Page 2: Extracted 913 characters, 56 lines from a0e05db6_NMFC_R1V0MO844PBLWNEAUETU_be689d75_page_002.pdf
2025-09-24 13:50:41,440 - INFO - Successfully processed page 2
2025-09-24 13:50:41,441 - INFO - Combined 2 pages into final text
2025-09-24 13:50:41,441 - INFO - Text validation for a0e05db6_NMFC_R1V0MO844PBLWNEAUETU: 2460 characters, 2 pages
2025-09-24 13:50:41,442 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:41,442 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:41,805 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'cdddff33-d29a-40db-bd94-a492ae1b6f8e', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:41 GMT', 'content-type': 'application/json', 'content-length': '2664', 'connection': 'keep-alive', 'x-amzn-requestid': 'cdddff33-d29a-40db-bd94-a492ae1b6f8e'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The pages are a "Inspection Correction Notice" with details like NMFC, weight, etc. This looks like a "weight_and_inspection_cert" or "inspection_cert" or maybe "nmfc_cert". Let\'s examine keywords:\n\n- "Inspection Correction Notice" indicates some type of correction notice. \n- It includes NMFC codes, weight before and after, etc. \n- It states "This will certify that this shipment has been (Inspected/Dimensioned/Weighed) and the descriptions have been found to be as indicated above." \n- The description mentions "Correction Notice" which could align with NMFC certificate? Actually NMFC certificates are "NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual".\n\nKeywords for nmfc_cert: "As described and As found or Original and inspection or Corrected class or Correction information" Also NMFC Code and class #.\n\nThe page has NMFC 189430, weight classes before and after. It also says "Correction Details". That fits nmfc_cert? The definition says "NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual". So yes, this is likely nmfc_cert.\n\nWe need to check if weight_and_inspection_cert also? But weight_and_inspection_cert is "Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". But here we have a correction notice. The guidelines: "Strictly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." Since we see "Correction Notice" and NMFC code, we classify as nmfc_cert.\n\nThus each page is nmfc_cert. They are consecutive pages of same document. There\'s no "continued" indicator, but they are separate pages of same document. According to instructions: "If a page is part of a multi-page single document: each page still gets the same doc_type (repeat the type on every page)." So page1 and page2 both nmfc_cert.\n\nThus output: an array of objects with page_no and doc_type.\n\nWe need to output via tool call. Let\'s do it.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2963, 'outputTokens': 506, 'totalTokens': 3469}, 'metrics': {'latencyMs': 2134}}
2025-09-24 13:50:41,805 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/e0d63d92_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:50:41,823 - INFO - 

NMFC_DH0JZ2JWDGRHD26BX74C.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:50:41,823 - INFO - 

✓ Saved result: output/run1_NMFC_DH0JZ2JWDGRHD26BX74C.json
2025-09-24 13:50:42,107 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e0d63d92_NMFC_DH0JZ2JWDGRHD26BX74C.pdf
2025-09-24 13:50:42,122 - INFO - Downloaded PDF size: 0.1 MB
2025-09-24 13:50:42,123 - INFO - Splitting PDF into individual pages...
2025-09-24 13:50:42,124 - INFO - Splitting PDF b1c8c81d_W_XCJLXZK140FUS8020ZAG into 1 pages
2025-09-24 13:50:42,134 - INFO - Split PDF into 1 pages
2025-09-24 13:50:42,134 - INFO - Processing pages with Textract in parallel...
2025-09-24 13:50:42,135 - INFO - Expected pages: [1]
2025-09-24 13:50:42,356 - INFO - Page 1: Extracted 443 characters, 72 lines from 14a4779b_NMFC_RUDVGETVRZO7XX6YNW7I_a4dac9e8_page_001.pdf
2025-09-24 13:50:42,356 - INFO - Successfully processed page 1
2025-09-24 13:50:42,356 - INFO - Combined 1 pages into final text
2025-09-24 13:50:42,356 - INFO - Text validation for 14a4779b_NMFC_RUDVGETVRZO7XX6YNW7I: 460 characters, 1 pages
2025-09-24 13:50:42,357 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:42,357 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:42,783 - INFO - Page 2: Extracted 540 characters, 29 lines from 4332abd1_NMFC_OR9EL08KIKNQPZ3UV3HH_9d7cc60c_page_002.pdf
2025-09-24 13:50:42,784 - INFO - Successfully processed page 2
2025-09-24 13:50:43,784 - INFO - Page 1: Extracted 1120 characters, 87 lines from 4332abd1_NMFC_OR9EL08KIKNQPZ3UV3HH_9d7cc60c_page_001.pdf
2025-09-24 13:50:43,785 - INFO - Successfully processed page 1
2025-09-24 13:50:43,791 - INFO - Page 1: Extracted 732 characters, 59 lines from 7aa950b2_W_DCY7SLNMWUXIENOREHQF_a233bee4_page_001.pdf
2025-09-24 13:50:43,792 - INFO - Combined 2 pages into final text
2025-09-24 13:50:43,792 - INFO - Text validation for 4332abd1_NMFC_OR9EL08KIKNQPZ3UV3HH: 1696 characters, 2 pages
2025-09-24 13:50:43,792 - INFO - Successfully processed page 1
2025-09-24 13:50:43,792 - INFO - Combined 1 pages into final text
2025-09-24 13:50:43,792 - INFO - Text validation for 7aa950b2_W_DCY7SLNMWUXIENOREHQF: 749 characters, 1 pages
2025-09-24 13:50:43,792 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:43,792 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:43,794 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:43,794 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:43,955 - INFO - Page 1: Extracted 626 characters, 49 lines from 333c8840_W_DFY1VDZWR7NBDLJV02G2_2e98c29c_page_001.pdf
2025-09-24 13:50:43,955 - INFO - Successfully processed page 1
2025-09-24 13:50:43,956 - INFO - Combined 1 pages into final text
2025-09-24 13:50:43,956 - INFO - Text validation for 333c8840_W_DFY1VDZWR7NBDLJV02G2: 643 characters, 1 pages
2025-09-24 13:50:43,956 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:43,956 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:44,226 - INFO - Page 1: Extracted 802 characters, 30 lines from d610f135_W_K9VSARJOKAIZHNJ5RBDT_4050ac9c_page_001.pdf
2025-09-24 13:50:44,226 - INFO - Successfully processed page 1
2025-09-24 13:50:44,227 - INFO - Combined 1 pages into final text
2025-09-24 13:50:44,227 - INFO - Text validation for d610f135_W_K9VSARJOKAIZHNJ5RBDT: 819 characters, 1 pages
2025-09-24 13:50:44,227 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:44,227 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:44,319 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '0123b365-8c5a-4eb7-8e6e-d3e4b4bed2c0', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:44 GMT', 'content-type': 'application/json', 'content-length': '741', 'connection': 'keep-alive', 'x-amzn-requestid': '0123b365-8c5a-4eb7-8e6e-d3e4b4bed2c0'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1.\n\nWe need to use tool classify_logistics_doc_type. Provide array of objects: {page_no: 1, doc_type: ...}. Determine type. The content: "NMFC CLASSIFICATION CERTIFICATE". That\'s explicitly a certificate. The enum has nmfc_cert. There are many NMFC keywords. So doc_type should be "nmfc_cert". No continuation. So output array with one object.\n\nWe must call function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2710, 'outputTokens': 117, 'totalTokens': 2827}, 'metrics': {'latencyMs': 731}}
2025-09-24 13:50:44,320 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/14a4779b_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:50:44,341 - INFO - 

NMFC_RUDVGETVRZO7XX6YNW7I.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:50:44,341 - INFO - 

✓ Saved result: output/run1_NMFC_RUDVGETVRZO7XX6YNW7I.json
2025-09-24 13:50:44,463 - INFO - Page 1: Extracted 517 characters, 31 lines from f5e8dd55_W_HFPAXYL947DH59AB12FL_1ce7b80a_page_001.pdf
2025-09-24 13:50:44,463 - INFO - Successfully processed page 1
2025-09-24 13:50:44,463 - INFO - Combined 1 pages into final text
2025-09-24 13:50:44,463 - INFO - Text validation for f5e8dd55_W_HFPAXYL947DH59AB12FL: 534 characters, 1 pages
2025-09-24 13:50:44,464 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:44,464 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:44,640 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/14a4779b_NMFC_RUDVGETVRZO7XX6YNW7I.pdf
2025-09-24 13:50:45,178 - INFO - Page 1: Extracted 580 characters, 48 lines from 4e450538_W_WRKSHW76B3QUG47QWR75_3cc036e7_page_001.pdf
2025-09-24 13:50:45,179 - INFO - Successfully processed page 1
2025-09-24 13:50:45,179 - INFO - Combined 1 pages into final text
2025-09-24 13:50:45,179 - INFO - Text validation for 4e450538_W_WRKSHW76B3QUG47QWR75: 597 characters, 1 pages
2025-09-24 13:50:45,179 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:45,180 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:45,875 - INFO - Page 1: Extracted 939 characters, 64 lines from 40a93b8e_W_A34CDFDJ66EDOZEKZWJL_a7b7b496_page_001.pdf
2025-09-24 13:50:45,875 - INFO - Successfully processed page 1
2025-09-24 13:50:45,875 - INFO - Combined 1 pages into final text
2025-09-24 13:50:45,876 - INFO - Text validation for 40a93b8e_W_A34CDFDJ66EDOZEKZWJL: 956 characters, 1 pages
2025-09-24 13:50:45,876 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:45,876 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:46,220 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '75d7b145-d9a0-4ab6-a693-e0d16246b3b3', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:46 GMT', 'content-type': 'application/json', 'content-length': '5009', 'connection': 'keep-alive', 'x-amzn-requestid': '75d7b145-d9a0-4ab6-a693-e0d16246b3b3'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The content appears to be a "Inspection Correction Notice" with weight and dimension details. The classification options: weight_and_inspection_cert, inspection_cert, nmfc_cert, etc. The description mentions "Inspection Correction Notice". This may be a correction notice for a weight and inspection certificate. The doc type "Inspection Correction Notice" likely corresponds to "weight_and_inspection_cert" or "inspection_cert". Let\'s examine criteria:\n\n- weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width, height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also check if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert. Also check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise, inspection_cert.\n\nOur page has weight, dimensions, "Inspection Correction Notice". There\'s mention of "Inspection photos" and "Comments". It seems to be a correction notice for an inspection certificate, likely weight_and_inspection_cert. It also has NMFC codes before and after. The NMFC code 189800-4 appears. That could be a NMFC classification certificate? But the doc says "Inspection Correction Notice". Check nmfc_cert criteria: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." Keywords: As described and As found or Original and inspection or Corrected class or Correction information. Also "NMFC Code, class #".\n\nOur doc has "NMFC to 189800-4" in comments. That seems like a correction to NMFC. The doc is a correction notice, so likely this is an NMFC certificate (or correction notice). But the doc appears to be a correction notice for an inspection, not necessarily NMFC classification correction. However, it says "NMFC to 189800-4". That might be a correction of NMFC code. The presence of "Inspection Correction Notice" indicates a correction to previous inspection certificate. So maybe it\'s a "nmfc_cert" because it\'s a correction notice with NMFC change.\n\nBut the categories: weight_and_inspection_cert, inspection_cert, nmfc_cert. The doc is a correction notice; the doc states "This will certify that this shipment has been (Inspected/Dimensioned/Weighed) and the descriptions have been found to be as indicated above." That sounds like a certification. It also says "Inspection Correction Notice". So maybe it\'s a weight_and_inspection_cert with a correction. But there\'s a distinct category nmfc_cert for NMFC classification certificate or correction notice. The doc mentions "NMFC to 189800-4" and is likely a correction to the NMFC classification. That suggests nmfc_cert. However, the doc also includes weight and dimension data, so it might be weight_and_inspection_cert. But the question: "If the doc is a correction notice, classify as nmfc_cert if it has NMFC code change." The doc specifically includes NMFC code. It\'s a correction notice for NMFC. The doc says "This will certify that this shipment has been (Inspected/Dimensioned/Weighed) and the descriptions have been found to be as indicated above." That is the same as weight_and_inspection_cert. But the correction notice indicates it\'s a corrected version.\n\nCheck the guidelines: "weight_and_inspection_cert - Weight and Inspection Certificate: ... Note: 1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert. 2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, inspection_cert."\n\nSo if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert. The nmfc_cert has keywords: "As described and As found or Original and inspection or Corrected against Original or Correction information", plus NMFC Code, class #. Our doc has "NMFC to 189800-4". That might match "NMFC Code" keyword. Also "Correction" appears in doc. So this doc has nmfc_cert keywords. Therefore classification: nmfc_cert.\n\nThus all pages are same doc, nmfc_cert. Each page should have same doc_type. But check continuation: page1, page2, page3. Page1 is first page, no continuation. Page2: we see same content. Does page2 indicate continuation? It doesn\'t start with "continued". No. But it\'s identical to page1. Could be duplicate copy? But spec says if it\'s continuation'}}}]}}, 'stopReason': 'max_tokens', 'usage': {'inputTokens': 3323, 'outputTokens': 997, 'totalTokens': 4320}, 'metrics': {'latencyMs': 3938}}
2025-09-24 13:50:46,220 - ERROR - Failed to extract tool response: Stop reason is not tool_use
2025-09-24 13:50:46,221 - ERROR - Processing failed for s3://document-extraction-logistically/temp/e3d7a014_NMFC_NJ4WSZ8BUQAW48V6403N.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:50:46,221 - ERROR - ❌ Classification failed for s3://document-extraction-logistically/temp/e3d7a014_NMFC_NJ4WSZ8BUQAW48V6403N.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:50:46,222 - ERROR - ❌ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 140, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 144, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 13:50:46,223 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 13:50:46,223 - ERROR - ✗ Failed to process /home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert/NMFC_NJ4WSZ8BUQAW48V6403N.pdf: Unexpected tool response format from Bedrock
2025-09-24 13:50:46,224 - ERROR - ✗ Full traceback: Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 140, in extract_tool_response
    raise Exception("Stop reason is not tool_use")
Exception: Stop reason is not tool_use

During handling of the above exception, another exception occurred:

Traceback (most recent call last):
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 276, in process_single_file
    result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)
  File "/usr/lib/python3.10/concurrent/futures/thread.py", line 58, in run
    result = self.fn(*self.args, **self.kwargs)
  File "/home/<USER>/Documents/repositories/test_logistically/scripts/test_classification.py", line 228, in run_classification_sync
    result = loop.run_until_complete(llm_classification(s3_uri))
  File "/usr/lib/python3.10/asyncio/base_events.py", line 649, in run_until_complete
    return future.result()
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 160, in main
    classification_result, bedrock_response = await get_document_types_with_bedrock(bedrock_processor, combined_text)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py", line 103, in get_document_types_with_bedrock
    content = bedrock_processor.extract_tool_response(response)
  File "/home/<USER>/Documents/repositories/document-data-extraction/app/llm/bedrock.py", line 144, in extract_tool_response
    raise Exception("Unexpected tool response format from Bedrock")
Exception: Unexpected tool response format from Bedrock

2025-09-24 13:50:46,224 - ERROR - 🐛 Debug info stored - will trigger debugger in main thread
2025-09-24 13:50:46,390 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '76698621-4387-44a3-b0c0-51c1e3ca08f1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:46 GMT', 'content-type': 'application/json', 'content-length': '4181', 'connection': 'keep-alive', 'x-amzn-requestid': '76698621-4387-44a3-b0c0-51c1e3ca08f1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a multi-page document. It appears to be a "Weight and Research Program" document. The content: includes "Weight and Research Program" header. It has weight, density, cube, etc. The description says "We certify that this shipment has been inspected and/or reweighed ...". It\'s a weight & research program. It seems like a "Weight and Inspection Certificate" or "Weight and Inspection Certificate"? However the definitions: weight_and_inspection_cert is a certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Not exactly. There\'s also nmfc_cert which is a NMFC Classification Certificate or Correction notice or Weight & inspection certificate but with "Inspected against original or Corrected against Actual". The document has "As found upon Examination" and "We certify that this shipment has been inspected and/or reweighed". That could be a weight and inspection certificate. The content also includes "Cube" measurement. So it fits weight_and_inspection_cert. The classification: weight_and_inspection_cert. But we need to check if nmfc_cert: "If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." What are nmfc_cert keywords? It says: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued. Keywords indication: As described or As found or Original and inspection or Corrected class or Correction information. Other keywords optional: NMFC Code, class #." The doc has "As found upon Examination" phrase. That might be a keyword for nmfc_cert. But does it have NMFC code? Not seeing. But "As found upon Examination" is in the doc. That phrase appears in the page.\n\nI think weight_and_inspection_cert is correct. But check nmfc_cert: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." The doc says "Original Correction" at top. There\'s a field "Original Correction" and "We certify that this shipment has been inspected and/or reweighed". That seems like a correction. It states "Original Correction 9/04/25". That might indicate a correction. The presence of "As found upon Examination" might be a key. But it\'s not "Original and inspection" or "Corrected class". It doesn\'t show NMFC code. The phrase "As found upon Examination" may be a keyword for nmfc_cert. The doc also says "Original Correction". This might indeed be a correction notice. But we don\'t see NMFC code. The definition says "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." So if this is a correction, it might be nmfc_cert. But do we have evidence? There\'s "Original Correction" and "We certify ... inspected". But the phrase "As found upon Examination" suggests it\'s a "weight & inspection certificate"? But it\'s possibly a correction.\n\nLet\'s read the spec: weight_and_inspection_cert: "Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". nmfc_cert: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." So if it\'s a correction, it\'s nmfc_cert. But we need to see if it\'s a correction. It has "Original Correction" header, "Date of Issuing" and "Original Correction". The doc has "Date of Issuing" and "Original Correction". So yes, it\'s a corrected weight & inspection certificate. The phrase "As found upon Examination" appears. So likely nmfc_cert.\n\nLet\'s choose nmfc_cert. The doc is multi-page: page1 and page2. So both pages get same doc_type: nmfc_cert.\n\nWe need to output via tool call classify_logistics_doc_type with array of objects: each has page_no and doc_type. For 2 pages.\n\nLet\'s produce that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3299, 'outputTokens': 846, 'totalTokens': 4145}, 'metrics': {'latencyMs': 3751}}
2025-09-24 13:50:46,390 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/a0e05db6_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:50:46,515 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/e3d7a014_NMFC_NJ4WSZ8BUQAW48V6403N.pdf
2025-09-24 13:50:46,538 - INFO - Page 1: Extracted 528 characters, 31 lines from b1c8c81d_W_XCJLXZK140FUS8020ZAG_b911aaac_page_001.pdf
2025-09-24 13:50:46,538 - INFO - Successfully processed page 1
2025-09-24 13:50:46,544 - INFO - Combined 1 pages into final text
2025-09-24 13:50:46,546 - INFO - Text validation for b1c8c81d_W_XCJLXZK140FUS8020ZAG: 545 characters, 1 pages
2025-09-24 13:50:46,560 - INFO - Analyzing document types with Bedrock...
2025-09-24 13:50:46,562 - INFO - Trying Bedrock model: openai.gpt-oss-20b-1:0
2025-09-24 13:50:46,588 - INFO - 

NMFC_R1V0MO844PBLWNEAUETU.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "nmfc_cert"
        },
        {
            "page_no": 2,
            "doc_type": "nmfc_cert"
        }
    ]
}
2025-09-24 13:50:46,588 - INFO - 

✓ Saved result: output/run1_NMFC_R1V0MO844PBLWNEAUETU.json
2025-09-24 13:50:46,832 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '51a03393-e8bc-4b46-b2d7-8c68f41c532d', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:46 GMT', 'content-type': 'application/json', 'content-length': '1092', 'connection': 'keep-alive', 'x-amzn-requestid': '51a03393-e8bc-4b46-b2d7-8c68f41c532d'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page1. It\'s a freight certificate with dimensions, weight, cube, etc. This is likely a weight_and_inspection_cert? The description: "Freight Certificate" with weight, cube, etc. The definition for weight_and_inspection_cert: "Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". It also says if it has nmfc_cert keywords, classify as nmfc_cert. There is no mention of NMFC or inspection correction. No NMFC code. The page includes weight, dimensions, cube. So weight_and_inspection_cert seems correct. It\'s not inspection_cert because inspection_cert is for inspections other than weight. So classify as weight_and_inspection_cert.\n\nReturn JSON via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2603, 'outputTokens': 178, 'totalTokens': 2781}, 'metrics': {'latencyMs': 963}}
2025-09-24 13:50:46,832 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/f5e8dd55_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:50:46,884 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/a0e05db6_NMFC_R1V0MO844PBLWNEAUETU.pdf
2025-09-24 13:50:46,898 - INFO - 

W_HFPAXYL947DH59AB12FL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:50:46,898 - INFO - 

✓ Saved result: output/run1_W_HFPAXYL947DH59AB12FL.json
2025-09-24 13:50:46,912 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'aa60ab47-e98a-4000-af4b-44cf729dcfee', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:46 GMT', 'content-type': 'application/json', 'content-length': '1160', 'connection': 'keep-alive', 'x-amzn-requestid': 'aa60ab47-e98a-4000-af4b-44cf729dcfee'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. The content describes a "WEIGHT & RESEARCH CERTIFICATE" with W&R codes. That seems to be a weight and inspection certificate. The definitions: weight_and_inspection_cert. Let\'s check if it\'s nmfc_cert? The page contains "W&R ISSUED" and "WR" and "W & R" codes, but not specifically NMFC code. The doc type seems "weight_and_inspection_cert". The second page continues. Also first page might continue (but the same doc). The first page has "WEIGHT & RESEARCH CERTIFICATE" header. So doc_type: weight_and_inspection_cert. The second page has same header, continuation. So doc_type same. So output two objects: page_no 1 doc_type weight_and_inspection_cert, page_no 2 doc_type weight_and_inspection_cert. Must call function.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"},{"page_no":2,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3121, 'outputTokens': 215, 'totalTokens': 3336}, 'metrics': {'latencyMs': 1053}}
2025-09-24 13:50:46,912 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4332abd1_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:50:47,193 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/f5e8dd55_W_HFPAXYL947DH59AB12FL.pdf
2025-09-24 13:50:47,216 - INFO - 

NMFC_OR9EL08KIKNQPZ3UV3HH.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        },
        {
            "page_no": 2,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:50:47,216 - INFO - 

✓ Saved result: output/run1_NMFC_OR9EL08KIKNQPZ3UV3HH.json
2025-09-24 13:50:47,247 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '87aaac99-473f-4ef2-8e38-efb2d8ae4c37', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:47 GMT', 'content-type': 'application/json', 'content-length': '1661', 'connection': 'keep-alive', 'x-amzn-requestid': '87aaac99-473f-4ef2-8e38-efb2d8ae4c37'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify page 1. The content is a weight certificate, with "WEIGHT CERTIFICATE" header. Keywords: Weight, Weighed Date, etc. It says "WEIGHT CERTIFICATION". Likely weight_and_inspection_cert or scale_ticket. The description says weight certificate. The keywords: Weight, Weighed Date, scales, legal for trade. This seems to be a weight certificate. The category "weight_and_inspection_cert" is weight and inspection certificate. There\'s also "scale_ticket". Which one fits? Let\'s see definitions:\n\n- scale_ticket: Weight record from a scale for vehicle/load. Keywords: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n\n- weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate.\n\nThe page says WEIGHT CERTIFICATE. So weight_and_inspection_cert is more appropriate. It also says "WEIGHT CERTIFICATION" and "We certify ... correct weight". So classify as weight_and_inspection_cert. There\'s no mention of inspection, but weight certificate qualifies. So doc_type: weight_and_inspection_cert. page_no: 1.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2669, 'outputTokens': 302, 'totalTokens': 2971}, 'metrics': {'latencyMs': 1366}}
2025-09-24 13:50:47,247 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/d610f135_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:50:47,271 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '997e1c09-1ce6-4c1f-a05d-5743da7009a5', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:47 GMT', 'content-type': 'application/json', 'content-length': '1858', 'connection': 'keep-alive', 'x-amzn-requestid': '997e1c09-1ce6-4c1f-a05d-5743da7009a5'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page (page1). The content includes "Weight & Inspection" "CERTIFIED WEIGHT CERTIFICATE" "Weight Certificate" "We Certify that this shipment ... has been reweighed and that the corrected weight as stated is the true and accurate declared weight: 1148 ... correct weight 1425" Also "Bill of Lading" label appears. There\'s no mention of inspection certificate vs weight and inspection cert. According to definitions: weight_and_inspection_cert: includes keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also there\'s rule: if weight_and_inspection_cert has keywords mentioned in nmfc_cert, classify as nmfc_cert else keep weight_and_inspection_cert. But we don\'t see nmfc_cert keywords. We also have "Weight & Inspection" and "CERTIFIED WEIGHT CERTIFICATE". So likely weight_and_inspection_cert. Also "Bill of Lading" appears near top, but this is a weight certificate that references Bill of Lading. According to combined_carrier_documents: multiple distinct document headers on one page. But here only weight certificate page. So doc_type: weight_and_inspection_cert.\n\nWe must output via tool call. The tool expects an array of documents objects with page_no and doc_type. We have one page. So we call classify_logistics_doc_type with documents: [{page_no:1, doc_type:"weight_and_inspection_cert"}].\n\nLet\'s do that.'}}}, {'toolUse': {'toolUseId': 'tooluse_rIfeGg_QSy6olaHnRfWPIw', 'name': 'classify_logistics_doc_type', 'input': {'documents': [{'page_no': 1, 'doc_type': 'weight_and_inspection_cert'}]}}}]}}, 'stopReason': 'tool_use', 'usage': {'inputTokens': 2682, 'outputTokens': 321, 'totalTokens': 3003}, 'metrics': {'latencyMs': 1444}}
2025-09-24 13:50:47,271 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/333c8840_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:50:47,512 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4332abd1_NMFC_OR9EL08KIKNQPZ3UV3HH.pdf
2025-09-24 13:50:47,530 - INFO - 

W_K9VSARJOKAIZHNJ5RBDT.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:50:47,530 - INFO - 

✓ Saved result: output/run1_W_K9VSARJOKAIZHNJ5RBDT.json
2025-09-24 13:50:47,681 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '3366a24f-3574-4c0e-aa18-960ea77edc72', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:47 GMT', 'content-type': 'application/json', 'content-length': '1419', 'connection': 'keep-alive', 'x-amzn-requestid': '3366a24f-3574-4c0e-aa18-960ea77edc72'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. There\'s only page1. We need to output an array of objects, each with page_no and doc_type. The doc_type: This seems to be a "Weight and Inspection Certificate" as it says "Linear Certificate" maybe "Weight and Inspection Certificate". The keywords: "Linear Certificate", "Certified Original", "Linear Certification", "W&R #", "Pro #", "Date of Correction", "Issuing Terminal", "Linear Feet", "Description: SKIDS", "Length x Width x Height", "Units", "Cu Ft", "Pounds". It\'s a certification of measurement. According to definitions: weight_and_inspection_cert: certificate confirming weight possibly with length, width and height but not rechecked or reweighted. This seems to be a certificate measuring linear feet. Also mentions "Date of Correction". That might be a correction certificate. It\'s a "Weight and Inspection Certificate". So doc_type should be "weight_and_inspection_cert". Output: one object: page_no: 1, doc_type: "weight_and_inspection_cert".\n\nWe must use the tool classify_logistics_doc_type.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2637, 'outputTokens': 251, 'totalTokens': 2888}, 'metrics': {'latencyMs': 1277}}
2025-09-24 13:50:47,681 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/4e450538_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:50:47,824 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/d610f135_W_K9VSARJOKAIZHNJ5RBDT.pdf
2025-09-24 13:50:47,840 - INFO - 

W_DFY1VDZWR7NBDLJV02G2.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:50:47,840 - INFO - 

✓ Saved result: output/run1_W_DFY1VDZWR7NBDLJV02G2.json
2025-09-24 13:50:48,133 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/333c8840_W_DFY1VDZWR7NBDLJV02G2.pdf
2025-09-24 13:50:48,140 - INFO - 

W_WRKSHW76B3QUG47QWR75.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:50:48,140 - INFO - 

✓ Saved result: output/run1_W_WRKSHW76B3QUG47QWR75.json
2025-09-24 13:50:48,431 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/4e450538_W_WRKSHW76B3QUG47QWR75.pdf
2025-09-24 13:50:48,530 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': '53a8729d-a4d8-4ce4-a42d-ed61bbc73e87', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:48 GMT', 'content-type': 'application/json', 'content-length': '2922', 'connection': 'keep-alive', 'x-amzn-requestid': '53a8729d-a4d8-4ce4-a42d-ed61bbc73e87'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We need to classify each page. Only one page given. We need to output object for page 1. Determine document type.\n\nDocument says "WEIGHT VALIDATION CERTIFICATE" and contains weight, verification, scale operator. That matches "weight_and_inspection_cert" or "inspection_cert" or "scale_ticket". Let\'s check definitions.\n\nScale Ticket: weight record from a scale for vehicle/load. Keywords: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight, Date, Time, Ticket no. This page has weight, but not Gross, Tare, Net; it has Verification of actual weight. But it\'s "WEIGHT VALIDATION CERTIFICATE". That is similar to weight_and_inspection_cert: certificate of analysis, weight certificate, inspection certificate, W&I. It says "Total Actual Verified Weight" etc. So likely weight_and_inspection_cert.\n\nCheck if nmfc_cert: requires "Inspected against original or Corrected against Actual". Not present.\n\nCheck inspection_cert: if not weight but inspection. Not weight.\n\nThus weight_and_inspection_cert.\n\nBut need to confirm if the page is a continuation. The page shows "4 of 8". So page 4 of 8. So this is page 4. But we only have page1 content. The content includes "4 of 8". So this is page 4 of 8. But we only have page 1 of the input. The instruction: For every page in the input PDF you MUST return exactly one object describing that page. Input only has one page labeled page1. But the content says 4 of 8. That suggests the PDF has multiple pages. But we only have this page. We need to output classification for this page. It might be continuation page 4 of 8. But we are not told preceding pages. We treat this page as part of a multi-page document. For classification, we just classify this page as weight_and_inspection_cert. But we must check if this is continuation: It starts with "FedEx" and "WEIGHT VALIDATION CERTIFICATE". Does it start with "continued"? No. It indicates page 4 of 8. But that means it\'s part of a multi-page doc but not necessarily a continuation page. It is still part of the same document. But we don\'t need to assign same type as previous because we don\'t have previous pages. So just assign weight_and_inspection_cert.\n\nBut careful: The doc says "WEIGHT VALIDATION CERTIFICATE" exactly. That matches "weight_and_inspection_cert" definition. So output.\n\nWe must use the tool classify_logistics_doc_type with arguments: documents: array of objects with page_no and doc_type. Since only one page, array of one object: {page_no:1, doc_type:"weight_and_inspection_cert"}. Let\'s respond via tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2715, 'outputTokens': 606, 'totalTokens': 3321}, 'metrics': {'latencyMs': 2665}}
2025-09-24 13:50:48,530 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/7aa950b2_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:50:48,542 - INFO - 

W_DCY7SLNMWUXIENOREHQF.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:50:48,542 - INFO - 

✓ Saved result: output/run1_W_DCY7SLNMWUXIENOREHQF.json
2025-09-24 13:50:48,737 - INFO - Bedrock response: {'ResponseMetadata': {'RequestId': 'f43bfc79-33a3-45b2-8dcf-a6d2df260f03', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:48 GMT', 'content-type': 'application/json', 'content-length': '1901', 'connection': 'keep-alive', 'x-amzn-requestid': 'f43bfc79-33a3-45b2-8dcf-a6d2df260f03'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a single page. The text shows "Inspection Certificate". There\'s mention of weight, dimensions, etc. Likely a "weight_and_inspection_cert" or "inspection_cert" or "nmfc_cert". Let\'s check definitions:\n\n- weight_and_inspection_cert: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also note: if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert.\n\n- inspection_cert: Inspection certificate other than weight and inspection certificate which doesn\'t have weight mentioned.\n\n- nmfc_cert: NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strictly with Inspected against original or Corrected against Actual. Keywords: As described or As found or Original and inspection or Corrected class or Correction information. Other keywords: NMFC Code, class #.\n\nThe page includes "Inspection Certificate" heading. It also includes weight, dimensions. No mention of NMFC codes or correction. So it\'s weight_and_inspection_cert.\n\nCheck for "Certificate No." etc. It has "NCWM Cerificate No. 09-076PA1." So maybe it\'s a certified weight. So yes weight_and_inspection_cert.\n\nThus doc_type: "weight_and_inspection_cert". page_no: 1.\n\nWe output as array of objects with page_no and doc_type. Use the tool.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"weight_and_inspection_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 2823, 'outputTokens': 335, 'totalTokens': 3158}, 'metrics': {'latencyMs': 1640}}
2025-09-24 13:50:48,738 - INFO - Successfully processed S3 document: s3://document-extraction-logistically/temp/40a93b8e_W_A34CDFDJ66EDOZEKZWJL.pdf
2025-09-24 13:50:48,836 - INFO - ✓ Deleted: s3://document-extraction-logistically/temp/7aa950b2_W_DCY7SLNMWUXIENOREHQF.pdf
2025-09-24 13:50:48,857 - INFO - 

W_A34CDFDJ66EDOZEKZWJL.pdf

{
    "documents": [
        {
            "page_no": 1,
            "doc_type": "weight_and_inspection_cert"
        }
    ]
}
2025-09-24 13:50:48,857 - INFO - 

✓ Saved result: output/run1_W_A34CDFDJ66EDOZEKZWJL.json
