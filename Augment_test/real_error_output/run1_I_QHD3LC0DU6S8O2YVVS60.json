{"classification_result": {"documents": [{"page_no": 1, "doc_type": "weight_and_inspection_cert"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 1.25716121956998e-08}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 1.9637036885455927e-08, "Y": 1.0}]}, "Id": "d5bffad1-37df-4023-8515-a22d97ba7e16", "Relationships": [{"Type": "CHILD", "Ids": ["3841d449-7a6e-4aa3-b183-1ea1a7eac896", "535fd720-c678-4a49-b803-2d9c99a55803", "5b5022c1-8e60-4415-a61c-f024eb17a336", "309b54b3-1615-45f2-852c-6f16b73cf42b", "305374ae-85cb-47a1-8d1f-7473dd55c562", "106f2c82-e527-49c1-a967-1d2d215cb016", "23b5a8ed-b278-46d6-b046-f8ecbced1761", "10696a7b-aedb-468e-9190-654929558c68", "f0456d90-64ee-44b0-8ceb-824372862c7b", "dcfc4dbd-e517-4faf-af44-1a5af2424b48", "c5478fd7-13f7-4c04-b710-607b745230a7", "d20bc9cc-239c-4927-a2ed-3f2fb745a662", "d9ff72a3-992f-4a72-8d8c-649894913044", "7d29fae6-9293-4e8b-8270-8244c28c4fb8", "c1899538-2782-4bd6-a3cb-c3c051447497", "d8ca4488-8127-4853-9f32-cfc0608ddaf0", "233244cc-2df1-4595-af49-3403e20e7c12", "10f27c79-f169-4157-9087-597824975030", "504db9ba-7104-4354-a783-5375dd821231", "df4d9420-8731-48cc-a4fd-c66375450e1d", "bf600f87-67a7-47e6-bb21-aaaeed9b1398", "3ae50c40-8790-4648-8233-5a68fc128b2f", "8f82c4cf-6e93-4d47-8ccf-2bbd93f4707c", "095132c0-5026-4cfc-8bd5-ba35590f8bae", "ee4896c3-2ea9-4828-93bb-15c85c6f361d", "bcf6a15a-d92c-437c-9d38-e64cf6b69a6d", "111cba91-4109-4376-b92a-793707417fe0", "80a9232c-8c82-484b-9dfa-b18b2e94b7cd", "ddeaa5f9-817e-4ef8-b10e-705f84672c16", "fc733cd8-b0ff-4bed-8d0c-fe2c9ef3767b", "532fc59d-a5d5-4740-a1c7-57325f9ef1b1", "545415ac-49d3-47e8-b58e-d370759828bd", "2023a488-5189-4018-9808-e184d7305254", "ca160b21-97f0-45e3-931d-836b7558e316", "aa56a335-309f-42e8-9226-47e159d12c11", "5d5c6766-4e36-4c95-8a6f-2b57f02ac461"]}]}, {"BlockType": "LINE", "Confidence": 99.77140808105469, "Text": "TForce", "Geometry": {"BoundingBox": {"Width": 0.22124814987182617, "Height": 0.030413439497351646, "Left": 0.08821188658475876, "Top": 0.04618319496512413}, "Polygon": [{"X": 0.08821188658475876, "Y": 0.04618346318602562}, {"X": 0.3094598948955536, "Y": 0.04618319496512413}, {"X": 0.3094600439071655, "Y": 0.07659641653299332}, {"X": 0.08821213990449905, "Y": 0.07659663259983063}]}, "Id": "3841d449-7a6e-4aa3-b183-1ea1a7eac896", "Relationships": [{"Type": "CHILD", "Ids": ["e684b6f7-f6bd-4f5d-b3ac-11e159d584f1"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "Freight", "Geometry": {"BoundingBox": {"Width": 0.10413113981485367, "Height": 0.01643381081521511, "Left": 0.19421155750751495, "Top": 0.08031381666660309}, "Polygon": [{"X": 0.19421155750751495, "Y": 0.08031391352415085}, {"X": 0.29834261536598206, "Y": 0.08031381666660309}, {"X": 0.2983427047729492, "Y": 0.09674753993749619}, {"X": 0.1942116618156433, "Y": 0.09674762189388275}]}, "Id": "535fd720-c678-4a49-b803-2d9c99a55803", "Relationships": [{"Type": "CHILD", "Ids": ["7cc6e87a-046e-4a32-a122-70c7a9b25d72"]}]}, {"BlockType": "LINE", "Confidence": 99.96366119384766, "Text": "CUBE MEASUREMENT CERTIFICATE", "Geometry": {"BoundingBox": {"Width": 0.4048713445663452, "Height": 0.014018786139786243, "Left": 0.29764631390571594, "Top": 0.10961568355560303}, "Polygon": [{"X": 0.29764631390571594, "Y": 0.1096159815788269}, {"X": 0.7025176882743835, "Y": 0.10961568355560303}, {"X": 0.7025176882743835, "Y": 0.1236342266201973}, {"X": 0.2976464033126831, "Y": 0.123634472489357}]}, "Id": "5b5022c1-8e60-4415-a61c-f024eb17a336", "Relationships": [{"Type": "CHILD", "Ids": ["c9a3f271-194c-4f1d-af5e-9d4041a6f3b2", "30bd4c94-c7ca-42ab-bcb6-74a5ac298255", "61ff7776-7f00-47ef-b29e-ed0c0c4a0a91"]}]}, {"BlockType": "LINE", "Confidence": 99.82402038574219, "Text": "Freight Bill Number:", "Geometry": {"BoundingBox": {"Width": 0.14265181124210358, "Height": 0.01232189778238535, "Left": 0.09035287797451019, "Top": 0.15650363266468048}, "Polygon": [{"X": 0.09035287797451019, "Y": 0.15650369226932526}, {"X": 0.2330045998096466, "Y": 0.15650363266468048}, {"X": 0.23300468921661377, "Y": 0.16882549226284027}, {"X": 0.09035297483205795, "Y": 0.16882553696632385}]}, "Id": "309b54b3-1615-45f2-852c-6f16b73cf42b", "Relationships": [{"Type": "CHILD", "Ids": ["6cffdc0f-28fe-49be-86f0-d9db83f31dd2", "082dbec1-2191-4939-b890-2c56f000e93e", "e55a7cdd-d065-4b45-9ede-cf78ca4e9e61"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "413999202", "Geometry": {"BoundingBox": {"Width": 0.0818236768245697, "Height": 0.010035033337771893, "Left": 0.2754664421081543, "Top": 0.15658408403396606}, "Polygon": [{"X": 0.2754664421081543, "Y": 0.15658409893512726}, {"X": 0.3572900891304016, "Y": 0.15658408403396606}, {"X": 0.357290118932724, "Y": 0.16661909222602844}, {"X": 0.2754665017127991, "Y": 0.16661910712718964}]}, "Id": "305374ae-85cb-47a1-8d1f-7473dd55c562", "Relationships": [{"Type": "CHILD", "Ids": ["9cac7324-f806-42c1-8ba7-f7cd0c3f04b5"]}]}, {"BlockType": "LINE", "Confidence": 99.87056732177734, "Text": "The information set forth within this document constitutes an", "Geometry": {"BoundingBox": {"Width": 0.4347594082355499, "Height": 0.010184531100094318, "Left": 0.4680711030960083, "Top": 0.15612974762916565}, "Polygon": [{"X": 0.4680711030960083, "Y": 0.15612991154193878}, {"X": 0.9028305411338806, "Y": 0.15612974762916565}, {"X": 0.9028304815292358, "Y": 0.16631415486335754}, {"X": 0.4680711328983307, "Y": 0.1663142889738083}]}, "Id": "106f2c82-e527-49c1-a967-1d2d215cb016", "Relationships": [{"Type": "CHILD", "Ids": ["32a20ab2-bbb5-4e59-bcfd-e3365d73d4de", "43941dd2-ca37-46f9-8abc-a4fd60cf1939", "e5e57aa7-87cb-4210-b844-24e05f12ad40", "c9227693-f019-4ab4-a4b1-329dd4b07d05", "07b3fa55-12f8-4554-8b83-4d4b90b88307", "8ccccc8d-dd61-41b4-a664-c2ef32f83ec8", "1223e619-6fa8-4683-994d-0d2528b0c72c", "43a450ac-03d4-42bd-9d37-cedb47385763", "847a2567-991f-4381-aff9-ea8a52daac11"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Measured Date:", "Geometry": {"BoundingBox": {"Width": 0.11489874869585037, "Height": 0.009900609962642193, "Left": 0.09037376195192337, "Top": 0.17053565382957458}, "Polygon": [{"X": 0.09037376195192337, "Y": 0.17053568363189697}, {"X": 0.20527243614196777, "Y": 0.17053565382957458}, {"X": 0.20527251064777374, "Y": 0.18043623864650726}, {"X": 0.09037384390830994, "Y": 0.18043625354766846}]}, "Id": "23b5a8ed-b278-46d6-b046-f8ecbced1761", "Relationships": [{"Type": "CHILD", "Ids": ["21b5eddf-5052-4447-963f-499a522eab1a", "fe7e605d-2ae8-4bb1-8c38-578d4948e316"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "8/27/2025", "Geometry": {"BoundingBox": {"Width": 0.07254963368177414, "Height": 0.00982828252017498, "Left": 0.27570897340774536, "Top": 0.1705651581287384}, "Polygon": [{"X": 0.27570897340774536, "Y": 0.1705651879310608}, {"X": 0.3482585549354553, "Y": 0.1705651581287384}, {"X": 0.3482585847377777, "Y": 0.18039342761039734}, {"X": 0.27570903301239014, "Y": 0.18039344251155853}]}, "Id": "10696a7b-aedb-468e-9190-654929558c68", "Relationships": [{"Type": "CHILD", "Ids": ["8c373968-7571-426b-b196-6a70cedab9e5"]}]}, {"BlockType": "LINE", "Confidence": 99.9489517211914, "Text": "accurate measurement of the shipment covered hereby and", "Geometry": {"BoundingBox": {"Width": 0.43119490146636963, "Height": 0.012426240369677544, "Left": 0.46872955560684204, "Top": 0.17042244970798492}, "Polygon": [{"X": 0.46872955560684204, "Y": 0.17042255401611328}, {"X": 0.8999244570732117, "Y": 0.17042244970798492}, {"X": 0.8999243974685669, "Y": 0.18284861743450165}, {"X": 0.46872958540916443, "Y": 0.18284869194030762}]}, "Id": "f0456d90-64ee-44b0-8ceb-824372862c7b", "Relationships": [{"Type": "CHILD", "Ids": ["2eda2e5a-9f27-4265-9d91-12773ab79727", "3c61b573-5411-4800-b669-f435b019c4c7", "cc052ad7-4b21-4298-8da7-3dfb6031ad50", "1c3f837c-bd40-49e6-8052-e3573cfef745", "0bdfae3c-81a5-4679-9c90-4d11f66bcd97", "ee0d27d0-9971-46ce-beb1-75d9044bd80c", "64e6c090-e450-4d7a-a23f-fd9bb4e89a5d", "0242bd2c-5e4d-49dc-9278-cc1888aa9346"]}]}, {"BlockType": "LINE", "Confidence": 99.9568862915039, "Text": "Total Handling Units:", "Geometry": {"BoundingBox": {"Width": 0.14882785081863403, "Height": 0.012408669106662273, "Left": 0.08967648446559906, "Top": 0.18469646573066711}, "Polygon": [{"X": 0.08967648446559906, "Y": 0.1846964806318283}, {"X": 0.23850424587726593, "Y": 0.18469646573066711}, {"X": 0.2385043352842331, "Y": 0.19710512459278107}, {"X": 0.08967658132314682, "Y": 0.19710513949394226}]}, "Id": "dcfc4dbd-e517-4faf-af44-1a5af2424b48", "Relationships": [{"Type": "CHILD", "Ids": ["2e4ed363-206f-4a54-b2ee-e93d72a77513", "549f0443-0f53-477a-8f3a-14d9270b6a6e", "486dffeb-528c-4957-8b13-48790f8272d8"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.008760890923440456, "Height": 0.009753457270562649, "Left": 0.27571943402290344, "Top": 0.18495996296405792}, "Polygon": [{"X": 0.27571943402290344, "Y": 0.18495996296405792}, {"X": 0.2844802737236023, "Y": 0.18495996296405792}, {"X": 0.2844803035259247, "Y": 0.19471341371536255}, {"X": 0.2757194936275482, "Y": 0.19471341371536255}]}, "Id": "c5478fd7-13f7-4c04-b710-607b745230a7", "Relationships": [{"Type": "CHILD", "Ids": ["bc6e67b6-7b50-4a0a-b629-e0582fad601d"]}]}, {"BlockType": "LINE", "Confidence": 99.87919616699219, "Text": "complies with applicable laws and tariffs. The measurements", "Geometry": {"BoundingBox": {"Width": 0.4386003911495209, "Height": 0.012348606251180172, "Left": 0.46836331486701965, "Top": 0.18472057580947876}, "Polygon": [{"X": 0.46836331486701965, "Y": 0.18472063541412354}, {"X": 0.9069637060165405, "Y": 0.18472057580947876}, {"X": 0.9069637060165405, "Y": 0.19706915318965912}, {"X": 0.46836337447166443, "Y": 0.1970691829919815}]}, "Id": "d20bc9cc-239c-4927-a2ed-3f2fb745a662", "Relationships": [{"Type": "CHILD", "Ids": ["66a303ca-ddf8-42d5-ac66-c2ca431524c7", "6730cfab-81e1-4f82-a612-51a4d23d110e", "57e7a70d-f4ce-4028-ab17-97382460b6f5", "b9647dc2-a027-4d7e-8aa5-40ce57080bed", "1c7bedda-ab30-4885-b4b6-991f0b01b689", "e4a7035c-793e-4528-9cde-a80c6e551ddd", "aa6ba0a4-abd1-448b-8940-10e43fe8c2f3", "32979a2b-1718-4394-ab70-b9bd67508b12"]}]}, {"BlockType": "LINE", "Confidence": 99.96392822265625, "Text": "Total Cubic Feet:", "Geometry": {"BoundingBox": {"Width": 0.12182172387838364, "Height": 0.010067845694720745, "Left": 0.08971608430147171, "Top": 0.1989535689353943}, "Polygon": [{"X": 0.08971608430147171, "Y": 0.1989535689353943}, {"X": 0.21153774857521057, "Y": 0.1989535689353943}, {"X": 0.21153780817985535, "Y": 0.2090214043855667}, {"X": 0.08971616625785828, "Y": 0.2090214043855667}]}, "Id": "d9ff72a3-992f-4a72-8d8c-649894913044", "Relationships": [{"Type": "CHILD", "Ids": ["7a878de4-40e3-40f3-9cd5-db06fe6c8597", "cdaa4f3f-5c3c-49e6-913a-9c7e0a6c99d4", "b16dcbf9-590b-42c4-9bcf-a9b9572a5aff"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "101.00", "Geometry": {"BoundingBox": {"Width": 0.04828323423862457, "Height": 0.009912040084600449, "Left": 0.27700912952423096, "Top": 0.1990281045436859}, "Polygon": [{"X": 0.27700912952423096, "Y": 0.1990281045436859}, {"X": 0.32529231905937195, "Y": 0.1990281045436859}, {"X": 0.32529234886169434, "Y": 0.20894014835357666}, {"X": 0.27700918912887573, "Y": 0.20894014835357666}]}, "Id": "7d29fae6-9293-4e8b-8270-8244c28c4fb8", "Relationships": [{"Type": "CHILD", "Ids": ["a38b4c24-a7de-40f3-a8b5-d284cacb11a1"]}]}, {"BlockType": "LINE", "Confidence": 99.97518920898438, "Text": "were performed by an NTEP certified dimensioning device", "Geometry": {"BoundingBox": {"Width": 0.4212275743484497, "Height": 0.012405158951878548, "Left": 0.46767404675483704, "Top": 0.1989142894744873}, "Polygon": [{"X": 0.46767404675483704, "Y": 0.1989142894744873}, {"X": 0.8889015913009644, "Y": 0.1989142894744873}, {"X": 0.8889015913009644, "Y": 0.2113194465637207}, {"X": 0.4676740765571594, "Y": 0.21131941676139832}]}, "Id": "c1899538-2782-4bd6-a3cb-c3c051447497", "Relationships": [{"Type": "CHILD", "Ids": ["fa6784ec-1591-411f-bf44-7f9756806043", "b9721a7f-f002-4c98-8054-c4093ef8807a", "8c1d1793-90cf-4a73-9544-09d673b6cdb5", "00e2515b-0b58-42c0-b85b-470a7cbc83b6", "d7446cc8-c0fa-471a-a0dd-22f55fb2be64", "211d340e-0441-4708-86ad-a229a298df59", "d9f63fc1-a660-4d3d-b0f3-8035db6ff6f0", "53e983bc-3a4f-4a17-a8f4-480838959f66"]}]}, {"BlockType": "LINE", "Confidence": 99.81086730957031, "Text": "All measurements are in imperial inches.", "Geometry": {"BoundingBox": {"Width": 0.2935152053833008, "Height": 0.012462975457310677, "Left": 0.08903651684522629, "Top": 0.2164202481508255}, "Polygon": [{"X": 0.08903651684522629, "Y": 0.2164202481508255}, {"X": 0.3825516700744629, "Y": 0.21642029285430908}, {"X": 0.38255172967910767, "Y": 0.22888323664665222}, {"X": 0.08903661370277405, "Y": 0.22888317704200745}]}, "Id": "d8ca4488-8127-4853-9f32-cfc0608ddaf0", "Relationships": [{"Type": "CHILD", "Ids": ["2b5fc6cd-9c52-4595-b361-93214b07b7fe", "657394de-507c-4c5b-b824-1808f6b63c1f", "87e6323f-5d5c-431e-b0d4-ac4bc0412a0f", "b3fa2d86-1fa8-438f-92f8-cd3baee99067", "9806ef62-4f92-40c8-93cd-66a22763a89d", "27fffe37-ce4a-45c8-81ca-e87027a048ab"]}]}, {"BlockType": "LINE", "Confidence": 99.88796997070312, "Text": "installed at our Las Vegas, NV facility.", "Geometry": {"BoundingBox": {"Width": 0.2712945342063904, "Height": 0.012398160062730312, "Left": 0.4680542051792145, "Top": 0.2132255882024765}, "Polygon": [{"X": 0.4680542051792145, "Y": 0.2132255882024765}, {"X": 0.7393487691879272, "Y": 0.2132256180047989}, {"X": 0.7393487095832825, "Y": 0.22562375664710999}, {"X": 0.46805423498153687, "Y": 0.2256236970424652}]}, "Id": "233244cc-2df1-4595-af49-3403e20e7c12", "Relationships": [{"Type": "CHILD", "Ids": ["ce3cfaff-2fbc-44f6-bb03-1b72733e8621", "04cc8c92-636c-4f5b-8b5d-d1e5672ffecc", "0e962106-2a13-4a46-8f93-7f4c9bf88ca5", "2613db94-d3d9-4452-b086-1f4aace9f476", "e4f5a150-885c-4baa-ba46-0dab65edcf7f", "59504566-99d0-4915-bc09-522bce9b6cd8", "2efe03e9-cebe-4c52-8d28-3d2f110eb501"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Handling", "Geometry": {"BoundingBox": {"Width": 0.06887675076723099, "Height": 0.012431303970515728, "Left": 0.23234586417675018, "Top": 0.24983234703540802}, "Polygon": [{"X": 0.23234586417675018, "Y": 0.24983234703540802}, {"X": 0.3012225329875946, "Y": 0.2498323768377304}, {"X": 0.30122262239456177, "Y": 0.2622636556625366}, {"X": 0.23234593868255615, "Y": 0.26226362586021423}]}, "Id": "10f27c79-f169-4157-9087-597824975030", "Relationships": [{"Type": "CHILD", "Ids": ["83cec291-ed8a-4e57-846c-c39538444838"]}]}, {"BlockType": "LINE", "Confidence": 99.88069152832031, "Text": "No Top Loading", "Geometry": {"BoundingBox": {"Width": 0.12170939892530441, "Height": 0.012452810071408749, "Left": 0.5573778748512268, "Top": 0.24984553456306458}, "Polygon": [{"X": 0.5573778748512268, "Y": 0.24984553456306458}, {"X": 0.6790872812271118, "Y": 0.24984557926654816}, {"X": 0.6790872812271118, "Y": 0.2622983455657959}, {"X": 0.5573779344558716, "Y": 0.2622982859611511}]}, "Id": "504db9ba-7104-4354-a783-5375dd821231", "Relationships": [{"Type": "CHILD", "Ids": ["8d08c491-507f-4154-9be5-0426cb5f390b", "7f3259c7-59ef-4337-b0b1-2a74662a598b", "bfe4bee6-9f8d-4460-99a2-9efe57a3b7d5"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Manual", "Geometry": {"BoundingBox": {"Width": 0.055025774985551834, "Height": 0.010019499808549881, "Left": 0.7287557125091553, "Top": 0.24984855949878693}, "Polygon": [{"X": 0.7287557125091553, "Y": 0.24984855949878693}, {"X": 0.7837814688682556, "Y": 0.24984858930110931}, {"X": 0.7837814688682556, "Y": 0.2598680555820465}, {"X": 0.7287557125091553, "Y": 0.2598680257797241}]}, "Id": "df4d9420-8731-48cc-a4fd-c66375450e1d", "Relationships": [{"Type": "CHILD", "Ids": ["e0970d79-3224-471e-b289-3f5bd42a64d4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Units", "Geometry": {"BoundingBox": {"Width": 0.039839375764131546, "Height": 0.010068708099424839, "Left": 0.2467268407344818, "Top": 0.26403146982192993}, "Polygon": [{"X": 0.2467268407344818, "Y": 0.26403146982192993}, {"X": 0.2865661680698395, "Y": 0.26403146982192993}, {"X": 0.28656622767448425, "Y": 0.2741001844406128}, {"X": 0.2467269003391266, "Y": 0.2741001546382904}]}, "Id": "bf600f87-67a7-47e6-bb21-aaaeed9b1398", "Relationships": [{"Type": "CHILD", "Ids": ["491b2dba-1512-4fbb-b34b-505efbd452e9"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Length", "Geometry": {"BoundingBox": {"Width": 0.05361667647957802, "Height": 0.012520795688033104, "Left": 0.3339036703109741, "Top": 0.2640165090560913}, "Polygon": [{"X": 0.3339036703109741, "Y": 0.2640165090560913}, {"X": 0.38752028346061707, "Y": 0.2640165388584137}, {"X": 0.38752034306526184, "Y": 0.27653729915618896}, {"X": 0.3339037299156189, "Y": 0.2765372693538666}]}, "Id": "3ae50c40-8790-4648-8233-5a68fc128b2f", "Relationships": [{"Type": "CHILD", "Ids": ["7606a9f5-539a-4fa2-ab49-eff8b580c0d6"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.04530364274978638, "Height": 0.010086838155984879, "Left": 0.4165536165237427, "Top": 0.2639833390712738}, "Polygon": [{"X": 0.4165536165237427, "Y": 0.2639833390712738}, {"X": 0.46185722947120667, "Y": 0.2639833688735962}, {"X": 0.46185725927352905, "Y": 0.2740701735019684}, {"X": 0.41655364632606506, "Y": 0.2740701735019684}]}, "Id": "8f82c4cf-6e93-4d47-8ccf-2bbd93f4707c", "Relationships": [{"Type": "CHILD", "Ids": ["dfc3830a-0680-40cc-90e5-f373e457574d"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Height", "Geometry": {"BoundingBox": {"Width": 0.05077504739165306, "Height": 0.012835207395255566, "Left": 0.48785629868507385, "Top": 0.2638368010520935}, "Polygon": [{"X": 0.48785629868507385, "Y": 0.2638368010520935}, {"X": 0.5386313199996948, "Y": 0.2638368010520935}, {"X": 0.5386313199996948, "Y": 0.27667200565338135}, {"X": 0.48785632848739624, "Y": 0.27667197585105896}]}, "Id": "095132c0-5026-4cfc-8bd5-ba35590f8bae", "Relationships": [{"Type": "CHILD", "Ids": ["7dcfb1c9-8f5f-43a2-a23a-abb77c8fddcf"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "or Stacking", "Geometry": {"BoundingBox": {"Width": 0.08841029554605484, "Height": 0.012660820037126541, "Left": 0.5741220712661743, "Top": 0.26391875743865967}, "Polygon": [{"X": 0.5741220712661743, "Y": 0.26391875743865967}, {"X": 0.6625323295593262, "Y": 0.26391878724098206}, {"X": 0.662532389163971, "Y": 0.2765795588493347}, {"X": 0.5741220712661743, "Y": 0.27657949924468994}]}, "Id": "ee4896c3-2ea9-4828-93bb-15c85c6f361d", "Relationships": [{"Type": "CHILD", "Ids": ["31d12657-d968-488b-bf49-12c2f8c5b705", "659eefc7-d82c-429e-9991-d53e74831005"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Measurement", "Geometry": {"BoundingBox": {"Width": 0.10471069812774658, "Height": 0.009903480298817158, "Left": 0.7042692303657532, "Top": 0.2641919255256653}, "Polygon": [{"X": 0.7042692303657532, "Y": 0.2641919255256653}, {"X": 0.8089799284934998, "Y": 0.26419198513031006}, {"X": 0.8089799284934998, "Y": 0.27409541606903076}, {"X": 0.7042692303657532, "Y": 0.274095356464386}]}, "Id": "bcf6a15a-d92c-437c-9d38-e64cf6b69a6d", "Relationships": [{"Type": "CHILD", "Ids": ["0df4bcae-9b3f-4494-a9aa-1b55c62d7277"]}]}, {"BlockType": "LINE", "Confidence": 99.61834716796875, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.005637028254568577, "Height": 0.009765950962901115, "Left": 0.29924696683883667, "Top": 0.29453152418136597}, "Polygon": [{"X": 0.29924696683883667, "Y": 0.29453152418136597}, {"X": 0.3048839569091797, "Y": 0.29453152418136597}, {"X": 0.3048839867115021, "Y": 0.30429747700691223}, {"X": 0.29924702644348145, "Y": 0.30429744720458984}]}, "Id": "111cba91-4109-4376-b92a-793707417fe0", "Relationships": [{"Type": "CHILD", "Ids": ["d61342f7-b622-48e7-9d84-36587e327dde"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "41.00", "Geometry": {"BoundingBox": {"Width": 0.04107247665524483, "Height": 0.010041771456599236, "Left": 0.3453444242477417, "Top": 0.2943840026855469}, "Polygon": [{"X": 0.3453444242477417, "Y": 0.2943840026855469}, {"X": 0.38641685247421265, "Y": 0.29438403248786926}, {"X": 0.38641688227653503, "Y": 0.30442577600479126}, {"X": 0.3453444540500641, "Y": 0.30442574620246887}]}, "Id": "80a9232c-8c82-484b-9dfa-b18b2e94b7cd", "Relationships": [{"Type": "CHILD", "Ids": ["9d623027-5625-4e2d-a164-20a98c0aee63"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "35.00", "Geometry": {"BoundingBox": {"Width": 0.04045151546597481, "Height": 0.01000198908150196, "Left": 0.4217550754547119, "Top": 0.2943864166736603}, "Polygon": [{"X": 0.4217550754547119, "Y": 0.2943864166736603}, {"X": 0.46220657229423523, "Y": 0.29438644647598267}, {"X": 0.4622066020965576, "Y": 0.3043884038925171}, {"X": 0.4217551350593567, "Y": 0.3043883740901947}]}, "Id": "ddeaa5f9-817e-4ef8-b10e-705f84672c16", "Relationships": [{"Type": "CHILD", "Ids": ["532905a5-55a6-49c1-833b-cadc4c49405a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "62.50", "Geometry": {"BoundingBox": {"Width": 0.04057396575808525, "Height": 0.00998679269105196, "Left": 0.4989919662475586, "Top": 0.2943877577781677}, "Polygon": [{"X": 0.4989919662475586, "Y": 0.2943877577781677}, {"X": 0.539565920829773, "Y": 0.2943877875804901}, {"X": 0.539565920829773, "Y": 0.3043745458126068}, {"X": 0.498991996049881, "Y": 0.3043745160102844}]}, "Id": "fc733cd8-b0ff-4bed-8d0c-fe2c9ef3767b", "Relationships": [{"Type": "CHILD", "Ids": ["3e69536b-4219-43db-b571-b73d1896a141"]}]}, {"BlockType": "LINE", "Confidence": 88.9317626953125, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011251786723732948, "Height": 0.010424042120575905, "Left": 0.6168341040611267, "Top": 0.29412609338760376}, "Polygon": [{"X": 0.6168341040611267, "Y": 0.29412609338760376}, {"X": 0.6280858516693115, "Y": 0.29412612318992615}, {"X": 0.6280858516693115, "Y": 0.3045501410961151}, {"X": 0.6168341040611267, "Y": 0.3045501410961151}]}, "Id": "532fc59d-a5d5-4740-a1c7-57325f9ef1b1", "Relationships": [{"Type": "CHILD", "Ids": ["55887aba-e34b-49b8-a14c-2438106ba8b1"]}]}, {"BlockType": "LINE", "Confidence": 99.56951904296875, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.005585659295320511, "Height": 0.009834064170718193, "Left": 0.29925814270973206, "Top": 0.31366899609565735}, "Polygon": [{"X": 0.29925814270973206, "Y": 0.31366899609565735}, {"X": 0.3048437535762787, "Y": 0.31366899609565735}, {"X": 0.3048437833786011, "Y": 0.3235030472278595}, {"X": 0.29925820231437683, "Y": 0.3235030472278595}]}, "Id": "545415ac-49d3-47e8-b58e-d370759828bd", "Relationships": [{"Type": "CHILD", "Ids": ["786ba09e-ea33-4cc1-9540-ef2d9f7645e3"]}]}, {"BlockType": "LINE", "Confidence": 99.912109375, "Text": "41.00", "Geometry": {"BoundingBox": {"Width": 0.041077181696891785, "Height": 0.010297536849975586, "Left": 0.34537607431411743, "Top": 0.31346115469932556}, "Polygon": [{"X": 0.34537607431411743, "Y": 0.31346115469932556}, {"X": 0.38645321130752563, "Y": 0.31346121430397034}, {"X": 0.386453241109848, "Y": 0.32375869154930115}, {"X": 0.3453761041164398, "Y": 0.32375866174697876}]}, "Id": "2023a488-5189-4018-9808-e184d7305254", "Relationships": [{"Type": "CHILD", "Ids": ["721fffde-90db-42dd-8de3-9325a91e342a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "34.00", "Geometry": {"BoundingBox": {"Width": 0.04043677821755409, "Height": 0.010263390839099884, "Left": 0.42187240719795227, "Top": 0.3134312629699707}, "Polygon": [{"X": 0.42187240719795227, "Y": 0.3134312629699707}, {"X": 0.4623091518878937, "Y": 0.3134313225746155}, {"X": 0.46230918169021606, "Y": 0.3236946761608124}, {"X": 0.42187243700027466, "Y": 0.3236946165561676}]}, "Id": "ca160b21-97f0-45e3-931d-836b7558e316", "Relationships": [{"Type": "CHILD", "Ids": ["4b3863ef-b859-4162-88e5-b623fb220ad5"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "62.50", "Geometry": {"BoundingBox": {"Width": 0.04046221077442169, "Height": 0.010043453425168991, "Left": 0.49897482991218567, "Top": 0.3135414719581604}, "Polygon": [{"X": 0.49897482991218567, "Y": 0.3135414719581604}, {"X": 0.5394369959831238, "Y": 0.3135415017604828}, {"X": 0.5394370555877686, "Y": 0.3235849142074585}, {"X": 0.49897485971450806, "Y": 0.3235848844051361}]}, "Id": "aa56a335-309f-42e8-9226-47e159d12c11", "Relationships": [{"Type": "CHILD", "Ids": ["3c92aeb1-dede-455e-b4e6-a06f4ece13cc"]}]}, {"BlockType": "LINE", "Confidence": 88.92857360839844, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.011442029848694801, "Height": 0.01062542200088501, "Left": 0.6167930364608765, "Top": 0.3132188320159912}, "Polygon": [{"X": 0.6167930364608765, "Y": 0.3132188320159912}, {"X": 0.6282351016998291, "Y": 0.3132188618183136}, {"X": 0.6282351016998291, "Y": 0.3238442540168762}, {"X": 0.6167930960655212, "Y": 0.3238442540168762}]}, "Id": "5d5c6766-4e36-4c95-8a6f-2b57f02ac461", "Relationships": [{"Type": "CHILD", "Ids": ["46a5f82a-f9be-43da-b44f-0fb258a4030d"]}]}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "TForce", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.22124814987182617, "Height": 0.030413439497351646, "Left": 0.08821188658475876, "Top": 0.04618319496512413}, "Polygon": [{"X": 0.08821188658475876, "Y": 0.04618346318602562}, {"X": 0.3094598948955536, "Y": 0.04618319496512413}, {"X": 0.3094600439071655, "Y": 0.07659641653299332}, {"X": 0.08821213990449905, "Y": 0.07659663259983063}]}, "Id": "e684b6f7-f6bd-4f5d-b3ac-11e159d584f1"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10413113981485367, "Height": 0.01643381081521511, "Left": 0.19421155750751495, "Top": 0.08031381666660309}, "Polygon": [{"X": 0.19421155750751495, "Y": 0.08031391352415085}, {"X": 0.29834261536598206, "Y": 0.08031381666660309}, {"X": 0.2983427047729492, "Y": 0.09674753993749619}, {"X": 0.1942116618156433, "Y": 0.09674762189388275}]}, "Id": "7cc6e87a-046e-4a32-a122-70c7a9b25d72"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUBE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0631149560213089, "Height": 0.013874832540750504, "Left": 0.29764631390571594, "Top": 0.10975964367389679}, "Polygon": [{"X": 0.29764631390571594, "Y": 0.10975968837738037}, {"X": 0.36076122522354126, "Y": 0.10975964367389679}, {"X": 0.36076128482818604, "Y": 0.12363443523645401}, {"X": 0.2976464033126831, "Y": 0.123634472489357}]}, "Id": "c9a3f271-194c-4f1d-af5e-9d4041a6f3b2"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "MEASUREMENT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1784958392381668, "Height": 0.01370830088853836, "Left": 0.36882761120796204, "Top": 0.10985192656517029}, "Polygon": [{"X": 0.36882761120796204, "Y": 0.10985205322504044}, {"X": 0.5473234057426453, "Y": 0.10985192656517029}, {"X": 0.54732346534729, "Y": 0.1235601156949997}, {"X": 0.3688276708126068, "Y": 0.12356022745370865}]}, "Id": "30bd4c94-c7ca-42ab-bcb6-74a5ac298255"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "CERTIFICATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14805328845977783, "Height": 0.01397494226694107, "Left": 0.5544643998146057, "Top": 0.10961568355560303}, "Polygon": [{"X": 0.5544643998146057, "Y": 0.10961579531431198}, {"X": 0.7025176882743835, "Y": 0.10961568355560303}, {"X": 0.7025176882743835, "Y": 0.12359053641557693}, {"X": 0.5544643998146057, "Y": 0.1235906258225441}]}, "Id": "61ff7776-7f00-47ef-b29e-ed0c0c4a0a91"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05061102658510208, "Height": 0.012321864254772663, "Left": 0.09035287797451019, "Top": 0.15650367736816406}, "Polygon": [{"X": 0.09035287797451019, "Y": 0.15650369226932526}, {"X": 0.14096380770206451, "Y": 0.15650367736816406}, {"X": 0.14096389710903168, "Y": 0.16882552206516266}, {"X": 0.09035297483205795, "Y": 0.16882553696632385}]}, "Id": "6cffdc0f-28fe-49be-86f0-d9db83f31dd2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02082221955060959, "Height": 0.00998956523835659, "Left": 0.14581963419914246, "Top": 0.15652425587177277}, "Polygon": [{"X": 0.14581963419914246, "Y": 0.15652427077293396}, {"X": 0.16664178669452667, "Y": 0.15652425587177277}, {"X": 0.16664186120033264, "Y": 0.1665138155221939}, {"X": 0.14581970870494843, "Y": 0.1665138304233551}]}, "Id": "082dbec1-2191-4939-b890-2c56f000e93e"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06062064319849014, "Height": 0.010124851949512959, "Left": 0.17238402366638184, "Top": 0.15651139616966248}, "Polygon": [{"X": 0.17238402366638184, "Y": 0.15651141107082367}, {"X": 0.2330045998096466, "Y": 0.15651139616966248}, {"X": 0.23300467431545258, "Y": 0.16663622856140137}, {"X": 0.1723840981721878, "Y": 0.16663624346256256}]}, "Id": "e55a7cdd-d065-4b45-9ede-cf78ca4e9e61"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "413999202", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0818236768245697, "Height": 0.010035033337771893, "Left": 0.2754664421081543, "Top": 0.15658408403396606}, "Polygon": [{"X": 0.2754664421081543, "Y": 0.15658409893512726}, {"X": 0.3572900891304016, "Y": 0.15658408403396606}, {"X": 0.357290118932724, "Y": 0.16661909222602844}, {"X": 0.2754665017127991, "Y": 0.16661910712718964}]}, "Id": "9cac7324-f806-42c1-8ba7-f7cd0c3f04b5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "The", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02807893231511116, "Height": 0.00997469574213028, "Left": 0.4680711030960083, "Top": 0.15624648332595825}, "Polygon": [{"X": 0.4680711030960083, "Y": 0.15624648332595825}, {"X": 0.49615001678466797, "Y": 0.15624648332595825}, {"X": 0.49615004658699036, "Y": 0.16622117161750793}, {"X": 0.4680711328983307, "Y": 0.16622117161750793}]}, "Id": "32a20ab2-bbb5-4e59-bcfd-e3365d73d4de"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "information", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08035986125469208, "Height": 0.010026238858699799, "Left": 0.5010086894035339, "Top": 0.1561872959136963}, "Polygon": [{"X": 0.5010086894035339, "Y": 0.15618732571601868}, {"X": 0.5813685059547424, "Y": 0.1561872959136963}, {"X": 0.5813685655593872, "Y": 0.1662135124206543}, {"X": 0.5010086894035339, "Y": 0.1662135273218155}]}, "Id": "43941dd2-ca37-46f9-8abc-a4fd60cf1939"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "set", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02219264768064022, "Height": 0.009509127587080002, "Left": 0.5862090587615967, "Top": 0.15671007335186005}, "Polygon": [{"X": 0.5862090587615967, "Y": 0.15671007335186005}, {"X": 0.6084017157554626, "Y": 0.15671007335186005}, {"X": 0.6084017157554626, "Y": 0.16621918976306915}, {"X": 0.5862090587615967, "Y": 0.16621918976306915}]}, "Id": "e5e57aa7-87cb-4210-b844-24e05f12ad40"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "forth", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03242804855108261, "Height": 0.010108818300068378, "Left": 0.6124477982521057, "Top": 0.156129851937294}, "Polygon": [{"X": 0.6124477982521057, "Y": 0.156129851937294}, {"X": 0.6448758244514465, "Y": 0.156129851937294}, {"X": 0.6448758840560913, "Y": 0.16623865067958832}, {"X": 0.6124477982521057, "Y": 0.1662386655807495}]}, "Id": "c9227693-f019-4ab4-a4b1-329dd4b07d05"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "within", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04193972051143646, "Height": 0.009977197274565697, "Left": 0.649177074432373, "Top": 0.15619318187236786}, "Polygon": [{"X": 0.649177074432373, "Y": 0.15619319677352905}, {"X": 0.6911168098449707, "Y": 0.15619318187236786}, {"X": 0.6911168098449707, "Y": 0.1661703735589981}, {"X": 0.649177074432373, "Y": 0.1661703884601593}]}, "Id": "07b3fa55-12f8-4554-8b83-4d4b90b88307"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025163447484374046, "Height": 0.009872687049210072, "Left": 0.6958581805229187, "Top": 0.15627935528755188}, "Polygon": [{"X": 0.6958581805229187, "Y": 0.15627937018871307}, {"X": 0.7210215926170349, "Y": 0.15627935528755188}, {"X": 0.7210215926170349, "Y": 0.16615203022956848}, {"X": 0.6958581805229187, "Y": 0.16615204513072968}]}, "Id": "8ccccc8d-dd61-41b4-a664-c2ef32f83ec8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "document", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07218492031097412, "Height": 0.009944508783519268, "Left": 0.7262552976608276, "Top": 0.15627601742744446}, "Polygon": [{"X": 0.7262552976608276, "Y": 0.15627604722976685}, {"X": 0.7984402179718018, "Y": 0.15627601742744446}, {"X": 0.7984402179718018, "Y": 0.1662205010652542}, {"X": 0.7262552976608276, "Y": 0.1662205308675766}]}, "Id": "1223e619-6fa8-4683-994d-0d2528b0c72c"}, {"BlockType": "WORD", "Confidence": 99.20280456542969, "Text": "constitutes", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07820049673318863, "Height": 0.009785627014935017, "Left": 0.8023685812950134, "Top": 0.1564486026763916}, "Polygon": [{"X": 0.8023685812950134, "Y": 0.156448632478714}, {"X": 0.8805691003799438, "Y": 0.1564486026763916}, {"X": 0.8805690407752991, "Y": 0.16623419523239136}, {"X": 0.8023685812950134, "Y": 0.16623422503471375}]}, "Id": "43a450ac-03d4-42bd-9d37-cedb47385763"}, {"BlockType": "WORD", "Confidence": 99.83159637451172, "Text": "an", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017782554030418396, "Height": 0.008016824722290039, "Left": 0.885047972202301, "Top": 0.1582973450422287}, "Polygon": [{"X": 0.885047972202301, "Y": 0.1582973450422287}, {"X": 0.9028304815292358, "Y": 0.1582973450422287}, {"X": 0.9028304815292358, "Y": 0.16631415486335754}, {"X": 0.885047972202301, "Y": 0.16631416976451874}]}, "Id": "847a2567-991f-4381-aff9-ea8a52daac11"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Measured", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07173482328653336, "Height": 0.009790362790226936, "Left": 0.09037376195192337, "Top": 0.17063722014427185}, "Polygon": [{"X": 0.09037376195192337, "Y": 0.17063724994659424}, {"X": 0.16210851073265076, "Y": 0.17063722014427185}, {"X": 0.16210858523845673, "Y": 0.18042758107185364}, {"X": 0.09037384390830994, "Y": 0.18042759597301483}]}, "Id": "21b5eddf-5052-4447-963f-499a522eab1a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037603385746479034, "Height": 0.009900596924126148, "Left": 0.1676691174507141, "Top": 0.17053565382957458}, "Polygon": [{"X": 0.1676691174507141, "Y": 0.17053565382957458}, {"X": 0.20527243614196777, "Y": 0.17053565382957458}, {"X": 0.20527251064777374, "Y": 0.18043623864650726}, {"X": 0.16766919195652008, "Y": 0.18043623864650726}]}, "Id": "fe7e605d-2ae8-4bb1-8c38-578d4948e316"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "8/27/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07254963368177414, "Height": 0.00982828252017498, "Left": 0.27570897340774536, "Top": 0.1705651581287384}, "Polygon": [{"X": 0.27570897340774536, "Y": 0.1705651879310608}, {"X": 0.3482585549354553, "Y": 0.1705651581287384}, {"X": 0.3482585847377777, "Y": 0.18039342761039734}, {"X": 0.27570903301239014, "Y": 0.18039344251155853}]}, "Id": "8c373968-7571-426b-b196-6a70cedab9e5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "accurate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06194652244448662, "Height": 0.009578380733728409, "Left": 0.46872955560684204, "Top": 0.17089895904064178}, "Polygon": [{"X": 0.46872955560684204, "Y": 0.17089897394180298}, {"X": 0.5306760668754578, "Y": 0.17089895904064178}, {"X": 0.5306760668754578, "Y": 0.1804773360490799}, {"X": 0.46872958540916443, "Y": 0.1804773509502411}]}, "Id": "2eda2e5a-9f27-4265-9d91-12773ab79727"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "measurement", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09924888610839844, "Height": 0.009364229626953602, "Left": 0.5357639789581299, "Top": 0.1711030900478363}, "Polygon": [{"X": 0.5357639789581299, "Y": 0.1711031198501587}, {"X": 0.6350128650665283, "Y": 0.1711030900478363}, {"X": 0.6350128650665283, "Y": 0.18046730756759644}, {"X": 0.5357639789581299, "Y": 0.18046732246875763}]}, "Id": "3c61b573-5411-4800-b669-f435b019c4c7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014412926509976387, "Height": 0.009924673475325108, "Left": 0.6399765014648438, "Top": 0.1704225093126297}, "Polygon": [{"X": 0.6399765014648438, "Y": 0.1704225093126297}, {"X": 0.6543894410133362, "Y": 0.1704225093126297}, {"X": 0.6543894410133362, "Y": 0.18034717440605164}, {"X": 0.6399765014648438, "Y": 0.18034718930721283}]}, "Id": "cc052ad7-4b21-4298-8da7-3dfb6031ad50"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02262936905026436, "Height": 0.009738423861563206, "Left": 0.6577854156494141, "Top": 0.17059168219566345}, "Polygon": [{"X": 0.6577854156494141, "Y": 0.17059168219566345}, {"X": 0.6804147958755493, "Y": 0.17059168219566345}, {"X": 0.6804147958755493, "Y": 0.1803300976753235}, {"X": 0.6577854156494141, "Y": 0.1803300976753235}]}, "Id": "1c3f837c-bd40-49e6-8052-e3573cfef745"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06634251773357391, "Height": 0.012133250944316387, "Left": 0.6851775050163269, "Top": 0.17052389681339264}, "Polygon": [{"X": 0.6851775050163269, "Y": 0.17052391171455383}, {"X": 0.751520037651062, "Y": 0.17052389681339264}, {"X": 0.751520037651062, "Y": 0.1826571375131607}, {"X": 0.6851775646209717, "Y": 0.1826571375131607}]}, "Id": "0bdfae3c-81a5-4679-9c90-4d11f66bcd97"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "covered", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057522255927324295, "Height": 0.009867106564342976, "Left": 0.7564395666122437, "Top": 0.17052413523197174}, "Polygon": [{"X": 0.7564395666122437, "Y": 0.17052415013313293}, {"X": 0.8139618039131165, "Y": 0.17052413523197174}, {"X": 0.8139618039131165, "Y": 0.18039123713970184}, {"X": 0.7564395666122437, "Y": 0.18039125204086304}]}, "Id": "ee0d27d0-9971-46ce-beb1-75d9044bd80c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "hereby", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049375299364328384, "Height": 0.012370657175779343, "Left": 0.8193055391311646, "Top": 0.1704779714345932}, "Polygon": [{"X": 0.8193055391311646, "Y": 0.1704779863357544}, {"X": 0.8686808347702026, "Y": 0.1704779714345932}, {"X": 0.8686807751655579, "Y": 0.18284863233566284}, {"X": 0.8193055391311646, "Y": 0.18284863233566284}]}, "Id": "64e6c090-e450-4d7a-a23f-fd9bb4e89a5d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026624873280525208, "Height": 0.009869672358036041, "Left": 0.8732995986938477, "Top": 0.170578271150589}, "Polygon": [{"X": 0.8732995986938477, "Y": 0.17057828605175018}, {"X": 0.8999244570732117, "Y": 0.170578271150589}, {"X": 0.8999243974685669, "Y": 0.18044793605804443}, {"X": 0.8732995986938477, "Y": 0.18044795095920563}]}, "Id": "0242bd2c-5e4d-49dc-9278-cc1888aa9346"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03430713713169098, "Height": 0.010047473013401031, "Left": 0.08967648446559906, "Top": 0.1846964806318283}, "Polygon": [{"X": 0.08967648446559906, "Y": 0.1846964806318283}, {"X": 0.12398353964090347, "Y": 0.1846964806318283}, {"X": 0.12398362159729004, "Y": 0.19474394619464874}, {"X": 0.08967656642198563, "Y": 0.19474396109580994}]}, "Id": "2e4ed363-206f-4a54-b2ee-e93d72a77513"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Handling", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06315980851650238, "Height": 0.012377461418509483, "Left": 0.12970617413520813, "Top": 0.18472766876220703}, "Polygon": [{"X": 0.12970617413520813, "Y": 0.18472768366336823}, {"X": 0.19286589324474335, "Y": 0.18472766876220703}, {"X": 0.1928659826517105, "Y": 0.19710512459278107}, {"X": 0.1297062784433365, "Y": 0.19710513949394226}]}, "Id": "549f0443-0f53-477a-8f3a-14d9270b6a6e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Units:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04009060934185982, "Height": 0.00999271497130394, "Left": 0.1984136998653412, "Top": 0.18470434844493866}, "Polygon": [{"X": 0.1984136998653412, "Y": 0.18470434844493866}, {"X": 0.23850424587726593, "Y": 0.18470434844493866}, {"X": 0.2385043203830719, "Y": 0.1946970522403717}, {"X": 0.19841377437114716, "Y": 0.1946970522403717}]}, "Id": "486dffeb-528c-4957-8b13-48790f8272d8"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "2", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008760890923440456, "Height": 0.009753457270562649, "Left": 0.27571943402290344, "Top": 0.18495996296405792}, "Polygon": [{"X": 0.27571943402290344, "Y": 0.18495996296405792}, {"X": 0.2844802737236023, "Y": 0.18495996296405792}, {"X": 0.2844803035259247, "Y": 0.19471341371536255}, {"X": 0.2757194936275482, "Y": 0.19471341371536255}]}, "Id": "bc6e67b6-7b50-4a0a-b629-e0582fad601d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "complies", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0635998323559761, "Height": 0.012348553165793419, "Left": 0.46836331486701965, "Top": 0.18472062051296234}, "Polygon": [{"X": 0.46836331486701965, "Y": 0.18472063541412354}, {"X": 0.5319631099700928, "Y": 0.18472062051296234}, {"X": 0.5319631695747375, "Y": 0.1970691680908203}, {"X": 0.46836337447166443, "Y": 0.1970691829919815}]}, "Id": "66a303ca-ddf8-42d5-ac66-c2ca431524c7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "with", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028926702216267586, "Height": 0.009942367672920227, "Left": 0.5365580916404724, "Top": 0.18472245335578918}, "Polygon": [{"X": 0.5365580916404724, "Y": 0.18472245335578918}, {"X": 0.5654847621917725, "Y": 0.18472245335578918}, {"X": 0.5654847621917725, "Y": 0.1946648210287094}, {"X": 0.5365580916404724, "Y": 0.1946648210287094}]}, "Id": "6730cfab-81e1-4f82-a612-51a4d23d110e"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "applicable", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07325734943151474, "Height": 0.012268010526895523, "Left": 0.5709918737411499, "Top": 0.18476620316505432}, "Polygon": [{"X": 0.5709918737411499, "Y": 0.18476620316505432}, {"X": 0.6442492008209229, "Y": 0.18476620316505432}, {"X": 0.6442492008209229, "Y": 0.19703420996665955}, {"X": 0.5709918737411499, "Y": 0.19703420996665955}]}, "Id": "57e7a70d-f4ce-4028-ab17-97382460b6f5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "laws", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03180889040231705, "Height": 0.009828649461269379, "Left": 0.6493549346923828, "Top": 0.18479759991168976}, "Polygon": [{"X": 0.6493549346923828, "Y": 0.18479759991168976}, {"X": 0.6811637878417969, "Y": 0.18479759991168976}, {"X": 0.6811637878417969, "Y": 0.19462624192237854}, {"X": 0.6493549346923828, "Y": 0.19462624192237854}]}, "Id": "b9647dc2-a027-4d7e-8aa5-40ce57080bed"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026406966149806976, "Height": 0.009759617038071156, "Left": 0.6863969564437866, "Top": 0.1848764568567276}, "Polygon": [{"X": 0.6863969564437866, "Y": 0.1848764568567276}, {"X": 0.7128039002418518, "Y": 0.1848764568567276}, {"X": 0.7128039002418518, "Y": 0.19463607668876648}, {"X": 0.6863969564437866, "Y": 0.19463607668876648}]}, "Id": "1c7bedda-ab30-4885-b4b6-991f0b01b689"}, {"BlockType": "WORD", "Confidence": 99.16294860839844, "Text": "tariffs.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043663933873176575, "Height": 0.009841620922088623, "Left": 0.717356264591217, "Top": 0.18476471304893494}, "Polygon": [{"X": 0.717356264591217, "Y": 0.18476472795009613}, {"X": 0.7610201835632324, "Y": 0.18476471304893494}, {"X": 0.7610201835632324, "Y": 0.19460633397102356}, {"X": 0.717356264591217, "Y": 0.19460633397102356}]}, "Id": "e4a7035c-793e-4528-9cde-a80c6e551ddd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "The", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02814778871834278, "Height": 0.009821453131735325, "Left": 0.7661975026130676, "Top": 0.18474312126636505}, "Polygon": [{"X": 0.7661975026130676, "Y": 0.18474312126636505}, {"X": 0.7943453192710876, "Y": 0.18474312126636505}, {"X": 0.7943452596664429, "Y": 0.1945645660161972}, {"X": 0.7661975026130676, "Y": 0.1945645660161972}]}, "Id": "aa6ba0a4-abd1-448b-8940-10e43fe8c2f3"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "measurements", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10736259073019028, "Height": 0.009351983666419983, "Left": 0.799601137638092, "Top": 0.18526126444339752}, "Polygon": [{"X": 0.799601137638092, "Y": 0.18526127934455872}, {"X": 0.9069637060165405, "Y": 0.18526126444339752}, {"X": 0.9069637060165405, "Y": 0.1946132481098175}, {"X": 0.799601137638092, "Y": 0.1946132481098175}]}, "Id": "32979a2b-1718-4394-ab70-b9bd67508b12"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03421145677566528, "Height": 0.010067839175462723, "Left": 0.08971608430147171, "Top": 0.1989535689353943}, "Polygon": [{"X": 0.08971608430147171, "Y": 0.1989535689353943}, {"X": 0.12392746657133102, "Y": 0.1989535689353943}, {"X": 0.123927541077137, "Y": 0.2090214043855667}, {"X": 0.08971616625785828, "Y": 0.2090214043855667}]}, "Id": "7a878de4-40e3-40f3-9cd5-db06fe6c8597"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Cubic", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04155715927481651, "Height": 0.00980505719780922, "Left": 0.12919770181179047, "Top": 0.1990552395582199}, "Polygon": [{"X": 0.12919770181179047, "Y": 0.1990552395582199}, {"X": 0.1707547903060913, "Y": 0.1990552395582199}, {"X": 0.17075486481189728, "Y": 0.20886030793190002}, {"X": 0.12919777631759644, "Y": 0.20886029303073883}]}, "Id": "cdaa4f3f-5c3c-49e6-913a-9c7e0a6c99d4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Feet:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035639937967061996, "Height": 0.009885675273835659, "Left": 0.17589786648750305, "Top": 0.1990971863269806}, "Polygon": [{"X": 0.17589786648750305, "Y": 0.1990971863269806}, {"X": 0.21153774857521057, "Y": 0.1990971863269806}, {"X": 0.21153780817985535, "Y": 0.20898286998271942}, {"X": 0.17589794099330902, "Y": 0.20898285508155823}]}, "Id": "b16dcbf9-590b-42c4-9bcf-a9b9572a5aff"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "101.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04828323423862457, "Height": 0.009912040084600449, "Left": 0.27700912952423096, "Top": 0.1990281045436859}, "Polygon": [{"X": 0.27700912952423096, "Y": 0.1990281045436859}, {"X": 0.32529231905937195, "Y": 0.1990281045436859}, {"X": 0.32529234886169434, "Y": 0.20894014835357666}, {"X": 0.27700918912887573, "Y": 0.20894014835357666}]}, "Id": "a38b4c24-a7de-40f3-a8b5-d284cacb11a1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "were", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03566162660717964, "Height": 0.007773151155561209, "Left": 0.46767404675483704, "Top": 0.20118246972560883}, "Polygon": [{"X": 0.46767404675483704, "Y": 0.20118246972560883}, {"X": 0.5033356547355652, "Y": 0.20118246972560883}, {"X": 0.5033356547355652, "Y": 0.20895563066005707}, {"X": 0.4676740765571594, "Y": 0.20895563066005707}]}, "Id": "fa6784ec-1591-411f-bf44-7f9756806043"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "performed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07384908199310303, "Height": 0.012254340574145317, "Left": 0.5085119009017944, "Top": 0.1990141123533249}, "Polygon": [{"X": 0.5085119009017944, "Y": 0.19901412725448608}, {"X": 0.5823609828948975, "Y": 0.1990141123533249}, {"X": 0.5823609828948975, "Y": 0.21126845479011536}, {"X": 0.5085119605064392, "Y": 0.21126845479011536}]}, "Id": "b9721a7f-f002-4c98-8054-c4093ef8807a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "by", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017019785940647125, "Height": 0.012183863669633865, "Left": 0.5874137878417969, "Top": 0.19904068112373352}, "Polygon": [{"X": 0.5874137878417969, "Y": 0.19904068112373352}, {"X": 0.6044335961341858, "Y": 0.19904068112373352}, {"X": 0.6044335961341858, "Y": 0.2112245410680771}, {"X": 0.5874138474464417, "Y": 0.2112245410680771}]}, "Id": "8c1d1793-90cf-4a73-9544-09d673b6cdb5"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "an", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01760643534362316, "Height": 0.007845001295208931, "Left": 0.6089586615562439, "Top": 0.20114664733409882}, "Polygon": [{"X": 0.6089586615562439, "Y": 0.20114664733409882}, {"X": 0.6265650987625122, "Y": 0.20114664733409882}, {"X": 0.6265650987625122, "Y": 0.2089916467666626}, {"X": 0.6089586615562439, "Y": 0.2089916467666626}]}, "Id": "00e2515b-0b58-42c0-b85b-470a7cbc83b6"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "NTEP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042733658105134964, "Height": 0.009948374703526497, "Left": 0.6321441531181335, "Top": 0.1989142894744873}, "Polygon": [{"X": 0.6321441531181335, "Y": 0.1989142894744873}, {"X": 0.6748778223991394, "Y": 0.1989142894744873}, {"X": 0.6748778223991394, "Y": 0.20886266231536865}, {"X": 0.6321441531181335, "Y": 0.20886266231536865}]}, "Id": "d7446cc8-c0fa-471a-a0dd-22f55fb2be64"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "certified", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056706853210926056, "Height": 0.009900959208607674, "Left": 0.6796886920928955, "Top": 0.19899998605251312}, "Polygon": [{"X": 0.6796886920928955, "Y": 0.19899998605251312}, {"X": 0.736395537853241, "Y": 0.19899998605251312}, {"X": 0.736395537853241, "Y": 0.20890094339847565}, {"X": 0.6796886920928955, "Y": 0.20890094339847565}]}, "Id": "211d340e-0441-4708-86ad-a229a298df59"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "dimensioning", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09539848566055298, "Height": 0.012392922304570675, "Left": 0.7414181232452393, "Top": 0.19892652332782745}, "Polygon": [{"X": 0.7414181232452393, "Y": 0.19892652332782745}, {"X": 0.8368166089057922, "Y": 0.19892652332782745}, {"X": 0.8368166089057922, "Y": 0.2113194316625595}, {"X": 0.7414181232452393, "Y": 0.2113194316625595}]}, "Id": "d9f63fc1-a660-4d3d-b0f3-8035db6ff6f0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "device", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046764202415943146, "Height": 0.009913039393723011, "Left": 0.8421373963356018, "Top": 0.19898787140846252}, "Polygon": [{"X": 0.8421373963356018, "Y": 0.19898787140846252}, {"X": 0.8889015913009644, "Y": 0.19898787140846252}, {"X": 0.8889015913009644, "Y": 0.20890091359615326}, {"X": 0.8421373963356018, "Y": 0.20890091359615326}]}, "Id": "53e983bc-3a4f-4a17-a8f4-480838959f66"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "All", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01825699210166931, "Height": 0.009973503649234772, "Left": 0.08903651684522629, "Top": 0.21662107110023499}, "Polygon": [{"X": 0.08903651684522629, "Y": 0.21662107110023499}, {"X": 0.10729342699050903, "Y": 0.21662107110023499}, {"X": 0.1072935089468956, "Y": 0.22659456729888916}, {"X": 0.08903659880161285, "Y": 0.22659456729888916}]}, "Id": "2b5fc6cd-9c52-4595-b361-93214b07b7fe"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "measurements", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1070723906159401, "Height": 0.009645996615290642, "Left": 0.11282934993505478, "Top": 0.2169109582901001}, "Polygon": [{"X": 0.11282934993505478, "Y": 0.2169109582901001}, {"X": 0.2199016809463501, "Y": 0.2169109731912613}, {"X": 0.21990174055099487, "Y": 0.2265569567680359}, {"X": 0.11282942444086075, "Y": 0.2265569418668747}]}, "Id": "657394de-507c-4c5b-b824-1808f6b63c1f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "are", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02299719862639904, "Height": 0.007850960828363895, "Left": 0.2248644232749939, "Top": 0.2186817079782486}, "Polygon": [{"X": 0.2248644232749939, "Y": 0.2186817079782486}, {"X": 0.2478615790605545, "Y": 0.2186817079782486}, {"X": 0.24786162376403809, "Y": 0.22653266787528992}, {"X": 0.22486448287963867, "Y": 0.22653266787528992}]}, "Id": "87e6323f-5d5c-431e-b0d4-ac4bc0412a0f"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "in", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011930550448596478, "Height": 0.010018778033554554, "Left": 0.25305524468421936, "Top": 0.21650178730487823}, "Polygon": [{"X": 0.25305524468421936, "Y": 0.21650178730487823}, {"X": 0.26498574018478394, "Y": 0.21650178730487823}, {"X": 0.2649857997894287, "Y": 0.22652055323123932}, {"X": 0.25305530428886414, "Y": 0.22652055323123932}]}, "Id": "b3fa2d86-1fa8-438f-92f8-cd3baee99067"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "imperial", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05664956197142601, "Height": 0.012462945654988289, "Left": 0.27023667097091675, "Top": 0.2164202779531479}, "Polygon": [{"X": 0.27023667097091675, "Y": 0.2164202779531479}, {"X": 0.3268861770629883, "Y": 0.2164202779531479}, {"X": 0.32688623666763306, "Y": 0.22888322174549103}, {"X": 0.2702367603778839, "Y": 0.22888320684432983}]}, "Id": "9806ef62-4f92-40c8-93cd-66a22763a89d"}, {"BlockType": "WORD", "Confidence": 99.40210723876953, "Text": "inches.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05042961984872818, "Height": 0.010183683596551418, "Left": 0.3321220874786377, "Top": 0.21649332344532013}, "Polygon": [{"X": 0.3321220874786377, "Y": 0.21649332344532013}, {"X": 0.3825516700744629, "Y": 0.21649332344532013}, {"X": 0.38255172967910767, "Y": 0.22667700052261353}, {"X": 0.33212214708328247, "Y": 0.22667700052261353}]}, "Id": "27fffe37-ce4a-45c8-81ca-e87027a048ab"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "installed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059762317687273026, "Height": 0.009959578514099121, "Left": 0.4680542051792145, "Top": 0.21326997876167297}, "Polygon": [{"X": 0.4680542051792145, "Y": 0.21326997876167297}, {"X": 0.5278164744377136, "Y": 0.21326997876167297}, {"X": 0.5278165340423584, "Y": 0.2232295572757721}, {"X": 0.46805423498153687, "Y": 0.2232295423746109}]}, "Id": "ce3cfaff-2fbc-44f6-bb03-1b72733e8621"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "at", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014122296124696732, "Height": 0.00965103879570961, "Left": 0.5326583385467529, "Top": 0.21365493535995483}, "Polygon": [{"X": 0.5326583385467529, "Y": 0.21365493535995483}, {"X": 0.5467805862426758, "Y": 0.21365495026111603}, {"X": 0.5467806458473206, "Y": 0.22330598533153534}, {"X": 0.5326583385467529, "Y": 0.22330598533153534}]}, "Id": "04cc8c92-636c-4f5b-8b5d-d1e5672ffecc"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "our", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02382570505142212, "Height": 0.007726161740720272, "Left": 0.5510280132293701, "Top": 0.21540337800979614}, "Polygon": [{"X": 0.5510280132293701, "Y": 0.21540337800979614}, {"X": 0.5748537182807922, "Y": 0.21540337800979614}, {"X": 0.5748537182807922, "Y": 0.2231295257806778}, {"X": 0.5510280728340149, "Y": 0.2231295257806778}]}, "Id": "0e962106-2a13-4a46-8f93-7f4c9bf88ca5"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Las", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025786450132727623, "Height": 0.009947044774889946, "Left": 0.5792256593704224, "Top": 0.2133079171180725}, "Polygon": [{"X": 0.5792256593704224, "Y": 0.2133079171180725}, {"X": 0.6050121188163757, "Y": 0.2133079320192337}, {"X": 0.6050121188163757, "Y": 0.2232549637556076}, {"X": 0.5792256593704224, "Y": 0.2232549637556076}]}, "Id": "2613db94-d3d9-4452-b086-1f4aace9f476"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Vegas,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049331050366163254, "Height": 0.012398134917020798, "Left": 0.6094628572463989, "Top": 0.2132256031036377}, "Polygon": [{"X": 0.6094628572463989, "Y": 0.2132256031036377}, {"X": 0.6587938666343689, "Y": 0.2132256031036377}, {"X": 0.6587938666343689, "Y": 0.2256237417459488}, {"X": 0.6094628572463989, "Y": 0.2256237268447876}]}, "Id": "e4f5a150-885c-4baa-ba46-0dab65edcf7f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "NV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022303182631731033, "Height": 0.009928897023200989, "Left": 0.6648447513580322, "Top": 0.21324686706066132}, "Polygon": [{"X": 0.6648447513580322, "Y": 0.21324686706066132}, {"X": 0.6871479153633118, "Y": 0.2132468819618225}, {"X": 0.6871479153633118, "Y": 0.2231757640838623}, {"X": 0.6648447513580322, "Y": 0.2231757640838623}]}, "Id": "59504566-99d0-4915-bc09-522bce9b6cd8"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "facility.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04775981977581978, "Height": 0.012288546189665794, "Left": 0.6915889382362366, "Top": 0.21333365142345428}, "Polygon": [{"X": 0.6915889382362366, "Y": 0.21333365142345428}, {"X": 0.7393487691879272, "Y": 0.21333366632461548}, {"X": 0.7393487095832825, "Y": 0.22562220692634583}, {"X": 0.6915889382362366, "Y": 0.22562219202518463}]}, "Id": "2efe03e9-cebe-4c52-8d28-3d2f110eb501"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Handling", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06887675076723099, "Height": 0.012431303970515728, "Left": 0.23234586417675018, "Top": 0.24983234703540802}, "Polygon": [{"X": 0.23234586417675018, "Y": 0.24983234703540802}, {"X": 0.3012225329875946, "Y": 0.2498323768377304}, {"X": 0.30122262239456177, "Y": 0.2622636556625366}, {"X": 0.23234593868255615, "Y": 0.26226362586021423}]}, "Id": "83cec291-ed8a-4e57-846c-c39538444838"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "No", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02089850790798664, "Height": 0.0099150650203228, "Left": 0.5573778748512268, "Top": 0.24992422759532928}, "Polygon": [{"X": 0.5573778748512268, "Y": 0.24992422759532928}, {"X": 0.5782763957977295, "Y": 0.24992424249649048}, {"X": 0.5782763957977295, "Y": 0.2598392963409424}, {"X": 0.5573779344558716, "Y": 0.2598392963409424}]}, "Id": "8d08c491-507f-4154-9be5-0426cb5f390b"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Top", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028537455946207047, "Height": 0.012197555042803288, "Left": 0.5830227732658386, "Top": 0.2499048411846161}, "Polygon": [{"X": 0.5830227732658386, "Y": 0.2499048411846161}, {"X": 0.6115602254867554, "Y": 0.24990485608577728}, {"X": 0.6115602254867554, "Y": 0.2621023952960968}, {"X": 0.5830228328704834, "Y": 0.2621023952960968}]}, "Id": "7f3259c7-59ef-4337-b0b1-2a74662a598b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Loading", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.062495190650224686, "Height": 0.012452788650989532, "Left": 0.6165921092033386, "Top": 0.24984554946422577}, "Polygon": [{"X": 0.6165921092033386, "Y": 0.24984554946422577}, {"X": 0.6790872812271118, "Y": 0.24984557926654816}, {"X": 0.6790872812271118, "Y": 0.2622983455657959}, {"X": 0.6165921092033386, "Y": 0.2622983157634735}]}, "Id": "bfe4bee6-9f8d-4460-99a2-9efe57a3b7d5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Manual", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055025774985551834, "Height": 0.010019499808549881, "Left": 0.7287557125091553, "Top": 0.24984855949878693}, "Polygon": [{"X": 0.7287557125091553, "Y": 0.24984855949878693}, {"X": 0.7837814688682556, "Y": 0.24984858930110931}, {"X": 0.7837814688682556, "Y": 0.2598680555820465}, {"X": 0.7287557125091553, "Y": 0.2598680257797241}]}, "Id": "e0970d79-3224-471e-b289-3f5bd42a64d4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Units", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039839375764131546, "Height": 0.010068708099424839, "Left": 0.2467268407344818, "Top": 0.26403146982192993}, "Polygon": [{"X": 0.2467268407344818, "Y": 0.26403146982192993}, {"X": 0.2865661680698395, "Y": 0.26403146982192993}, {"X": 0.28656622767448425, "Y": 0.2741001844406128}, {"X": 0.2467269003391266, "Y": 0.2741001546382904}]}, "Id": "491b2dba-1512-4fbb-b34b-505efbd452e9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Length", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05361667647957802, "Height": 0.012520795688033104, "Left": 0.3339036703109741, "Top": 0.2640165090560913}, "Polygon": [{"X": 0.3339036703109741, "Y": 0.2640165090560913}, {"X": 0.38752028346061707, "Y": 0.2640165388584137}, {"X": 0.38752034306526184, "Y": 0.27653729915618896}, {"X": 0.3339037299156189, "Y": 0.2765372693538666}]}, "Id": "7606a9f5-539a-4fa2-ab49-eff8b580c0d6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04530364274978638, "Height": 0.010086838155984879, "Left": 0.4165536165237427, "Top": 0.2639833390712738}, "Polygon": [{"X": 0.4165536165237427, "Y": 0.2639833390712738}, {"X": 0.46185722947120667, "Y": 0.2639833688735962}, {"X": 0.46185725927352905, "Y": 0.2740701735019684}, {"X": 0.41655364632606506, "Y": 0.2740701735019684}]}, "Id": "dfc3830a-0680-40cc-90e5-f373e457574d"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Height", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05077504739165306, "Height": 0.012835207395255566, "Left": 0.48785629868507385, "Top": 0.2638368010520935}, "Polygon": [{"X": 0.48785629868507385, "Y": 0.2638368010520935}, {"X": 0.5386313199996948, "Y": 0.2638368010520935}, {"X": 0.5386313199996948, "Y": 0.27667200565338135}, {"X": 0.48785632848739624, "Y": 0.27667197585105896}]}, "Id": "7dcfb1c9-8f5f-43a2-a23a-abb77c8fddcf"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "or", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016849642619490623, "Height": 0.007966007106006145, "Left": 0.5741220712661743, "Top": 0.2661471664905548}, "Polygon": [{"X": 0.5741220712661743, "Y": 0.2661471664905548}, {"X": 0.5909717082977295, "Y": 0.2661471664905548}, {"X": 0.5909717082977295, "Y": 0.27411317825317383}, {"X": 0.5741220712661743, "Y": 0.27411317825317383}]}, "Id": "31d12657-d968-488b-bf49-12c2f8c5b705"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Stacking", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06757766008377075, "Height": 0.01266080979257822, "Left": 0.5949547290802002, "Top": 0.26391875743865967}, "Polygon": [{"X": 0.5949547290802002, "Y": 0.26391875743865967}, {"X": 0.6625323295593262, "Y": 0.26391878724098206}, {"X": 0.662532389163971, "Y": 0.2765795588493347}, {"X": 0.5949547290802002, "Y": 0.27657952904701233}]}, "Id": "659eefc7-d82c-429e-9991-d53e74831005"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Measurement", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10471069812774658, "Height": 0.009903480298817158, "Left": 0.7042692303657532, "Top": 0.2641919255256653}, "Polygon": [{"X": 0.7042692303657532, "Y": 0.2641919255256653}, {"X": 0.8089799284934998, "Y": 0.26419198513031006}, {"X": 0.8089799284934998, "Y": 0.27409541606903076}, {"X": 0.7042692303657532, "Y": 0.274095356464386}]}, "Id": "0df4bcae-9b3f-4494-a9aa-1b55c62d7277"}, {"BlockType": "WORD", "Confidence": 99.61834716796875, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005637028254568577, "Height": 0.009765950962901115, "Left": 0.29924696683883667, "Top": 0.29453152418136597}, "Polygon": [{"X": 0.29924696683883667, "Y": 0.29453152418136597}, {"X": 0.3048839569091797, "Y": 0.29453152418136597}, {"X": 0.3048839867115021, "Y": 0.30429747700691223}, {"X": 0.29924702644348145, "Y": 0.30429744720458984}]}, "Id": "d61342f7-b622-48e7-9d84-36587e327dde"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "41.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04107247665524483, "Height": 0.010041771456599236, "Left": 0.3453444242477417, "Top": 0.2943840026855469}, "Polygon": [{"X": 0.3453444242477417, "Y": 0.2943840026855469}, {"X": 0.38641685247421265, "Y": 0.29438403248786926}, {"X": 0.38641688227653503, "Y": 0.30442577600479126}, {"X": 0.3453444540500641, "Y": 0.30442574620246887}]}, "Id": "9d623027-5625-4e2d-a164-20a98c0aee63"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "35.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04045151546597481, "Height": 0.01000198908150196, "Left": 0.4217550754547119, "Top": 0.2943864166736603}, "Polygon": [{"X": 0.4217550754547119, "Y": 0.2943864166736603}, {"X": 0.46220657229423523, "Y": 0.29438644647598267}, {"X": 0.4622066020965576, "Y": 0.3043884038925171}, {"X": 0.4217551350593567, "Y": 0.3043883740901947}]}, "Id": "532905a5-55a6-49c1-833b-cadc4c49405a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "62.50", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04057396575808525, "Height": 0.00998679269105196, "Left": 0.4989919662475586, "Top": 0.2943877577781677}, "Polygon": [{"X": 0.4989919662475586, "Y": 0.2943877577781677}, {"X": 0.539565920829773, "Y": 0.2943877875804901}, {"X": 0.539565920829773, "Y": 0.3043745458126068}, {"X": 0.498991996049881, "Y": 0.3043745160102844}]}, "Id": "3e69536b-4219-43db-b571-b73d1896a141"}, {"BlockType": "WORD", "Confidence": 88.9317626953125, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011251786723732948, "Height": 0.010424042120575905, "Left": 0.6168341040611267, "Top": 0.29412609338760376}, "Polygon": [{"X": 0.6168341040611267, "Y": 0.29412609338760376}, {"X": 0.6280858516693115, "Y": 0.29412612318992615}, {"X": 0.6280858516693115, "Y": 0.3045501410961151}, {"X": 0.6168341040611267, "Y": 0.3045501410961151}]}, "Id": "55887aba-e34b-49b8-a14c-2438106ba8b1"}, {"BlockType": "WORD", "Confidence": 99.56951904296875, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005585659295320511, "Height": 0.009834064170718193, "Left": 0.29925814270973206, "Top": 0.31366899609565735}, "Polygon": [{"X": 0.29925814270973206, "Y": 0.31366899609565735}, {"X": 0.3048437535762787, "Y": 0.31366899609565735}, {"X": 0.3048437833786011, "Y": 0.3235030472278595}, {"X": 0.29925820231437683, "Y": 0.3235030472278595}]}, "Id": "786ba09e-ea33-4cc1-9540-ef2d9f7645e3"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "41.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041077181696891785, "Height": 0.010297536849975586, "Left": 0.34537607431411743, "Top": 0.31346115469932556}, "Polygon": [{"X": 0.34537607431411743, "Y": 0.31346115469932556}, {"X": 0.38645321130752563, "Y": 0.31346121430397034}, {"X": 0.386453241109848, "Y": 0.32375869154930115}, {"X": 0.3453761041164398, "Y": 0.32375866174697876}]}, "Id": "721fffde-90db-42dd-8de3-9325a91e342a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "34.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04043677821755409, "Height": 0.010263390839099884, "Left": 0.42187240719795227, "Top": 0.3134312629699707}, "Polygon": [{"X": 0.42187240719795227, "Y": 0.3134312629699707}, {"X": 0.4623091518878937, "Y": 0.3134313225746155}, {"X": 0.46230918169021606, "Y": 0.3236946761608124}, {"X": 0.42187243700027466, "Y": 0.3236946165561676}]}, "Id": "4b3863ef-b859-4162-88e5-b623fb220ad5"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "62.50", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04046221077442169, "Height": 0.010043453425168991, "Left": 0.49897482991218567, "Top": 0.3135414719581604}, "Polygon": [{"X": 0.49897482991218567, "Y": 0.3135414719581604}, {"X": 0.5394369959831238, "Y": 0.3135415017604828}, {"X": 0.5394370555877686, "Y": 0.3235849142074585}, {"X": 0.49897485971450806, "Y": 0.3235848844051361}]}, "Id": "3c92aeb1-dede-455e-b4e6-a06f4ece13cc"}, {"BlockType": "WORD", "Confidence": 88.92857360839844, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011442029848694801, "Height": 0.01062542200088501, "Left": 0.6167930364608765, "Top": 0.3132188320159912}, "Polygon": [{"X": 0.6167930364608765, "Y": 0.3132188320159912}, {"X": 0.6282351016998291, "Y": 0.3132188618183136}, {"X": 0.6282351016998291, "Y": 0.3238442540168762}, {"X": 0.6167930960655212, "Y": 0.3238442540168762}]}, "Id": "46a5f82a-f9be-43da-b44f-0fb258a4030d"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "733d92ec-9d17-47e5-9c5e-bb00b5407030", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "733d92ec-9d17-47e5-9c5e-bb00b5407030", "content-type": "application/x-amz-json-1.1", "content-length": "65255", "date": "Wed, 24 Sep 2025 07:37:22 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "519c7a63-44da-401f-93fe-97daeff192c0", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 07:37:27 GMT", "content-type": "application/json", "content-length": "3555", "connection": "keep-alive", "x-amzn-requestid": "519c7a63-44da-401f-93fe-97daeff192c0"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. The content: \"TF<PERSON>ce Freight CUBE MEASUREMENT CERTIFICATE Freight Bill Number: 413999202 ... Measured Date: 8/27/2025 ... Total Handling Units: 2 ... Total Cubic Feet: 101.00 ...\".\n\nThis appears to be a \"CUBE MEASUREMENT CERTIFICATE\" which is likely a \"weight_and_inspection_cert\" or \"nmfc_cert\"? But we need to map to the provided categories.\n\nLet's see definitions: weight_and_inspection_cert: Certificate/Report issued by authority confirming weight with length, width and height. Keywords: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate. Also, if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify as nmfc_cert. For inspection_cert: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\nnmfc_cert: NMFC Classification Certificate or Correction notice. Keywords: fright (As described and As found) or (Original and inspection) or Corrected class or Correction information. Also optional: NMFC Code, class #.\n\nBut we have \"CUBE MEASUREMENT CERTIFICATE\" with dimensions. The keywords: \"Cube Measurement Certificate\". That indicates measurement of cubic volume for shipping. Not exactly weight, but measurement. The category \"weight_and_inspection_cert\" may be appropriate: It's a certificate that includes measurements (length, width, height). The definition says \"certificate/report issued by authority confirming weight with length, width and height\". It mentions weight but also length width height. It could be \"weight_and_inspection_cert\". However, it might also be a \"pack_list\" or \"inspection_cert\". The pack list has items and quantity, but this is just measurement of dimensions. The inspector certificate is for inspection but no weight? The document is a measurement certificate.\n\nWe should check if there is a category for \"CUBE MEASUREMENT CERTIFICATE\". Not explicitly. But \"weight_and_inspection_cert\" might be the best fit.\n\nAlternatively, there's \"inspection_cert\" for Inspection Certificate, but no weight. This has weight? Actually, it has no weight, but only cubic feet. But it says \"Total Cubic Feet: 101.00\". It's a measurement certificate. The definition: \"Certificate/Report issued by authority confirming weight with length, width and height.\" It includes length, width and height. The document includes length, width, height but no weight. But the definition says weight with length width height. But maybe weight_and_inspection_cert covers any measurement certificates. The instruction: \"If weight_and_inspection_cert is having keywwords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\" The keywords for nmfc_cert: \"fright (As described and As found)\" or \"Original and inspection\" or \"Corrected class\" or \"Correction information\" or \"NMFC Code\", \"class #\". Our document doesn't mention those. So we keep as weight_and_inspection_cert.\n\nThus classify page 1 as weight_and_inspection_cert.\n\nWe must output a JSON with array of documents: only one page. Use tool call: classify_logistics_doc_type."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"weight_and_inspection_cert\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2591, "outputTokens": 689, "totalTokens": 3280}, "metrics": {"latencyMs": 3058}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/59d38ba3_I_QHD3LC0DU6S8O2YVVS60.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight with length, width and height.\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. If weight_and_inspection_cert is having keywwords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: This will include keywords like fright (As described and As found) or (Original and inspection) or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nT<PERSON><PERSON><PERSON>\nFreight\nCUBE MEASUREMENT CERTIFICATE\nFreight Bill Number:\n413999202\nThe information set forth within this document constitutes an\nMeasured Date:\n8/27/2025\naccurate measurement of the shipment covered hereby and\nTotal Handling Units:\n2\ncomplies with applicable laws and tariffs. The measurements\nTotal Cubic Feet:\n101.00\nwere performed by an NTEP certified dimensioning device\nAll measurements are in imperial inches.\ninstalled at our Las Vegas, NV facility.\nHandling\nNo Top Loading\nManual\nUnits\nLength\nWidth\nHeight\nor Stacking\nMeasurement\n1\n41.00\n35.00\n62.50\nX\n1\n41.00\n34.00\n62.50\nX\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}