#!/usr/bin/env python3
"""
Test script to reproduce the actual Bedrock error and trigger debug mode.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the scripts directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'scripts'))

# Enable debug mode
os.environ['DEBUG_ON_ERROR'] = 'true'

from test_classification import FileProcessor


async def test_with_real_files():
    """Test with real files that might cause the Bedrock error."""
    print("🧪 Testing with Real Files (Debug Mode Enabled)")
    print("=" * 60)
    print("🐛 DEBUG_ON_ERROR=true")
    
    # Create processor
    processor = FileProcessor("Augment_test/real_error_output", "Augment_test/real_error_logs")
    
    # Test with the folder that caused the error
    test_folder = "/home/<USER>/Documents/repositories/test_logistically/verified_docs/weight_and_inspection_cert"
    
    if not os.path.exists(test_folder):
        print(f"❌ Test folder not found: {test_folder}")
        return
    
    print(f"📁 Testing with folder: {test_folder}")
    print(f"🎯 Processing only 1 file to trigger the error quickly")
    
    try:
        # Process only 1 file to trigger the error quickly
        stats = await processor.process_files(test_folder, max_files=1)
        
        print(f"\n📊 Results: {stats}")
        
        # Check if debug info was stored
        if processor.debug_info:
            print(f"\n🐛 Debug info was stored!")
            print(f"   Location: {processor.debug_info.get('location', 'unknown')}")
            print(f"   Exception: {processor.debug_info.get('exception', 'unknown')}")
            print(f"   File: {processor.debug_info.get('file_path', 'unknown')}")
            print(f"   S3 URI: {processor.debug_info.get('s3_uri', 'unknown')}")
            print(f"\n🎯 The debugger would have been triggered!")
        else:
            print(f"\nℹ️ No debug info stored - no errors occurred")
        
    except Exception as e:
        print(f"\n❌ Exception in main: {e}")
        import traceback
        print(f"❌ Traceback: {traceback.format_exc()}")


def show_debug_instructions():
    """Show instructions for using debug mode."""
    print("\n📋 Debug Mode Instructions")
    print("=" * 50)
    print("To actually enter debug mode when the error occurs:")
    print()
    print("1. Run the script normally (it will trigger debug automatically):")
    print("   python scripts/test_classification.py")
    print()
    print("2. Or run with explicit debug flag:")
    print("   python scripts/test_classification.py /path/to/folder --debug")
    print()
    print("3. When the error occurs, you'll see:")
    print("   🐛 ENTERING DEBUG MODE")
    print("   (Pdb) ")
    print()
    print("4. Use these pdb commands:")
    print("   l          - List current code")
    print("   p var      - Print variable value")
    print("   pp var     - Pretty print variable")
    print("   u          - Move up the call stack")
    print("   d          - Move down the call stack")
    print("   c          - Continue execution")
    print("   q          - Quit debugger")
    print()
    print("5. To inspect the error:")
    print("   p self.debug_info")
    print("   p self.debug_info['exception']")
    print("   p self.debug_info['traceback']")
    print()
    print("6. To see the current state:")
    print("   p locals()")
    print("   p globals()")


async def main():
    """Run the test."""
    print("🚀 Real Error Debug Test")
    print("=" * 60)
    
    # Show instructions first
    show_debug_instructions()
    
    print(f"\n🧪 Running test (debug info will be stored, but debugger won't start)")
    print(f"This is to verify the debug mechanism works without entering interactive mode")
    
    # Run the test
    await test_with_real_files()
    
    print(f"\n🎉 Test completed!")
    print(f"\n🎯 To actually trigger the debugger:")
    print(f"   python scripts/test_classification.py")
    print(f"   (The script will automatically enter debug mode on errors)")


if __name__ == "__main__":
    asyncio.run(main())
