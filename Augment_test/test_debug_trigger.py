#!/usr/bin/env python3
"""
Test script to trigger debug mode and verify it works correctly.
This script will intentionally cause an error to test the debug functionality.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add the scripts directory to the path
sys.path.append(os.path.join(os.path.dirname(os.path.dirname(__file__)), 'scripts'))

# Enable debug mode
os.environ['DEBUG_ON_ERROR'] = 'true'

from test_classification import FileProcessor


async def test_debug_trigger():
    """Test that debug mode is triggered on errors."""
    print("🧪 Testing Debug Trigger")
    print("=" * 50)
    print("🐛 Debug mode enabled")
    
    # Create processor
    processor = FileProcessor("Augment_test/debug_test_output", "Augment_test/debug_test_logs")
    
    # Test with a folder that will cause an error
    print("\n📁 Testing with non-existent folder (should trigger error and debug)...")
    
    try:
        stats = await processor.process_files("/non/existent/folder", max_files=1)
        print(f"Unexpected success: {stats}")
    except Exception as e:
        print(f"✅ Error caught as expected: {e}")
    
    # Test with a folder that exists but has no supported files
    print("\n📁 Testing with folder containing no supported files...")
    
    # Create a temporary folder with unsupported files
    temp_dir = Path("Augment_test/temp_debug_test")
    temp_dir.mkdir(exist_ok=True)
    
    # Create some unsupported files
    (temp_dir / "test.txt").write_text("test content")
    (temp_dir / "test.json").write_text('{"test": "data"}')
    
    try:
        stats = await processor.process_files(str(temp_dir), max_files=1)
        print(f"Result: {stats}")
        
        # Check if debug info was stored
        if processor.debug_info:
            print(f"🐛 Debug info was stored: {processor.debug_info}")
        else:
            print("ℹ️ No debug info stored (no errors occurred)")
        
    except Exception as e:
        print(f"Error: {e}")
    finally:
        # Clean up
        import shutil
        if temp_dir.exists():
            shutil.rmtree(temp_dir)
    
    print("\n✅ Debug trigger test completed")


def test_manual_debug_trigger():
    """Test manual debug trigger."""
    print("\n🧪 Testing Manual Debug Trigger")
    print("=" * 50)
    
    # Create processor
    processor = FileProcessor("Augment_test/debug_test_output", "Augment_test/debug_test_logs")
    
    # Manually set debug info to simulate an error
    processor.debug_info = {
        'exception': Exception("Test exception for debugging"),
        'traceback': "Traceback (test):\n  File test.py, line 1, in test\n    raise Exception('Test')",
        'file_path': '/test/file/path.pdf',
        's3_uri': 's3://test-bucket/test-file.pdf',
        'location': 'test_function'
    }
    
    print("🐛 Debug info manually set")
    print(f"Debug info: {processor.debug_info}")
    
    # This would trigger the debugger if we were in the process_files method
    if processor.debug_info and os.getenv('DEBUG_ON_ERROR', '').lower() in ('true', '1', 'yes'):
        print("✅ Debug mode would be triggered")
        print("🐛 In real scenario, debugger would start here")
    else:
        print("❌ Debug mode would not be triggered")
    
    print("\n✅ Manual debug trigger test completed")


async def main():
    """Run debug trigger tests."""
    print("🚀 Debug Trigger Test")
    print("=" * 60)
    
    # Test 1: Manual debug trigger
    test_manual_debug_trigger()
    
    # Test 2: Actual debug trigger (commented out to avoid entering debugger)
    print("\n⚠️ Actual debug trigger test skipped to avoid entering debugger")
    print("To test actual debug trigger:")
    print("1. Uncomment the test_debug_trigger() call below")
    print("2. Run this script")
    print("3. The script will enter debug mode when errors occur")
    
    # await test_debug_trigger()  # Uncomment to test actual debug trigger
    
    print(f"\n🎉 Debug trigger tests completed!")
    print(f"\n📋 How to test debug mode:")
    print(f"1. Run: python scripts/test_classification.py (with no args - uses default test)")
    print(f"2. Or run: python scripts/test_classification.py /path/to/folder --debug")
    print(f"3. Or set: DEBUG_ON_ERROR=true python scripts/test_classification.py /path/to/folder")
    print(f"4. When an error occurs, the debugger will start")
    print(f"5. Use pdb commands: 'l' (list), 'p var' (print), 'c' (continue), 'q' (quit)")


if __name__ == "__main__":
    asyncio.run(main())
