#!/usr/bin/env python3
"""
Test Classification Script

This script:
- Uploads files to S3 bucket inside temp folder
- Gives S3 URI to main.py function to classify
- Deletes temp files after classification
- Uses AWS credentials from .env file
- Uses main.py file to get results
- Stores whole result in output folder with serial number
- Classifies all files inside given folder and its subfolders
- Takes argument for max number of files to process
- Uses async to process faster
- Output JSON files have same name as input files
"""

import sys
import os
import asyncio
import json
import argparse
import uuid
import logging
import concurrent.futures
import traceback
import pdb
from pathlib import Path
from typing import List, Dict, Tuple, Optional, Union
import boto3
from botocore.exceptions import ClientError
import dotenv
from datetime import datetime

# Add the main script path to sys.path
sys.path.append(r"/home/<USER>/Documents/repositories/document-data-extraction")
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Load environment variables
dotenv.load_dotenv()

# Import the main classification function
llm_classification = None

try:
    # Try importing from the external repository first
    sys.path.insert(0, r"/home/<USER>/Documents/repositories/document-data-extraction")
    from app.llm.classification import main as llm_classification
    print("✓ Successfully imported from external repository: app.llm.classification")
except ImportError as e:
    print(f"⚠ Failed to import from external repository: {e}")
    try:
        # Fallback to local scripts directory
        from scripts.main import main as llm_classification
        print("✓ Successfully imported from local scripts: scripts.main")
    except ImportError as e2:
        print(f"⚠ Failed to import from local scripts: {e2}")
        try:
            # Last resort - try direct import
            import importlib.util
            main_py_path = os.path.join(os.path.dirname(__file__), "main.py")
            if os.path.exists(main_py_path):
                spec = importlib.util.spec_from_file_location("main", main_py_path)
                if spec and spec.loader:
                    main_module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(main_module)
                    llm_classification = main_module.main
                    print(f"✓ Successfully imported from direct path: {main_py_path}")
                else:
                    print(f"❌ Failed to create module spec for: {main_py_path}")
            else:
                print(f"❌ main.py not found at: {main_py_path}")
        except Exception as e3:
            print(f"❌ Failed to import via direct path: {e3}")

if llm_classification is None:
    print("❌ CRITICAL: Could not import classification function from any source!")
    print("Please ensure one of the following exists:")
    print("1. /home/<USER>/Documents/repositories/document-data-extraction/app/llm/classification.py")
    print("2. scripts/main.py in the current directory")
    sys.exit(1)


def setup_logging(log_dir: str = "logs") -> logging.Logger:
    """Setup logging configuration with both file and console handlers."""
    # Create logs directory
    log_path = Path(log_dir)
    log_path.mkdir(exist_ok=True)

    # Create log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = log_path / f"test_classification_{timestamp}.log"

    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file, encoding='utf-8'),
            logging.StreamHandler(sys.stdout)
        ],
        force=True
    )

    logger = logging.getLogger(__name__)
    logger.info(f"Logging initialized. Log file: {log_file}")
    return logger


class S3Manager:
    """Manages S3 operations for file upload and deletion."""
    
    def __init__(self):
        """Initialize S3 client with credentials from environment."""
        self.s3_client = boto3.client(
            's3',
            aws_access_key_id=os.getenv('AWS_ACCESS_KEY_ID'),
            aws_secret_access_key=os.getenv('AWS_SECRET_ACCESS_KEY'),
            region_name=os.getenv('AWS_DEFAULT_REGION', 'us-east-1')
        )
        self.bucket_name = os.getenv('S3_BUCKET_NAME', 'document-extraction-logistically')
        self.temp_prefix = 'temp'
    
    async def upload_file(self, local_file_path: str) -> str:
        """
        Upload a file to S3 temp folder.
        
        Args:
            local_file_path: Path to the local file
            
        Returns:
            S3 URI of the uploaded file
        """
        file_name = Path(local_file_path).name
        # Generate unique filename to avoid conflicts
        unique_id = str(uuid.uuid4())[:8]
        s3_key = f"{self.temp_prefix}/{unique_id}_{file_name}"
        
        try:
            # Upload file to S3
            self.s3_client.upload_file(local_file_path, self.bucket_name, s3_key)
            s3_uri = f"s3://{self.bucket_name}/{s3_key}"
            logging.info(f"✓ Uploaded: {local_file_path} -> {s3_uri}")
            return s3_uri
        except ClientError as e:
            logging.error(f"✗ Failed to upload {local_file_path}: {e}")
            raise
    
    async def delete_file(self, s3_uri: str) -> bool:
        """
        Delete a file from S3.
        
        Args:
            s3_uri: S3 URI of the file to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Parse S3 URI
            if not s3_uri.startswith('s3://'):
                raise ValueError(f"Invalid S3 URI: {s3_uri}")
            
            # Extract bucket and key from URI
            uri_parts = s3_uri[5:].split('/', 1)  # Remove 's3://' and split
            bucket = uri_parts[0]
            key = uri_parts[1] if len(uri_parts) > 1 else ''
            
            # Only delete files from temp folder for safety
            if not key.startswith(self.temp_prefix):
                logging.warning(f"⚠ Skipping deletion - file not in temp folder: {s3_uri}")
                return False

            # Delete the file
            self.s3_client.delete_object(Bucket=bucket, Key=key)
            logging.info(f"✓ Deleted: {s3_uri}")
            return True
        except ClientError as e:
            logging.error(f"✗ Failed to delete {s3_uri}: {e}")
            return False


class FileProcessor:
    """Processes files for classification."""

    def __init__(self, output_dir: str = "output", log_dir: str = "logs"):
        """Initialize file processor."""
        self.s3_manager = S3Manager()
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(exist_ok=True)
        self.processed_count = 0
        self.logger = setup_logging(log_dir)
        self.debug_info = None  # Store debug info for main thread debugging
    
    def get_supported_files(self, folder_path: str) -> List[str]:
        """
        Get all supported files from folder and subfolders.
        
        Args:
            folder_path: Path to the folder to scan
            
        Returns:
            List of file paths
        """
        supported_extensions = {'.pdf', '.jpg', '.jpeg', '.png', '.tif', '.tiff'}
        files = []
        
        folder = Path(folder_path)
        if not folder.exists():
            raise ValueError(f"Folder does not exist: {folder_path}")
        
        # Recursively find all supported files
        for file_path in folder.rglob('*'):
            if file_path.is_file() and file_path.suffix.lower() in supported_extensions:
                files.append(str(file_path))
        
        return sorted(files)

    def run_classification_sync(self, s3_uri: str) -> Dict:
        """Run classification synchronously in a thread."""
        if llm_classification is None:
            raise RuntimeError("Classification function not available - import failed")

        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            self.logger.debug(f"🔍 Starting classification for: {s3_uri}")
            result = loop.run_until_complete(llm_classification(s3_uri))
            self.logger.debug(f"✓ Classification completed for: {s3_uri}")
            return result
        except Exception as e:
            self.logger.error(f"❌ Classification failed for {s3_uri}: {str(e)}")
            self.logger.error(f"❌ Full traceback: {traceback.format_exc()}")

            # Store debug info for main thread debugging
            if os.getenv('DEBUG_ON_ERROR', '').lower() in ('true', '1', 'yes'):
                self.debug_info = {
                    'exception': e,
                    'traceback': traceback.format_exc(),
                    's3_uri': s3_uri,
                    'location': 'run_classification_sync'
                }
                self.logger.error("🐛 Debug info stored - will trigger debugger in main thread")
            raise
        finally:
            loop.close()

    async def process_single_file(self, file_path: str, run_number: int) -> Tuple[bool, str, str, Dict]:
        """
        Process a single file through the classification pipeline.

        Args:
            file_path: Path to the local file
            run_number: Run number for output file naming

        Returns:
            Tuple of (success, error_message, filename, classification_result)
        """
        s3_uri = None
        filename = Path(file_path).name
        classification_result = {}

        try:
            # Upload file to S3
            start_time = datetime.now()
            self.logger.info(f"⬆️ [{start_time.strftime('%H:%M:%S')}] Uploading: {filename}")
            s3_uri = await self.s3_manager.upload_file(file_path)

            # Classify using main.py function in thread pool for true parallelism
            classify_time = datetime.now()
            self.logger.info(f"🔍 [{classify_time.strftime('%H:%M:%S')}] Starting classification: {filename}")

            # Use thread pool executor to run classification in parallel
            loop = asyncio.get_event_loop()
            with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                result = await loop.run_in_executor(executor, self.run_classification_sync, s3_uri)

            # Extract classification result for terminal output
            classification_result = result.get("classification_result", {})

            # Generate output filename with same name as input file
            input_filename = Path(file_path).stem  # filename without extension
            output_filename = f"run{run_number}_{input_filename}.json"
            output_path = self.output_dir / output_filename

            # Save result to output folder
            with open(output_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, indent=4, ensure_ascii=False, default=str)

            # Log filename and classification result
            self.logger.info(f"\n\n{filename}\n\n{json.dumps(classification_result, indent=4)}")
            self.logger.info(f"\n\n✓ Saved result: {output_path}")
            self.processed_count += 1

            return True, "", filename, classification_result

        except Exception as e:
            error_msg = f"Failed to process {file_path}: {str(e)}"
            self.logger.error(f"✗ {error_msg}")
            self.logger.error(f"✗ Full traceback: {traceback.format_exc()}")

            # Store debug info for main thread debugging
            if os.getenv('DEBUG_ON_ERROR', '').lower() in ('true', '1', 'yes'):
                self.debug_info = {
                    'exception': e,
                    'traceback': traceback.format_exc(),
                    'file_path': file_path,
                    's3_uri': s3_uri,
                    'location': 'process_single_file'
                }
                self.logger.error("🐛 Debug info stored - will trigger debugger in main thread")

            return False, error_msg, filename, classification_result

        finally:
            # Always try to delete the temp file from S3
            if s3_uri:
                await self.s3_manager.delete_file(s3_uri)
    
    def print_classification_table(self, processed_files: List[Dict], files: List[str], run_number: int = 1) -> None:
        """
        Print a formatted table with PDF file links, page numbers, classified categories, and JSON output links.

        Args:
            processed_files: List of processed file information
            files: List of original file paths
            run_number: Run number for finding corresponding JSON files
        """
        if not processed_files:
            self.logger.info("\n📋 No files were successfully processed.")
            return

        # Create a mapping from filename to full path for clickable links
        file_path_map = {}
        for file_path in files:
            filename = Path(file_path).name
            file_path_map[filename] = os.path.abspath(file_path)

        # Prepare table data
        table_data = []
        for file_info in processed_files:
            filename = file_info['filename']
            classification = file_info['classification']
            file_path = file_path_map.get(filename, filename)

            # Generate JSON output file path
            input_filename = Path(filename).stem  # filename without extension
            json_filename = f"run{run_number}_{input_filename}.json"
            json_path = os.path.abspath(self.output_dir / json_filename)

            # Extract documents from classification result
            documents = classification.get('documents', [])

            if documents:
                for doc in documents:
                    page_no = doc.get('page_no', 'N/A')
                    doc_type = doc.get('doc_type', 'Unknown')
                    table_data.append({
                        'file_path': file_path,
                        'filename': filename,
                        'page_no': page_no,
                        'category': doc_type,
                        'json_path': json_path
                    })
            else:
                # If no documents found, still show the file
                table_data.append({
                    'file_path': file_path,
                    'filename': filename,
                    'page_no': 'N/A',
                    'category': 'No classification',
                    'json_path': json_path
                })

        # Print table header
        self.logger.info("\n" + "="*140)
        self.logger.info("📋 CLASSIFICATION RESULTS TABLE")
        self.logger.info("="*140)
        self.logger.info(f"{'PDF FILE LINK':<50} {'PAGE':<6} {'CATEGORY':<20} {'JSON OUTPUT LINK':<50}")
        self.logger.info("-"*140)

        # Print table rows
        for row in table_data:
            # Create clickable file links (file:// protocol for terminal)
            pdf_link = f"file://{row['file_path']}"
            json_link = f"file://{row['json_path']}"
            filename_display = row['filename'][:45] + "..." if len(row['filename']) > 45 else row['filename']
            category_display = row['category'][:18] + "..." if len(row['category']) > 18 else row['category']
            json_filename = Path(row['json_path']).name
            json_display = json_filename[:45] + "..." if len(json_filename) > 45 else json_filename

            self.logger.info(f"{filename_display:<50} {str(row['page_no']):<6} {category_display:<20} {json_display:<50}")
            self.logger.info(f"  PDF → {pdf_link}")
            self.logger.info(f"  JSON→ {json_link}")
            self.logger.info("")

        self.logger.info("-"*140)
        self.logger.info(f"Total entries: {len(table_data)}")
        self.logger.info("="*140)

        # Also print a compact version for easy viewing
        self.logger.info("\n📋 COMPACT TABLE VIEW:")
        self.logger.info("-"*100)
        for i, row in enumerate(table_data, 1):
            json_name = Path(row['json_path']).name
            self.logger.info(f"{i:3d}. {row['filename']:<35} Page {row['page_no']:<3} → {row['category']:<15} | {json_name}")
        self.logger.info("-"*100)

    async def process_files(self, folder_path: str, max_files: Optional[int] = None, run_number: int = 1) -> Dict:
        """
        Process all files in the folder with async processing.

        Args:
            folder_path: Path to the folder containing files
            max_files: Maximum number of files to process (None for all)
            run_number: Run number for output file naming

        Returns:
            Dictionary with processing statistics
        """
        # Get all supported files
        files = self.get_supported_files(folder_path)

        if not files:
            self.logger.warning(f"⚠ No supported files found in {folder_path}")
            return {"total_files": 0, "processed": 0, "failed": 0, "errors": []}

        # Limit files if max_files is specified
        if max_files and max_files > 0:
            files = files[:max_files]

        self.logger.info(f"📁 Found {len(files)} files to process")
        self.logger.info(f"🚀 Starting processing with run number: {run_number}")

        # Process files with TRUE PARALLEL processing using thread pool
        self.logger.info(f"🚀 Processing {len(files)} files in FORCED PARALLEL MODE...")

        # Create all tasks at once for maximum parallelism
        start_time = datetime.now()

        # Use asyncio.gather with immediate task creation for true parallelism
        self.logger.info(f"🚀 Creating {len(files)} parallel tasks...")

        # Create all tasks simultaneously - this forces true parallel execution
        tasks = [
            asyncio.create_task(self.process_single_file(file_path, run_number))
            for file_path in files
        ]

        self.logger.info(f"🚀 All {len(tasks)} tasks created - executing in parallel...")

        # Execute all tasks in parallel
        results = await asyncio.gather(*tasks, return_exceptions=True)
        end_time = datetime.now()

        # Collect statistics
        successful = 0
        failed = 0
        errors = []
        processed_files = []

        for i, result in enumerate(results):
            if isinstance(result, Exception):
                failed += 1
                errors.append(f"File {files[i]}: {str(result)}")
            elif isinstance(result, tuple) and len(result) >= 4:
                if result[0]:  # success
                    successful += 1
                    processed_files.append({
                        "filename": result[2],
                        "classification": result[3],
                        "file_path": files[i]  # Add original file path
                    })
                else:  # failure
                    failed += 1
                    errors.append(result[1])
            else:
                failed += 1
                errors.append(f"File {files[i]}: Invalid result format")

        # Print summary
        duration = (end_time - start_time).total_seconds()
        self.logger.info(f"\n📊 Processing Summary:")
        self.logger.info(f"   Total files: {len(files)}")
        self.logger.info(f"   Successful: {successful}")
        self.logger.info(f"   Failed: {failed}")
        self.logger.info(f"   Duration: {duration:.2f} seconds")
        self.logger.info(f"   Output directory: {self.output_dir}")

        # Print processed files summary
        if processed_files:
            self.logger.info(f"\n📋 Successfully Processed Files:")
            for file_info in processed_files:
                self.logger.info(f"   📄 {file_info['filename']}: {json.dumps(file_info['classification'], separators=(',', ':'))}")

        if errors:
            self.logger.error(f"\n❌ Errors:")
            for error in errors[:5]:  # Show first 5 errors
                self.logger.error(f"   {error}")
            if len(errors) > 5:
                self.logger.error(f"   ... and {len(errors) - 5} more errors")

        # Print the classification table at the end
        self.print_classification_table(processed_files, files, run_number)

        # Check if debug info was stored and trigger debugger in main thread
        if self.debug_info and os.getenv('DEBUG_ON_ERROR', '').lower() in ('true', '1', 'yes'):
            self.logger.error("🐛 Error occurred during processing - entering debugger...")
            self.logger.error(f"🐛 Debug info: {self.debug_info}")
            self.logger.error(f"🐛 Location: {self.debug_info.get('location', 'unknown')}")
            self.logger.error(f"🐛 Exception: {self.debug_info.get('exception', 'unknown')}")
            self.logger.error(f"🐛 Traceback:\n{self.debug_info.get('traceback', 'unknown')}")

            # Enter debugger with the stored exception info
            print("\n" + "="*60)
            print("🐛 ENTERING DEBUG MODE")
            print("="*60)
            print(f"Error occurred in: {self.debug_info.get('location', 'unknown')}")
            print(f"File: {self.debug_info.get('file_path', self.debug_info.get('s3_uri', 'unknown'))}")
            print(f"Exception: {self.debug_info.get('exception', 'unknown')}")
            print("\nUse 'c' to continue, 'q' to quit, 'l' to list code, 'p variable' to print variables")
            print("="*60)
            pdb.set_trace()

        return {
            "total_files": len(files),
            "processed": successful,
            "failed": failed,
            "errors": errors,
            "duration_seconds": duration,
            "processed_files": processed_files  # Include processed files in return
        }


async def main():
    """Main function to run the classification test."""
    parser = argparse.ArgumentParser(description='Test document classification with S3 upload/download')
    parser.add_argument('folder_path', help='Path to folder containing files to classify')
    parser.add_argument('--max-files', type=int, default=None,
                       help='Maximum number of files to process (default: all files)')
    parser.add_argument('--output-dir', default='output',
                       help='Output directory for results (default: output)')
    parser.add_argument('--run-number', type=int, default=1,
                       help='Run number for output file naming (default: 1)')
    parser.add_argument('--debug', action='store_true',
                       help='Enable debug mode with post-mortem debugging on errors')
    parser.add_argument('--debug-on-start', action='store_true',
                       help='Start debugger immediately at the beginning')

    args = parser.parse_args()

    # Set debug environment variable if debug mode is enabled
    if args.debug:
        os.environ['DEBUG_ON_ERROR'] = 'true'
        print("🐛 Debug mode enabled - will enter debugger on errors")

    # Start debugger immediately if requested
    if args.debug_on_start:
        print("🐛 Starting debugger at the beginning...")
        pdb.set_trace()

    # Validate folder path
    if not os.path.exists(args.folder_path):
        print(f"❌ Error: Folder does not exist: {args.folder_path}")
        return 1

    # Initialize processor with logging
    processor = FileProcessor(args.output_dir, "logs")

    try:
        # Process files
        stats = await processor.process_files(
            args.folder_path,
            args.max_files,
            args.run_number
        )

        # Save processing statistics
        stats_file = processor.output_dir / f"run{args.run_number}_stats.json"
        with open(stats_file, 'w', encoding='utf-8') as f:
            json.dump(stats, f, indent=4, ensure_ascii=False, default=str)

        processor.logger.info(f"\n📈 Statistics saved to: {stats_file}")

        # Print final summary with table
        processor.logger.info(f"\n🎉 Processing completed!")
        processor.logger.info(f"📊 Summary: {stats['processed']}/{stats['total_files']} files processed successfully")
        if stats['failed'] > 0:
            processor.logger.info(f"❌ {stats['failed']} files failed")
        processor.logger.info(f"⏱️  Total duration: {stats['duration_seconds']:.2f} seconds")
        processor.logger.info(f"📁 Output directory: {args.output_dir}")
        processor.logger.info(f"📈 Statistics file: {stats_file}")

        return 0 if stats['failed'] == 0 else 1

    except Exception as e:
        processor.logger.error(f"❌ Fatal error: {e}")
        processor.logger.error(f"❌ Full traceback: {traceback.format_exc()}")

        # Enable debugger on fatal error if in debug mode
        if os.getenv('DEBUG_ON_ERROR', '').lower() in ('true', '1', 'yes'):
            processor.logger.error("🐛 Entering debugger due to fatal error...")
            pdb.post_mortem()

        return 1


if __name__ == "__main__":
    # Example usage in script
    if len(sys.argv) == 1:
        # Default test run with debug support
        print("🧪 Running test with default parameters...")
        print("🐛 Debug mode enabled by default for test run")

        # Enable debug mode for default test
        os.environ['DEBUG_ON_ERROR'] = 'true'

        test_folder = "/home/<USER>/Documents/repositories/test_logistically/verified_docs_v1/weight_and_inspection_cert"
        max_files = 1

        async def test_run():
            try:
                processor = FileProcessor("output", "logs")
                stats = await processor.process_files(test_folder, max_files, 1)
                processor.logger.info(f"\n✅ Test completed: {stats}")
            except Exception as e:
                print(f"❌ Fatal error in test run: {e}")
                print(f"❌ Full traceback: {traceback.format_exc()}")

                # Enable debugger on fatal error
                print("🐛 Entering debugger due to fatal error...")
                pdb.post_mortem()
                raise

        asyncio.run(test_run())
    else:
        # Run with command line arguments
        exit_code = asyncio.run(main())
        sys.exit(exit_code)
