{"classification_result": {"documents": [{"page_no": 1, "doc_type": "po"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "3d68c2be-8872-405d-9a81-df32a5cb22c8", "Relationships": [{"Type": "CHILD", "Ids": ["0bfa5417-694a-4952-b7b7-5ed9dcb8c31c", "bbc85ddf-6b75-4a1e-b7d5-b25415e5a376", "dbc146ea-b9fe-4c21-ac62-d16c8dc2a84a", "c2bfef11-a0c7-4857-82f5-e84be5ef758b", "b8697f63-c36a-4bb9-939b-cb81d65729cf", "b4f95cb2-85ad-4493-85e0-09a29c70c6f2", "a5b506d6-4c0c-432c-b961-e36cdd766213", "894b335a-bc8e-4dd7-bcfc-ec83bc9cc104", "73776f0d-a9d4-420a-b520-5e3e1813f55a", "2337f7fa-65b8-43ee-90f3-6c333aa26b6b", "3c9c193a-b9f5-4f89-a2b5-73af8aac77b6", "724f19cf-f061-4a36-9258-46445f6c3063", "4701d646-cf0c-4cf4-bdd2-eba339da4294", "c149d8b2-ad37-47f4-9543-451b39382ca8", "d0ea4caa-3812-451b-9d0d-5f27d3ca29a7", "cebe1c30-cce6-4772-b3a9-0d3f9813486d", "8ac4dc15-88bf-4a5d-8faf-fca81cb94042", "6754955f-89e4-49da-b3c3-82d3dd161db9", "ebd28e80-1453-4b1d-939d-f89b02728669", "d05722be-8d1f-41c5-b29d-77bd436351a6", "94963063-7d84-40df-8ae0-d9a8679aa1c2", "ad3473b4-e631-417e-870c-4babc120e3f6", "8aa60877-7669-42f4-a37c-a475003fce79", "86c214be-2484-46cf-a561-2cc81cf7d9d9", "9560d870-5404-4e6d-a5c2-2455ec7350b6", "159bf293-64a1-4061-b8e6-eace8a247a08", "15409626-5a85-404d-b008-3c5973147739", "1b7fb5b2-eada-4037-9e70-e3c96514c386", "fe9d49aa-b7da-4fc9-b08a-f960a315fc81", "cf4ff8c9-5e5d-4184-8ac9-d6450a292c98", "76d5eea5-82e6-4cc4-9669-46a8a50c1e1b", "b99b70c6-8e3d-4303-af55-ceef7a4f5c37", "e1e65f1d-ffa5-41df-b46e-c64e4a9346cb", "81e1cbc7-08bd-40b5-84e0-0f9099ff5f57", "c14ae6c5-a5b3-4ef3-b58d-9a303e8ae79e", "03a0a56a-f6b6-42ad-98a8-089fc6b6889e", "026378e7-bc50-45cb-a55c-9e93eef44a2f", "598bb25a-f182-4bca-b962-08ce904b7a87", "84edbc5b-e9e0-4521-b1b2-42488a3ea1e1", "e980d95c-abc1-48c5-a09c-59a9d199a9a0", "e444d9d9-e707-48ba-abd9-9a6c4e5f49ba", "1818b75a-c82a-413c-b274-095e30db616f", "373b1969-cb3d-4534-bbbf-09c58fa6e827", "16d034ff-754a-44c0-8b45-56b81098c591", "aa6e655a-68fb-4864-a260-cf65a468db2d", "5c1f9734-f5c2-4864-9078-67eae3000ee6"]}]}, {"BlockType": "LINE", "Confidence": 99.98027038574219, "Text": "Big Joe Handling Systems", "Geometry": {"BoundingBox": {"Width": 0.16349628567695618, "Height": 0.009405409917235374, "Left": 0.2817622721195221, "Top": 0.06081521511077881}, "Polygon": [{"X": 0.2817622721195221, "Y": 0.060815878212451935}, {"X": 0.44525855779647827, "Y": 0.06081521511077881}, {"X": 0.44525855779647827, "Y": 0.0702199712395668}, {"X": 0.2817623019218445, "Y": 0.07022062689065933}]}, "Id": "0bfa5417-694a-4952-b7b7-5ed9dcb8c31c", "Relationships": [{"Type": "CHILD", "Ids": ["c3e7302e-9fc5-4902-8a78-d271b1e7bb7d", "b509ffea-e384-4d68-92b8-088db9a2bf68", "f76b08de-2701-472d-8eb6-c65f3a4b1448", "4d8d2f0c-b939-4018-9ecf-25b0e9a025ed"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Purchase Order", "Geometry": {"BoundingBox": {"Width": 0.3212885856628418, "Height": 0.028086096048355103, "Left": 0.6201949119567871, "Top": 0.04792019724845886}, "Polygon": [{"X": 0.6201949119567871, "Y": 0.047921523451805115}, {"X": 0.9414834380149841, "Y": 0.04792019724845886}, {"X": 0.9414833188056946, "Y": 0.0760050117969513}, {"X": 0.6201949119567871, "Y": 0.07600629329681396}]}, "Id": "bbc85ddf-6b75-4a1e-b7d5-b25415e5a376", "Relationships": [{"Type": "CHILD", "Ids": ["ae8c5e7e-d696-4a1a-a2e6-410656b8b17e", "4fd4ce30-fcd6-4f29-86e4-f7a67ab68404"]}]}, {"BlockType": "LINE", "Confidence": 94.912109375, "Text": "BIG", "Geometry": {"BoundingBox": {"Width": 0.08005804568529129, "Height": 0.018342377617955208, "Left": 0.08251762390136719, "Top": 0.0746232345700264}, "Polygon": [{"X": 0.08251762390136719, "Y": 0.07462355494499207}, {"X": 0.1625755876302719, "Y": 0.0746232345700264}, {"X": 0.16257567703723907, "Y": 0.09296530485153198}, {"X": 0.08251772820949554, "Y": 0.09296561777591705}]}, "Id": "dbc146ea-b9fe-4c21-ac62-d16c8dc2a84a", "Relationships": [{"Type": "CHILD", "Ids": ["448015a3-173a-4624-b908-a636ecb7c508"]}]}, {"BlockType": "LINE", "Confidence": 94.17889404296875, "Text": "JOE", "Geometry": {"BoundingBox": {"Width": 0.09248780459165573, "Height": 0.01882021129131317, "Left": 0.16339731216430664, "Top": 0.07447873055934906}, "Polygon": [{"X": 0.16339731216430664, "Y": 0.0744791030883789}, {"X": 0.2558850347995758, "Y": 0.07447873055934906}, {"X": 0.25588512420654297, "Y": 0.09329858422279358}, {"X": 0.1633973866701126, "Y": 0.09329894185066223}]}, "Id": "c2bfef11-a0c7-4857-82f5-e84be5ef758b", "Relationships": [{"Type": "CHILD", "Ids": ["fa8e0a3b-94c4-4e40-bb29-c4958bb0a747"]}]}, {"BlockType": "LINE", "Confidence": 99.9124755859375, "Text": "25932 Eden Landing Rd. Hayward, CA 94545", "Geometry": {"BoundingBox": {"Width": 0.22952143847942352, "Height": 0.008360087871551514, "Left": 0.2816944122314453, "Top": 0.07360942661762238}, "Polygon": [{"X": 0.2816944122314453, "Y": 0.0736103430390358}, {"X": 0.51121586561203, "Y": 0.07360942661762238}, {"X": 0.51121586561203, "Y": 0.08196861296892166}, {"X": 0.2816944420337677, "Y": 0.08196951448917389}]}, "Id": "b8697f63-c36a-4bb9-939b-cb81d65729cf", "Relationships": [{"Type": "CHILD", "Ids": ["5f584f6c-a663-43b4-9aee-037d5896df10", "f6b36552-894d-419a-92a3-180f0d9e848c", "8714ea95-a91f-4273-935a-61e760676cde", "8953c856-a981-476c-899d-9d11ba3988c3", "9b1ccbc9-7f04-4f48-8145-3652ee26db6a", "00b38eeb-7314-4088-9a16-d3842a126754", "7e77019a-098c-4a8e-a2b7-88c3a7925def"]}]}, {"BlockType": "LINE", "Confidence": 99.19244384765625, "Text": "PH: (*************", "Geometry": {"BoundingBox": {"Width": 0.0993078351020813, "Height": 0.008120045997202396, "Left": 0.2822999358177185, "Top": 0.08580455183982849}, "Polygon": [{"X": 0.2822999358177185, "Y": 0.08580494672060013}, {"X": 0.3816077709197998, "Y": 0.08580455183982849}, {"X": 0.3816077709197998, "Y": 0.09392421692609787}, {"X": 0.2822999656200409, "Y": 0.09392460435628891}]}, "Id": "b4f95cb2-85ad-4493-85e0-09a29c70c6f2", "Relationships": [{"Type": "CHILD", "Ids": ["f19e4dda-4ffd-4e11-9695-420801afa3d0", "9acbb2ab-728e-45fb-98bf-24601bbb0f50", "c171d80e-e56f-4c49-a86b-9b0b9556511e"]}]}, {"BlockType": "LINE", "Confidence": 99.3031234741211, "Text": "FAX: (*************", "Geometry": {"BoundingBox": {"Width": 0.10556130111217499, "Height": 0.008186311461031437, "Left": 0.40668290853500366, "Top": 0.08571512252092361}, "Polygon": [{"X": 0.40668290853500366, "Y": 0.08571553975343704}, {"X": 0.5122442245483398, "Y": 0.08571512252092361}, {"X": 0.5122442245483398, "Y": 0.09390103071928024}, {"X": 0.40668293833732605, "Y": 0.09390143305063248}]}, "Id": "a5b506d6-4c0c-432c-b961-e36cdd766213", "Relationships": [{"Type": "CHILD", "Ids": ["944d291e-8711-4fa8-9654-50f9e5d9d405", "44c67879-a9b7-42d8-a1b7-c44520d32df9", "b3d1bca5-1f14-4ac8-9b47-4f71b22bc5d3"]}]}, {"BlockType": "LINE", "Confidence": 99.96054077148438, "Text": "Pending Receipt", "Geometry": {"BoundingBox": {"Width": 0.09550905972719193, "Height": 0.010359090752899647, "Left": 0.6181321144104004, "Top": 0.08678270876407623}, "Polygon": [{"X": 0.6181321144104004, "Y": 0.08678308129310608}, {"X": 0.7136411666870117, "Y": 0.08678270876407623}, {"X": 0.7136411666870117, "Y": 0.09714142978191376}, {"X": 0.6181321144104004, "Y": 0.0971418023109436}]}, "Id": "894b335a-bc8e-4dd7-bcfc-ec83bc9cc104", "Relationships": [{"Type": "CHILD", "Ids": ["2efe65b7-4ef0-4c23-98d4-bd9311778f06", "3a85749b-dfe4-4fc6-8f90-d15d857f95c1"]}]}, {"BlockType": "LINE", "Confidence": 97.67916870117188, "Text": "www.BigJoeLift.com", "Geometry": {"BoundingBox": {"Width": 0.10732562839984894, "Height": 0.008487005718052387, "Left": 0.28214749693870544, "Top": 0.09766234457492828}, "Polygon": [{"X": 0.28214749693870544, "Y": 0.09766275435686111}, {"X": 0.3894731104373932, "Y": 0.09766234457492828}, {"X": 0.3894731104373932, "Y": 0.10614893585443497}, {"X": 0.28214752674102783, "Y": 0.1061493456363678}]}, "Id": "73776f0d-a9d4-420a-b520-5e3e1813f55a", "Relationships": [{"Type": "CHILD", "Ids": ["d2aca64b-8e00-4470-9495-5bf6f94d6b51"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "#95371", "Geometry": {"BoundingBox": {"Width": 0.08356638997793198, "Height": 0.015386692248284817, "Left": 0.8537198305130005, "Top": 0.09860842674970627}, "Polygon": [{"X": 0.8537198901176453, "Y": 0.09860874712467194}, {"X": 0.9372861981391907, "Y": 0.09860842674970627}, {"X": 0.9372861385345459, "Y": 0.11399479955434799}, {"X": 0.8537198305130005, "Y": 0.11399511247873306}]}, "Id": "2337f7fa-65b8-43ee-90f3-6c333aa26b6b", "Relationships": [{"Type": "CHILD", "Ids": ["0fef8070-dc02-4d4d-898d-24e1b33efd64"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "7/7/2025", "Geometry": {"BoundingBox": {"Width": 0.06286142766475677, "Height": 0.010031228885054588, "Left": 0.8776538968086243, "Top": 0.11875366419553757}, "Polygon": [{"X": 0.877653956413269, "Y": 0.11875390261411667}, {"X": 0.9405153393745422, "Y": 0.11875366419553757}, {"X": 0.9405152797698975, "Y": 0.1287846565246582}, {"X": 0.8776538968086243, "Y": 0.1287848949432373}]}, "Id": "3c9c193a-b9f5-4f89-a2b5-73af8aac77b6", "Relationships": [{"Type": "CHILD", "Ids": ["9f78f7e2-f186-4e1b-8723-ea29e5e665ef"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.04493730142712593, "Height": 0.008390954695641994, "Left": 0.06629518419504166, "Top": 0.15075194835662842}, "Polygon": [{"X": 0.06629518419504166, "Y": 0.15075211226940155}, {"X": 0.1112324446439743, "Y": 0.15075194835662842}, {"X": 0.11123248934745789, "Y": 0.15914274752140045}, {"X": 0.06629522889852524, "Y": 0.1591428965330124}]}, "Id": "724f19cf-f061-4a36-9258-46445f6c3063", "Relationships": [{"Type": "CHILD", "Ids": ["1b960582-aa93-412a-8911-7306e59e2818"]}]}, {"BlockType": "LINE", "Confidence": 99.6778335571289, "Text": "Ship To", "Geometry": {"BoundingBox": {"Width": 0.045515984296798706, "Height": 0.010628256015479565, "Left": 0.3072052299976349, "Top": 0.1504891812801361}, "Polygon": [{"X": 0.3072052299976349, "Y": 0.15048934519290924}, {"X": 0.3527211844921112, "Y": 0.1504891812801361}, {"X": 0.3527212142944336, "Y": 0.161117285490036}, {"X": 0.3072052597999573, "Y": 0.16111743450164795}]}, "Id": "4701d646-cf0c-4cf4-bdd2-eba339da4294", "Relationships": [{"Type": "CHILD", "Ids": ["00a588e3-f74a-435f-9b9d-d78a44bcbcab", "3269db76-1130-4df5-b4c9-8eb74ff78eae"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "TOTAL", "Geometry": {"BoundingBox": {"Width": 0.06236431375145912, "Height": 0.012096742168068886, "Left": 0.5476253032684326, "Top": 0.1485794186592102}, "Polygon": [{"X": 0.5476253032684326, "Y": 0.1485796421766281}, {"X": 0.609989583492279, "Y": 0.1485794186592102}, {"X": 0.609989583492279, "Y": 0.16067594289779663}, {"X": 0.5476253032684326, "Y": 0.16067616641521454}]}, "Id": "c149d8b2-ad37-47f4-9543-451b39382ca8", "Relationships": [{"Type": "CHILD", "Ids": ["f86f40d7-d789-4d63-966b-f01efe98d559"]}]}, {"BlockType": "LINE", "Confidence": 99.97342681884766, "Text": "Global Forwarding Enterprises", "Geometry": {"BoundingBox": {"Width": 0.22480419278144836, "Height": 0.011567376554012299, "Left": 0.0667731910943985, "Top": 0.16795934736728668}, "Polygon": [{"X": 0.0667731910943985, "Y": 0.16796012222766876}, {"X": 0.2915773391723633, "Y": 0.16795934736728668}, {"X": 0.29157739877700806, "Y": 0.1795259565114975}, {"X": 0.06677325069904327, "Y": 0.17952673137187958}]}, "Id": "d0ea4caa-3812-451b-9d0d-5f27d3ca29a7", "Relationships": [{"Type": "CHILD", "Ids": ["d27d3414-eab8-4d46-bb6c-4c7c1ca0ce8e", "2a310ced-2b89-4a3b-b74d-5b35a097f83d", "c065b8f3-ca95-45d1-bbd5-01eb4ae37ada"]}]}, {"BlockType": "LINE", "Confidence": 98.47453308105469, "Text": "<PERSON> - Hayward", "Geometry": {"BoundingBox": {"Width": 0.18108311295509338, "Height": 0.011570598930120468, "Left": 0.3075096309185028, "Top": 0.16793517768383026}, "Polygon": [{"X": 0.3075096309185028, "Y": 0.1679358035326004}, {"X": 0.4885927438735962, "Y": 0.16793517768383026}, {"X": 0.4885927438735962, "Y": 0.1795051544904709}, {"X": 0.3075096607208252, "Y": 0.17950578033924103}]}, "Id": "cebe1c30-cce6-4772-b3a9-0d3f9813486d", "Relationships": [{"Type": "CHILD", "Ids": ["a2603eb0-2259-4342-8ddf-65513af0a928", "4cb9d5ee-3486-42d3-80d9-7a8016cb644c", "9cdbcefd-1e9d-4a21-bfa3-66b6302cce46", "18f322cd-135b-4926-a9b8-e638e9f495bb", "2bf00952-0c73-45f0-9b96-5ff4af3a35d4"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "LLC", "Geometry": {"BoundingBox": {"Width": 0.026417456567287445, "Height": 0.009239179082214832, "Left": 0.06691258400678635, "Top": 0.1816728115081787}, "Polygon": [{"X": 0.06691258400678635, "Y": 0.18167290091514587}, {"X": 0.09332999587059021, "Y": 0.1816728115081787}, {"X": 0.09333004057407379, "Y": 0.1909119039773941}, {"X": 0.06691263616085052, "Y": 0.19091199338436127}]}, "Id": "8ac4dc15-88bf-4a5d-8faf-fca81cb94042", "Relationships": [{"Type": "CHILD", "Ids": ["af402f54-736e-4023-957b-94ee09f2be97"]}]}, {"BlockType": "LINE", "Confidence": 99.96562194824219, "Text": "25932 Eden Landing Rd.", "Geometry": {"BoundingBox": {"Width": 0.16224049031734467, "Height": 0.011318630538880825, "Left": 0.3070559501647949, "Top": 0.18168552219867706}, "Polygon": [{"X": 0.3070559501647949, "Y": 0.18168607354164124}, {"X": 0.4692964553833008, "Y": 0.18168552219867706}, {"X": 0.4692964553833008, "Y": 0.1930036097764969}, {"X": 0.3070559799671173, "Y": 0.19300414621829987}]}, "Id": "6754955f-89e4-49da-b3c3-82d3dd161db9", "Relationships": [{"Type": "CHILD", "Ids": ["534a4913-d3ba-4a70-8f4a-b1fc55de9e86", "364787bf-a5ff-428a-be18-f23a3643d1b7", "349a4589-0ebc-4f78-8da2-8335477ea7ae", "b547055a-d834-4966-8a5c-c75c95b1644a"]}]}, {"BlockType": "LINE", "Confidence": 99.83561706542969, "Text": "200 Craig Rd. St 220", "Geometry": {"BoundingBox": {"Width": 0.13620395958423615, "Height": 0.01159562636166811, "Left": 0.06657242774963379, "Top": 0.19513797760009766}, "Polygon": [{"X": 0.06657242774963379, "Y": 0.19513842463493347}, {"X": 0.20277634263038635, "Y": 0.19513797760009766}, {"X": 0.20277638733386993, "Y": 0.20673316717147827}, {"X": 0.06657249480485916, "Y": 0.2067335993051529}]}, "Id": "ebd28e80-1453-4b1d-939d-f89b02728669", "Relationships": [{"Type": "CHILD", "Ids": ["f4b17169-1718-4f87-9c57-7035979c0a65", "2f021664-c77e-4707-a2c3-977e9c18c8fd", "cd51a2cd-c743-4997-b362-6097f40d1c91", "77c70fc7-09fe-4180-897f-d7aff1b5bb75", "f2add8a1-09e0-4c5e-b6a2-aff2fceae4c8"]}]}, {"BlockType": "LINE", "Confidence": 99.95063781738281, "Text": "Hayward CA 94545", "Geometry": {"BoundingBox": {"Width": 0.12572933733463287, "Height": 0.01124502345919609, "Left": 0.30781739950180054, "Top": 0.19534659385681152}, "Polygon": [{"X": 0.30781739950180054, "Y": 0.19534701108932495}, {"X": 0.4335467219352722, "Y": 0.19534659385681152}, {"X": 0.4335467517375946, "Y": 0.20659120380878448}, {"X": 0.3078174293041229, "Y": 0.2065916210412979}]}, "Id": "d05722be-8d1f-41c5-b29d-77bd436351a6", "Relationships": [{"Type": "CHILD", "Ids": ["2dd5caa5-bc71-40d2-b15b-316c18529514", "4308804a-9d6c-49c7-af42-3fe749fb6b95", "f00eaf37-3d9f-4e9c-9180-209b040f2547"]}]}, {"BlockType": "LINE", "Confidence": 99.95416259765625, "Text": "Manalapan NJ 07726", "Geometry": {"BoundingBox": {"Width": 0.13933530449867249, "Height": 0.011218215338885784, "Left": 0.06691035628318787, "Top": 0.20905974507331848}, "Polygon": [{"X": 0.06691035628318787, "Y": 0.2090602070093155}, {"X": 0.20624561607837677, "Y": 0.20905974507331848}, {"X": 0.20624566078186035, "Y": 0.22027751803398132}, {"X": 0.06691042333841324, "Y": 0.22027796506881714}]}, "Id": "94963063-7d84-40df-8ae0-d9a8679aa1c2", "Relationships": [{"Type": "CHILD", "Ids": ["10835e35-9d0a-4886-b056-f489be07f696", "f61456ec-eef3-461e-9e74-29f9da9a10b1", "5c4888a6-bf43-4089-82f8-6f8eab5cb2c0"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "United States", "Geometry": {"BoundingBox": {"Width": 0.08775652199983597, "Height": 0.00919326301664114, "Left": 0.30755606293678284, "Top": 0.20903632044792175}, "Polygon": [{"X": 0.30755606293678284, "Y": 0.20903660356998444}, {"X": 0.3953125774860382, "Y": 0.20903632044792175}, {"X": 0.3953126072883606, "Y": 0.21822930872440338}, {"X": 0.3075560927391052, "Y": 0.21822959184646606}]}, "Id": "ad3473b4-e631-417e-870c-4babc120e3f6", "Relationships": [{"Type": "CHILD", "Ids": ["9515dc17-3648-4f6b-b4c5-3004a4a76df4", "bb8be03f-f92b-44cd-ba41-7544dfd05903"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "$422.97", "Geometry": {"BoundingBox": {"Width": 0.16387654840946198, "Height": 0.03226066380739212, "Left": 0.7688495516777039, "Top": 0.18581847846508026}, "Polygon": [{"X": 0.7688496708869934, "Y": 0.18581902980804443}, {"X": 0.9327261447906494, "Y": 0.18581847846508026}, {"X": 0.9327259659767151, "Y": 0.21807861328125}, {"X": 0.7688495516777039, "Y": 0.21807913482189178}]}, "Id": "8aa60877-7669-42f4-a37c-a475003fce79", "Relationships": [{"Type": "CHILD", "Ids": ["ee6d9a98-dac9-48bd-bc6a-9350e4890068"]}]}, {"BlockType": "LINE", "Confidence": 99.9853515625, "Text": "United States", "Geometry": {"BoundingBox": {"Width": 0.08788874000310898, "Height": 0.00924442708492279, "Left": 0.0669642761349678, "Top": 0.22264273464679718}, "Polygon": [{"X": 0.0669642761349678, "Y": 0.22264300286769867}, {"X": 0.1548529714345932, "Y": 0.22264273464679718}, {"X": 0.15485301613807678, "Y": 0.2318868786096573}, {"X": 0.06696432828903198, "Y": 0.23188716173171997}]}, "Id": "86c214be-2484-46cf-a561-2cc81cf7d9d9", "Relationships": [{"Type": "CHILD", "Ids": ["3a2985eb-ee73-4fd3-9d83-9ea2ce84b381", "ab6d76d0-4d1e-4b82-88f4-162dbae3e59e"]}]}, {"BlockType": "LINE", "Confidence": 99.95063781738281, "Text": "Receive By: 7/18/2025", "Geometry": {"BoundingBox": {"Width": 0.15246790647506714, "Height": 0.011462950147688389, "Left": 0.7804251313209534, "Top": 0.231617271900177}, "Polygon": [{"X": 0.7804251909255981, "Y": 0.2316177487373352}, {"X": 0.9328930377960205, "Y": 0.231617271900177}, {"X": 0.9328929781913757, "Y": 0.2430797666311264}, {"X": 0.7804251313209534, "Y": 0.2430802285671234}]}, "Id": "9560d870-5404-4e6d-a5c2-2455ec7350b6", "Relationships": [{"Type": "CHILD", "Ids": ["8a4d246b-3c92-45e6-a3f5-d8fb2226ef0f", "bfe49169-6c5e-46d3-82f1-5b6aee1fca80", "1e51331e-b56f-4867-8ca0-ea259c841afe"]}]}, {"BlockType": "LINE", "Confidence": 99.9109115600586, "Text": "Receive By", "Geometry": {"BoundingBox": {"Width": 0.0696040466427803, "Height": 0.010334646329283714, "Left": 0.06674324721097946, "Top": 0.25222939252853394}, "Polygon": [{"X": 0.06674324721097946, "Y": 0.25222960114479065}, {"X": 0.13634724915027618, "Y": 0.25222939252853394}, {"X": 0.13634729385375977, "Y": 0.2625638246536255}, {"X": 0.06674330681562424, "Y": 0.2625640332698822}]}, "Id": "159bf293-64a1-4061-b8e6-eace8a247a08", "Relationships": [{"Type": "CHILD", "Ids": ["0e94c921-eb3d-4c53-b523-10b5cc5f5d76", "c3c0ffe3-4370-43f2-a724-2d676c12e2ba"]}]}, {"BlockType": "LINE", "Confidence": 99.64684295654297, "Text": "Vendor #", "Geometry": {"BoundingBox": {"Width": 0.05639754235744476, "Height": 0.008581279776990414, "Left": 0.3601459264755249, "Top": 0.25209715962409973}, "Polygon": [{"X": 0.3601459264755249, "Y": 0.25209730863571167}, {"X": 0.41654345393180847, "Y": 0.25209715962409973}, {"X": 0.41654348373413086, "Y": 0.2606782615184784}, {"X": 0.3601459562778473, "Y": 0.2606784403324127}]}, "Id": "15409626-5a85-404d-b008-3c5973147739", "Relationships": [{"Type": "CHILD", "Ids": ["d4e04a18-e2f3-4075-837b-3fa9a43532f7", "dbb8cc4f-4091-44af-ae9b-dd73fe245974"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Billing Phone", "Geometry": {"BoundingBox": {"Width": 0.08337266743183136, "Height": 0.010446006432175636, "Left": 0.6549813151359558, "Top": 0.25224220752716064}, "Polygon": [{"X": 0.6549813747406006, "Y": 0.25224244594573975}, {"X": 0.7383540272712708, "Y": 0.25224220752716064}, {"X": 0.738353967666626, "Y": 0.2626879811286926}, {"X": 0.6549813151359558, "Y": 0.26268821954727173}]}, "Id": "1b7fb5b2-eada-4037-9e70-e3c96514c386", "Relationships": [{"Type": "CHILD", "Ids": ["5bcb79b7-a5d3-4c29-a300-84166b613404", "f7c7defe-d51d-4a21-946c-601fa727ee7e"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "7/18/2025", "Geometry": {"BoundingBox": {"Width": 0.06568499654531479, "Height": 0.00922228954732418, "Left": 0.06631606817245483, "Top": 0.26750436425209045}, "Polygon": [{"X": 0.06631606817245483, "Y": 0.26750457286834717}, {"X": 0.13200102746486664, "Y": 0.26750436425209045}, {"X": 0.13200107216835022, "Y": 0.27672648429870605}, {"X": 0.06631612032651901, "Y": 0.2767266631126404}]}, "Id": "fe9d49aa-b7da-4fc9-b08a-f960a315fc81", "Relationships": [{"Type": "CHILD", "Ids": ["dafc8c66-e0f5-46f3-a2d5-c1f99562ef5e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Quantity", "Geometry": {"BoundingBox": {"Width": 0.05290158838033676, "Height": 0.010498124174773693, "Left": 0.08316806703805923, "Top": 0.29312795400619507}, "Polygon": [{"X": 0.08316806703805923, "Y": 0.293128103017807}, {"X": 0.13606961071491241, "Y": 0.29312795400619507}, {"X": 0.136069655418396, "Y": 0.3036259412765503}, {"X": 0.08316811919212341, "Y": 0.30362609028816223}]}, "Id": "cf4ff8c9-5e5d-4184-8ac9-d6450a292c98", "Relationships": [{"Type": "CHILD", "Ids": ["54e0e3ad-1478-4a89-ae32-9d7e8b8bd88e"]}]}, {"BlockType": "LINE", "Confidence": 99.89098358154297, "Text": "<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.026606256142258644, "Height": 0.008383997716009617, "Left": 0.16853205859661102, "Top": 0.29314491152763367}, "Polygon": [{"X": 0.16853205859661102, "Y": 0.29314500093460083}, {"X": 0.19513827562332153, "Y": 0.29314491152763367}, {"X": 0.19513830542564392, "Y": 0.30152884125709534}, {"X": 0.1685320883989334, "Y": 0.3015289008617401}]}, "Id": "76d5eea5-82e6-4cc4-9669-46a8a50c1e1b", "Relationships": [{"Type": "CHILD", "Ids": ["e4fe760c-e5b4-4359-b452-db5964943409"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Options", "Geometry": {"BoundingBox": {"Width": 0.049284934997558594, "Height": 0.01028106827288866, "Left": 0.5757012963294983, "Top": 0.29323700070381165}, "Polygon": [{"X": 0.5757012963294983, "Y": 0.2932371199131012}, {"X": 0.6249862313270569, "Y": 0.29323700070381165}, {"X": 0.6249862313270569, "Y": 0.3035179376602173}, {"X": 0.5757012963294983, "Y": 0.30351805686950684}]}, "Id": "b99b70c6-8e3d-4303-af55-ceef7a4f5c37", "Relationships": [{"Type": "CHILD", "Ids": ["b4928ca3-a73d-47eb-b1a6-5fdf0358c438"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Rate", "Geometry": {"BoundingBox": {"Width": 0.02798309735953808, "Height": 0.008422311395406723, "Left": 0.7693250775337219, "Top": 0.29314565658569336}, "Polygon": [{"X": 0.7693250775337219, "Y": 0.29314571619033813}, {"X": 0.7973081469535828, "Y": 0.29314565658569336}, {"X": 0.7973081469535828, "Y": 0.3015678822994232}, {"X": 0.7693250775337219, "Y": 0.301567941904068}]}, "Id": "e1e65f1d-ffa5-41df-b46e-c64e4a9346cb", "Relationships": [{"Type": "CHILD", "Ids": ["642cde54-9d96-4773-b1e6-b680835d9434"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.049531806260347366, "Height": 0.008376441895961761, "Left": 0.883677065372467, "Top": 0.29317596554756165}, "Polygon": [{"X": 0.8836771249771118, "Y": 0.2931761145591736}, {"X": 0.9332088828086853, "Y": 0.29317596554756165}, {"X": 0.9332088828086853, "Y": 0.30155229568481445}, {"X": 0.883677065372467, "Y": 0.301552414894104}]}, "Id": "81e1cbc7-08bd-40b5-84e0-0f9099ff5f57", "Relationships": [{"Type": "CHILD", "Ids": ["de9549de-478a-4042-9126-30244704ffe1"]}]}, {"BlockType": "LINE", "Confidence": 99.41167449951172, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.005230480805039406, "Height": 0.008581405505537987, "Left": 0.1064104437828064, "Top": 0.31449127197265625}, "Polygon": [{"X": 0.1064104437828064, "Y": 0.31449130177497864}, {"X": 0.11164087802171707, "Y": 0.31449127197265625}, {"X": 0.11164092272520065, "Y": 0.3230726718902588}, {"X": 0.10641048848628998, "Y": 0.3230726718902588}]}, "Id": "c14ae6c5-a5b3-4ef3-b58d-9a303e8ae79e", "Relationships": [{"Type": "CHILD", "Ids": ["c93e1a33-be03-4fa9-81a4-7fdc8b119649"]}]}, {"BlockType": "LINE", "Confidence": 99.95565795898438, "Text": "FREIGHT (PO)", "Geometry": {"BoundingBox": {"Width": 0.09891446679830551, "Height": 0.011669488623738289, "Left": 0.1692672222852707, "Top": 0.3207259774208069}, "Polygon": [{"X": 0.1692672222852707, "Y": 0.3207262456417084}, {"X": 0.2681816518306732, "Y": 0.3207259774208069}, {"X": 0.2681816816329956, "Y": 0.3323952257633209}, {"X": 0.16926728188991547, "Y": 0.3323954641819}]}, "Id": "03a0a56a-f6b6-42ad-98a8-089fc6b6889e", "Relationships": [{"Type": "CHILD", "Ids": ["e673d7ce-0e6a-4285-aba5-f4cfd34072ea", "c199beba-5ec4-4f1d-980c-58db75d6098c"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "$422.97", "Geometry": {"BoundingBox": {"Width": 0.05356718599796295, "Height": 0.01086129155009985, "Left": 0.743829607963562, "Top": 0.3169335722923279}, "Polygon": [{"X": 0.7438296675682068, "Y": 0.3169337213039398}, {"X": 0.7973968386650085, "Y": 0.3169335722923279}, {"X": 0.7973967790603638, "Y": 0.32779473066329956}, {"X": 0.743829607963562, "Y": 0.3277948796749115}]}, "Id": "026378e7-bc50-45cb-a55c-9e93eef44a2f", "Relationships": [{"Type": "CHILD", "Ids": ["a4ced776-fd5f-409a-ab50-32c05d1fed3e"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "$422.97", "Geometry": {"BoundingBox": {"Width": 0.05334289371967316, "Height": 0.010595420375466347, "Left": 0.8796473741531372, "Top": 0.31698328256607056}, "Polygon": [{"X": 0.8796473741531372, "Y": 0.3169834017753601}, {"X": 0.9329902529716492, "Y": 0.31698328256607056}, {"X": 0.9329901933670044, "Y": 0.3275785446166992}, {"X": 0.8796473741531372, "Y": 0.32757869362831116}]}, "Id": "598bb25a-f182-4bca-b962-08ce904b7a87", "Relationships": [{"Type": "CHILD", "Ids": ["60717bf2-72dc-4199-8d5e-93f00a0815c1"]}]}, {"BlockType": "LINE", "Confidence": 99.70633697509766, "Text": "Bufdalo NY", "Geometry": {"BoundingBox": {"Width": 0.0736922174692154, "Height": 0.009054758585989475, "Left": 0.1689646691083908, "Top": 0.3378324806690216}, "Polygon": [{"X": 0.1689646691083908, "Y": 0.33783265948295593}, {"X": 0.24265684187412262, "Y": 0.3378324806690216}, {"X": 0.2426568865776062, "Y": 0.3468870520591736}, {"X": 0.1689646989107132, "Y": 0.3468872606754303}]}, "Id": "84edbc5b-e9e0-4521-b1b2-42488a3ea1e1", "Relationships": [{"Type": "CHILD", "Ids": ["b9716863-ebd0-4f8d-a694-06029747722d", "d9ecdfee-fcb2-46ea-9d8c-de30fb7b2a0b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.03379287198185921, "Height": 0.008950673043727875, "Left": 0.7516760230064392, "Top": 0.37601542472839355}, "Polygon": [{"X": 0.7516760230064392, "Y": 0.3760155141353607}, {"X": 0.7854688763618469, "Y": 0.37601542472839355}, {"X": 0.7854688763618469, "Y": 0.38496604561805725}, {"X": 0.7516760230064392, "Y": 0.384966105222702}]}, "Id": "e980d95c-abc1-48c5-a09c-59a9d199a9a0", "Relationships": [{"Type": "CHILD", "Ids": ["5bb15963-8318-41ab-9085-6f532d1bdcfa"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "$422.97", "Geometry": {"BoundingBox": {"Width": 0.05259009450674057, "Height": 0.010957999154925346, "Left": 0.8800909519195557, "Top": 0.37534236907958984}, "Polygon": [{"X": 0.8800910115242004, "Y": 0.3753424882888794}, {"X": 0.9326810836791992, "Y": 0.37534236907958984}, {"X": 0.9326810240745544, "Y": 0.386300265789032}, {"X": 0.8800909519195557, "Y": 0.38630038499832153}]}, "Id": "e444d9d9-e707-48ba-abd9-9a6c4e5f49ba", "Relationships": [{"Type": "CHILD", "Ids": ["97ea18f2-014b-405d-8f64-4fef7be965a7"]}]}, {"BlockType": "LINE", "Confidence": 99.48589324951172, "Text": "PLEASE SEND ALL INVOICES TO", "Geometry": {"BoundingBox": {"Width": 0.827544629573822, "Height": 0.030232278630137444, "Left": 0.05855097621679306, "Top": 0.4321165680885315}, "Polygon": [{"X": 0.05855097621679306, "Y": 0.4321182370185852}, {"X": 0.8860955834388733, "Y": 0.4321165680885315}, {"X": 0.8860954642295837, "Y": 0.4623473286628723}, {"X": 0.05855114758014679, "Y": 0.4623488485813141}]}, "Id": "1818b75a-c82a-413c-b274-095e30db616f", "Relationships": [{"Type": "CHILD", "Ids": ["76f5e63b-03b1-46b9-9181-b84138dd7ee1", "44eac225-7c38-48d3-8664-6daefe06dc04", "791285b8-7dc6-4b65-a5a1-fb16bf99f473", "9f72f530-45bf-42bb-8ac2-6ee8515294c1", "6150cb88-f1fc-441e-bf12-88f549ae6657"]}]}, {"BlockType": "LINE", "Confidence": 89.22618865966797, "Text": "<EMAIL> OR CALL", "Geometry": {"BoundingBox": {"Width": 0.8238015174865723, "Height": 0.03025289997458458, "Left": 0.05758785083889961, "Top": 0.48036444187164307}, "Polygon": [{"X": 0.05758785083889961, "Y": 0.4803658723831177}, {"X": 0.8813893795013428, "Y": 0.48036444187164307}, {"X": 0.8813892602920532, "Y": 0.5106160640716553}, {"X": 0.05758802220225334, "Y": 0.5106173157691956}]}, "Id": "373b1969-cb3d-4534-bbbf-09c58fa6e827", "Relationships": [{"Type": "CHILD", "Ids": ["10fcb182-f986-4030-87ad-a4cc0ad3d416", "162c6cc8-2e1c-46e6-9142-dd13864ff608", "3e778ad1-335e-48ea-9797-ad4f63f3ef94"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "510-785-6900", "Geometry": {"BoundingBox": {"Width": 0.2945959270000458, "Height": 0.030028486624360085, "Left": 0.06012586131691933, "Top": 0.5290759801864624}, "Polygon": [{"X": 0.06012586131691933, "Y": 0.5290763974189758}, {"X": 0.35472172498703003, "Y": 0.5290759801864624}, {"X": 0.3547217845916748, "Y": 0.5591040849685669}, {"X": 0.06012602895498276, "Y": 0.5591044425964355}]}, "Id": "16d034ff-754a-44c0-8b45-56b81098c591", "Relationships": [{"Type": "CHILD", "Ids": ["d4eb92cc-edf0-40d3-92ee-aa09f34553aa"]}]}, {"BlockType": "LINE", "Confidence": 99.15178680419922, "Text": "1 of 1", "Geometry": {"BoundingBox": {"Width": 0.030536210164427757, "Height": 0.00869656354188919, "Left": 0.8796964883804321, "Top": 0.9285483956336975}, "Polygon": [{"X": 0.8796964883804321, "Y": 0.9285483956336975}, {"X": 0.910232663154602, "Y": 0.9285483956336975}, {"X": 0.910232663154602, "Y": 0.9372449517250061}, {"X": 0.8796964883804321, "Y": 0.9372449517250061}]}, "Id": "aa6e655a-68fb-4864-a260-cf65a468db2d", "Relationships": [{"Type": "CHILD", "Ids": ["7499717b-7fe2-4f31-9ef8-19059c454555", "dbfe8a12-e612-48f4-8631-8606cc05b68a", "e4110f4d-73cb-467a-8f17-91893bdddae0"]}]}, {"BlockType": "LINE", "Confidence": 99.951171875, "Text": "95371", "Geometry": {"BoundingBox": {"Width": 0.03468852490186691, "Height": 0.008173900656402111, "Left": 0.1215488463640213, "Top": 0.9549195766448975}, "Polygon": [{"X": 0.1215488463640213, "Y": 0.9549195766448975}, {"X": 0.15623733401298523, "Y": 0.9549195766448975}, {"X": 0.1562373787164688, "Y": 0.9630934596061707}, {"X": 0.12154889106750488, "Y": 0.9630934596061707}]}, "Id": "5c1f9734-f5c2-4864-9078-67eae3000ee6", "Relationships": [{"Type": "CHILD", "Ids": ["b397359c-5c42-4e4b-9106-b5bade60b30f"]}]}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Big", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020110346376895905, "Height": 0.009322009980678558, "Left": 0.2817622721195221, "Top": 0.060887157917022705}, "Polygon": [{"X": 0.2817622721195221, "Y": 0.06088723987340927}, {"X": 0.301872581243515, "Y": 0.060887157917022705}, {"X": 0.3018726110458374, "Y": 0.0702090859413147}, {"X": 0.2817623019218445, "Y": 0.07020916789770126}]}, "Id": "c3e7302e-9fc5-4902-8a78-d271b1e7bb7d"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022567549720406532, "Height": 0.007503136992454529, "Left": 0.30592066049575806, "Top": 0.06096605211496353}, "Polygon": [{"X": 0.30592066049575806, "Y": 0.060966141521930695}, {"X": 0.32848817110061646, "Y": 0.06096605211496353}, {"X": 0.32848820090293884, "Y": 0.0684690997004509}, {"X": 0.30592066049575806, "Y": 0.06846918910741806}]}, "Id": "b509ffea-e384-4d68-92b8-088db9a2bf68"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Handling", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05517177656292915, "Height": 0.00940497312694788, "Left": 0.3326347768306732, "Top": 0.06081544980406761}, "Polygon": [{"X": 0.3326347768306732, "Y": 0.06081567332148552}, {"X": 0.3878065347671509, "Y": 0.06081544980406761}, {"X": 0.3878065347671509, "Y": 0.07022020220756531}, {"X": 0.3326347768306732, "Y": 0.07022041827440262}]}, "Id": "f76b08de-2701-472d-8eb6-c65f3a4b1448"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Systems", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05332605913281441, "Height": 0.009270776994526386, "Left": 0.39193251729011536, "Top": 0.06092679873108864}, "Polygon": [{"X": 0.39193251729011536, "Y": 0.06092701852321625}, {"X": 0.44525855779647827, "Y": 0.06092679873108864}, {"X": 0.44525855779647827, "Y": 0.07019736617803574}, {"X": 0.39193251729011536, "Y": 0.07019757479429245}]}, "Id": "4d8d2f0c-b939-4018-9ecf-25b0e9a025ed"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.19059409201145172, "Height": 0.027472224086523056, "Left": 0.6201949119567871, "Top": 0.04853406921029091}, "Polygon": [{"X": 0.6201949119567871, "Y": 0.048534855246543884}, {"X": 0.8107889890670776, "Y": 0.04853406921029091}, {"X": 0.8107888698577881, "Y": 0.07600553333759308}, {"X": 0.6201949119567871, "Y": 0.07600629329681396}]}, "Id": "ae8c5e7e-d696-4a1a-a2e6-410656b8b17e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11667757481336594, "Height": 0.02806171402335167, "Left": 0.8248059153556824, "Top": 0.04792019724845886}, "Polygon": [{"X": 0.8248059749603271, "Y": 0.047920677810907364}, {"X": 0.9414834380149841, "Y": 0.04792019724845886}, {"X": 0.9414833188056946, "Y": 0.07598144561052322}, {"X": 0.8248059153556824, "Y": 0.07598190754652023}]}, "Id": "4fd4ce30-fcd6-4f29-86e4-f7a67ab68404"}, {"BlockType": "WORD", "Confidence": 94.912109375, "Text": "BIG", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08005804568529129, "Height": 0.018342377617955208, "Left": 0.08251762390136719, "Top": 0.0746232345700264}, "Polygon": [{"X": 0.08251762390136719, "Y": 0.07462355494499207}, {"X": 0.1625755876302719, "Y": 0.0746232345700264}, {"X": 0.16257567703723907, "Y": 0.09296530485153198}, {"X": 0.08251772820949554, "Y": 0.09296561777591705}]}, "Id": "448015a3-173a-4624-b908-a636ecb7c508"}, {"BlockType": "WORD", "Confidence": 94.17889404296875, "Text": "JOE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09248780459165573, "Height": 0.01882021129131317, "Left": 0.16339731216430664, "Top": 0.07447873055934906}, "Polygon": [{"X": 0.16339731216430664, "Y": 0.0744791030883789}, {"X": 0.2558850347995758, "Y": 0.07447873055934906}, {"X": 0.25588512420654297, "Y": 0.09329858422279358}, {"X": 0.1633973866701126, "Y": 0.09329894185066223}]}, "Id": "fa8e0a3b-94c4-4e40-bb29-c4958bb0a747"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "25932", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03167181462049484, "Height": 0.0068762945011258125, "Left": 0.2816944122314453, "Top": 0.07373670488595963}, "Polygon": [{"X": 0.2816944122314453, "Y": 0.07373683154582977}, {"X": 0.31336620450019836, "Y": 0.07373670488595963}, {"X": 0.31336620450019836, "Y": 0.08061287552118301}, {"X": 0.2816944122314453, "Y": 0.08061300218105316}]}, "Id": "5f584f6c-a663-43b4-9aee-037d5896df10"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "Eden", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02568252757191658, "Height": 0.006965312175452709, "Left": 0.3168717920780182, "Top": 0.0736638680100441}, "Polygon": [{"X": 0.3168717920780182, "Y": 0.07366397231817245}, {"X": 0.3425543010234833, "Y": 0.0736638680100441}, {"X": 0.3425543010234833, "Y": 0.08062908053398132}, {"X": 0.3168717920780182, "Y": 0.08062918484210968}]}, "Id": "f6b36552-894d-419a-92a3-180f0d9e848c"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Landing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03988868370652199, "Height": 0.00817810371518135, "Left": 0.3461969196796417, "Top": 0.07366489619016647}, "Polygon": [{"X": 0.3461969196796417, "Y": 0.07366505265235901}, {"X": 0.3860855996608734, "Y": 0.07366489619016647}, {"X": 0.3860855996608734, "Y": 0.08184283971786499}, {"X": 0.3461969494819641, "Y": 0.08184299618005753}]}, "Id": "8714ea95-a91f-4273-935a-61e760676cde"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "Rd.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01692008227109909, "Height": 0.00702115660533309, "Left": 0.3897560238838196, "Top": 0.07361887395381927}, "Polygon": [{"X": 0.3897560238838196, "Y": 0.07361894100904465}, {"X": 0.4066760838031769, "Y": 0.07361887395381927}, {"X": 0.40667611360549927, "Y": 0.08063995838165283}, {"X": 0.38975605368614197, "Y": 0.0806400254368782}]}, "Id": "8953c856-a981-476c-899d-9d11ba3988c3"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04726460576057434, "Height": 0.008359366096556187, "Left": 0.41019874811172485, "Top": 0.07360964268445969}, "Polygon": [{"X": 0.41019874811172485, "Y": 0.07360982894897461}, {"X": 0.4574633538722992, "Y": 0.07360964268445969}, {"X": 0.4574633538722992, "Y": 0.08196882158517838}, {"X": 0.41019877791404724, "Y": 0.0819690078496933}]}, "Id": "9b1ccbc9-7f04-4f48-8145-3652ee26db6a"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "CA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016085054725408554, "Height": 0.006941172294318676, "Left": 0.4610636532306671, "Top": 0.07363732159137726}, "Polygon": [{"X": 0.4610636532306671, "Y": 0.07363738119602203}, {"X": 0.47714871168136597, "Y": 0.07363732159137726}, {"X": 0.47714871168136597, "Y": 0.08057842403650284}, {"X": 0.4610636532306671, "Y": 0.08057849109172821}]}, "Id": "00b38eeb-7314-4088-9a16-d3842a126754"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "94545", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03148161992430687, "Height": 0.006881343200802803, "Left": 0.4797342121601105, "Top": 0.07368648797273636}, "Polygon": [{"X": 0.4797342121601105, "Y": 0.0736866146326065}, {"X": 0.51121586561203, "Y": 0.07368648797273636}, {"X": 0.51121586561203, "Y": 0.08056771010160446}, {"X": 0.47973424196243286, "Y": 0.08056783676147461}]}, "Id": "7e77019a-098c-4a8e-a2b7-88c3a7925def"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "PH:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018170151859521866, "Height": 0.006664962507784367, "Left": 0.2822999656200409, "Top": 0.08630958199501038}, "Polygon": [{"X": 0.2822999656200409, "Y": 0.08630965650081635}, {"X": 0.3004700839519501, "Y": 0.08630958199501038}, {"X": 0.30047011375427246, "Y": 0.0929744765162468}, {"X": 0.2822999656200409, "Y": 0.09297454357147217}]}, "Id": "f19e4dda-4ffd-4e11-9695-420801afa3d0"}, {"BlockType": "WORD", "Confidence": 98.671875, "Text": "(800)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02571379393339157, "Height": 0.00811975821852684, "Left": 0.3044363856315613, "Top": 0.0858047604560852}, "Polygon": [{"X": 0.3044363856315613, "Y": 0.08580485731363297}, {"X": 0.33015015721321106, "Y": 0.0858047604560852}, {"X": 0.33015015721321106, "Y": 0.09392441809177399}, {"X": 0.3044363856315613, "Y": 0.09392451494932175}]}, "Id": "9acbb2ab-728e-45fb-98bf-24601bbb0f50"}, {"BlockType": "WORD", "Confidence": 99.01287841796875, "Text": "835-1133", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04797670617699623, "Height": 0.0069799176417291164, "Left": 0.33363106846809387, "Top": 0.08610409498214722}, "Polygon": [{"X": 0.33363106846809387, "Y": 0.08610428869724274}, {"X": 0.3816077709197998, "Y": 0.08610409498214722}, {"X": 0.3816077709197998, "Y": 0.09308382868766785}, {"X": 0.33363109827041626, "Y": 0.09308401495218277}]}, "Id": "c171d80e-e56f-4c49-a86b-9b0b9556511e"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "FAX:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02445937879383564, "Height": 0.006970059126615524, "Left": 0.40668290853500366, "Top": 0.08610209822654724}, "Polygon": [{"X": 0.40668290853500366, "Y": 0.086102195084095}, {"X": 0.43114227056503296, "Y": 0.08610209822654724}, {"X": 0.43114230036735535, "Y": 0.0930720642209053}, {"X": 0.40668293833732605, "Y": 0.09307216107845306}]}, "Id": "944d291e-8711-4fa8-9654-50f9e5d9d405"}, {"BlockType": "WORD", "Confidence": 99.091796875, "Text": "(510)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02550126053392887, "Height": 0.00818599946796894, "Left": 0.4350907802581787, "Top": 0.08571532368659973}, "Polygon": [{"X": 0.4350907802581787, "Y": 0.08571542799472809}, {"X": 0.46059203147888184, "Y": 0.08571532368659973}, {"X": 0.46059203147888184, "Y": 0.09390122443437576}, {"X": 0.4350907802581787, "Y": 0.09390132874250412}]}, "Id": "44c67879-a9b7-42d8-a1b7-c44520d32df9"}, {"BlockType": "WORD", "Confidence": 98.9453125, "Text": "785-0908", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04811232537031174, "Height": 0.006825033575296402, "Left": 0.4641318917274475, "Top": 0.08618246018886566}, "Polygon": [{"X": 0.4641318917274475, "Y": 0.08618264645338058}, {"X": 0.5122442245483398, "Y": 0.08618246018886566}, {"X": 0.5122442245483398, "Y": 0.09300730377435684}, {"X": 0.4641318917274475, "Y": 0.09300749003887177}]}, "Id": "b3d1bca5-1f14-4ac8-9b47-4f71b22bc5d3"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Pending", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04712498188018799, "Height": 0.01028219424188137, "Left": 0.6181321144104004, "Top": 0.08685960620641708}, "Polygon": [{"X": 0.6181321144104004, "Y": 0.086859792470932}, {"X": 0.6652570962905884, "Y": 0.08685960620641708}, {"X": 0.6652570962905884, "Y": 0.09714161604642868}, {"X": 0.6181321144104004, "Y": 0.0971418023109436}]}, "Id": "2efe65b7-4ef0-4c23-98d4-bd9311778f06"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04428185150027275, "Height": 0.010316744446754456, "Left": 0.6693593263626099, "Top": 0.08678270876407623}, "Polygon": [{"X": 0.6693593263626099, "Y": 0.08678288012742996}, {"X": 0.7136411666870117, "Y": 0.08678270876407623}, {"X": 0.7136411666870117, "Y": 0.09709928184747696}, {"X": 0.6693593263626099, "Y": 0.09709945321083069}]}, "Id": "3a85749b-dfe4-4fc6-8f90-d15d857f95c1"}, {"BlockType": "WORD", "Confidence": 97.67916870117188, "Text": "www.BigJoeLift.com", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10732562839984894, "Height": 0.008487005718052387, "Left": 0.28214749693870544, "Top": 0.09766234457492828}, "Polygon": [{"X": 0.28214749693870544, "Y": 0.09766275435686111}, {"X": 0.3894731104373932, "Y": 0.09766234457492828}, {"X": 0.3894731104373932, "Y": 0.10614893585443497}, {"X": 0.28214752674102783, "Y": 0.1061493456363678}]}, "Id": "d2aca64b-8e00-4470-9495-5bf6f94d6b51"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "#95371", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08356638997793198, "Height": 0.015386692248284817, "Left": 0.8537198305130005, "Top": 0.09860842674970627}, "Polygon": [{"X": 0.8537198901176453, "Y": 0.09860874712467194}, {"X": 0.9372861981391907, "Y": 0.09860842674970627}, {"X": 0.9372861385345459, "Y": 0.11399479955434799}, {"X": 0.8537198305130005, "Y": 0.11399511247873306}]}, "Id": "0fef8070-dc02-4d4d-898d-24e1b33efd64"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "7/7/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06286142766475677, "Height": 0.010031228885054588, "Left": 0.8776538968086243, "Top": 0.11875366419553757}, "Polygon": [{"X": 0.877653956413269, "Y": 0.11875390261411667}, {"X": 0.9405153393745422, "Y": 0.11875366419553757}, {"X": 0.9405152797698975, "Y": 0.1287846565246582}, {"X": 0.8776538968086243, "Y": 0.1287848949432373}]}, "Id": "9f78f7e2-f186-4e1b-8723-ea29e5e665ef"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04493730142712593, "Height": 0.008390954695641994, "Left": 0.06629518419504166, "Top": 0.15075194835662842}, "Polygon": [{"X": 0.06629518419504166, "Y": 0.15075211226940155}, {"X": 0.1112324446439743, "Y": 0.15075194835662842}, {"X": 0.11123248934745789, "Y": 0.15914274752140045}, {"X": 0.06629522889852524, "Y": 0.1591428965330124}]}, "Id": "1b960582-aa93-412a-8911-7306e59e2818"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02831900492310524, "Height": 0.01062819454818964, "Left": 0.3072052299976349, "Top": 0.15048924088478088}, "Polygon": [{"X": 0.3072052299976349, "Y": 0.15048934519290924}, {"X": 0.33552420139312744, "Y": 0.15048924088478088}, {"X": 0.33552423119544983, "Y": 0.1611173450946808}, {"X": 0.3072052597999573, "Y": 0.16111743450164795}]}, "Id": "00a588e3-f74a-435f-9b9d-d78a44bcbcab"}, {"BlockType": "WORD", "Confidence": 99.44355773925781, "Text": "To", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0150733208283782, "Height": 0.008224422112107277, "Left": 0.3376478850841522, "Top": 0.1508381962776184}, "Polygon": [{"X": 0.3376478850841522, "Y": 0.15083825588226318}, {"X": 0.3527211844921112, "Y": 0.1508381962776184}, {"X": 0.3527212142944336, "Y": 0.15906256437301636}, {"X": 0.3376479148864746, "Y": 0.15906262397766113}]}, "Id": "3269db76-1130-4df5-b4c9-8eb74ff78eae"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TOTAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06236431375145912, "Height": 0.012096742168068886, "Left": 0.5476253032684326, "Top": 0.1485794186592102}, "Polygon": [{"X": 0.5476253032684326, "Y": 0.1485796421766281}, {"X": 0.609989583492279, "Y": 0.1485794186592102}, {"X": 0.609989583492279, "Y": 0.16067594289779663}, {"X": 0.5476253032684326, "Y": 0.16067616641521454}]}, "Id": "f86f40d7-d789-4d63-966b-f01efe98d559"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Global", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044516708701848984, "Height": 0.009346064180135727, "Left": 0.0667731910943985, "Top": 0.16795997321605682}, "Polygon": [{"X": 0.0667731910943985, "Y": 0.16796012222766876}, {"X": 0.1112898513674736, "Y": 0.16795997321605682}, {"X": 0.11128989607095718, "Y": 0.1773058921098709}, {"X": 0.06677324324846268, "Y": 0.17730604112148285}]}, "Id": "d27d3414-eab8-4d46-bb6c-4c7c1ca0ce8e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Forwarding", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07660381495952606, "Height": 0.011509321630001068, "Left": 0.12353938817977905, "Top": 0.1680172085762024}, "Polygon": [{"X": 0.12353938817977905, "Y": 0.16801747679710388}, {"X": 0.20014315843582153, "Y": 0.1680172085762024}, {"X": 0.20014320313930511, "Y": 0.17952626943588257}, {"X": 0.12353944033384323, "Y": 0.17952653765678406}]}, "Id": "2a310ced-2b89-4a3b-b74d-5b35a097f83d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Enterprises", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07924924045801163, "Height": 0.011252819560468197, "Left": 0.21232815086841583, "Top": 0.16797907650470734}, "Polygon": [{"X": 0.21232815086841583, "Y": 0.16797935962677002}, {"X": 0.2915773391723633, "Y": 0.16797907650470734}, {"X": 0.29157739877700806, "Y": 0.17923162877559662}, {"X": 0.21232819557189941, "Y": 0.1792318969964981}]}, "Id": "c065b8f3-ca95-45d1-bbd5-01eb4ae37ada"}, {"BlockType": "WORD", "Confidence": 99.09378814697266, "Text": "Big", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020762939006090164, "Height": 0.01148323155939579, "Left": 0.3075096309185028, "Top": 0.1680225431919098}, "Polygon": [{"X": 0.3075096309185028, "Y": 0.16802261769771576}, {"X": 0.3282725214958191, "Y": 0.1680225431919098}, {"X": 0.3282725512981415, "Y": 0.17950570583343506}, {"X": 0.3075096607208252, "Y": 0.17950578033924103}]}, "Id": "a2603eb0-2259-4342-8ddf-65513af0a928"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02387712337076664, "Height": 0.009105791337788105, "Left": 0.33233949542045593, "Top": 0.16817393898963928}, "Polygon": [{"X": 0.33233949542045593, "Y": 0.16817402839660645}, {"X": 0.3562166094779968, "Y": 0.16817393898963928}, {"X": 0.3562166094779968, "Y": 0.17727965116500854}, {"X": 0.3323395252227783, "Y": 0.17727972567081451}]}, "Id": "4cb9d5ee-3486-42d3-80d9-7a8016cb644c"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "Handling", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05752019211649895, "Height": 0.011513798497617245, "Left": 0.3607417345046997, "Top": 0.16793541610240936}, "Polygon": [{"X": 0.3607417345046997, "Y": 0.16793562471866608}, {"X": 0.41826188564300537, "Y": 0.16793541610240936}, {"X": 0.41826191544532776, "Y": 0.17944902181625366}, {"X": 0.3607417345046997, "Y": 0.17944921553134918}]}, "Id": "9cdbcefd-1e9d-4a21-bfa3-66b6302cce46"}, {"BlockType": "WORD", "Confidence": 93.56285858154297, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004944578744471073, "Height": 0.002257790183648467, "Left": 0.4225909411907196, "Top": 0.17238561809062958}, "Polygon": [{"X": 0.4225909411907196, "Y": 0.17238563299179077}, {"X": 0.4275355041027069, "Y": 0.17238561809062958}, {"X": 0.4275355041027069, "Y": 0.1746433973312378}, {"X": 0.4225909411907196, "Y": 0.174643412232399}]}, "Id": "18f322cd-135b-4926-a9b8-e638e9f495bb"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056926727294921875, "Height": 0.011263145133852959, "Left": 0.4316660165786743, "Top": 0.16811126470565796}, "Polygon": [{"X": 0.4316660165786743, "Y": 0.16811145842075348}, {"X": 0.4885927438735962, "Y": 0.16811126470565796}, {"X": 0.4885927438735962, "Y": 0.17937421798706055}, {"X": 0.4316660165786743, "Y": 0.17937441170215607}]}, "Id": "2bf00952-0c73-45f0-9b96-5ff4af3a35d4"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026417456567287445, "Height": 0.009239179082214832, "Left": 0.06691258400678635, "Top": 0.1816728115081787}, "Polygon": [{"X": 0.06691258400678635, "Y": 0.18167290091514587}, {"X": 0.09332999587059021, "Y": 0.1816728115081787}, {"X": 0.09333004057407379, "Y": 0.1909119039773941}, {"X": 0.06691263616085052, "Y": 0.19091199338436127}]}, "Id": "af402f54-736e-4023-957b-94ee09f2be97"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "25932", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041072458028793335, "Height": 0.008912184275686741, "Left": 0.3070559501647949, "Top": 0.18196949362754822}, "Polygon": [{"X": 0.3070559501647949, "Y": 0.18196962773799896}, {"X": 0.34812840819358826, "Y": 0.18196949362754822}, {"X": 0.34812840819358826, "Y": 0.19088155031204224}, {"X": 0.3070559799671173, "Y": 0.19088168442249298}]}, "Id": "534a4913-d3ba-4a70-8f4a-b1fc55de9e86"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Eden", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033559899777173996, "Height": 0.009023955091834068, "Left": 0.35268136858940125, "Top": 0.18175947666168213}, "Polygon": [{"X": 0.35268136858940125, "Y": 0.18175959587097168}, {"X": 0.38624125719070435, "Y": 0.18175947666168213}, {"X": 0.38624125719070435, "Y": 0.1907833218574524}, {"X": 0.35268136858940125, "Y": 0.19078342616558075}]}, "Id": "364787bf-a5ff-428a-be18-f23a3643d1b7"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Landing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05160503089427948, "Height": 0.011278411373496056, "Left": 0.3910091519355774, "Top": 0.18172545731067657}, "Polygon": [{"X": 0.3910091519355774, "Y": 0.1817256361246109}, {"X": 0.4426141679286957, "Y": 0.18172545731067657}, {"X": 0.44261419773101807, "Y": 0.19300369918346405}, {"X": 0.3910091817378998, "Y": 0.19300387799739838}]}, "Id": "349a4589-0ebc-4f78-8da2-8335477ea7ae"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Rd.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021656066179275513, "Height": 0.00939630065113306, "Left": 0.44764038920402527, "Top": 0.18168552219867706}, "Polygon": [{"X": 0.44764038920402527, "Y": 0.18168559670448303}, {"X": 0.4692964553833008, "Y": 0.18168552219867706}, {"X": 0.4692964553833008, "Y": 0.19108174741268158}, {"X": 0.44764038920402527, "Y": 0.19108182191848755}]}, "Id": "b547055a-d834-4966-8a5c-c75c95b1644a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024335626512765884, "Height": 0.00878096278756857, "Left": 0.06657242774963379, "Top": 0.19565846025943756}, "Polygon": [{"X": 0.06657242774963379, "Y": 0.19565854966640472}, {"X": 0.09090801328420639, "Y": 0.19565846025943756}, {"X": 0.09090805798768997, "Y": 0.20443934202194214}, {"X": 0.06657247990369797, "Y": 0.2044394314289093}]}, "Id": "f4b17169-1718-4f87-9c57-7035979c0a65"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03457358852028847, "Height": 0.011595291085541248, "Left": 0.09537462145090103, "Top": 0.19513821601867676}, "Polygon": [{"X": 0.09537462145090103, "Y": 0.1951383352279663}, {"X": 0.12994815409183502, "Y": 0.19513821601867676}, {"X": 0.1299482136964798, "Y": 0.20673339068889618}, {"X": 0.0953746810555458, "Y": 0.20673350989818573}]}, "Id": "2f021664-c77e-4707-a2c3-977e9c18c8fd"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "Rd.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021602820605039597, "Height": 0.009119960479438305, "Left": 0.13487699627876282, "Top": 0.19536779820919037}, "Polygon": [{"X": 0.13487699627876282, "Y": 0.19536787271499634}, {"X": 0.15647977590560913, "Y": 0.19536779820919037}, {"X": 0.1564798206090927, "Y": 0.20448769629001617}, {"X": 0.1348770409822464, "Y": 0.20448777079582214}]}, "Id": "cd51a2cd-c743-4997-b362-6097f40d1c91"}, {"BlockType": "WORD", "Confidence": 99.73473358154297, "Text": "St", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014063861221075058, "Height": 0.009216039441525936, "Left": 0.16073118150234222, "Top": 0.19532321393489838}, "Polygon": [{"X": 0.16073118150234222, "Y": 0.19532325863838196}, {"X": 0.174795001745224, "Y": 0.19532321393489838}, {"X": 0.17479504644870758, "Y": 0.2045392096042633}, {"X": 0.1607312262058258, "Y": 0.2045392543077469}]}, "Id": "77c70fc7-09fe-4180-897f-d7aff1b5bb75"}, {"BlockType": "WORD", "Confidence": 99.521484375, "Text": "220", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02435292676091194, "Height": 0.008821875788271427, "Left": 0.1784234493970871, "Top": 0.195631206035614}, "Polygon": [{"X": 0.1784234493970871, "Y": 0.19563129544258118}, {"X": 0.20277634263038635, "Y": 0.195631206035614}, {"X": 0.20277637243270874, "Y": 0.2044530063867569}, {"X": 0.17842347919940948, "Y": 0.20445308089256287}]}, "Id": "f2add8a1-09e0-4c5e-b6a2-aff2fceae4c8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05658182501792908, "Height": 0.011244794353842735, "Left": 0.30781739950180054, "Top": 0.19534681737422943}, "Polygon": [{"X": 0.30781739950180054, "Y": 0.19534701108932495}, {"X": 0.3643991947174072, "Y": 0.19534681737422943}, {"X": 0.3643992245197296, "Y": 0.2065914273262024}, {"X": 0.3078174293041229, "Y": 0.2065916210412979}]}, "Id": "2dd5caa5-bc71-40d2-b15b-316c18529514"}, {"BlockType": "WORD", "Confidence": 99.87145233154297, "Text": "CA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020560070872306824, "Height": 0.009120437316596508, "Left": 0.368742436170578, "Top": 0.1953476369380951}, "Polygon": [{"X": 0.368742436170578, "Y": 0.19534771144390106}, {"X": 0.38930249214172363, "Y": 0.1953476369380951}, {"X": 0.38930249214172363, "Y": 0.2044680118560791}, {"X": 0.368742436170578, "Y": 0.20446807146072388}]}, "Id": "4308804a-9d6c-49c7-af42-3fe749fb6b95"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "94545", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04015939682722092, "Height": 0.00877771433442831, "Left": 0.3933873474597931, "Top": 0.19575834274291992}, "Polygon": [{"X": 0.3933873474597931, "Y": 0.19575846195220947}, {"X": 0.4335467219352722, "Y": 0.19575834274291992}, {"X": 0.4335467219352722, "Y": 0.20453591644763947}, {"X": 0.3933873474597931, "Y": 0.2045360505580902}]}, "Id": "f00eaf37-3d9f-4e9c-9180-209b040f2547"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Manala<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07213066518306732, "Height": 0.011217997409403324, "Left": 0.06691035628318787, "Top": 0.2090599685907364}, "Polygon": [{"X": 0.06691035628318787, "Y": 0.2090602070093155}, {"X": 0.1390409767627716, "Y": 0.2090599685907364}, {"X": 0.1390410214662552, "Y": 0.22027774155139923}, {"X": 0.06691042333841324, "Y": 0.22027796506881714}]}, "Id": "10835e35-9d0a-4886-b056-f489be07f696"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "NJ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017292335629463196, "Height": 0.009054572321474552, "Left": 0.1438482403755188, "Top": 0.20907945930957794}, "Polygon": [{"X": 0.1438482403755188, "Y": 0.20907950401306152}, {"X": 0.1611405313014984, "Y": 0.20907945930957794}, {"X": 0.161140576004982, "Y": 0.21813397109508514}, {"X": 0.1438482701778412, "Y": 0.21813403069972992}]}, "Id": "f61456ec-eef3-461e-9e74-29f9da9a10b1"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "07726", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04059057682752609, "Height": 0.009078378789126873, "Left": 0.16565507650375366, "Top": 0.209162175655365}, "Polygon": [{"X": 0.16565507650375366, "Y": 0.20916230976581573}, {"X": 0.20624561607837677, "Y": 0.209162175655365}, {"X": 0.20624566078186035, "Y": 0.218240424990654}, {"X": 0.16565512120723724, "Y": 0.21824055910110474}]}, "Id": "5c4888a6-bf43-4089-82f8-6f8eab5cb2c0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "United", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04216283932328224, "Height": 0.008985362015664577, "Left": 0.30755606293678284, "Top": 0.2091454565525055}, "Polygon": [{"X": 0.30755606293678284, "Y": 0.20914559066295624}, {"X": 0.3497188985347748, "Y": 0.2091454565525055}, {"X": 0.34971892833709717, "Y": 0.2181306779384613}, {"X": 0.3075560927391052, "Y": 0.21813081204891205}]}, "Id": "9515dc17-3648-4f6b-b4c5-3004a4a76df4"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "States", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04098020866513252, "Height": 0.009193114005029202, "Left": 0.3543323874473572, "Top": 0.20903632044792175}, "Polygon": [{"X": 0.3543323874473572, "Y": 0.2090364545583725}, {"X": 0.3953125774860382, "Y": 0.20903632044792175}, {"X": 0.3953126072883606, "Y": 0.21822930872440338}, {"X": 0.35433241724967957, "Y": 0.21822944283485413}]}, "Id": "bb8be03f-f92b-44cd-ba41-7544dfd05903"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "$422.97", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16387654840946198, "Height": 0.03226066380739212, "Left": 0.7688495516777039, "Top": 0.18581847846508026}, "Polygon": [{"X": 0.7688496708869934, "Y": 0.18581902980804443}, {"X": 0.9327261447906494, "Y": 0.18581847846508026}, {"X": 0.9327259659767151, "Y": 0.21807861328125}, {"X": 0.7688495516777039, "Y": 0.21807913482189178}]}, "Id": "ee6d9a98-dac9-48bd-bc6a-9350e4890068"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "United", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041860297322273254, "Height": 0.0091518834233284, "Left": 0.0669642761349678, "Top": 0.22273527085781097}, "Polygon": [{"X": 0.0669642761349678, "Y": 0.22273540496826172}, {"X": 0.10882452875375748, "Y": 0.22273527085781097}, {"X": 0.10882457345724106, "Y": 0.23188702762126923}, {"X": 0.06696432828903198, "Y": 0.23188716173171997}]}, "Id": "3a2985eb-ee73-4fd3-9d83-9ea2ce84b381"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "States", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041232455521821976, "Height": 0.009182309731841087, "Left": 0.1136205643415451, "Top": 0.22264273464679718}, "Polygon": [{"X": 0.1136205643415451, "Y": 0.22264285385608673}, {"X": 0.1548529714345932, "Y": 0.22264273464679718}, {"X": 0.15485301613807678, "Y": 0.23182490468025208}, {"X": 0.11362060904502869, "Y": 0.23182503879070282}]}, "Id": "ab6d76d0-4d1e-4b82-88f4-162dbae3e59e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Receive", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055507026612758636, "Height": 0.009257117286324501, "Left": 0.7804251313209534, "Top": 0.23174184560775757}, "Polygon": [{"X": 0.7804251909255981, "Y": 0.2317420244216919}, {"X": 0.835932195186615, "Y": 0.23174184560775757}, {"X": 0.8359321355819702, "Y": 0.2409987896680832}, {"X": 0.7804251313209534, "Y": 0.24099896848201752}]}, "Id": "8a4d246b-3c92-45e6-a3f5-d8fb2226ef0f"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "By:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022706352174282074, "Height": 0.011446328833699226, "Left": 0.8400362730026245, "Top": 0.231633722782135}, "Polygon": [{"X": 0.8400363326072693, "Y": 0.23163378238677979}, {"X": 0.8627426028251648, "Y": 0.231633722782135}, {"X": 0.8627426028251648, "Y": 0.24307997524738312}, {"X": 0.8400362730026245, "Y": 0.2430800497531891}]}, "Id": "bfe49169-6c5e-46d3-82f1-5b6aee1fca80"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "7/18/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06595048308372498, "Height": 0.009521913714706898, "Left": 0.8669425845146179, "Top": 0.231617271900177}, "Polygon": [{"X": 0.8669425845146179, "Y": 0.23161748051643372}, {"X": 0.9328930377960205, "Y": 0.231617271900177}, {"X": 0.9328929781913757, "Y": 0.2411389946937561}, {"X": 0.8669425845146179, "Y": 0.24113918840885162}]}, "Id": "1e51331e-b56f-4867-8ca0-ea259c841afe"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Receive", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049266163259744644, "Height": 0.008483290672302246, "Left": 0.06674324721097946, "Top": 0.2522294521331787}, "Polygon": [{"X": 0.06674324721097946, "Y": 0.25222960114479065}, {"X": 0.11600936949253082, "Y": 0.2522294521331787}, {"X": 0.11600940674543381, "Y": 0.260712593793869}, {"X": 0.06674329191446304, "Y": 0.26071274280548096}]}, "Id": "0e94c921-eb3d-4c53-b523-10b5cc5f5d76"}, {"BlockType": "WORD", "Confidence": 99.85112762451172, "Text": "By", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016247892752289772, "Height": 0.010317129082977772, "Left": 0.12009939551353455, "Top": 0.2522467374801636}, "Polygon": [{"X": 0.12009939551353455, "Y": 0.25224679708480835}, {"X": 0.13634724915027618, "Y": 0.2522467374801636}, {"X": 0.13634729385375977, "Y": 0.2625638246536255}, {"X": 0.12009944766759872, "Y": 0.26256388425827026}]}, "Id": "c3c0ffe3-4370-43f2-a724-2d676c12e2ba"}, {"BlockType": "WORD", "Confidence": 99.79173278808594, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045230478048324585, "Height": 0.008581246249377728, "Left": 0.3601459264755249, "Top": 0.2520971894264221}, "Polygon": [{"X": 0.3601459264755249, "Y": 0.25209730863571167}, {"X": 0.4053764045238495, "Y": 0.2520971894264221}, {"X": 0.4053764045238495, "Y": 0.2606782913208008}, {"X": 0.3601459562778473, "Y": 0.2606784403324127}]}, "Id": "d4e04a18-e2f3-4075-837b-3fa9a43532f7"}, {"BlockType": "WORD", "Confidence": 99.501953125, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008146727457642555, "Height": 0.008078076876699924, "Left": 0.40839675068855286, "Top": 0.25236889719963074}, "Polygon": [{"X": 0.40839675068855286, "Y": 0.2523689270019531}, {"X": 0.41654345393180847, "Y": 0.25236889719963074}, {"X": 0.41654348373413086, "Y": 0.2604469358921051}, {"X": 0.40839678049087524, "Y": 0.2604469656944275}]}, "Id": "dbb8cc4f-4091-44af-ae9b-dd73fe245974"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Billing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039513908326625824, "Height": 0.01043700147420168, "Left": 0.6549813151359558, "Top": 0.25225120782852173}, "Polygon": [{"X": 0.6549813747406006, "Y": 0.2522513270378113}, {"X": 0.6944952607154846, "Y": 0.25225120782852173}, {"X": 0.6944952011108398, "Y": 0.2626881003379822}, {"X": 0.6549813151359558, "Y": 0.26268821954727173}]}, "Id": "5bcb79b7-a5d3-4c29-a300-84166b613404"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Phone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03963521122932434, "Height": 0.008400254882872105, "Left": 0.698718786239624, "Top": 0.25224220752716064}, "Polygon": [{"X": 0.698718786239624, "Y": 0.2522423267364502}, {"X": 0.7383540272712708, "Y": 0.25224220752716064}, {"X": 0.738353967666626, "Y": 0.2606423497200012}, {"X": 0.698718786239624, "Y": 0.26064246892929077}]}, "Id": "f7c7defe-d51d-4a21-946c-601fa727ee7e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "7/18/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06568499654531479, "Height": 0.00922228954732418, "Left": 0.06631606817245483, "Top": 0.26750436425209045}, "Polygon": [{"X": 0.06631606817245483, "Y": 0.26750457286834717}, {"X": 0.13200102746486664, "Y": 0.26750436425209045}, {"X": 0.13200107216835022, "Y": 0.27672648429870605}, {"X": 0.06631612032651901, "Y": 0.2767266631126404}]}, "Id": "dafc8c66-e0f5-46f3-a2d5-c1f99562ef5e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05290158838033676, "Height": 0.010498124174773693, "Left": 0.08316806703805923, "Top": 0.29312795400619507}, "Polygon": [{"X": 0.08316806703805923, "Y": 0.293128103017807}, {"X": 0.13606961071491241, "Y": 0.29312795400619507}, {"X": 0.136069655418396, "Y": 0.3036259412765503}, {"X": 0.08316811919212341, "Y": 0.30362609028816223}]}, "Id": "54e0e3ad-1478-4a89-ae32-9d7e8b8bd88e"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026606256142258644, "Height": 0.008383997716009617, "Left": 0.16853205859661102, "Top": 0.29314491152763367}, "Polygon": [{"X": 0.16853205859661102, "Y": 0.29314500093460083}, {"X": 0.19513827562332153, "Y": 0.29314491152763367}, {"X": 0.19513830542564392, "Y": 0.30152884125709534}, {"X": 0.1685320883989334, "Y": 0.3015289008617401}]}, "Id": "e4fe760c-e5b4-4359-b452-db5964943409"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Options", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049284934997558594, "Height": 0.01028106827288866, "Left": 0.5757012963294983, "Top": 0.29323700070381165}, "Polygon": [{"X": 0.5757012963294983, "Y": 0.2932371199131012}, {"X": 0.6249862313270569, "Y": 0.29323700070381165}, {"X": 0.6249862313270569, "Y": 0.3035179376602173}, {"X": 0.5757012963294983, "Y": 0.30351805686950684}]}, "Id": "b4928ca3-a73d-47eb-b1a6-5fdf0358c438"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Rate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02798309735953808, "Height": 0.008422311395406723, "Left": 0.7693250775337219, "Top": 0.29314565658569336}, "Polygon": [{"X": 0.7693250775337219, "Y": 0.29314571619033813}, {"X": 0.7973081469535828, "Y": 0.29314565658569336}, {"X": 0.7973081469535828, "Y": 0.3015678822994232}, {"X": 0.7693250775337219, "Y": 0.301567941904068}]}, "Id": "642cde54-9d96-4773-b1e6-b680835d9434"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049531806260347366, "Height": 0.008376441895961761, "Left": 0.883677065372467, "Top": 0.29317596554756165}, "Polygon": [{"X": 0.8836771249771118, "Y": 0.2931761145591736}, {"X": 0.9332088828086853, "Y": 0.29317596554756165}, {"X": 0.9332088828086853, "Y": 0.30155229568481445}, {"X": 0.883677065372467, "Y": 0.301552414894104}]}, "Id": "de9549de-478a-4042-9126-30244704ffe1"}, {"BlockType": "WORD", "Confidence": 99.41167449951172, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005230480805039406, "Height": 0.008581405505537987, "Left": 0.1064104437828064, "Top": 0.31449127197265625}, "Polygon": [{"X": 0.1064104437828064, "Y": 0.31449130177497864}, {"X": 0.11164087802171707, "Y": 0.31449127197265625}, {"X": 0.11164092272520065, "Y": 0.3230726718902588}, {"X": 0.10641048848628998, "Y": 0.3230726718902588}]}, "Id": "c93e1a33-be03-4fa9-81a4-7fdc8b119649"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "FREIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06363187730312347, "Height": 0.009179683402180672, "Left": 0.1692672222852707, "Top": 0.32080674171447754}, "Polygon": [{"X": 0.1692672222852707, "Y": 0.32080692052841187}, {"X": 0.23289906978607178, "Y": 0.32080674171447754}, {"X": 0.23289909958839417, "Y": 0.3299862742424011}, {"X": 0.16926726698875427, "Y": 0.32998642325401306}]}, "Id": "e673d7ce-0e6a-4285-aba5-f4cfd34072ea"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "(PO)", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03099675476551056, "Height": 0.011669314466416836, "Left": 0.23718492686748505, "Top": 0.3207259774208069}, "Polygon": [{"X": 0.23718492686748505, "Y": 0.32072606682777405}, {"X": 0.2681816518306732, "Y": 0.3207259774208069}, {"X": 0.2681816816329956, "Y": 0.3323952257633209}, {"X": 0.23718497157096863, "Y": 0.3323952853679657}]}, "Id": "c199beba-5ec4-4f1d-980c-58db75d6098c"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "$422.97", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05356718599796295, "Height": 0.01086129155009985, "Left": 0.743829607963562, "Top": 0.3169335722923279}, "Polygon": [{"X": 0.7438296675682068, "Y": 0.3169337213039398}, {"X": 0.7973968386650085, "Y": 0.3169335722923279}, {"X": 0.7973967790603638, "Y": 0.32779473066329956}, {"X": 0.743829607963562, "Y": 0.3277948796749115}]}, "Id": "a4ced776-fd5f-409a-ab50-32c05d1fed3e"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "$422.97", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05334289371967316, "Height": 0.010595420375466347, "Left": 0.8796473741531372, "Top": 0.31698328256607056}, "Polygon": [{"X": 0.8796473741531372, "Y": 0.3169834017753601}, {"X": 0.9329902529716492, "Y": 0.31698328256607056}, {"X": 0.9329901933670044, "Y": 0.3275785446166992}, {"X": 0.8796473741531372, "Y": 0.32757869362831116}]}, "Id": "60717bf2-72dc-4199-8d5e-93f00a0815c1"}, {"BlockType": "WORD", "Confidence": 99.44196319580078, "Text": "<PERSON><PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04908454418182373, "Height": 0.009054696187376976, "Left": 0.1689646691083908, "Top": 0.3378325402736664}, "Polygon": [{"X": 0.1689646691083908, "Y": 0.33783265948295593}, {"X": 0.21804916858673096, "Y": 0.3378325402736664}, {"X": 0.21804921329021454, "Y": 0.34688711166381836}, {"X": 0.1689646989107132, "Y": 0.3468872606754303}]}, "Id": "b9716863-ebd0-4f8d-a694-06029747722d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "NY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019868740811944008, "Height": 0.008957932703197002, "Left": 0.22278814017772675, "Top": 0.3378506302833557}, "Polygon": [{"X": 0.22278814017772675, "Y": 0.3378506600856781}, {"X": 0.24265684187412262, "Y": 0.3378506302833557}, {"X": 0.2426568865776062, "Y": 0.3468084931373596}, {"X": 0.22278816998004913, "Y": 0.3468085527420044}]}, "Id": "d9ecdfee-fcb2-46ea-9d8c-de30fb7b2a0b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03379287198185921, "Height": 0.008950673043727875, "Left": 0.7516760230064392, "Top": 0.37601542472839355}, "Polygon": [{"X": 0.7516760230064392, "Y": 0.3760155141353607}, {"X": 0.7854688763618469, "Y": 0.37601542472839355}, {"X": 0.7854688763618469, "Y": 0.38496604561805725}, {"X": 0.7516760230064392, "Y": 0.384966105222702}]}, "Id": "5bb15963-8318-41ab-9085-6f532d1bdcfa"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "$422.97", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05259009450674057, "Height": 0.010957999154925346, "Left": 0.8800909519195557, "Top": 0.37534236907958984}, "Polygon": [{"X": 0.8800910115242004, "Y": 0.3753424882888794}, {"X": 0.9326810836791992, "Y": 0.37534236907958984}, {"X": 0.9326810240745544, "Y": 0.386300265789032}, {"X": 0.8800909519195557, "Y": 0.38630038499832153}]}, "Id": "97ea18f2-014b-405d-8f64-4fef7be965a7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PLEASE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.20287591218948364, "Height": 0.02982150763273239, "Left": 0.05855097621679306, "Top": 0.43216097354888916}, "Polygon": [{"X": 0.05855097621679306, "Y": 0.4321613907814026}, {"X": 0.26142677664756775, "Y": 0.43216097354888916}, {"X": 0.2614268958568573, "Y": 0.4619821012020111}, {"X": 0.05855114385485649, "Y": 0.46198248863220215}]}, "Id": "76f5e63b-03b1-46b9-9181-b84138dd7ee1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SEND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1381920576095581, "Height": 0.029991433024406433, "Left": 0.2759418487548828, "Top": 0.4321175217628479}, "Polygon": [{"X": 0.2759418487548828, "Y": 0.4321177899837494}, {"X": 0.41413384675979614, "Y": 0.4321175217628479}, {"X": 0.41413387656211853, "Y": 0.46210870146751404}, {"X": 0.27594193816185, "Y": 0.46210893988609314}]}, "Id": "44eac225-7c38-48d3-8664-6daefe06dc04"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "ALL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10670053958892822, "Height": 0.028911156579852104, "Left": 0.4250699281692505, "Top": 0.4324154853820801}, "Polygon": [{"X": 0.4250699281692505, "Y": 0.4324156939983368}, {"X": 0.5317704677581787, "Y": 0.4324154853820801}, {"X": 0.5317704677581787, "Y": 0.4613264501094818}, {"X": 0.4250699579715729, "Y": 0.46132662892341614}]}, "Id": "791285b8-7dc6-4b65-a5a1-fb16bf99f473"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "INVOICES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2545658051967621, "Height": 0.030144430696964264, "Left": 0.5451184511184692, "Top": 0.4322035312652588}, "Polygon": [{"X": 0.5451184511184692, "Y": 0.4322040379047394}, {"X": 0.7996842265129089, "Y": 0.4322035312652588}, {"X": 0.7996841669082642, "Y": 0.46234747767448425}, {"X": 0.5451184511184692, "Y": 0.46234795451164246}]}, "Id": "9f72f530-45bf-42bb-8ac2-6ee8515294c1"}, {"BlockType": "WORD", "Confidence": 97.52869415283203, "Text": "TO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07248052209615707, "Height": 0.029268408194184303, "Left": 0.813615083694458, "Top": 0.4322560131549835}, "Polygon": [{"X": 0.8136151432991028, "Y": 0.43225616216659546}, {"X": 0.8860955834388733, "Y": 0.4322560131549835}, {"X": 0.8860954642295837, "Y": 0.4615243077278137}, {"X": 0.813615083694458, "Y": 0.46152442693710327}]}, "Id": "6150cb88-f1fc-441e-bf12-88f549ae6657"}, {"BlockType": "WORD", "Confidence": 67.67857360839844, "Text": "<EMAIL>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.5811248421669006, "Height": 0.030225491151213646, "Left": 0.05758785083889961, "Top": 0.48039183020591736}, "Polygon": [{"X": 0.05758785083889961, "Y": 0.48039284348487854}, {"X": 0.6387127041816711, "Y": 0.48039183020591736}, {"X": 0.6387126445770264, "Y": 0.5106164216995239}, {"X": 0.05758802220225334, "Y": 0.5106173157691956}]}, "Id": "10fcb182-f986-4030-87ad-a4cc0ad3d416"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "OR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07814931124448776, "Height": 0.029999379068613052, "Left": 0.6467807292938232, "Top": 0.4804654121398926}, "Polygon": [{"X": 0.646780788898468, "Y": 0.48046553134918213}, {"X": 0.7249300479888916, "Y": 0.4804654121398926}, {"X": 0.7249299883842468, "Y": 0.5104646682739258}, {"X": 0.6467807292938232, "Y": 0.5104647874832153}]}, "Id": "162c6cc8-2e1c-46e6-9142-dd13864ff608"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CALL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14236009120941162, "Height": 0.029958436265587807, "Left": 0.7390292882919312, "Top": 0.48036444187164307}, "Polygon": [{"X": 0.7390293478965759, "Y": 0.48036468029022217}, {"X": 0.8813893795013428, "Y": 0.48036444187164307}, {"X": 0.8813892602920532, "Y": 0.510322630405426}, {"X": 0.7390292882919312, "Y": 0.5103228688240051}]}, "Id": "3e778ad1-335e-48ea-9797-ad4f63f3ef94"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "510-785-6900", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2945959270000458, "Height": 0.030028486624360085, "Left": 0.06012586131691933, "Top": 0.5290759801864624}, "Polygon": [{"X": 0.06012586131691933, "Y": 0.5290763974189758}, {"X": 0.35472172498703003, "Y": 0.5290759801864624}, {"X": 0.3547217845916748, "Y": 0.5591040849685669}, {"X": 0.06012602895498276, "Y": 0.5591044425964355}]}, "Id": "d4eb92cc-edf0-40d3-92ee-aa09f34553aa"}, {"BlockType": "WORD", "Confidence": 98.98118591308594, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0051124682649970055, "Height": 0.008158688433468342, "Left": 0.8796964883804321, "Top": 0.9290862679481506}, "Polygon": [{"X": 0.8796964883804321, "Y": 0.9290862679481506}, {"X": 0.8848089575767517, "Y": 0.9290862679481506}, {"X": 0.8848088979721069, "Y": 0.9372449517250061}, {"X": 0.8796964883804321, "Y": 0.9372449517250061}]}, "Id": "7499717b-7fe2-4f31-9ef8-19059c454555"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011087152175605297, "Height": 0.008587544783949852, "Left": 0.8899157643318176, "Top": 0.9285483956336975}, "Polygon": [{"X": 0.8899158239364624, "Y": 0.9285483956336975}, {"X": 0.9010029435157776, "Y": 0.9285483956336975}, {"X": 0.9010028839111328, "Y": 0.9371359348297119}, {"X": 0.8899157643318176, "Y": 0.9371359348297119}]}, "Id": "dbfe8a12-e612-48f4-8631-8606cc05b68a"}, {"BlockType": "WORD", "Confidence": 98.60112762451172, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005023960489779711, "Height": 0.007936366833746433, "Left": 0.9052087068557739, "Top": 0.9289823770523071}, "Polygon": [{"X": 0.9052087664604187, "Y": 0.9289823770523071}, {"X": 0.910232663154602, "Y": 0.9289823770523071}, {"X": 0.910232663154602, "Y": 0.9369187355041504}, {"X": 0.9052087068557739, "Y": 0.9369187355041504}]}, "Id": "e4110f4d-73cb-467a-8f17-91893bdddae0"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "95371", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03468852490186691, "Height": 0.008173900656402111, "Left": 0.1215488463640213, "Top": 0.9549195766448975}, "Polygon": [{"X": 0.1215488463640213, "Y": 0.9549195766448975}, {"X": 0.15623733401298523, "Y": 0.9549195766448975}, {"X": 0.1562373787164688, "Y": 0.9630934596061707}, {"X": 0.12154889106750488, "Y": 0.9630934596061707}]}, "Id": "b397359c-5c42-4e4b-9106-b5bade60b30f"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "43d1cfca-2a3d-4fc8-9d9d-60d5f4ebddda", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "43d1cfca-2a3d-4fc8-9d9d-60d5f4ebddda", "content-type": "application/x-amz-json-1.1", "content-length": "74584", "date": "Wed, 24 Sep 2025 09:00:18 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "a9bdd210-3045-48cb-b969-70419bb72ed9", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 09:00:21 GMT", "content-type": "application/json", "content-length": "861", "connection": "keep-alive", "x-amzn-requestid": "a9bdd210-3045-48cb-b969-70419bb72ed9"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "The user provided a page content: seems a Purchase Order page. The page shows \"Purchase Order\" header, vendor info, etc. The page is the entire document? It says \"1 of 1\" at bottom. So likely the document is a PO. According to the instructions: we must return exactly one object with page_no and doc_type. The doc_type should be \"po\". Let's confirm: The header is \"Purchase Order\". No other pages. So classification: po.\n\nWe must output via tool call classify_logistics_doc_type with documents array containing one object.\n\nLet's produce."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"po\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2688, "outputTokens": 136, "totalTokens": 2824}, "metrics": {"latencyMs": 863}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/9146558a_XIYO2PDG2ILYXT57MES6.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nBig Joe Handling Systems\nPurchase Order\nBIG\nJOE\n25932 Eden Landing Rd. Hayward, CA 94545\nPH: (*************\nFAX: (*************\nPending Receipt\nwww.BigJoeLift.com\n#95371\n7/7/2025\nVendor\nShip To\nTOTAL\nGlobal Forwarding Enterprises\nBig Joe Handling - Hayward\nLLC\n25932 Eden Landing Rd.\n200 Craig Rd. St 220\nHayward CA 94545\nManalapan NJ 07726\nUnited States\n$422.97\nUnited States\nReceive By: 7/18/2025\nReceive By\nVendor #\nBilling Phone\n7/18/2025\nQuantity\nItem\nOptions\nRate\nAmount\n1\nFREIGHT (PO)\n$422.97\n$422.97\nBufdalo NY\nTotal\n$422.97\nPLEASE SEND ALL INVOICES TO\n<EMAIL> OR CALL\n510-785-6900\n1 of 1\n95371\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}