{"classification_result": {"documents": [{"page_no": 1, "doc_type": "weight_and_inspection_cert"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 1.9616864577187698e-08, "Y": 1.0}]}, "Id": "cb722396-2551-490d-8237-c59cadaf44c5", "Relationships": [{"Type": "CHILD", "Ids": ["6d075546-880a-4df1-bc0e-f8b87713a793", "5c58f792-b335-4be6-bfc2-1658a5bea7fa", "4fb9c327-d082-403a-943f-73872bc3b5c2", "dc1aa63d-347b-4972-89c3-e61d9909a8a0", "3ab8394c-2c3a-4c51-bf02-6de817ea3849", "1d17bec4-a395-445b-ae14-15429cf1b416", "a8135120-a5ca-4c5f-b42f-f67a5bae7bc6", "e1b1eaec-a3d5-459b-8fa4-f5430a0dd3bc", "38073757-9cad-4114-a6d8-1a2336865bfa", "31d4f0da-09e0-414b-be99-e152e8ad4486", "d24c48d8-2773-408a-840b-35a9591f9bce", "7e226fe0-4f74-417c-8710-3659fe68bef5", "df0b2f63-e9b1-4609-bf8c-622b2e92e4e7", "db53e3eb-5250-4bce-91b9-cb763f3dff86", "947fd5e0-b1b9-4843-81ac-22a81216d7ad", "cbeaeca0-2453-4a06-aa17-45c89428efb6", "c18ff8a0-6b80-4497-bbfb-a120826bfb81", "111cdd8b-3a69-4827-a6c0-ef3d7641b00e", "070db501-c160-4701-8ff0-3de072a0e930", "ffc85788-5cd8-4f4d-bcd9-1af7630b0c59", "0d2922e1-7415-4a21-b8f1-0f8cbf6078f7", "b086538e-fdea-4ab1-8986-cf0b550063b5", "d9a0d008-083b-4a48-9562-3bb0a062c6c3", "0d667395-fe28-47db-91ec-ac5e89291053", "3340362d-7d21-41a7-ae85-c832f9513479", "7fda46ec-e17d-41ce-88db-b7459ee7644b", "d2bf140f-a448-491a-9808-43779079c0fe", "b2ca1faf-db37-415e-848c-d52bc14dd355", "f3a43546-cf11-489f-bbea-1980f1070523", "4babf755-c1d5-4d22-8116-c13633b397a0", "db787064-a06f-4d10-96a7-e662f092c8a2"]}]}, {"BlockType": "LINE", "Confidence": 82.90676879882812, "Text": "®", "Geometry": {"BoundingBox": {"Width": 0.006544790230691433, "Height": 0.005231551360338926, "Left": 0.34556087851524353, "Top": 0.08397401124238968}, "Polygon": [{"X": 0.34556087851524353, "Y": 0.08397402614355087}, {"X": 0.35210567712783813, "Y": 0.08397401124238968}, {"X": 0.35210567712783813, "Y": 0.0892055556178093}, {"X": 0.34556087851524353, "Y": 0.08920556306838989}]}, "Id": "6d075546-880a-4df1-bc0e-f8b87713a793", "Relationships": [{"Type": "CHILD", "Ids": ["447bb0ed-532b-4d6d-959f-3d56ae454dad"]}]}, {"BlockType": "LINE", "Confidence": 91.42359161376953, "Text": "ABF", "Geometry": {"BoundingBox": {"Width": 0.16419638693332672, "Height": 0.0437476709485054, "Left": 0.1863717883825302, "Top": 0.0837559700012207}, "Polygon": [{"X": 0.1863717883825302, "Y": 0.08375624567270279}, {"X": 0.35056814551353455, "Y": 0.0837559700012207}, {"X": 0.35056817531585693, "Y": 0.1275033950805664}, {"X": 0.18637189269065857, "Y": 0.1275036484003067}]}, "Id": "5c58f792-b335-4be6-bfc2-1658a5bea7fa", "Relationships": [{"Type": "CHILD", "Ids": ["3cd2f454-29b0-49fc-84f7-8ff94ff2a0e2"]}]}, {"BlockType": "LINE", "Confidence": 98.85921478271484, "Text": "DIMENSION/CUBE", "Geometry": {"BoundingBox": {"Width": 0.33914610743522644, "Height": 0.02311941795051098, "Left": 0.49188968539237976, "Top": 0.10118571668863297}, "Polygon": [{"X": 0.49188971519470215, "Y": 0.10118626803159714}, {"X": 0.8310357928276062, "Y": 0.10118571668863297}, {"X": 0.8310356736183167, "Y": 0.12430461496114731}, {"X": 0.49188968539237976, "Y": 0.12430513650178909}]}, "Id": "4fb9c327-d082-403a-943f-73872bc3b5c2", "Relationships": [{"Type": "CHILD", "Ids": ["529c84c3-269e-4203-90aa-a48c4f0fc3a3"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Freight", "Geometry": {"BoundingBox": {"Width": 0.06602073460817337, "Height": 0.014087923802435398, "Left": 0.2620227038860321, "Top": 0.13036800920963287}, "Polygon": [{"X": 0.2620227038860321, "Y": 0.13036811351776123}, {"X": 0.3280434310436249, "Y": 0.13036800920963287}, {"X": 0.3280434310436249, "Y": 0.14445583522319794}, {"X": 0.2620227336883545, "Y": 0.1444559246301651}]}, "Id": "dc1aa63d-347b-4972-89c3-e61d9909a8a0", "Relationships": [{"Type": "CHILD", "Ids": ["3f1a1920-7d16-4381-af06-d21ebf81752a"]}]}, {"BlockType": "LINE", "Confidence": 99.56153869628906, "Text": "CERTIFICATE", "Geometry": {"BoundingBox": {"Width": 0.29736945033073425, "Height": 0.01909993216395378, "Left": 0.5122085213661194, "Top": 0.12711404263973236}, "Polygon": [{"X": 0.5122085809707642, "Y": 0.12711448967456818}, {"X": 0.809578001499176, "Y": 0.12711404263973236}, {"X": 0.8095778822898865, "Y": 0.14621354639530182}, {"X": 0.5122085213661194, "Y": 0.14621397852897644}]}, "Id": "3ab8394c-2c3a-4c51-bf02-6de817ea3849", "Relationships": [{"Type": "CHILD", "Ids": ["5cc8010a-fbdd-4495-9d23-779fb1215851"]}]}, {"BlockType": "LINE", "Confidence": 98.8974838256836, "Text": "An ArcBest Company", "Geometry": {"BoundingBox": {"Width": 0.14804215729236603, "Height": 0.011951346881687641, "Left": 0.18612322211265564, "Top": 0.1528737097978592}, "Polygon": [{"X": 0.18612322211265564, "Y": 0.1528739184141159}, {"X": 0.3341653645038605, "Y": 0.1528737097978592}, {"X": 0.33416539430618286, "Y": 0.16482485830783844}, {"X": 0.18612325191497803, "Y": 0.16482506692409515}]}, "Id": "1d17bec4-a395-445b-ae14-15429cf1b416", "Relationships": [{"Type": "CHILD", "Ids": ["2aba74a1-d418-4e2b-87d3-ed46c51e4679", "75ff1403-895d-4f7d-90bd-8a4aaf0ec568", "e9dec3e8-9016-4591-a219-81430b65b0e2"]}]}, {"BlockType": "LINE", "Confidence": 99.87065887451172, "Text": "Pronumber:", "Geometry": {"BoundingBox": {"Width": 0.09246920794248581, "Height": 0.011047291569411755, "Left": 0.11052803695201874, "Top": 0.18101191520690918}, "Polygon": [{"X": 0.11052803695201874, "Y": 0.18101203441619873}, {"X": 0.20299722254276276, "Y": 0.18101191520690918}, {"X": 0.20299725234508514, "Y": 0.19205908477306366}, {"X": 0.11052807420492172, "Y": 0.1920592039823532}]}, "Id": "a8135120-a5ca-4c5f-b42f-f67a5bae7bc6", "Relationships": [{"Type": "CHILD", "Ids": ["50141dd5-73dd-467a-be5d-8937c74462b4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "352309638", "Geometry": {"BoundingBox": {"Width": 0.08982306718826294, "Height": 0.011103572323918343, "Left": 0.3737112283706665, "Top": 0.1811026781797409}, "Polygon": [{"X": 0.3737112283706665, "Y": 0.18110279738903046}, {"X": 0.46353429555892944, "Y": 0.1811026781797409}, {"X": 0.46353429555892944, "Y": 0.19220614433288574}, {"X": 0.3737112283706665, "Y": 0.1922062486410141}]}, "Id": "e1b1eaec-a3d5-459b-8fa4-f5430a0dd3bc", "Relationships": [{"Type": "CHILD", "Ids": ["06da4c88-c2eb-458b-9c08-90c434566460"]}]}, {"BlockType": "LINE", "Confidence": 99.9658203125, "Text": "Trailer No: CITY 410498", "Geometry": {"BoundingBox": {"Width": 0.19430261850357056, "Height": 0.011172185651957989, "Left": 0.63603675365448, "Top": 0.180928036570549}, "Polygon": [{"X": 0.6360368132591248, "Y": 0.1809283047914505}, {"X": 0.8303393721580505, "Y": 0.180928036570549}, {"X": 0.8303393125534058, "Y": 0.19209997355937958}, {"X": 0.63603675365448, "Y": 0.19210022687911987}]}, "Id": "38073757-9cad-4114-a6d8-1a2336865bfa", "Relationships": [{"Type": "CHILD", "Ids": ["450f9429-15ca-42e1-bd43-3ed9e1ff4efc", "839cf69a-87f5-4756-b4b6-1d31a4260097", "63d61a9a-fdd6-4a22-b002-dfafb9a48f27", "e3601e55-4072-4a26-88bb-7c025a430c36"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Shipper:", "Geometry": {"BoundingBox": {"Width": 0.06541091948747635, "Height": 0.013754081912338734, "Left": 0.11052986234426498, "Top": 0.19902680814266205}, "Polygon": [{"X": 0.11052986234426498, "Y": 0.1990268975496292}, {"X": 0.17594075202941895, "Y": 0.19902680814266205}, {"X": 0.17594078183174133, "Y": 0.21278081834316254}, {"X": 0.11052990704774857, "Y": 0.2127808928489685}]}, "Id": "31d4f0da-09e0-414b-be99-e152e8ad4486", "Relationships": [{"Type": "CHILD", "Ids": ["2d8854f2-c460-4c9a-a72f-8e9a002a901e"]}]}, {"BlockType": "LINE", "Confidence": 99.9402084350586, "Text": "Headstone Deals", "Geometry": {"BoundingBox": {"Width": 0.13699108362197876, "Height": 0.011009757407009602, "Left": 0.3744701147079468, "Top": 0.19915220141410828}, "Polygon": [{"X": 0.3744701147079468, "Y": 0.1991523802280426}, {"X": 0.5114611983299255, "Y": 0.19915220141410828}, {"X": 0.5114611983299255, "Y": 0.21016179025173187}, {"X": 0.3744701147079468, "Y": 0.2101619690656662}]}, "Id": "d24c48d8-2773-408a-840b-35a9591f9bce", "Relationships": [{"Type": "CHILD", "Ids": ["c99a8df8-d258-4cd5-ac8c-45fa30e642fe", "af1a1bd9-be4a-447f-91ff-c8b2ef15ae46"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Totals", "Geometry": {"BoundingBox": {"Width": 0.05161131173372269, "Height": 0.011102463118731976, "Left": 0.10968312621116638, "Top": 0.24648213386535645}, "Polygon": [{"X": 0.10968312621116638, "Y": 0.24648219347000122}, {"X": 0.16129440069198608, "Y": 0.24648213386535645}, {"X": 0.16129443049430847, "Y": 0.2575845420360565}, {"X": 0.10968315601348877, "Y": 0.2575846016407013}]}, "Id": "7e226fe0-4f74-417c-8710-3659fe68bef5", "Relationships": [{"Type": "CHILD", "Ids": ["80399951-3ede-4f03-8f63-fffe3be80cbe"]}]}, {"BlockType": "LINE", "Confidence": 99.94548797607422, "Text": "Total Weight:", "Geometry": {"BoundingBox": {"Width": 0.10294857621192932, "Height": 0.013788904994726181, "Left": 0.1098165512084961, "Top": 0.2648628354072571}, "Polygon": [{"X": 0.1098165512084961, "Y": 0.26486295461654663}, {"X": 0.21276509761810303, "Y": 0.2648628354072571}, {"X": 0.21276512742042542, "Y": 0.2786516547203064}, {"X": 0.10981659591197968, "Y": 0.27865174412727356}]}, "Id": "df0b2f63-e9b1-4609-bf8c-622b2e92e4e7", "Relationships": [{"Type": "CHILD", "Ids": ["efe7c190-00f6-4c35-8f93-c07387ad04cb", "0bd505d6-2808-4282-8341-be3a8ecdc3e6"]}]}, {"BlockType": "LINE", "Confidence": 97.44609069824219, "Text": "280 lbs", "Geometry": {"BoundingBox": {"Width": 0.057782694697380066, "Height": 0.011156139895319939, "Left": 0.3737349510192871, "Top": 0.2649824619293213}, "Polygon": [{"X": 0.3737349510192871, "Y": 0.26498252153396606}, {"X": 0.431517630815506, "Y": 0.2649824619293213}, {"X": 0.431517630815506, "Y": 0.2761385440826416}, {"X": 0.3737349510192871, "Y": 0.2761386036872864}]}, "Id": "db53e3eb-5250-4bce-91b9-cb763f3dff86", "Relationships": [{"Type": "CHILD", "Ids": ["977ac6e3-430a-4675-bbc2-08add0dbb327", "cd43ad7f-052d-426d-ac52-fb1e3f77598f"]}]}, {"BlockType": "LINE", "Confidence": 99.88719940185547, "Text": "Total Rated Dimensions:", "Geometry": {"BoundingBox": {"Width": 0.19399678707122803, "Height": 0.010948152281343937, "Left": 0.10999516397714615, "Top": 0.2834796905517578}, "Polygon": [{"X": 0.10999516397714615, "Y": 0.28347986936569214}, {"X": 0.3039919435977936, "Y": 0.2834796905517578}, {"X": 0.3039919435977936, "Y": 0.29442766308784485}, {"X": 0.10999520123004913, "Y": 0.2944278419017792}]}, "Id": "947fd5e0-b1b9-4843-81ac-22a81216d7ad", "Relationships": [{"Type": "CHILD", "Ids": ["99b48cf6-c6d7-4824-981a-1850e39e424f", "fa5bc125-eb08-4365-9926-d4341335017e", "24a37f45-45fe-4ebb-aba0-7c16c96dd876"]}]}, {"BlockType": "LINE", "Confidence": 98.92366790771484, "Text": "48 L X 40 W X 16 H", "Geometry": {"BoundingBox": {"Width": 0.15823505818843842, "Height": 0.011254629120230675, "Left": 0.3737664222717285, "Top": 0.2832982838153839}, "Polygon": [{"X": 0.3737664222717285, "Y": 0.28329843282699585}, {"X": 0.5320014953613281, "Y": 0.2832982838153839}, {"X": 0.5320014357566833, "Y": 0.2945527732372284}, {"X": 0.3737664222717285, "Y": 0.29455292224884033}]}, "Id": "cbeaeca0-2453-4a06-aa17-45c89428efb6", "Relationships": [{"Type": "CHILD", "Ids": ["df408409-03a7-4c09-8bfe-6393ae5ff9d4", "5de04d78-2c36-4534-9a9d-c2c41a7cc9e0", "5ddf2115-15c1-4ec4-a341-815f3d587531", "42065ed3-65fd-42b8-b287-352c652efd73", "c557ff5b-8a95-4d7b-ae8e-ad2df72e5cc6", "d3d3cfe0-081d-418a-9b05-d3d9e77669fd", "1a128e22-eb24-4411-9854-5a4eb702e79a", "b2dad6ec-a935-46cd-9810-05946291d5b2"]}]}, {"BlockType": "LINE", "Confidence": 99.95525360107422, "Text": "Total Mileage:", "Geometry": {"BoundingBox": {"Width": 0.10993698239326477, "Height": 0.013781802728772163, "Left": 0.10994478315114975, "Top": 0.30163636803627014}, "Polygon": [{"X": 0.10994478315114975, "Y": 0.3016364574432373}, {"X": 0.21988174319267273, "Y": 0.30163636803627014}, {"X": 0.21988175809383392, "Y": 0.3154180943965912}, {"X": 0.10994482785463333, "Y": 0.31541818380355835}]}, "Id": "c18ff8a0-6b80-4497-bbfb-a120826bfb81", "Relationships": [{"Type": "CHILD", "Ids": ["d81503d6-cb3b-4cc8-9a02-dd6395e6c340", "3addb160-16ac-430f-8ea5-93d2a505b49f"]}]}, {"BlockType": "LINE", "Confidence": 99.84375, "Text": "1,900 Miles", "Geometry": {"BoundingBox": {"Width": 0.09043123573064804, "Height": 0.012709901668131351, "Left": 0.3750159740447998, "Top": 0.30161741375923157}, "Polygon": [{"X": 0.3750159740447998, "Y": 0.30161750316619873}, {"X": 0.46544721722602844, "Y": 0.30161741375923157}, {"X": 0.46544718742370605, "Y": 0.3143272399902344}, {"X": 0.3750159740447998, "Y": 0.31432729959487915}]}, "Id": "111cdd8b-3a69-4827-a6c0-ef3d7641b00e", "Relationships": [{"Type": "CHILD", "Ids": ["0e9f2d65-5593-492b-89bd-adb3ac43aa81", "38c4487a-e589-42f9-8e72-32aaaa0deda5"]}]}, {"BlockType": "LINE", "Confidence": 99.99674224853516, "Text": "Total Rated Cube:", "Geometry": {"BoundingBox": {"Width": 0.14325794577598572, "Height": 0.011030927300453186, "Left": 0.10994534194469452, "Top": 0.32013410329818726}, "Polygon": [{"X": 0.10994534194469452, "Y": 0.3201342225074768}, {"X": 0.25320327281951904, "Y": 0.32013410329818726}, {"X": 0.25320327281951904, "Y": 0.3311648964881897}, {"X": 0.1099453791975975, "Y": 0.33116501569747925}]}, "Id": "070db501-c160-4701-8ff0-3de072a0e930", "Relationships": [{"Type": "CHILD", "Ids": ["938feb6f-07da-45ba-ba19-c909b0e791a3", "119ed31a-3a92-42f5-af7b-8cb8dc7a1ef5", "d5c5abcf-6ae2-44b7-b809-3ea4699e8022"]}]}, {"BlockType": "LINE", "Confidence": 99.89583587646484, "Text": "17.78 <PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.13636669516563416, "Height": 0.011121213436126709, "Left": 0.3750312924385071, "Top": 0.32007160782814026}, "Polygon": [{"X": 0.3750312924385071, "Y": 0.3200717270374298}, {"X": 0.5113980174064636, "Y": 0.32007160782814026}, {"X": 0.5113979578018188, "Y": 0.3311927020549774}, {"X": 0.3750312924385071, "Y": 0.33119282126426697}]}, "Id": "ffc85788-5cd8-4f4d-bcd9-1af7630b0c59", "Relationships": [{"Type": "CHILD", "Ids": ["2175dc67-cc2d-45ee-a736-bbe3bfd90b35", "81410d05-cb7e-4c60-be66-6a5b2c747e70", "805e6cee-e494-4cfb-8321-db6990302763"]}]}, {"BlockType": "LINE", "Confidence": 99.99674224853516, "Text": "Total Rated Density:", "Geometry": {"BoundingBox": {"Width": 0.16021817922592163, "Height": 0.013626997359097004, "Left": 0.11006525903940201, "Top": 0.3382006287574768}, "Polygon": [{"X": 0.11006525903940201, "Y": 0.33820074796676636}, {"X": 0.27028343081474304, "Y": 0.3382006287574768}, {"X": 0.27028343081474304, "Y": 0.3518275022506714}, {"X": 0.11006529629230499, "Y": 0.35182762145996094}]}, "Id": "0d2922e1-7415-4a21-b8f1-0f8cbf6078f7", "Relationships": [{"Type": "CHILD", "Ids": ["8c18a50f-ff3f-4f91-a9ae-1687091281bd", "a49aa912-629b-4e15-916e-7892f89e483c", "fe03f915-aa7c-4ffb-9772-955574e2f37e"]}]}, {"BlockType": "LINE", "Confidence": 99.55078125, "Text": "15.748 PCF", "Geometry": {"BoundingBox": {"Width": 0.09403590857982635, "Height": 0.011022278107702732, "Left": 0.37491366267204285, "Top": 0.33837810158729553}, "Polygon": [{"X": 0.37491366267204285, "Y": 0.3383781611919403}, {"X": 0.468949556350708, "Y": 0.33837810158729553}, {"X": 0.468949556350708, "Y": 0.3494003117084503}, {"X": 0.37491366267204285, "Y": 0.3494003713130951}]}, "Id": "b086538e-fdea-4ab1-8986-cf0b550063b5", "Relationships": [{"Type": "CHILD", "Ids": ["9b86e932-655f-4e86-9235-e1508e86a182", "f469ec07-4a59-41e5-a2ca-0f33d925797f"]}]}, {"BlockType": "LINE", "Confidence": 99.92630004882812, "Text": "Trailer Measurements Done By Dock", "Geometry": {"BoundingBox": {"Width": 0.31239157915115356, "Height": 0.013727175071835518, "Left": 0.10968217998743057, "Top": 0.3858337998390198}, "Polygon": [{"X": 0.10968217998743057, "Y": 0.3858340084552765}, {"X": 0.42207375168800354, "Y": 0.3858337998390198}, {"X": 0.42207375168800354, "Y": 0.399560809135437}, {"X": 0.10968222469091415, "Y": 0.39956098794937134}]}, "Id": "d9a0d008-083b-4a48-9562-3bb0a062c6c3", "Relationships": [{"Type": "CHILD", "Ids": ["e146c76f-64f1-4b81-b6bf-ee2baa5f81d3", "ba03420f-ee76-45fd-8ea6-7246413e1b3d", "4f6078bf-1502-42dd-9b7f-32ca5ee303e6", "5f85bf52-a1cd-499a-8194-49102e13aaa3", "7fe135f6-cddd-4ec1-950d-46b2431b33b4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Name", "Geometry": {"BoundingBox": {"Width": 0.04676304757595062, "Height": 0.010930419899523258, "Left": 0.21485596895217896, "Top": 0.40411847829818726}, "Polygon": [{"X": 0.21485596895217896, "Y": 0.40411850810050964}, {"X": 0.2616190016269684, "Y": 0.40411847829818726}, {"X": 0.2616190016269684, "Y": 0.41504886746406555}, {"X": 0.21485598385334015, "Y": 0.41504889726638794}]}, "Id": "0d667395-fe28-47db-91ec-ac5e89291053", "Relationships": [{"Type": "CHILD", "Ids": ["12ea2679-0fe0-47bf-97a2-277f9c4909a8"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Station", "Geometry": {"BoundingBox": {"Width": 0.05527513474225998, "Height": 0.010969388298690319, "Left": 0.47340425848960876, "Top": 0.4040168523788452}, "Polygon": [{"X": 0.47340428829193115, "Y": 0.4040168821811676}, {"X": 0.528679370880127, "Y": 0.4040168523788452}, {"X": 0.528679370880127, "Y": 0.4149862229824066}, {"X": 0.47340425848960876, "Y": 0.414986252784729}]}, "Id": "3340362d-7d21-41a7-ae85-c832f9513479", "Relationships": [{"Type": "CHILD", "Ids": ["1c817c1c-c8b7-40f4-a658-8126d3c9410b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.036862775683403015, "Height": 0.010849637910723686, "Left": 0.7453412413597107, "Top": 0.4041246771812439}, "Polygon": [{"X": 0.7453413009643555, "Y": 0.4041246771812439}, {"X": 0.7822040319442749, "Y": 0.4041246771812439}, {"X": 0.7822039723396301, "Y": 0.41497430205345154}, {"X": 0.7453412413597107, "Y": 0.41497430205345154}]}, "Id": "7fda46ec-e17d-41ce-88db-b7459ee7644b", "Relationships": [{"Type": "CHILD", "Ids": ["54aa188f-3a09-430a-989d-50767e58691e"]}]}, {"BlockType": "LINE", "Confidence": 99.90035247802734, "Text": "<PERSON><PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.12828165292739868, "Height": 0.013511269353330135, "Left": 0.17404647171497345, "Top": 0.42260441184043884}, "Polygon": [{"X": 0.17404647171497345, "Y": 0.4226044714450836}, {"X": 0.30232810974121094, "Y": 0.42260441184043884}, {"X": 0.30232810974121094, "Y": 0.4361156225204468}, {"X": 0.17404650151729584, "Y": 0.43611568212509155}]}, "Id": "d2bf140f-a448-491a-9808-43779079c0fe", "Relationships": [{"Type": "CHILD", "Ids": ["d957677b-b67d-4f05-b514-f461bd916ac4", "fc7aaa89-d2d9-4706-b84a-1e2f5485e3b2"]}]}, {"BlockType": "LINE", "Confidence": 97.17948150634766, "Text": "352 - Avenel, NJ", "Geometry": {"BoundingBox": {"Width": 0.13210774958133698, "Height": 0.013055645860731602, "Left": 0.4350687265396118, "Top": 0.422454297542572}, "Polygon": [{"X": 0.4350687265396118, "Y": 0.4224543571472168}, {"X": 0.5671764612197876, "Y": 0.422454297542572}, {"X": 0.5671764612197876, "Y": 0.43550989031791687}, {"X": 0.4350687265396118, "Y": 0.43550994992256165}]}, "Id": "b2ca1faf-db37-415e-848c-d52bc14dd355", "Relationships": [{"Type": "CHILD", "Ids": ["987f2607-caab-445e-873a-f2cbb1b33687", "46628c80-3bcc-40f7-b7ec-defbbb1575f6", "3d3cbadc-4d85-4086-a127-1a2dad8f01df", "dfa2025b-c188-44a3-8f95-23468f153ae1"]}]}, {"BlockType": "LINE", "Confidence": 99.71028900146484, "Text": "07/16/2025 2:10 PM", "Geometry": {"BoundingBox": {"Width": 0.1620771437883377, "Height": 0.011287374421954155, "Left": 0.6825687885284424, "Top": 0.4224686324596405}, "Polygon": [{"X": 0.6825687885284424, "Y": 0.4224686920642853}, {"X": 0.8446459174156189, "Y": 0.4224686324596405}, {"X": 0.8446458578109741, "Y": 0.43375593423843384}, {"X": 0.6825687885284424, "Y": 0.4337559938430786}]}, "Id": "f3a43546-cf11-489f-bbea-1980f1070523", "Relationships": [{"Type": "CHILD", "Ids": ["b34bd528-c499-4350-b6ea-154eef069c42", "1b1ecb52-6503-449c-af59-07ca3ce9579e", "236442cc-e7eb-442b-bc0f-3600ddf72c50"]}]}, {"BlockType": "LINE", "Confidence": 99.95753479003906, "Text": "Dimensions outlined above are actual dimensions and may be subject to Item 110 paragraph 14 for", "Geometry": {"BoundingBox": {"Width": 0.5932554006576538, "Height": 0.011593159288167953, "Left": 0.08117885142564774, "Top": 0.46901386976242065}, "Polygon": [{"X": 0.08117885142564774, "Y": 0.4690140187740326}, {"X": 0.6744343042373657, "Y": 0.46901386976242065}, {"X": 0.674434244632721, "Y": 0.48060688376426697}, {"X": 0.08117889612913132, "Y": 0.4806070029735565}]}, "Id": "4babf755-c1d5-4d22-8116-c13633b397a0", "Relationships": [{"Type": "CHILD", "Ids": ["ea94ffbb-794d-4b70-858e-1f109c089fbd", "ee38b64d-e81f-4488-9a9b-9429242c9ca2", "9b471313-0919-4069-b8b0-737325525ab9", "e7f16794-2602-4c11-a01b-f23a432c44f5", "6a629475-a420-455c-b22c-c408e0e5282a", "51a629bb-b747-4b6a-ab81-b4b556769e31", "4f1c28a2-e1a8-4991-897f-24cbb3dfa58d", "4533caed-86c8-4686-96ff-a93a89706ccb", "ad92db93-1e0e-4776-89a9-10967b288224", "5f40a068-9ebd-4ddf-9dab-bb74db749bbc", "75b49e64-e94b-42c9-81cb-f9fac1780fb7", "df86e8ed-3eeb-494d-87e9-02d44746b8e9", "3c1715e9-8248-47cc-bd43-cb5e6edb0b05", "0b9a4c0e-1e2b-4d50-8df9-a791d40659e1", "bcb99f4d-2ddd-45ba-83b6-e4bba7bfa930", "fe35f1f1-9ba7-4b8b-b8ff-94a753e7aa1e"]}]}, {"BlockType": "LINE", "Confidence": 99.98345947265625, "Text": "calculating effective occupancy.", "Geometry": {"BoundingBox": {"Width": 0.1916247457265854, "Height": 0.011674925684928894, "Left": 0.08120424300432205, "Top": 0.4832383096218109}, "Polygon": [{"X": 0.08120424300432205, "Y": 0.4832383394241333}, {"X": 0.27282896637916565, "Y": 0.4832383096218109}, {"X": 0.27282899618148804, "Y": 0.4949131906032562}, {"X": 0.08120428025722504, "Y": 0.4949132204055786}]}, "Id": "db787064-a06f-4d10-96a7-e662f092c8a2", "Relationships": [{"Type": "CHILD", "Ids": ["296c8eea-d49e-4cb3-9cc4-1c8c7e807b33", "7c43ed39-874e-4990-90ef-5640ec522b3c", "08141b78-5245-4d3e-9b87-c096ab49fddc"]}]}, {"BlockType": "WORD", "Confidence": 82.90676879882812, "Text": "®", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006544790230691433, "Height": 0.005231551360338926, "Left": 0.34556087851524353, "Top": 0.08397401124238968}, "Polygon": [{"X": 0.34556087851524353, "Y": 0.08397402614355087}, {"X": 0.35210567712783813, "Y": 0.08397401124238968}, {"X": 0.35210567712783813, "Y": 0.0892055556178093}, {"X": 0.34556087851524353, "Y": 0.08920556306838989}]}, "Id": "447bb0ed-532b-4d6d-959f-3d56ae454dad"}, {"BlockType": "WORD", "Confidence": 91.42359161376953, "Text": "ABF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16419638693332672, "Height": 0.0437476709485054, "Left": 0.1863717883825302, "Top": 0.0837559700012207}, "Polygon": [{"X": 0.1863717883825302, "Y": 0.08375624567270279}, {"X": 0.35056814551353455, "Y": 0.0837559700012207}, {"X": 0.35056817531585693, "Y": 0.1275033950805664}, {"X": 0.18637189269065857, "Y": 0.1275036484003067}]}, "Id": "3cd2f454-29b0-49fc-84f7-8ff94ff2a0e2"}, {"BlockType": "WORD", "Confidence": 98.85921478271484, "Text": "DIMENSION/CUBE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.33914610743522644, "Height": 0.02311941795051098, "Left": 0.49188968539237976, "Top": 0.10118571668863297}, "Polygon": [{"X": 0.49188971519470215, "Y": 0.10118626803159714}, {"X": 0.8310357928276062, "Y": 0.10118571668863297}, {"X": 0.8310356736183167, "Y": 0.12430461496114731}, {"X": 0.49188968539237976, "Y": 0.12430513650178909}]}, "Id": "529c84c3-269e-4203-90aa-a48c4f0fc3a3"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06602073460817337, "Height": 0.014087923802435398, "Left": 0.2620227038860321, "Top": 0.13036800920963287}, "Polygon": [{"X": 0.2620227038860321, "Y": 0.13036811351776123}, {"X": 0.3280434310436249, "Y": 0.13036800920963287}, {"X": 0.3280434310436249, "Y": 0.14445583522319794}, {"X": 0.2620227336883545, "Y": 0.1444559246301651}]}, "Id": "3f1a1920-7d16-4381-af06-d21ebf81752a"}, {"BlockType": "WORD", "Confidence": 99.56153869628906, "Text": "CERTIFICATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.29736945033073425, "Height": 0.01909993216395378, "Left": 0.5122085213661194, "Top": 0.12711404263973236}, "Polygon": [{"X": 0.5122085809707642, "Y": 0.12711448967456818}, {"X": 0.809578001499176, "Y": 0.12711404263973236}, {"X": 0.8095778822898865, "Y": 0.14621354639530182}, {"X": 0.5122085213661194, "Y": 0.14621397852897644}]}, "Id": "5cc8010a-fbdd-4495-9d23-779fb1215851"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "An", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01833299919962883, "Height": 0.009173985570669174, "Left": 0.18612322211265564, "Top": 0.15317994356155396}, "Polygon": [{"X": 0.18612322211265564, "Y": 0.15317997336387634}, {"X": 0.20445619523525238, "Y": 0.15317994356155396}, {"X": 0.20445622503757477, "Y": 0.16235390305519104}, {"X": 0.18612323701381683, "Y": 0.16235393285751343}]}, "Id": "2aba74a1-d418-4e2b-87d3-ed46c51e4679"}, {"BlockType": "WORD", "Confidence": 96.82178497314453, "Text": "ArcBest", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058094706386327744, "Height": 0.009328647516667843, "Left": 0.2088058441877365, "Top": 0.15304400026798248}, "Polygon": [{"X": 0.2088058441877365, "Y": 0.15304407477378845}, {"X": 0.26690053939819336, "Y": 0.15304400026798248}, {"X": 0.26690053939819336, "Y": 0.16237255930900574}, {"X": 0.2088058590888977, "Y": 0.1623726338148117}]}, "Id": "75ff1403-895d-4f7d-90bd-8a4aaf0ec568"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Company", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06347786635160446, "Height": 0.01195122953504324, "Left": 0.2706875205039978, "Top": 0.1528737097978592}, "Polygon": [{"X": 0.2706875205039978, "Y": 0.15287379920482635}, {"X": 0.3341653645038605, "Y": 0.1528737097978592}, {"X": 0.33416539430618286, "Y": 0.16482485830783844}, {"X": 0.2706875205039978, "Y": 0.1648249477148056}]}, "Id": "e9dec3e8-9016-4591-a219-81430b65b0e2"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Pronumber:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09246920794248581, "Height": 0.011047291569411755, "Left": 0.11052803695201874, "Top": 0.18101191520690918}, "Polygon": [{"X": 0.11052803695201874, "Y": 0.18101203441619873}, {"X": 0.20299722254276276, "Y": 0.18101191520690918}, {"X": 0.20299725234508514, "Y": 0.19205908477306366}, {"X": 0.11052807420492172, "Y": 0.1920592039823532}]}, "Id": "50141dd5-73dd-467a-be5d-8937c74462b4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "352309638", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08982306718826294, "Height": 0.011103572323918343, "Left": 0.3737112283706665, "Top": 0.1811026781797409}, "Polygon": [{"X": 0.3737112283706665, "Y": 0.18110279738903046}, {"X": 0.46353429555892944, "Y": 0.1811026781797409}, {"X": 0.46353429555892944, "Y": 0.19220614433288574}, {"X": 0.3737112283706665, "Y": 0.1922062486410141}]}, "Id": "06da4c88-c2eb-458b-9c08-90c434566460"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050973568111658096, "Height": 0.010954533703625202, "Left": 0.63603675365448, "Top": 0.1810583472251892}, "Polygon": [{"X": 0.6360368132591248, "Y": 0.18105840682983398}, {"X": 0.6870103478431702, "Y": 0.1810583472251892}, {"X": 0.6870102882385254, "Y": 0.19201281666755676}, {"X": 0.63603675365448, "Y": 0.19201287627220154}]}, "Id": "450f9429-15ca-42e1-bd43-3ed9e1ff4efc"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "No:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0264002475887537, "Height": 0.011072097346186638, "Left": 0.6920583248138428, "Top": 0.18102805316448212}, "Polygon": [{"X": 0.6920583844184875, "Y": 0.1810280978679657}, {"X": 0.7184585928916931, "Y": 0.18102805316448212}, {"X": 0.7184585332870483, "Y": 0.19210012257099152}, {"X": 0.6920583248138428, "Y": 0.1921001523733139}]}, "Id": "839cf69a-87f5-4756-b4b6-1d31a4260097"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "CITY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04036648944020271, "Height": 0.011076513677835464, "Left": 0.724669337272644, "Top": 0.18092812597751617}, "Polygon": [{"X": 0.7246693968772888, "Y": 0.18092818558216095}, {"X": 0.7650358080863953, "Y": 0.18092812597751617}, {"X": 0.7650357484817505, "Y": 0.19200459122657776}, {"X": 0.724669337272644, "Y": 0.19200465083122253}]}, "Id": "63d61a9a-fdd6-4a22-b002-dfafb9a48f27"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "410498", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06042822077870369, "Height": 0.010986443608999252, "Left": 0.7699111700057983, "Top": 0.18103894591331482}, "Polygon": [{"X": 0.7699112296104431, "Y": 0.1810390204191208}, {"X": 0.8303393721580505, "Y": 0.18103894591331482}, {"X": 0.8303393125534058, "Y": 0.1920253038406372}, {"X": 0.7699111700057983, "Y": 0.19202537834644318}]}, "Id": "e3601e55-4072-4a26-88bb-7c025a430c36"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Shipper:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06541091948747635, "Height": 0.013754081912338734, "Left": 0.11052986234426498, "Top": 0.19902680814266205}, "Polygon": [{"X": 0.11052986234426498, "Y": 0.1990268975496292}, {"X": 0.17594075202941895, "Y": 0.19902680814266205}, {"X": 0.17594078183174133, "Y": 0.21278081834316254}, {"X": 0.11052990704774857, "Y": 0.2127808928489685}]}, "Id": "2d8854f2-c460-4c9a-a72f-8e9a002a901e"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Headstone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08621614426374435, "Height": 0.011009693145751953, "Left": 0.3744701147079468, "Top": 0.19915227591991425}, "Polygon": [{"X": 0.3744701147079468, "Y": 0.1991523802280426}, {"X": 0.46068626642227173, "Y": 0.19915227591991425}, {"X": 0.46068626642227173, "Y": 0.21016184985637665}, {"X": 0.3744701147079468, "Y": 0.2101619690656662}]}, "Id": "c99a8df8-d258-4cd5-ac8c-45fa30e642fe"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Deals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04521114006638527, "Height": 0.010936052538454533, "Left": 0.46625006198883057, "Top": 0.19918932020664215}, "Polygon": [{"X": 0.46625009179115295, "Y": 0.19918937981128693}, {"X": 0.5114611983299255, "Y": 0.19918932020664215}, {"X": 0.5114611983299255, "Y": 0.21012532711029053}, {"X": 0.46625006198883057, "Y": 0.2101253867149353}]}, "Id": "af1a1bd9-be4a-447f-91ff-c8b2ef15ae46"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Totals", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05161131173372269, "Height": 0.011102463118731976, "Left": 0.10968312621116638, "Top": 0.24648213386535645}, "Polygon": [{"X": 0.10968312621116638, "Y": 0.24648219347000122}, {"X": 0.16129440069198608, "Y": 0.24648213386535645}, {"X": 0.16129443049430847, "Y": 0.2575845420360565}, {"X": 0.10968315601348877, "Y": 0.2575846016407013}]}, "Id": "80399951-3ede-4f03-8f63-fffe3be80cbe"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03781477361917496, "Height": 0.011112128384411335, "Left": 0.1098165512084961, "Top": 0.26486292481422424}, "Polygon": [{"X": 0.1098165512084961, "Y": 0.26486295461654663}, {"X": 0.14763128757476807, "Y": 0.26486292481422424}, {"X": 0.14763131737709045, "Y": 0.27597498893737793}, {"X": 0.10981658101081848, "Y": 0.2759750485420227}]}, "Id": "efe7c190-00f6-4c35-8f93-c07387ad04cb"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05993150919675827, "Height": 0.013707414269447327, "Left": 0.15283361077308655, "Top": 0.26494428515434265}, "Polygon": [{"X": 0.15283361077308655, "Y": 0.2649443447589874}, {"X": 0.21276509761810303, "Y": 0.26494428515434265}, {"X": 0.21276512742042542, "Y": 0.2786516547203064}, {"X": 0.15283365547657013, "Y": 0.27865171432495117}]}, "Id": "0bd505d6-2808-4282-8341-be3a8ecdc3e6"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "280", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029715221375226974, "Height": 0.011156111024320126, "Left": 0.3737349510192871, "Top": 0.2649824917316437}, "Polygon": [{"X": 0.3737349510192871, "Y": 0.26498252153396606}, {"X": 0.4034501612186432, "Y": 0.2649824917316437}, {"X": 0.4034501612186432, "Y": 0.276138573884964}, {"X": 0.3737349510192871, "Y": 0.2761386036872864}]}, "Id": "977ac6e3-430a-4675-bbc2-08add0dbb327"}, {"BlockType": "WORD", "Confidence": 94.94100952148438, "Text": "lbs", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02242872677743435, "Height": 0.010742166079580784, "Left": 0.4090889096260071, "Top": 0.26517224311828613}, "Polygon": [{"X": 0.4090889096260071, "Y": 0.26517224311828613}, {"X": 0.431517630815506, "Y": 0.26517224311828613}, {"X": 0.431517630815506, "Y": 0.27591437101364136}, {"X": 0.4090889096260071, "Y": 0.27591440081596375}]}, "Id": "cd43ad7f-052d-426d-ac52-fb1e3f77598f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037389758974313736, "Height": 0.010948002338409424, "Left": 0.10999516397714615, "Top": 0.28347983956336975}, "Polygon": [{"X": 0.10999516397714615, "Y": 0.28347986936569214}, {"X": 0.1473848968744278, "Y": 0.28347983956336975}, {"X": 0.14738492667675018, "Y": 0.2944277822971344}, {"X": 0.10999520123004913, "Y": 0.2944278419017792}]}, "Id": "99b48cf6-c6d7-4824-981a-1850e39e424f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Rated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0467238649725914, "Height": 0.010807320475578308, "Left": 0.15381984412670135, "Top": 0.2835935056209564}, "Polygon": [{"X": 0.15381984412670135, "Y": 0.2835935354232788}, {"X": 0.20054367184638977, "Y": 0.2835935056209564}, {"X": 0.20054370164871216, "Y": 0.29440078139305115}, {"X": 0.15381987392902374, "Y": 0.29440081119537354}]}, "Id": "fa5bc125-eb08-4365-9926-d4341335017e"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Dimensions:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09696175903081894, "Height": 0.010912691242992878, "Left": 0.20703019201755524, "Top": 0.2834863066673279}, "Polygon": [{"X": 0.20703019201755524, "Y": 0.28348639607429504}, {"X": 0.3039919435977936, "Y": 0.2834863066673279}, {"X": 0.3039919435977936, "Y": 0.2943989038467407}, {"X": 0.20703020691871643, "Y": 0.2943989932537079}]}, "Id": "24a37f45-45fe-4ebb-aba0-7c16c96dd876"}, {"BlockType": "WORD", "Confidence": 99.853515625, "Text": "48", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019447343423962593, "Height": 0.010942968539893627, "Left": 0.3737664222717285, "Top": 0.2835685610771179}, "Polygon": [{"X": 0.3737664222717285, "Y": 0.2835685610771179}, {"X": 0.39321374893188477, "Y": 0.2835685610771179}, {"X": 0.39321374893188477, "Y": 0.29451149702072144}, {"X": 0.3737664222717285, "Y": 0.2945115268230438}]}, "Id": "df408409-03a7-4c09-8bfe-6393ae5ff9d4"}, {"BlockType": "WORD", "Confidence": 99.29448699951172, "Text": "L", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009671926498413086, "Height": 0.011005237698554993, "Left": 0.39923742413520813, "Top": 0.28343790769577026}, "Polygon": [{"X": 0.39923742413520813, "Y": 0.28343793749809265}, {"X": 0.4089093506336212, "Y": 0.28343790769577026}, {"X": 0.4089093506336212, "Y": 0.29444316029548645}, {"X": 0.39923742413520813, "Y": 0.29444316029548645}]}, "Id": "5de04d78-2c36-4534-9a9d-c2c41a7cc9e0"}, {"BlockType": "WORD", "Confidence": 97.75450134277344, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012429571710526943, "Height": 0.01106173824518919, "Left": 0.4135148823261261, "Top": 0.28337886929512024}, "Polygon": [{"X": 0.4135148823261261, "Y": 0.28337886929512024}, {"X": 0.4259444773197174, "Y": 0.28337886929512024}, {"X": 0.425944447517395, "Y": 0.2944405972957611}, {"X": 0.4135148823261261, "Y": 0.2944405972957611}]}, "Id": "5ddf2115-15c1-4ec4-a341-815f3d587531"}, {"BlockType": "WORD", "Confidence": 99.609375, "Text": "40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02034015953540802, "Height": 0.010836265981197357, "Left": 0.43034833669662476, "Top": 0.28360316157341003}, "Polygon": [{"X": 0.43034836649894714, "Y": 0.28360316157341003}, {"X": 0.45068851113319397, "Y": 0.28360316157341003}, {"X": 0.45068851113319397, "Y": 0.2944394052028656}, {"X": 0.43034833669662476, "Y": 0.294439435005188}]}, "Id": "42065ed3-65fd-42b8-b287-352c652efd73"}, {"BlockType": "WORD", "Confidence": 99.55735778808594, "Text": "W", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01783633418381214, "Height": 0.010819087736308575, "Left": 0.45510393381118774, "Top": 0.2834729850292206}, "Polygon": [{"X": 0.45510393381118774, "Y": 0.28347301483154297}, {"X": 0.47294026613235474, "Y": 0.2834729850292206}, {"X": 0.47294026613235474, "Y": 0.29429206252098083}, {"X": 0.45510393381118774, "Y": 0.29429206252098083}]}, "Id": "c557ff5b-8a95-4d7b-ae8e-ad2df72e5cc6"}, {"BlockType": "WORD", "Confidence": 98.1287841796875, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012423158623278141, "Height": 0.011155807413160801, "Left": 0.4775581657886505, "Top": 0.2832983136177063}, "Polygon": [{"X": 0.4775581657886505, "Y": 0.2832983434200287}, {"X": 0.4899813234806061, "Y": 0.2832983136177063}, {"X": 0.4899812936782837, "Y": 0.2944541275501251}, {"X": 0.4775581657886505, "Y": 0.2944541275501251}]}, "Id": "d3d3cfe0-081d-418a-9b05-d3d9e77669fd"}, {"BlockType": "WORD", "Confidence": 99.365234375, "Text": "16", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018461085855960846, "Height": 0.01099309977144003, "Left": 0.4959329068660736, "Top": 0.2835597097873688}, "Polygon": [{"X": 0.495932936668396, "Y": 0.2835597097873688}, {"X": 0.5143939852714539, "Y": 0.2835597097873688}, {"X": 0.5143939852714539, "Y": 0.2945527732372284}, {"X": 0.4959329068660736, "Y": 0.2945528030395508}]}, "Id": "1a128e22-eb24-4411-9854-5a4eb702e79a"}, {"BlockType": "WORD", "Confidence": 97.8260498046875, "Text": "H", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011625777930021286, "Height": 0.010800197720527649, "Left": 0.520375669002533, "Top": 0.28358784317970276}, "Polygon": [{"X": 0.5203757286071777, "Y": 0.28358784317970276}, {"X": 0.5320014953613281, "Y": 0.28358784317970276}, {"X": 0.5320014357566833, "Y": 0.2943880259990692}, {"X": 0.520375669002533, "Y": 0.2943880259990692}]}, "Id": "b2dad6ec-a935-46cd-9810-05946291d5b2"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037486713379621506, "Height": 0.010951882228255272, "Left": 0.10994478315114975, "Top": 0.3016364276409149}, "Polygon": [{"X": 0.10994478315114975, "Y": 0.3016364574432373}, {"X": 0.14743146300315857, "Y": 0.3016364276409149}, {"X": 0.14743149280548096, "Y": 0.31258827447891235}, {"X": 0.10994482040405273, "Y": 0.31258830428123474}]}, "Id": "d81503d6-cb3b-4cc8-9a02-dd6395e6c340"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Mileage:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06645472347736359, "Height": 0.013722969219088554, "Left": 0.15342703461647034, "Top": 0.30169516801834106}, "Polygon": [{"X": 0.15342703461647034, "Y": 0.30169522762298584}, {"X": 0.21988174319267273, "Y": 0.30169516801834106}, {"X": 0.21988175809383392, "Y": 0.3154180943965912}, {"X": 0.15342707931995392, "Y": 0.3154181241989136}]}, "Id": "3addb160-16ac-430f-8ea5-93d2a505b49f"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "1,900", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043364640325307846, "Height": 0.01263413019478321, "Left": 0.3750159740447998, "Top": 0.3016931712627411}, "Polygon": [{"X": 0.3750159740447998, "Y": 0.30169323086738586}, {"X": 0.41838061809539795, "Y": 0.3016931712627411}, {"X": 0.41838061809539795, "Y": 0.31432726979255676}, {"X": 0.3750159740447998, "Y": 0.31432729959487915}]}, "Id": "0e9f2d65-5593-492b-89bd-adb3ac43aa81"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041378434747457504, "Height": 0.010960670188069344, "Left": 0.42406877875328064, "Top": 0.30161741375923157}, "Polygon": [{"X": 0.42406877875328064, "Y": 0.30161744356155396}, {"X": 0.46544721722602844, "Y": 0.30161741375923157}, {"X": 0.46544718742370605, "Y": 0.3125780522823334}, {"X": 0.42406877875328064, "Y": 0.31257808208465576}]}, "Id": "38c4487a-e589-42f9-8e72-32aaaa0deda5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03749297559261322, "Height": 0.011030840687453747, "Left": 0.10994534194469452, "Top": 0.32013416290283203}, "Polygon": [{"X": 0.10994534194469452, "Y": 0.3201342225074768}, {"X": 0.14743828773498535, "Y": 0.32013416290283203}, {"X": 0.14743831753730774, "Y": 0.33116498589515686}, {"X": 0.1099453791975975, "Y": 0.33116501569747925}]}, "Id": "938feb6f-07da-45ba-ba19-c909b0e791a3"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Rated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046675823628902435, "Height": 0.010875381529331207, "Left": 0.15378113090991974, "Top": 0.3201565742492676}, "Polygon": [{"X": 0.15378113090991974, "Y": 0.32015663385391235}, {"X": 0.20045693218708038, "Y": 0.3201565742492676}, {"X": 0.20045696198940277, "Y": 0.3310319185256958}, {"X": 0.15378116071224213, "Y": 0.3310319483280182}]}, "Id": "119ed31a-3a92-42f5-af7b-8cb8dc7a1ef5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Cube:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.046871162950992584, "Height": 0.010979341343045235, "Left": 0.20633211731910706, "Top": 0.3201426863670349}, "Polygon": [{"X": 0.20633211731910706, "Y": 0.3201427161693573}, {"X": 0.25320327281951904, "Y": 0.3201426863670349}, {"X": 0.25320327281951904, "Y": 0.3311219811439514}, {"X": 0.20633214712142944, "Y": 0.3311220109462738}]}, "Id": "d5c5abcf-6ae2-44b7-b809-3ea4699e8022"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "17.78", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043415866792201996, "Height": 0.01112113706767559, "Left": 0.3750312924385071, "Top": 0.32007166743278503}, "Polygon": [{"X": 0.3750312924385071, "Y": 0.3200717270374298}, {"X": 0.4184471666812897, "Y": 0.32007166743278503}, {"X": 0.4184471666812897, "Y": 0.3311927914619446}, {"X": 0.3750312924385071, "Y": 0.33119282126426697}]}, "Id": "2175dc67-cc2d-45ee-a736-bbe3bfd90b35"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Cubic", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045745305716991425, "Height": 0.010862244293093681, "Left": 0.4241652488708496, "Top": 0.3200869858264923}, "Polygon": [{"X": 0.424165278673172, "Y": 0.3200870454311371}, {"X": 0.46991056203842163, "Y": 0.3200869858264923}, {"X": 0.46991056203842163, "Y": 0.33094921708106995}, {"X": 0.4241652488708496, "Y": 0.33094924688339233}]}, "Id": "81410d05-cb7e-4c60-be66-6a5b2c747e70"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Feet", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035785503685474396, "Height": 0.010813392698764801, "Left": 0.47561249136924744, "Top": 0.3203466832637787}, "Polygon": [{"X": 0.4756125211715698, "Y": 0.3203467130661011}, {"X": 0.5113980174064636, "Y": 0.3203466832637787}, {"X": 0.5113979578018188, "Y": 0.3311600387096405}, {"X": 0.47561249136924744, "Y": 0.3311600685119629}]}, "Id": "805e6cee-e494-4cfb-8321-db6990302763"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037358466535806656, "Height": 0.011022203601896763, "Left": 0.11006525903940201, "Top": 0.3383297622203827}, "Polygon": [{"X": 0.11006525903940201, "Y": 0.3383297920227051}, {"X": 0.14742369949817657, "Y": 0.3383297622203827}, {"X": 0.14742372930049896, "Y": 0.3493519425392151}, {"X": 0.1100652888417244, "Y": 0.3493519723415375}]}, "Id": "8c18a50f-ff3f-4f91-a9ae-1687091281bd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Rated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04731443151831627, "Height": 0.011002207174897194, "Left": 0.1534612625837326, "Top": 0.3382006585597992}, "Polygon": [{"X": 0.1534612625837326, "Y": 0.33820071816444397}, {"X": 0.20077568292617798, "Y": 0.3382006585597992}, {"X": 0.20077569782733917, "Y": 0.34920284152030945}, {"X": 0.153461292386055, "Y": 0.34920287132263184}]}, "Id": "a49aa912-629b-4e15-916e-7892f89e483c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Density:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06358332931995392, "Height": 0.013477646745741367, "Left": 0.20670011639595032, "Top": 0.3383499085903168}, "Polygon": [{"X": 0.20670011639595032, "Y": 0.33834993839263916}, {"X": 0.27028343081474304, "Y": 0.3383499085903168}, {"X": 0.27028343081474304, "Y": 0.3518275022506714}, {"X": 0.2067001461982727, "Y": 0.35182756185531616}]}, "Id": "fe03f915-aa7c-4ffb-9772-955574e2f37e"}, {"BlockType": "WORD", "Confidence": 99.169921875, "Text": "15.748", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05368179827928543, "Height": 0.011004846543073654, "Left": 0.37491366267204285, "Top": 0.33839553594589233}, "Polygon": [{"X": 0.37491366267204285, "Y": 0.3383955657482147}, {"X": 0.4285954535007477, "Y": 0.33839553594589233}, {"X": 0.4285954535007477, "Y": 0.3494003415107727}, {"X": 0.37491366267204285, "Y": 0.3494003713130951}]}, "Id": "9b86e932-655f-4e86-9235-e1508e86a182"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "PCF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03431317210197449, "Height": 0.010867071337997913, "Left": 0.4346364140510559, "Top": 0.33837810158729553}, "Polygon": [{"X": 0.4346364140510559, "Y": 0.3383781313896179}, {"X": 0.468949556350708, "Y": 0.33837810158729553}, {"X": 0.468949556350708, "Y": 0.3492451608181}, {"X": 0.4346364140510559, "Y": 0.3492451608181}]}, "Id": "f469ec07-4a59-41e5-a2ca-0f33d925797f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Trailer", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05493469163775444, "Height": 0.010954484343528748, "Left": 0.10968217998743057, "Top": 0.38593506813049316}, "Polygon": [{"X": 0.10968217998743057, "Y": 0.38593509793281555}, {"X": 0.16461683809757233, "Y": 0.38593506813049316}, {"X": 0.16461686789989471, "Y": 0.3968895375728607}, {"X": 0.10968221724033356, "Y": 0.3968895673751831}]}, "Id": "e146c76f-64f1-4b81-b6bf-ee2baa5f81d3"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Measurements", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12466094642877579, "Height": 0.011061456054449081, "Left": 0.16977132856845856, "Top": 0.3858943283557892}, "Polygon": [{"X": 0.16977132856845856, "Y": 0.38589441776275635}, {"X": 0.29443225264549255, "Y": 0.3858943283557892}, {"X": 0.29443228244781494, "Y": 0.3969557285308838}, {"X": 0.16977135837078094, "Y": 0.39695578813552856}]}, "Id": "ba03420f-ee76-45fd-8ea6-7246413e1b3d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Done", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04429704695940018, "Height": 0.011081278324127197, "Left": 0.30051279067993164, "Top": 0.3859160840511322}, "Polygon": [{"X": 0.30051279067993164, "Y": 0.3859161138534546}, {"X": 0.3448098301887512, "Y": 0.3859160840511322}, {"X": 0.3448098301887512, "Y": 0.396997332572937}, {"X": 0.30051279067993164, "Y": 0.3969973623752594}]}, "Id": "4f6078bf-1502-42dd-9b7f-32ca5ee303e6"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "By", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02273612469434738, "Height": 0.013727016746997833, "Left": 0.35059094429016113, "Top": 0.38583382964134216}, "Polygon": [{"X": 0.35059094429016113, "Y": 0.38583385944366455}, {"X": 0.3733270466327667, "Y": 0.38583382964134216}, {"X": 0.3733270466327667, "Y": 0.3995608389377594}, {"X": 0.35059094429016113, "Y": 0.3995608687400818}]}, "Id": "5f85bf52-a1cd-499a-8194-49102e13aaa3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04351583123207092, "Height": 0.010826352052390575, "Left": 0.378557950258255, "Top": 0.3859325647354126}, "Polygon": [{"X": 0.378557950258255, "Y": 0.385932594537735}, {"X": 0.42207375168800354, "Y": 0.3859325647354126}, {"X": 0.42207375168800354, "Y": 0.39675888419151306}, {"X": 0.378557950258255, "Y": 0.39675891399383545}]}, "Id": "7fe135f6-cddd-4ec1-950d-46b2431b33b4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Name", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04676304757595062, "Height": 0.010930419899523258, "Left": 0.21485596895217896, "Top": 0.40411847829818726}, "Polygon": [{"X": 0.21485596895217896, "Y": 0.40411850810050964}, {"X": 0.2616190016269684, "Y": 0.40411847829818726}, {"X": 0.2616190016269684, "Y": 0.41504886746406555}, {"X": 0.21485598385334015, "Y": 0.41504889726638794}]}, "Id": "12ea2679-0fe0-47bf-97a2-277f9c4909a8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Station", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05527513474225998, "Height": 0.010969388298690319, "Left": 0.47340425848960876, "Top": 0.4040168523788452}, "Polygon": [{"X": 0.47340428829193115, "Y": 0.4040168821811676}, {"X": 0.528679370880127, "Y": 0.4040168523788452}, {"X": 0.528679370880127, "Y": 0.4149862229824066}, {"X": 0.47340425848960876, "Y": 0.414986252784729}]}, "Id": "1c817c1c-c8b7-40f4-a658-8126d3c9410b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036862775683403015, "Height": 0.010849637910723686, "Left": 0.7453412413597107, "Top": 0.4041246771812439}, "Polygon": [{"X": 0.7453413009643555, "Y": 0.4041246771812439}, {"X": 0.7822040319442749, "Y": 0.4041246771812439}, {"X": 0.7822039723396301, "Y": 0.41497430205345154}, {"X": 0.7453412413597107, "Y": 0.41497430205345154}]}, "Id": "54aa188f-3a09-430a-989d-50767e58691e"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05721929296851158, "Height": 0.010888570919632912, "Left": 0.17404647171497345, "Top": 0.42260444164276123}, "Polygon": [{"X": 0.17404647171497345, "Y": 0.4226044714450836}, {"X": 0.23126573860645294, "Y": 0.42260444164276123}, {"X": 0.23126575350761414, "Y": 0.4334929883480072}, {"X": 0.17404650151729584, "Y": 0.4334930181503296}]}, "Id": "d957677b-b67d-4f05-b514-f461bd916ac4"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06599061191082001, "Height": 0.01345018856227398, "Left": 0.23633751273155212, "Top": 0.42266547679901123}, "Polygon": [{"X": 0.23633751273155212, "Y": 0.4226655066013336}, {"X": 0.30232810974121094, "Y": 0.42266547679901123}, {"X": 0.30232810974121094, "Y": 0.4361156225204468}, {"X": 0.23633752763271332, "Y": 0.43611565232276917}]}, "Id": "fc7aaa89-d2d9-4706-b84a-1e2f5485e3b2"}, {"BlockType": "WORD", "Confidence": 99.53702545166016, "Text": "352", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0326036661863327, "Height": 0.010998429730534554, "Left": 0.4350687265396118, "Top": 0.4225357472896576}, "Polygon": [{"X": 0.4350687265396118, "Y": 0.42253577709198}, {"X": 0.4676723778247833, "Y": 0.4225357472896576}, {"X": 0.4676723778247833, "Y": 0.433534175157547}, {"X": 0.4350687265396118, "Y": 0.433534175157547}]}, "Id": "987f2607-caab-445e-873a-f2cbb1b33687"}, {"BlockType": "WORD", "Confidence": 89.35746002197266, "Text": "-", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005877962335944176, "Height": 0.0023722618352621794, "Left": 0.46965745091438293, "Top": 0.4283265471458435}, "Polygon": [{"X": 0.46965745091438293, "Y": 0.4283265471458435}, {"X": 0.47553542256355286, "Y": 0.4283265471458435}, {"X": 0.47553539276123047, "Y": 0.43069881200790405}, {"X": 0.46965745091438293, "Y": 0.43069881200790405}]}, "Id": "46628c80-3bcc-40f7-b7ec-defbbb1575f6"}, {"BlockType": "WORD", "Confidence": 99.87224578857422, "Text": "Avenel,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05921238660812378, "Height": 0.012877210043370724, "Left": 0.4804132282733917, "Top": 0.42263272404670715}, "Polygon": [{"X": 0.4804132580757141, "Y": 0.42263275384902954}, {"X": 0.5396256446838379, "Y": 0.42263272404670715}, {"X": 0.5396255850791931, "Y": 0.43550989031791687}, {"X": 0.4804132282733917, "Y": 0.43550992012023926}]}, "Id": "3d3cbadc-4d85-4086-a127-1a2dad8f01df"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "NJ", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021399417892098427, "Height": 0.011089809238910675, "Left": 0.5457770824432373, "Top": 0.422454297542572}, "Polygon": [{"X": 0.5457770824432373, "Y": 0.422454297542572}, {"X": 0.5671764612197876, "Y": 0.422454297542572}, {"X": 0.5671764612197876, "Y": 0.4335440993309021}, {"X": 0.5457770824432373, "Y": 0.4335440993309021}]}, "Id": "dfa2025b-c188-44a3-8f95-23468f153ae1"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "07/16/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08965527266263962, "Height": 0.01118924468755722, "Left": 0.6825687885284424, "Top": 0.4224686622619629}, "Polygon": [{"X": 0.6825687885284424, "Y": 0.4224686920642853}, {"X": 0.7722240686416626, "Y": 0.4224686622619629}, {"X": 0.7722240090370178, "Y": 0.43365785479545593}, {"X": 0.6825687885284424, "Y": 0.4336578845977783}]}, "Id": "b34bd528-c499-4350-b6ea-154eef069c42"}, {"BlockType": "WORD", "Confidence": 99.5703125, "Text": "2:10", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03440781310200691, "Height": 0.011252599768340588, "Left": 0.7777166962623596, "Top": 0.42250335216522217}, "Polygon": [{"X": 0.7777166962623596, "Y": 0.42250338196754456}, {"X": 0.812124490737915, "Y": 0.42250335216522217}, {"X": 0.8121244311332703, "Y": 0.43375593423843384}, {"X": 0.7777166962623596, "Y": 0.4337559640407562}]}, "Id": "1b1ecb52-6503-449c-af59-07ca3ce9579e"}, {"BlockType": "WORD", "Confidence": 99.638671875, "Text": "PM", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026347104460000992, "Height": 0.010879220440983772, "Left": 0.8182988166809082, "Top": 0.42276719212532043}, "Polygon": [{"X": 0.818298876285553, "Y": 0.42276719212532043}, {"X": 0.8446459174156189, "Y": 0.42276719212532043}, {"X": 0.8446458578109741, "Y": 0.43364638090133667}, {"X": 0.8182988166809082, "Y": 0.43364641070365906}]}, "Id": "236442cc-e7eb-442b-bc0f-3600ddf72c50"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Dimensions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07157628983259201, "Height": 0.00893399864435196, "Left": 0.08117885142564774, "Top": 0.46921271085739136}, "Polygon": [{"X": 0.08117885142564774, "Y": 0.46921274065971375}, {"X": 0.15275512635707855, "Y": 0.46921271085739136}, {"X": 0.15275514125823975, "Y": 0.4781467020511627}, {"X": 0.08117888867855072, "Y": 0.4781467318534851}]}, "Id": "ea94ffbb-794d-4b70-858e-1f109c089fbd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "outlined", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04930208623409271, "Height": 0.008868868462741375, "Left": 0.1567339450120926, "Top": 0.4691767692565918}, "Polygon": [{"X": 0.1567339450120926, "Y": 0.4691767692565918}, {"X": 0.2060360163450241, "Y": 0.4691767692565918}, {"X": 0.2060360312461853, "Y": 0.47804561257362366}, {"X": 0.15673397481441498, "Y": 0.47804564237594604}]}, "Id": "ee38b64d-e81f-4488-9a9b-9429242c9ca2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "above", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03599059209227562, "Height": 0.008840699680149555, "Left": 0.20941726863384247, "Top": 0.4692149758338928}, "Polygon": [{"X": 0.20941726863384247, "Y": 0.4692149758338928}, {"X": 0.245407834649086, "Y": 0.4692149758338928}, {"X": 0.2454078495502472, "Y": 0.4780556559562683}, {"X": 0.20941728353500366, "Y": 0.4780556857585907}]}, "Id": "9b471313-0919-4069-b8b0-737325525ab9"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "are", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018370358273386955, "Height": 0.0064223636873066425, "Left": 0.24891775846481323, "Top": 0.47159072756767273}, "Polygon": [{"X": 0.24891775846481323, "Y": 0.47159072756767273}, {"X": 0.26728811860084534, "Y": 0.47159072756767273}, {"X": 0.26728811860084534, "Y": 0.4780130684375763}, {"X": 0.24891777336597443, "Y": 0.4780130982398987}]}, "Id": "e7f16794-2602-4c11-a01b-f23a432c44f5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "actual", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035669948905706406, "Height": 0.009075846523046494, "Left": 0.27097612619400024, "Top": 0.46904173493385315}, "Polygon": [{"X": 0.27097612619400024, "Y": 0.46904173493385315}, {"X": 0.30664607882499695, "Y": 0.46904173493385315}, {"X": 0.30664607882499695, "Y": 0.47811755537986755}, {"X": 0.27097615599632263, "Y": 0.47811755537986755}]}, "Id": "6a629475-a420-455c-b22c-c408e0e5282a"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "dimensions", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06803827732801437, "Height": 0.009089900180697441, "Left": 0.31057408452033997, "Top": 0.46901392936706543}, "Polygon": [{"X": 0.31057408452033997, "Y": 0.4690139591693878}, {"X": 0.37861236929893494, "Y": 0.46901392936706543}, {"X": 0.37861236929893494, "Y": 0.4781038165092468}, {"X": 0.31057408452033997, "Y": 0.4781038463115692}]}, "Id": "51a629bb-b747-4b6a-ab81-b4b556769e31"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02175445482134819, "Height": 0.008926015347242355, "Left": 0.3823860287666321, "Top": 0.4691341817378998}, "Polygon": [{"X": 0.3823860287666321, "Y": 0.4691341817378998}, {"X": 0.4041404724121094, "Y": 0.4691341817378998}, {"X": 0.4041404724121094, "Y": 0.47806018590927124}, {"X": 0.3823860287666321, "Y": 0.47806018590927124}]}, "Id": "4f1c28a2-e1a8-4991-897f-24cbb3dfa58d"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "may", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02625599503517151, "Height": 0.008708211593329906, "Left": 0.4076123833656311, "Top": 0.4717337191104889}, "Polygon": [{"X": 0.4076123833656311, "Y": 0.4717337191104889}, {"X": 0.4338683784008026, "Y": 0.4717337191104889}, {"X": 0.4338683784008026, "Y": 0.4804419279098511}, {"X": 0.4076123833656311, "Y": 0.4804419279098511}]}, "Id": "4533caed-86c8-4686-96ff-a93a89706ccb"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "be", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014591105282306671, "Height": 0.008896342478692532, "Left": 0.43713271617889404, "Top": 0.4691045582294464}, "Polygon": [{"X": 0.43713271617889404, "Y": 0.4691045582294464}, {"X": 0.4517238140106201, "Y": 0.4691045582294464}, {"X": 0.4517238140106201, "Y": 0.4780009090900421}, {"X": 0.43713271617889404, "Y": 0.4780009090900421}]}, "Id": "ad92db93-1e0e-4776-89a9-10967b288224"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "subject", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04300111532211304, "Height": 0.011463496834039688, "Left": 0.4551527500152588, "Top": 0.4690726101398468}, "Polygon": [{"X": 0.4551527500152588, "Y": 0.4690726101398468}, {"X": 0.4981538653373718, "Y": 0.4690726101398468}, {"X": 0.49815383553504944, "Y": 0.4805360734462738}, {"X": 0.4551527500152588, "Y": 0.4805361032485962}]}, "Id": "5f40a068-9ebd-4ddf-9dab-bb74db749bbc"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "to", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.011559096165001392, "Height": 0.007351268548518419, "Left": 0.5015245079994202, "Top": 0.47057628631591797}, "Polygon": [{"X": 0.5015245676040649, "Y": 0.47057628631591797}, {"X": 0.5130836367607117, "Y": 0.47057628631591797}, {"X": 0.5130836367607117, "Y": 0.4779275357723236}, {"X": 0.5015245079994202, "Y": 0.4779275357723236}]}, "Id": "75b49e64-e94b-42c9-81cb-f9fac1780fb7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027772855013608932, "Height": 0.008696167729794979, "Left": 0.517153799533844, "Top": 0.46937596797943115}, "Polygon": [{"X": 0.517153799533844, "Y": 0.46937596797943115}, {"X": 0.544926643371582, "Y": 0.46937596797943115}, {"X": 0.544926643371582, "Y": 0.4780721068382263}, {"X": 0.517153799533844, "Y": 0.4780721068382263}]}, "Id": "df86e8ed-3eeb-494d-87e9-02d44746b8e9"}, {"BlockType": "WORD", "Confidence": 99.8046875, "Text": "110", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021057212725281715, "Height": 0.008832442574203014, "Left": 0.5495284795761108, "Top": 0.4692605435848236}, "Polygon": [{"X": 0.5495284795761108, "Y": 0.4692605435848236}, {"X": 0.5705856680870056, "Y": 0.4692605435848236}, {"X": 0.5705856680870056, "Y": 0.4780929982662201}, {"X": 0.5495284795761108, "Y": 0.4780929982662201}]}, "Id": "3c1715e9-8248-47cc-bd43-cb5e6edb0b05"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "paragraph", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06059238314628601, "Height": 0.011177766136825085, "Left": 0.5738162994384766, "Top": 0.4694291353225708}, "Polygon": [{"X": 0.5738162994384766, "Y": 0.4694291353225708}, {"X": 0.6344086527824402, "Y": 0.4694291353225708}, {"X": 0.6344086527824402, "Y": 0.48060688376426697}, {"X": 0.5738162994384766, "Y": 0.48060688376426697}]}, "Id": "0b9a4c0e-1e2b-4d50-8df9-a791d40659e1"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "14", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014015301130712032, "Height": 0.008785919286310673, "Left": 0.6388260722160339, "Top": 0.4692036509513855}, "Polygon": [{"X": 0.6388260722160339, "Y": 0.4692036807537079}, {"X": 0.6528413891792297, "Y": 0.4692036509513855}, {"X": 0.652841329574585, "Y": 0.4779895842075348}, {"X": 0.6388260722160339, "Y": 0.4779895842075348}]}, "Id": "bcb99f4d-2ddd-45ba-83b6-e4bba7bfa930"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "for", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017698094248771667, "Height": 0.008883691392838955, "Left": 0.6567361950874329, "Top": 0.46923065185546875}, "Polygon": [{"X": 0.6567361950874329, "Y": 0.46923065185546875}, {"X": 0.6744343042373657, "Y": 0.46923065185546875}, {"X": 0.674434244632721, "Y": 0.4781143367290497}, {"X": 0.6567361950874329, "Y": 0.4781143367290497}]}, "Id": "fe35f1f1-9ba7-4b8b-b8ff-94a753e7aa1e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "calculating", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06592697650194168, "Height": 0.011492665857076645, "Left": 0.08120424300432205, "Top": 0.48342055082321167}, "Polygon": [{"X": 0.08120424300432205, "Y": 0.48342058062553406}, {"X": 0.14713118970394135, "Y": 0.48342055082321167}, {"X": 0.14713121950626373, "Y": 0.4949132204055786}, {"X": 0.08120428025722504, "Y": 0.4949132204055786}]}, "Id": "296c8eea-d49e-4cb3-9cc4-1c8c7e807b33"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "effective", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052391793578863144, "Height": 0.009175362065434456, "Left": 0.15061166882514954, "Top": 0.4832383096218109}, "Polygon": [{"X": 0.15061166882514954, "Y": 0.4832383394241333}, {"X": 0.2030034363269806, "Y": 0.4832383096218109}, {"X": 0.20300345122814178, "Y": 0.4924136698246002}, {"X": 0.15061168372631073, "Y": 0.4924136698246002}]}, "Id": "7c43ed39-874e-4990-90ef-5640ec522b3c"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "occupancy.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06605710089206696, "Height": 0.00875705387443304, "Left": 0.2067718803882599, "Top": 0.4859817624092102}, "Polygon": [{"X": 0.2067718803882599, "Y": 0.4859817922115326}, {"X": 0.27282896637916565, "Y": 0.4859817624092102}, {"X": 0.27282899618148804, "Y": 0.4947388172149658}, {"X": 0.20677189528942108, "Y": 0.4947388172149658}]}, "Id": "08141b78-5245-4d3e-9b87-c096ab49fddc"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "9230b78e-31fc-464b-82d7-4f8092dcb437", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "9230b78e-31fc-464b-82d7-4f8092dcb437", "content-type": "application/x-amz-json-1.1", "content-length": "60415", "date": "Wed, 24 Sep 2025 08:35:58 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "ca6fb62d-2338-4462-845b-26a1a9ee382a", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 08:36:01 GMT", "content-type": "application/json", "content-length": "1379", "connection": "keep-alive", "x-amzn-requestid": "ca6fb62d-2338-4462-845b-26a1a9ee382a"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. The content shows \"ABF DIMENSION/CUBE Freight CERTIFICATE\" and \"Freight Certificate\" with pronumber, trailer no, shipper, total weight, dimensions. This looks like a freight certificate, possibly a weight and inspection certificate? The definition: weight_and_inspection_cert requires weight and inspection certificate. This looks like a freight certificate that includes weight and dimensions. The keywords: \"Total Weight\" indicates weight. Also \"Total Rated Dimensions\". The header: \"Freight CERTIFICATE\". That matches weight and inspection certificate? The description of weight_and_inspection_cert: \"Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\". This matches. Also the specification says if it includes weight and inspection, classify as weight_and_inspection_cert. So doc_type should be weight_and_inspection_cert.\n\nWe return an array of documents: only one page, page_no: 1, doc_type: \"weight_and_inspection_cert\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"weight_and_inspection_cert\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2610, "outputTokens": 227, "totalTokens": 2837}, "metrics": {"latencyMs": 1182}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/df6d32b7_W_XCJLXZK140FUS8020ZAG.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\n®\nABF\nDIMENSION/CUBE\nFreight\nCERTIFICATE\nAn ArcBest Company\nPronumber:\n352309638\nTrailer No: CITY 410498\nShipper:\nHeadstone Deals\nTotals\nTotal Weight:\n280 lbs\nTotal Rated Dimensions:\n48 L X 40 W X 16 H\nTotal Mileage:\n1,900 Miles\nTotal Rated Cube:\n17.78 Cubic Feet\nTotal Rated Density:\n15.748 PCF\nTrailer Measurements Done By Dock\nName\nStation\nDate\nLouinel Auguste\n352 - Avenel, NJ\n07/16/2025 2:10 PM\nDimensions outlined above are actual dimensions and may be subject to Item 110 paragraph 14 for\ncalculating effective occupancy.\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}