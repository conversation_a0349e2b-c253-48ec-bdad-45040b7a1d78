{"classification_result": {"documents": [{"page_no": 1, "doc_type": "weight_and_inspection_cert"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 6.21251743382345e-08, "Y": 1.3097367457248765e-07}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "b3e0c767-1273-4c91-a4c7-98aa41ef227e", "Relationships": [{"Type": "CHILD", "Ids": ["cf784544-a639-4f90-890f-06f1fd568bd0", "06b24a0e-e540-4ea8-af71-d5d004d916f2", "fbb875d5-cb8d-4293-ab57-e391726ad49b", "c3ef6655-858f-4b10-a798-1dce757ef951", "09d03f93-fc40-4f79-87bb-0f93499236fd", "1123a786-6609-4f88-ab1c-d629b6d76851", "f0f91cbb-923d-48d6-b623-58c1bc04cc70", "5d28d5f1-a23a-4e12-b815-b1f2407b7edc", "6c7553c2-04b5-460f-afc8-4ef60d3e02a2", "792e6364-ab06-4b44-be2c-e1346834c864", "e94215be-9e20-4518-88ef-5b3f717ad4e4", "4fa0ad54-db46-4e88-809b-962797891565", "b0aa2da3-5866-4139-b0e7-bfbf2d2f8529", "cde6f9c6-8b16-48b9-a8de-97dfa3fb1d9a", "c460a759-ce90-4967-8549-f31cf63d1218", "23ca7610-a214-41d7-9bd3-d7a490039426", "a2d34ff2-d1c8-47ae-b532-9ae2d8152bfd", "ce703bd6-5f3e-4c5e-9cbe-0fa245611c19", "b19c937c-365d-42d4-ba2c-4d859c289862", "d000393c-9f11-4baf-bdb3-26f7f9beb048", "1784f458-c732-4960-b484-83ab79501b3d", "d86b3c37-daa2-4bc0-806c-02241dab6b7d", "2e60c5b4-013b-404d-ac19-e704f80d3680", "3e2c4b05-93c1-413f-8c26-1fc672eaa794", "0cdf80fb-4565-4b7b-aab4-07d2056b0c44", "d7f08236-1094-4380-b00a-b48dfb095174", "a5827ff7-6eaf-4b44-86f8-8e9c67a8c410", "30607885-ccd0-45f0-acf9-25056da78f64", "669d5753-b20e-4541-82bc-c74e34892f2d", "3011ed25-c9d6-4559-a1e6-5ef735340673", "8cd99e9e-339c-4e41-8b74-8a40d98adc5e", "67c6d3d6-383c-4255-b581-76e9031ca690", "966d65e5-b987-472a-9872-14e8b0168cab", "2d3b219f-ad17-4eea-961c-1e91bea7716c", "818089c6-52fa-4aed-ac25-620aa6c74e73", "03acae5b-0f61-4941-a477-409692e7583b", "13eaee46-d3fc-4475-821d-1978d20805f8", "992b39b1-1cfd-4732-b8fa-8ea6cff2bb75", "164ae1ee-dddd-4400-91b5-63f02f7fdfdd", "8b423edd-a436-4885-b3ce-1227779ed95c", "0c72efe4-b5a8-4c36-9fb1-1090902ec4e3", "2e03e472-1249-4e2e-8446-e0ec65840839", "fd4bdcec-656b-4cf9-a0ec-67f3b27f0e86", "18b84fba-bcf1-4220-a736-66c5249f8ad7", "54922a42-a777-4895-810f-6acce035c689", "38ffb2ea-c70d-4a41-897a-2a0fc5c6ddee", "a791dfac-e26e-4685-b5e3-b4d160e8c6c2", "8638bd37-69e0-44b4-b8d2-7222b620e44a", "94825c05-f6ee-49ca-87ac-dfc82a6827d9"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Central Transport", "Geometry": {"BoundingBox": {"Width": 0.1148308515548706, "Height": 0.01157828513532877, "Left": 0.05718902871012688, "Top": 0.05624642223119736}, "Polygon": [{"X": 0.05718908831477165, "Y": 0.05624745413661003}, {"X": 0.17201988399028778, "Y": 0.05624642223119736}, {"X": 0.1720198094844818, "Y": 0.06782364845275879}, {"X": 0.05718902871012688, "Y": 0.06782470643520355}]}, "Id": "cf784544-a639-4f90-890f-06f1fd568bd0", "Relationships": [{"Type": "CHILD", "Ids": ["31dbf09b-9442-4675-8f64-5347bea474ca", "8741cf18-fbec-4d36-bc4d-70f81147717a"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "08/31/2025", "Geometry": {"BoundingBox": {"Width": 0.0731736347079277, "Height": 0.009412871673703194, "Left": 0.8657994270324707, "Top": 0.05629698932170868}, "Polygon": [{"X": 0.865799605846405, "Y": 0.05629764869809151}, {"X": 0.938973069190979, "Y": 0.05629698932170868}, {"X": 0.9389729499816895, "Y": 0.065709188580513}, {"X": 0.8657994270324707, "Y": 0.06570985913276672}]}, "Id": "06b24a0e-e540-4ea8-af71-d5d004d916f2", "Relationships": [{"Type": "CHILD", "Ids": ["253025f4-507f-4e46-b8ef-13342b0db20a"]}]}, {"BlockType": "LINE", "Confidence": 99.89218139648438, "Text": "PRO#: 227407699", "Geometry": {"BoundingBox": {"Width": 0.12172986567020416, "Height": 0.009408773854374886, "Left": 0.057394713163375854, "Top": 0.07145050168037415}, "Polygon": [{"X": 0.057394761592149734, "Y": 0.07145162671804428}, {"X": 0.17912457883358002, "Y": 0.07145050168037415}, {"X": 0.17912451922893524, "Y": 0.08085812628269196}, {"X": 0.057394713163375854, "Y": 0.08085927367210388}]}, "Id": "fbb875d5-cb8d-4293-ab57-e391726ad49b", "Relationships": [{"Type": "CHILD", "Ids": ["c3869898-47d4-4db6-933a-cec6bdd860ff", "ddff8fc7-9e1f-4eb0-9c88-3bf7422c3ee8"]}]}, {"BlockType": "LINE", "Confidence": 99.89476776123047, "Text": "Weight & Inspection", "Geometry": {"BoundingBox": {"Width": 0.13100199401378632, "Height": 0.011712157167494297, "Left": 0.8079518675804138, "Top": 0.07136138528585434}, "Polygon": [{"X": 0.8079520463943481, "Y": 0.07136259227991104}, {"X": 0.9389538764953613, "Y": 0.07136138528585434}, {"X": 0.938953697681427, "Y": 0.08307230472564697}, {"X": 0.8079518675804138, "Y": 0.08307354152202606}]}, "Id": "c3ef6655-858f-4b10-a798-1dce757ef951", "Relationships": [{"Type": "CHILD", "Ids": ["fe95e8f8-2679-48e0-bff2-cbb18ea1999a", "ed399212-7591-42f1-99c5-14f2c373c57a", "7dca30ba-56bd-48eb-952c-14a3854f300c"]}]}, {"BlockType": "LINE", "Confidence": 99.94140625, "Text": "CENTRAL TRANSPORT", "Geometry": {"BoundingBox": {"Width": 0.16294315457344055, "Height": 0.013988837599754333, "Left": 0.4383789896965027, "Top": 0.1918865293264389}, "Polygon": [{"X": 0.43837910890579224, "Y": 0.19188834726810455}, {"X": 0.6013221144676208, "Y": 0.1918865293264389}, {"X": 0.6013219952583313, "Y": 0.205873504281044}, {"X": 0.4383789896965027, "Y": 0.20587536692619324}]}, "Id": "09d03f93-fc40-4f79-87bb-0f93499236fd", "Relationships": [{"Type": "CHILD", "Ids": ["49d3263f-5dee-4a45-aa7f-75975944d772", "9c5a1253-d190-41dc-a123-26b217fba5dc"]}]}, {"BlockType": "LINE", "Confidence": 99.88745880126953, "Text": "12225 Stephens Road", "Geometry": {"BoundingBox": {"Width": 0.12882336974143982, "Height": 0.01064684335142374, "Left": 0.4399399757385254, "Top": 0.2119910717010498}, "Polygon": [{"X": 0.43994009494781494, "Y": 0.211992546916008}, {"X": 0.5687633752822876, "Y": 0.2119910717010498}, {"X": 0.568763256072998, "Y": 0.2226364016532898}, {"X": 0.4399399757385254, "Y": 0.22263790667057037}]}, "Id": "1123a786-6609-4f88-ab1c-d629b6d76851", "Relationships": [{"Type": "CHILD", "Ids": ["53fc8f13-6797-404c-bd7a-8e919e3e572a", "83bdf443-0aad-49fc-9ee5-a1301ac3f37b", "968a9689-ba5a-4bb5-b161-374a815a47ca"]}]}, {"BlockType": "LINE", "Confidence": 99.85464477539062, "Text": "Warren, MI 48089", "Geometry": {"BoundingBox": {"Width": 0.113776296377182, "Height": 0.010304750874638557, "Left": 0.44664397835731506, "Top": 0.22568704187870026}, "Polygon": [{"X": 0.4466440677642822, "Y": 0.2256883829832077}, {"X": 0.5604202747344971, "Y": 0.22568704187870026}, {"X": 0.5604201555252075, "Y": 0.23599044978618622}, {"X": 0.44664397835731506, "Y": 0.23599180579185486}]}, "Id": "f0f91cbb-923d-48d6-b623-58c1bc04cc70", "Relationships": [{"Type": "CHILD", "Ids": ["980d211c-72af-46b3-a920-bc04fc983ed9", "1a04cce5-0b04-4b9c-9fd9-987930978bbb", "b4344dd4-b80c-4232-ade1-789fd0de6a3c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CERTIFIED WEIGHT CERTIFICATE", "Geometry": {"BoundingBox": {"Width": 0.2301705777645111, "Height": 0.009217815473675728, "Left": 0.39221125841140747, "Top": 0.2900317907333374}, "Polygon": [{"X": 0.39221134781837463, "Y": 0.2900347113609314}, {"X": 0.622381865978241, "Y": 0.2900317907333374}, {"X": 0.6223817467689514, "Y": 0.2992466390132904}, {"X": 0.39221125841140747, "Y": 0.2992495894432068}]}, "Id": "5d28d5f1-a23a-4e12-b815-b1f2407b7edc", "Relationships": [{"Type": "CHILD", "Ids": ["f8e54260-b90c-408a-b357-20f2bbf20bb8", "2e8235a4-d547-4c43-af1d-d80ad4fd5bd0", "4be4eb10-0d9a-49db-824c-079f8b5170a3"]}]}, {"BlockType": "LINE", "Confidence": 99.87091827392578, "Text": "Pro Number: 227407699", "Geometry": {"BoundingBox": {"Width": 0.17372579872608185, "Height": 0.009269524365663528, "Left": 0.15433162450790405, "Top": 0.31617191433906555}, "Polygon": [{"X": 0.15433168411254883, "Y": 0.3161742091178894}, {"X": 0.3280574083328247, "Y": 0.31617191433906555}, {"X": 0.32805734872817993, "Y": 0.3254391551017761}, {"X": 0.15433162450790405, "Y": 0.3254414498806}]}, "Id": "6c7553c2-04b5-460f-afc8-4ef60d3e02a2", "Relationships": [{"Type": "CHILD", "Ids": ["941f81c8-cbfb-4344-9da7-ca17774a26c2", "8fc34952-47db-4cbc-96b8-48eea3611fa8", "5c705409-c2ff-44d5-9622-83726e4be7a6"]}]}, {"BlockType": "LINE", "Confidence": 99.96366119384766, "Text": "Bill of Lading", "Geometry": {"BoundingBox": {"Width": 0.08661317825317383, "Height": 0.011761363595724106, "Left": 0.6965249180793762, "Top": 0.3158475160598755}, "Polygon": [{"X": 0.6965250372886658, "Y": 0.3158486485481262}, {"X": 0.78313809633255, "Y": 0.3158475160598755}, {"X": 0.7831379175186157, "Y": 0.3276077210903168}, {"X": 0.6965249180793762, "Y": 0.3276088535785675}]}, "Id": "792e6364-ab06-4b44-be2c-e1346834c864", "Relationships": [{"Type": "CHILD", "Ids": ["47b6fc37-1b93-4ec8-910e-cdc2419f5488", "de329712-c38b-4fd5-8465-9d53b9910684", "a35353c0-8718-4b30-8e5f-4bb0316ca8ea"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Date: 08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.1218271404504776, "Height": 0.01059777196496725, "Left": 0.15446539223194122, "Top": 0.32907816767692566}, "Polygon": [{"X": 0.154465451836586, "Y": 0.3290797770023346}, {"X": 0.2762925326824188, "Y": 0.32907816767692566}, {"X": 0.27629244327545166, "Y": 0.33967429399490356}, {"X": 0.15446539223194122, "Y": 0.3396759331226349}]}, "Id": "e94215be-9e20-4518-88ef-5b3f717ad4e4", "Relationships": [{"Type": "CHILD", "Ids": ["299fff1f-dd5b-4aa5-8bc1-a0c45af9fe76", "a7236a80-57f9-4c93-b256-e9d4c0bf5a75"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "DIAMOND ENVELOPE", "Geometry": {"BoundingBox": {"Width": 0.10091961175203323, "Height": 0.006404740270227194, "Left": 0.16249734163284302, "Top": 0.3682035207748413}, "Polygon": [{"X": 0.1624973714351654, "Y": 0.3682049512863159}, {"X": 0.26341694593429565, "Y": 0.3682035207748413}, {"X": 0.2634168863296509, "Y": 0.3746068477630615}, {"X": 0.16249734163284302, "Y": 0.37460827827453613}]}, "Id": "4fa0ad54-db46-4e88-809b-962797891565", "Relationships": [{"Type": "CHILD", "Ids": ["6a1b538b-990c-4a8b-9410-ab92c00ae888", "76416d46-33c0-444d-8848-ee88a52f27d6"]}]}, {"BlockType": "LINE", "Confidence": 99.74091339111328, "Text": "Weight Certificate", "Geometry": {"BoundingBox": {"Width": 0.08476640284061432, "Height": 0.008311820216476917, "Left": 0.7232305407524109, "Top": 0.3676687777042389}, "Polygon": [{"X": 0.7232306599617004, "Y": 0.3676699697971344}, {"X": 0.807996928691864, "Y": 0.3676687777042389}, {"X": 0.8079968094825745, "Y": 0.37597939372062683}, {"X": 0.7232305407524109, "Y": 0.37598058581352234}]}, "Id": "b0aa2da3-5866-4139-b0e7-bfbf2d2f8529", "Relationships": [{"Type": "CHILD", "Ids": ["7449e7a4-0b68-46b2-8e2e-39ee6e059509", "7cf69c21-7686-4c81-933e-1d9548554068"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "2270 WHITE OAK CIR", "Geometry": {"BoundingBox": {"Width": 0.10405416041612625, "Height": 0.006522432900965214, "Left": 0.16246169805526733, "Top": 0.37667709589004517}, "Polygon": [{"X": 0.16246172785758972, "Y": 0.37667855620384216}, {"X": 0.266515851020813, "Y": 0.37667709589004517}, {"X": 0.2665157914161682, "Y": 0.3831980526447296}, {"X": 0.16246169805526733, "Y": 0.3831995129585266}]}, "Id": "cde6f9c6-8b16-48b9-a8de-97dfa3fb1d9a", "Relationships": [{"Type": "CHILD", "Ids": ["e627b598-d1ba-4a4c-a3d8-ac54e74c2f2e", "1555b32b-6ca1-49b3-9eef-ebdeb19b6d9d", "f6bea819-ac69-4eb9-bc3c-96546e027f87", "4b09747a-a8f5-4bb1-a456-407b224859ec"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "We Certify that this shipment", "Geometry": {"BoundingBox": {"Width": 0.13997326791286469, "Height": 0.008207225240767002, "Left": 0.7073788642883301, "Top": 0.37618619203567505}, "Polygon": [{"X": 0.7073789834976196, "Y": 0.37618815898895264}, {"X": 0.847352147102356, "Y": 0.37618619203567505}, {"X": 0.8473520278930664, "Y": 0.3843914270401001}, {"X": 0.7073788642883301, "Y": 0.3843934237957001}]}, "Id": "c460a759-ce90-4967-8549-f31cf63d1218", "Relationships": [{"Type": "CHILD", "Ids": ["773f322c-fadf-4e56-99d8-88eabdbc9fcd", "4e052189-2325-4ed0-b3c3-308f3296109d", "02a52d4c-4d77-4190-9031-d441cf91a9c3", "f6fcdaa2-8c39-44ee-955f-96190f1ea8c0", "a051dfd5-3d8c-43ff-96c0-e03669578abb"]}]}, {"BlockType": "LINE", "Confidence": 99.71878814697266, "Text": "AURORA, IL 60502", "Geometry": {"BoundingBox": {"Width": 0.09084655344486237, "Height": 0.007452928461134434, "Left": 0.16181287169456482, "Top": 0.3850322663784027}, "Polygon": [{"X": 0.1618129163980484, "Y": 0.3850335478782654}, {"X": 0.2526594400405884, "Y": 0.3850322663784027}, {"X": 0.2526593804359436, "Y": 0.3924838900566101}, {"X": 0.16181287169456482, "Y": 0.39248520135879517}]}, "Id": "23ca7610-a214-41d7-9bd3-d7a490039426", "Relationships": [{"Type": "CHILD", "Ids": ["9c4731d8-0fc7-43a0-8fa8-faefac091c5e", "e459a0b3-ad3c-4f8b-9610-dc51fcdb710d", "fe9997e2-513e-45fa-8e51-d1f23828dc55"]}]}, {"BlockType": "LINE", "Confidence": 99.95428466796875, "Text": "has been reweighed and that", "Geometry": {"BoundingBox": {"Width": 0.1379849761724472, "Height": 0.008062225766479969, "Left": 0.7077588438987732, "Top": 0.3848668932914734}, "Polygon": [{"X": 0.7077589631080627, "Y": 0.384868860244751}, {"X": 0.8457438349723816, "Y": 0.3848668932914734}, {"X": 0.845743715763092, "Y": 0.3929271399974823}, {"X": 0.7077588438987732, "Y": 0.3929291069507599}]}, "Id": "a2d34ff2-d1c8-47ae-b532-9ae2d8152bfd", "Relationships": [{"Type": "CHILD", "Ids": ["45150398-5485-49e4-8cf9-e710d2379851", "e0c206c5-80fb-4837-8607-b0b9b7a67ddd", "e5ee7ab1-7310-427f-9d02-e391d8090884", "3daf2ee5-6e4d-4aa7-b83f-b025a545521e", "5481917c-f6b1-4937-911d-065644b9ac66"]}]}, {"BlockType": "LINE", "Confidence": 99.98030853271484, "Text": "the corrected weight as stated", "Geometry": {"BoundingBox": {"Width": 0.14533431828022003, "Height": 0.008183035999536514, "Left": 0.707314670085907, "Top": 0.39328068494796753}, "Polygon": [{"X": 0.7073147296905518, "Y": 0.39328277111053467}, {"X": 0.8526489734649658, "Y": 0.39328068494796753}, {"X": 0.8526488542556763, "Y": 0.4014616012573242}, {"X": 0.707314670085907, "Y": 0.40146371722221375}]}, "Id": "ce703bd6-5f3e-4c5e-9cbe-0fa245611c19", "Relationships": [{"Type": "CHILD", "Ids": ["d104628f-1f42-45af-a4df-99023b0c1018", "39080325-9256-4da5-b5b0-51f95150c113", "d0257aa7-232f-4c17-8d97-00a18655033d", "d904a262-e09f-41bb-b05e-f8593d21226c", "535572be-9be4-48c9-a540-e9a4bbb83649"]}]}, {"BlockType": "LINE", "Confidence": 99.91458129882812, "Text": "is the true and accurate", "Geometry": {"BoundingBox": {"Width": 0.11372467130422592, "Height": 0.00710035115480423, "Left": 0.7076213955879211, "Top": 0.40173888206481934}, "Polygon": [{"X": 0.7076214551925659, "Y": 0.40174052119255066}, {"X": 0.8213460445404053, "Y": 0.40173888206481934}, {"X": 0.8213459253311157, "Y": 0.40883758664131165}, {"X": 0.7076213955879211, "Y": 0.40883922576904297}]}, "Id": "b19c937c-365d-42d4-ba2c-4d859c289862", "Relationships": [{"Type": "CHILD", "Ids": ["914e4f81-5a38-4d54-84d3-c9e2946407a0", "9f4eedea-9a92-4309-8b15-39e9e2c658d8", "9b2b236c-5428-4e6a-b1f2-a16c13ddae34", "35b828b6-a8cc-492d-b12c-5619b7197832", "28d3fef4-1ddf-4972-869b-8dc847c1c78d"]}]}, {"BlockType": "LINE", "Confidence": 99.90074920654297, "Text": "Declared Weight: 1148", "Geometry": {"BoundingBox": {"Width": 0.10963373631238937, "Height": 0.008119854144752026, "Left": 0.16241343319416046, "Top": 0.41033288836479187}, "Polygon": [{"X": 0.16241347789764404, "Y": 0.4103344976902008}, {"X": 0.27204716205596924, "Y": 0.41033288836479187}, {"X": 0.27204710245132446, "Y": 0.41845113039016724}, {"X": 0.16241343319416046, "Y": 0.41845273971557617}]}, "Id": "d000393c-9f11-4baf-bdb3-26f7f9beb048", "Relationships": [{"Type": "CHILD", "Ids": ["e8582662-adcf-49ac-be66-b5ea05077c4a", "3ca506f7-610b-41b8-9af4-5b4382cfed7f", "52ec0b00-75c2-4fa4-9ee2-65dd22da9780"]}]}, {"BlockType": "LINE", "Confidence": 99.9280014038086, "Text": "weight of the shipment", "Geometry": {"BoundingBox": {"Width": 0.1111871749162674, "Height": 0.008168041706085205, "Left": 0.7069318294525146, "Top": 0.4102623462677002}, "Polygon": [{"X": 0.7069319486618042, "Y": 0.4102639853954315}, {"X": 0.8181189894676208, "Y": 0.4102623462677002}, {"X": 0.8181188702583313, "Y": 0.4184287488460541}, {"X": 0.7069318294525146, "Y": 0.4184303879737854}]}, "Id": "1784f458-c732-4960-b484-83ab79501b3d", "Relationships": [{"Type": "CHILD", "Ids": ["c69ff903-4ee1-4d6f-bb7d-75bf6e26325d", "d49e73eb-ccfe-4791-b264-0d62e52b7d39", "5dc2a84c-1e6f-4c56-88b6-27f28b252198", "d42f3e98-fc53-4cb0-bcd4-3a40d9e5461e"]}]}, {"BlockType": "LINE", "Confidence": 99.86766815185547, "Text": "Corrected Weight: 1425", "Geometry": {"BoundingBox": {"Width": 0.11429888010025024, "Height": 0.008122419007122517, "Left": 0.16247409582138062, "Top": 0.41887912154197693}, "Polygon": [{"X": 0.1624741405248642, "Y": 0.41888079047203064}, {"X": 0.27677297592163086, "Y": 0.41887912154197693}, {"X": 0.2767729163169861, "Y": 0.4269998371601105}, {"X": 0.16247409582138062, "Y": 0.4270015358924866}]}, "Id": "d86b3c37-daa2-4bc0-806c-02241dab6b7d", "Relationships": [{"Type": "CHILD", "Ids": ["cce99f27-708b-4b8b-8aec-d278234dd0d5", "65473b38-a6b2-4267-88f3-3ff90edbbbad", "f9899cbd-8921-4316-80f1-4d65801a2a66"]}]}, {"BlockType": "LINE", "Confidence": 99.7120132446289, "Text": "HU", "Geometry": {"BoundingBox": {"Width": 0.017055867239832878, "Height": 0.006777491886168718, "Left": 0.16441786289215088, "Top": 0.4541013836860657}, "Polygon": [{"X": 0.16441790759563446, "Y": 0.45410165190696716}, {"X": 0.1814737319946289, "Y": 0.4541013836860657}, {"X": 0.18147367238998413, "Y": 0.4608786106109619}, {"X": 0.16441786289215088, "Y": 0.4608788788318634}]}, "Id": "2e60c5b4-013b-404d-ac19-e704f80d3680", "Relationships": [{"Type": "CHILD", "Ids": ["8d4d3c9c-cdaf-4c5e-8811-b84b4377829b"]}]}, {"BlockType": "LINE", "Confidence": 99.89058685302734, "Text": "<PERSON>an Date", "Geometry": {"BoundingBox": {"Width": 0.0600147508084774, "Height": 0.0071591404266655445, "Left": 0.19829589128494263, "Top": 0.45388081669807434}, "Polygon": [{"X": 0.1982959508895874, "Y": 0.45388174057006836}, {"X": 0.2583106458187103, "Y": 0.45388081669807434}, {"X": 0.25831058621406555, "Y": 0.4610390365123749}, {"X": 0.19829589128494263, "Y": 0.4610399603843689}]}, "Id": "3e2c4b05-93c1-413f-8c26-1fc672eaa794", "Relationships": [{"Type": "CHILD", "Ids": ["55f47607-2c08-40dc-b20a-ae3a28b64a6c", "e7a1d074-7f71-471c-a9c6-d4e587c6650e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.02890188619494438, "Height": 0.007359205279499292, "Left": 0.2874515652656555, "Top": 0.4534839987754822}, "Polygon": [{"X": 0.2874516248703003, "Y": 0.453484445810318}, {"X": 0.316353440284729, "Y": 0.4534839987754822}, {"X": 0.31635338068008423, "Y": 0.4608427584171295}, {"X": 0.2874515652656555, "Y": 0.46084320545196533}]}, "Id": "0cdf80fb-4565-4b7b-aab4-07d2056b0c44", "Relationships": [{"Type": "CHILD", "Ids": ["9113960b-69fe-4abf-8da8-c558e397ca35"]}]}, {"BlockType": "LINE", "Confidence": 99.78605651855469, "Text": "Weight Terminal", "Geometry": {"BoundingBox": {"Width": 0.09505319595336914, "Height": 0.009380130097270012, "Left": 0.3337138891220093, "Top": 0.45351311564445496}, "Polygon": [{"X": 0.33371397852897644, "Y": 0.45351457595825195}, {"X": 0.4287670850753784, "Y": 0.45351311564445496}, {"X": 0.42876699566841125, "Y": 0.4628917872905731}, {"X": 0.3337138891220093, "Y": 0.4628932476043701}]}, "Id": "d7f08236-1094-4380-b00a-b48dfb095174", "Relationships": [{"Type": "CHILD", "Ids": ["b64b6674-4ab6-454d-8393-7db13999ee74", "f8d6c41a-7803-4417-b269-751ad822b94f"]}]}, {"BlockType": "LINE", "Confidence": 99.86088562011719, "Text": "Pro Number", "Geometry": {"BoundingBox": {"Width": 0.0677749365568161, "Height": 0.007399872411042452, "Left": 0.46933484077453613, "Top": 0.4536535441875458}, "Polygon": [{"X": 0.4693349301815033, "Y": 0.45365455746650696}, {"X": 0.5371097922325134, "Y": 0.4536535441875458}, {"X": 0.5371096730232239, "Y": 0.4610523581504822}, {"X": 0.46933484077453613, "Y": 0.46105340123176575}]}, "Id": "a5827ff7-6eaf-4b44-86f8-8e9c67a8c410", "Relationships": [{"Type": "CHILD", "Ids": ["30965192-dae6-4c3a-b528-d8119cf56155", "65e6aec6-05ff-414f-9b95-255049b9d621"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "33136199", "Geometry": {"BoundingBox": {"Width": 0.058709219098091125, "Height": 0.007329114247113466, "Left": 0.1687587946653366, "Top": 0.4650166928768158}, "Polygon": [{"X": 0.1687588393688202, "Y": 0.4650176167488098}, {"X": 0.22746801376342773, "Y": 0.4650166928768158}, {"X": 0.22746795415878296, "Y": 0.47234490513801575}, {"X": 0.1687587946653366, "Y": 0.47234582901000977}]}, "Id": "30607885-ccd0-45f0-acf9-25056da78f64", "Relationships": [{"Type": "CHILD", "Ids": ["5c77223c-dce8-49c8-a3f0-d407e87dc5e9"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.0687561109662056, "Height": 0.008462921716272831, "Left": 0.24354542791843414, "Top": 0.46471866965293884}, "Polygon": [{"X": 0.24354548752307892, "Y": 0.4647197127342224}, {"X": 0.31230154633522034, "Y": 0.46471866965293884}, {"X": 0.3123014569282532, "Y": 0.47318050265312195}, {"X": 0.24354542791843414, "Y": 0.4731815755367279}]}, "Id": "669d5753-b20e-4541-82bc-c74e34892f2d", "Relationships": [{"Type": "CHILD", "Ids": ["8815062f-3fc2-4bd3-aaaf-d6a175ff608c"]}]}, {"BlockType": "LINE", "Confidence": 99.89178466796875, "Text": "00:02 880", "Geometry": {"BoundingBox": {"Width": 0.06711716204881668, "Height": 0.007216433063149452, "Left": 0.3340480327606201, "Top": 0.4650701880455017}, "Polygon": [{"X": 0.3340480923652649, "Y": 0.4650712311267853}, {"X": 0.4011651873588562, "Y": 0.4650701880455017}, {"X": 0.4011651277542114, "Y": 0.47228556871414185}, {"X": 0.3340480327606201, "Y": 0.4722866117954254}]}, "Id": "3011ed25-c9d6-4559-a1e6-5ef735340673", "Relationships": [{"Type": "CHILD", "Ids": ["5326459f-fe7e-41d4-8358-5ffa055b6192", "1b244ae0-a4a0-4b2e-806d-4badce7fbe38"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "463", "Geometry": {"BoundingBox": {"Width": 0.021867085248231888, "Height": 0.00682410504668951, "Left": 0.43186819553375244, "Top": 0.46527284383773804}, "Polygon": [{"X": 0.4318682551383972, "Y": 0.4652732014656067}, {"X": 0.45373526215553284, "Y": 0.46527284383773804}, {"X": 0.45373520255088806, "Y": 0.47209662199020386}, {"X": 0.43186819553375244, "Y": 0.4720969498157501}]}, "Id": "8cd99e9e-339c-4e41-8b74-8a40d98adc5e", "Relationships": [{"Type": "CHILD", "Ids": ["60de7d43-1b08-4d08-a17d-d265dac2b6ee"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "227407699", "Geometry": {"BoundingBox": {"Width": 0.060560133308172226, "Height": 0.007165390066802502, "Left": 0.5148974657058716, "Top": 0.4650382697582245}, "Polygon": [{"X": 0.5148975253105164, "Y": 0.4650391936302185}, {"X": 0.5754575729370117, "Y": 0.4650382697582245}, {"X": 0.5754575133323669, "Y": 0.47220271825790405}, {"X": 0.5148974657058716, "Y": 0.47220367193222046}]}, "Id": "67c6d3d6-383c-4255-b581-76e9031ca690", "Relationships": [{"Type": "CHILD", "Ids": ["461a0331-9e70-45c3-924f-077f5500d143"]}]}, {"BlockType": "LINE", "Confidence": 99.873046875, "Text": "33136200", "Geometry": {"BoundingBox": {"Width": 0.058386050164699554, "Height": 0.006965055596083403, "Left": 0.16896452009677887, "Top": 0.47567445039749146}, "Polygon": [{"X": 0.16896456480026245, "Y": 0.4756753742694855}, {"X": 0.22735057771205902, "Y": 0.47567445039749146}, {"X": 0.22735051810741425, "Y": 0.4826385974884033}, {"X": 0.16896452009677887, "Y": 0.48263952136039734}]}, "Id": "966d65e5-b987-472a-9872-14e8b0168cab", "Relationships": [{"Type": "CHILD", "Ids": ["08c2ca8d-a745-4b83-aa4f-d286ff4c1596"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.06862763315439224, "Height": 0.008570958860218525, "Left": 0.24351204931735992, "Top": 0.47528213262557983}, "Polygon": [{"X": 0.2435121089220047, "Y": 0.4752832055091858}, {"X": 0.31213968992233276, "Y": 0.47528213262557983}, {"X": 0.3121396005153656, "Y": 0.48385199904441833}, {"X": 0.24351204931735992, "Y": 0.4838530719280243}]}, "Id": "2d3b219f-ad17-4eea-961c-1e91bea7716c", "Relationships": [{"Type": "CHILD", "Ids": ["2287e76c-f613-4910-b723-c703e2b9d299"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "00:03", "Geometry": {"BoundingBox": {"Width": 0.033893387764692307, "Height": 0.0071488418616354465, "Left": 0.3341608941555023, "Top": 0.4757031202316284}, "Polygon": [{"X": 0.3341609537601471, "Y": 0.475703626871109}, {"X": 0.3680543005466461, "Y": 0.4757031202316284}, {"X": 0.36805421113967896, "Y": 0.48285141587257385}, {"X": 0.3341608941555023, "Y": 0.48285195231437683}]}, "Id": "818089c6-52fa-4aed-ac25-620aa6c74e73", "Relationships": [{"Type": "CHILD", "Ids": ["46a3e37d-8997-40e7-b8c8-fe99787f006b"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "545", "Geometry": {"BoundingBox": {"Width": 0.02147347666323185, "Height": 0.006957860663533211, "Left": 0.3794381320476532, "Top": 0.4757300317287445}, "Polygon": [{"X": 0.379438191652298, "Y": 0.47573035955429077}, {"X": 0.4009115993976593, "Y": 0.4757300317287445}, {"X": 0.4009115397930145, "Y": 0.4826875627040863}, {"X": 0.3794381320476532, "Y": 0.48268789052963257}]}, "Id": "03acae5b-0f61-4941-a477-409692e7583b", "Relationships": [{"Type": "CHILD", "Ids": ["34f0bd91-e79a-4a6b-8055-1fe539a7f27e"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "463", "Geometry": {"BoundingBox": {"Width": 0.021909311413764954, "Height": 0.006979272235184908, "Left": 0.43184787034988403, "Top": 0.4756060838699341}, "Polygon": [{"X": 0.4318479299545288, "Y": 0.47560641169548035}, {"X": 0.4537571966648102, "Y": 0.4756060838699341}, {"X": 0.453757107257843, "Y": 0.48258501291275024}, {"X": 0.43184787034988403, "Y": 0.4825853407382965}]}, "Id": "13eaee46-d3fc-4475-821d-1978d20805f8", "Relationships": [{"Type": "CHILD", "Ids": ["b96efeee-4bf4-454d-8819-667a354db43a"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "227407699", "Geometry": {"BoundingBox": {"Width": 0.06058836355805397, "Height": 0.006870943587273359, "Left": 0.514869749546051, "Top": 0.4756676256656647}, "Polygon": [{"X": 0.5148698091506958, "Y": 0.4756685793399811}, {"X": 0.5754581093788147, "Y": 0.4756676256656647}, {"X": 0.5754579901695251, "Y": 0.4825376272201538}, {"X": 0.514869749546051, "Y": 0.4825385808944702}]}, "Id": "992b39b1-1cfd-4732-b8fa-8ea6cff2bb75", "Relationships": [{"Type": "CHILD", "Ids": ["30760787-f6de-45ae-859b-1d96f19e7471"]}]}, {"BlockType": "LINE", "Confidence": 99.83238983154297, "Text": "3", "Geometry": {"BoundingBox": {"Width": 0.0063613103702664375, "Height": 0.006829916033893824, "Left": 0.16887255012989044, "Top": 0.48619070649147034}, "Polygon": [{"X": 0.16887259483337402, "Y": 0.4861908257007599}, {"X": 0.175233855843544, "Y": 0.48619070649147034}, {"X": 0.17523381114006042, "Y": 0.49302053451538086}, {"X": 0.16887255012989044, "Y": 0.493020623922348}]}, "Id": "164ae1ee-dddd-4400-91b5-63f02f7fdfdd", "Relationships": [{"Type": "CHILD", "Ids": ["82dd8436-e85f-4544-9718-2e825b90f3bc"]}]}, {"BlockType": "LINE", "Confidence": 99.68511199951172, "Text": "4", "Geometry": {"BoundingBox": {"Width": 0.0070258076302707195, "Height": 0.006691001355648041, "Left": 0.16855113208293915, "Top": 0.49654772877693176}, "Polygon": [{"X": 0.16855117678642273, "Y": 0.4965478479862213}, {"X": 0.17557694017887115, "Y": 0.49654772877693176}, {"X": 0.17557689547538757, "Y": 0.5032386183738708}, {"X": 0.16855113208293915, "Y": 0.5032387375831604}]}, "Id": "8b423edd-a436-4885-b3ce-1227779ed95c", "Relationships": [{"Type": "CHILD", "Ids": ["0a9ad415-7565-44a7-9198-6f24ce1880e7"]}]}, {"BlockType": "LINE", "Confidence": 99.79252624511719, "Text": "5", "Geometry": {"BoundingBox": {"Width": 0.006197942886501551, "Height": 0.006747648119926453, "Left": 0.1691218912601471, "Top": 0.5072013139724731}, "Polygon": [{"X": 0.16912193596363068, "Y": 0.5072013735771179}, {"X": 0.1753198355436325, "Y": 0.5072013139724731}, {"X": 0.17531979084014893, "Y": 0.5139488577842712}, {"X": 0.1691218912601471, "Y": 0.5139489769935608}]}, "Id": "0c72efe4-b5a8-4c36-9fb1-1090902ec4e3", "Relationships": [{"Type": "CHILD", "Ids": ["6354ac64-1396-4bf3-9f1d-605223b08e86"]}]}, {"BlockType": "LINE", "Confidence": 99.71281433105469, "Text": "6", "Geometry": {"BoundingBox": {"Width": 0.00636710412800312, "Height": 0.0069289072416722775, "Left": 0.16888143122196198, "Top": 0.5180972814559937}, "Polygon": [{"X": 0.16888147592544556, "Y": 0.5180974006652832}, {"X": 0.17524853348731995, "Y": 0.5180972814559937}, {"X": 0.17524848878383636, "Y": 0.5250260829925537}, {"X": 0.16888143122196198, "Y": 0.5250262022018433}]}, "Id": "2e03e472-1249-4e2e-8446-e0ec65840839", "Relationships": [{"Type": "CHILD", "Ids": ["3476ffdf-88dd-484d-be29-42cad8ea9d08"]}]}, {"BlockType": "LINE", "Confidence": 99.88201141357422, "Text": "7", "Geometry": {"BoundingBox": {"Width": 0.006649814546108246, "Height": 0.006885642185807228, "Left": 0.16893725097179413, "Top": 0.5284852385520935}, "Polygon": [{"X": 0.1689372956752777, "Y": 0.5284853577613831}, {"X": 0.17558707296848297, "Y": 0.5284852385520935}, {"X": 0.1755870282649994, "Y": 0.5353707671165466}, {"X": 0.16893725097179413, "Y": 0.5353708863258362}]}, "Id": "fd4bdcec-656b-4cf9-a0ec-67f3b27f0e86", "Relationships": [{"Type": "CHILD", "Ids": ["7bc18e2e-7668-4ca4-b1e8-0c2f24439637"]}]}, {"BlockType": "LINE", "Confidence": 99.67375183105469, "Text": "8", "Geometry": {"BoundingBox": {"Width": 0.006595203652977943, "Height": 0.006734260357916355, "Left": 0.1688777208328247, "Top": 0.5392356514930725}, "Polygon": [{"X": 0.1688777655363083, "Y": 0.5392357707023621}, {"X": 0.1754729151725769, "Y": 0.5392356514930725}, {"X": 0.17547287046909332, "Y": 0.5459697842597961}, {"X": 0.1688777208328247, "Y": 0.5459699034690857}]}, "Id": "18b84fba-bcf1-4220-a736-66c5249f8ad7", "Relationships": [{"Type": "CHILD", "Ids": ["31115a1e-4a3f-4e75-ba0f-c48bd1908e66"]}]}, {"BlockType": "LINE", "Confidence": 99.60299682617188, "Text": "9", "Geometry": {"BoundingBox": {"Width": 0.006715078372508287, "Height": 0.007085907738655806, "Left": 0.16874417662620544, "Top": 0.5495967268943787}, "Polygon": [{"X": 0.16874422132968903, "Y": 0.5495968461036682}, {"X": 0.17545926570892334, "Y": 0.5495967268943787}, {"X": 0.17545920610427856, "Y": 0.5566825270652771}, {"X": 0.16874417662620544, "Y": 0.5566826462745667}]}, "Id": "54922a42-a777-4895-810f-6acce035c689", "Relationships": [{"Type": "CHILD", "Ids": ["4e7b07e7-c6b8-4320-bb94-8531ff631ce9"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "10", "Geometry": {"BoundingBox": {"Width": 0.012692058458924294, "Height": 0.006892743520438671, "Left": 0.1696051061153412, "Top": 0.5602093935012817}, "Polygon": [{"X": 0.16960515081882477, "Y": 0.5602095723152161}, {"X": 0.18229715526103973, "Y": 0.5602093935012817}, {"X": 0.18229711055755615, "Y": 0.5671018958091736}, {"X": 0.1696051061153412, "Y": 0.5671021342277527}]}, "Id": "38ffb2ea-c70d-4a41-897a-2a0fc5c6ddee", "Relationships": [{"Type": "CHILD", "Ids": ["8862a2a7-0907-4529-9c2b-f956f3951028"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.027860941365361214, "Height": 0.007223647087812424, "Left": 0.16397152841091156, "Top": 0.5709370970726013}, "Polygon": [{"X": 0.16397157311439514, "Y": 0.5709375739097595}, {"X": 0.19183246791362762, "Y": 0.5709370970726013}, {"X": 0.19183242321014404, "Y": 0.5781602263450623}, {"X": 0.16397152841091156, "Y": 0.5781607031822205}]}, "Id": "a791dfac-e26e-4685-b5e3-b4d160e8c6c2", "Relationships": [{"Type": "CHILD", "Ids": ["8913c35e-fdd8-46d3-9477-ed04ed09c3c0"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "1425", "Geometry": {"BoundingBox": {"Width": 0.02627403661608696, "Height": 0.006929267197847366, "Left": 0.33493518829345703, "Top": 0.5713433623313904}, "Polygon": [{"X": 0.3349352478981018, "Y": 0.5713438391685486}, {"X": 0.3612092137336731, "Y": 0.5713433623313904}, {"X": 0.3612091541290283, "Y": 0.5782721638679504}, {"X": 0.33493518829345703, "Y": 0.5782726407051086}]}, "Id": "8638bd37-69e0-44b4-b8d2-7222b620e44a", "Relationships": [{"Type": "CHILD", "Ids": ["33009cf3-9dd7-4d00-817e-0dab297e5d73"]}]}, {"BlockType": "LINE", "Confidence": 99.75875091552734, "Text": "Page 3/3", "Geometry": {"BoundingBox": {"Width": 0.05226879566907883, "Height": 0.010070810094475746, "Left": 0.8169759511947632, "Top": 0.9619084596633911}, "Polygon": [{"X": 0.8169761300086975, "Y": 0.9619096517562866}, {"X": 0.8692447543144226, "Y": 0.9619084596633911}, {"X": 0.8692446351051331, "Y": 0.9719780087471008}, {"X": 0.8169759511947632, "Y": 0.9719792604446411}]}, "Id": "94825c05-f6ee-49ca-87ac-dfc82a6827d9", "Relationships": [{"Type": "CHILD", "Ids": ["3ef228ff-d433-4375-a834-ad9bf99a99d7", "3de7233f-4d22-439a-a97b-136e8f2736a8"]}]}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Central", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047523383051157, "Height": 0.009486476890742779, "Left": 0.05718903988599777, "Top": 0.05624702572822571}, "Polygon": [{"X": 0.05718908831477165, "Y": 0.05624745413661003}, {"X": 0.10471241921186447, "Y": 0.05624702572822571}, {"X": 0.10471236705780029, "Y": 0.06573306769132614}, {"X": 0.05718903988599777, "Y": 0.06573350727558136}]}, "Id": "31dbf09b-9442-4675-8f64-5347bea474ca"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Transport", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06371095031499863, "Height": 0.011445809155702591, "Left": 0.10830892622470856, "Top": 0.056378427892923355}, "Polygon": [{"X": 0.10830899327993393, "Y": 0.05637900158762932}, {"X": 0.17201988399028778, "Y": 0.056378427892923355}, {"X": 0.1720198094844818, "Y": 0.06782364845275879}, {"X": 0.10830892622470856, "Y": 0.06782423704862595}]}, "Id": "8741cf18-fbec-4d36-bc4d-70f81147717a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "08/31/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0731736347079277, "Height": 0.009412871673703194, "Left": 0.8657994270324707, "Top": 0.05629698932170868}, "Polygon": [{"X": 0.865799605846405, "Y": 0.05629764869809151}, {"X": 0.938973069190979, "Y": 0.05629698932170868}, {"X": 0.9389729499816895, "Y": 0.065709188580513}, {"X": 0.8657994270324707, "Y": 0.06570985913276672}]}, "Id": "253025f4-507f-4e46-b8ef-13342b0db20a"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "PRO#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043377768248319626, "Height": 0.009395409375429153, "Left": 0.057394713163375854, "Top": 0.07146386057138443}, "Polygon": [{"X": 0.057394761592149734, "Y": 0.07146426290273666}, {"X": 0.10077248513698578, "Y": 0.07146386057138443}, {"X": 0.1007724329829216, "Y": 0.08085886389017105}, {"X": 0.057394713163375854, "Y": 0.08085927367210388}]}, "Id": "c3869898-47d4-4db6-933a-cec6bdd860ff"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07391535490751266, "Height": 0.009347072802484035, "Left": 0.10520923137664795, "Top": 0.07145050168037415}, "Polygon": [{"X": 0.10520928353071213, "Y": 0.07145117968320847}, {"X": 0.17912457883358002, "Y": 0.07145050168037415}, {"X": 0.17912451922893524, "Y": 0.08079687505960464}, {"X": 0.10520923137664795, "Y": 0.08079757541418076}]}, "Id": "ddff8fc7-9e1f-4eb0-9c88-3bf7422c3ee8"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04729338735342026, "Height": 0.011711383238434792, "Left": 0.8079518675804138, "Top": 0.07136216014623642}, "Polygon": [{"X": 0.8079520463943481, "Y": 0.07136259227991104}, {"X": 0.8552452921867371, "Y": 0.07136216014623642}, {"X": 0.8552451133728027, "Y": 0.08307309448719025}, {"X": 0.8079518675804138, "Y": 0.08307354152202606}]}, "Id": "fe95e8f8-2679-48e0-bff2-cbb18ea1999a"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010260049253702164, "Height": 0.00927706342190504, "Left": 0.8587119579315186, "Top": 0.07156898826360703}, "Polygon": [{"X": 0.8587120771408081, "Y": 0.07156908512115479}, {"X": 0.8689720034599304, "Y": 0.07156898826360703}, {"X": 0.8689718842506409, "Y": 0.08084595203399658}, {"X": 0.8587119579315186, "Y": 0.08084604889154434}]}, "Id": "ed399212-7591-42f1-99c5-14f2c373c57a"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06620460003614426, "Height": 0.011348029598593712, "Left": 0.8727492690086365, "Top": 0.07158870249986649}, "Polygon": [{"X": 0.8727494478225708, "Y": 0.07158931344747543}, {"X": 0.9389538764953613, "Y": 0.07158870249986649}, {"X": 0.938953697681427, "Y": 0.0829361081123352}, {"X": 0.8727492690086365, "Y": 0.08293673396110535}]}, "Id": "7dca30ba-56bd-48eb-952c-14a3854f300c"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "CENTRAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0677325576543808, "Height": 0.01386458333581686, "Left": 0.4383789896965027, "Top": 0.19190871715545654}, "Polygon": [{"X": 0.43837910890579224, "Y": 0.19190946221351624}, {"X": 0.5061115622520447, "Y": 0.19190871715545654}, {"X": 0.5061113834381104, "Y": 0.2057725191116333}, {"X": 0.4383789896965027, "Y": 0.20577329397201538}]}, "Id": "49d3263f-5dee-4a45-aa7f-75975944d772"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "TRANSPORT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09249085932970047, "Height": 0.013988036662340164, "Left": 0.508831262588501, "Top": 0.1918865293264389}, "Polygon": [{"X": 0.5088314414024353, "Y": 0.19188755750656128}, {"X": 0.6013221144676208, "Y": 0.1918865293264389}, {"X": 0.6013219952583313, "Y": 0.205873504281044}, {"X": 0.508831262588501, "Y": 0.20587456226348877}]}, "Id": "9c5a1253-d190-41dc-a123-26b217fba5dc"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "12225", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035280805081129074, "Height": 0.00855326559394598, "Left": 0.4399400055408478, "Top": 0.2120484709739685}, "Polygon": [{"X": 0.43994009494781494, "Y": 0.21204887330532074}, {"X": 0.47522079944610596, "Y": 0.2120484709739685}, {"X": 0.4752207100391388, "Y": 0.22060133516788483}, {"X": 0.4399400055408478, "Y": 0.22060173749923706}]}, "Id": "53fc8f13-6797-404c-bd7a-8e919e3e572a"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05482405051589012, "Height": 0.010645987465977669, "Left": 0.47985780239105225, "Top": 0.21199145913124084}, "Polygon": [{"X": 0.4798579216003418, "Y": 0.211992084980011}, {"X": 0.5346818566322327, "Y": 0.21199145913124084}, {"X": 0.5346817374229431, "Y": 0.22263680398464203}, {"X": 0.47985780239105225, "Y": 0.22263744473457336}]}, "Id": "83bdf443-0aad-49fc-9ee5-a1301ac3f37b"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Road", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02949291281402111, "Height": 0.008774659596383572, "Left": 0.5392704606056213, "Top": 0.2120930254459381}, "Polygon": [{"X": 0.5392705202102661, "Y": 0.21209336817264557}, {"X": 0.5687633752822876, "Y": 0.2120930254459381}, {"X": 0.568763256072998, "Y": 0.2208673506975174}, {"X": 0.5392704606056213, "Y": 0.22086769342422485}]}, "Id": "968a9689-ba5a-4bb5-b161-374a815a47ca"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04800622910261154, "Height": 0.010209893807768822, "Left": 0.44664397835731506, "Top": 0.2257819026708603}, "Polygon": [{"X": 0.4466440677642822, "Y": 0.22578246891498566}, {"X": 0.4946502149105072, "Y": 0.2257819026708603}, {"X": 0.49465009570121765, "Y": 0.2359912246465683}, {"X": 0.44664397835731506, "Y": 0.23599180579185486}]}, "Id": "980d211c-72af-46b3-a920-bc04fc983ed9"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "MI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0159319918602705, "Height": 0.00851416401565075, "Left": 0.49934086203575134, "Top": 0.22570796310901642}, "Polygon": [{"X": 0.4993409514427185, "Y": 0.22570815682411194}, {"X": 0.515272855758667, "Y": 0.22570796310901642}, {"X": 0.5152727365493774, "Y": 0.234221950173378}, {"X": 0.49934086203575134, "Y": 0.23422212898731232}]}, "Id": "1a04cce5-0b04-4b9c-9fd9-987930978bbb"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "48089", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036994144320487976, "Height": 0.008614152669906616, "Left": 0.5234261155128479, "Top": 0.22568704187870026}, "Polygon": [{"X": 0.5234262347221375, "Y": 0.22568747401237488}, {"X": 0.5604202747344971, "Y": 0.22568704187870026}, {"X": 0.5604201555252075, "Y": 0.23430076241493225}, {"X": 0.5234261155128479, "Y": 0.23430119454860687}]}, "Id": "b4344dd4-b80c-4232-ade1-789fd0de6a3c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CERTIFIED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07389842718839645, "Height": 0.008876699954271317, "Left": 0.39221125841140747, "Top": 0.29037290811538696}, "Polygon": [{"X": 0.39221134781837463, "Y": 0.290373831987381}, {"X": 0.4661096930503845, "Y": 0.29037290811538696}, {"X": 0.46610960364341736, "Y": 0.2992486357688904}, {"X": 0.39221125841140747, "Y": 0.2992495894432068}]}, "Id": "f8e54260-b90c-408a-b357-20f2bbf20bb8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WEIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05728071182966232, "Height": 0.009197513572871685, "Left": 0.47053226828575134, "Top": 0.2900329828262329}, "Polygon": [{"X": 0.4705323576927185, "Y": 0.2900336980819702}, {"X": 0.5278129577636719, "Y": 0.2900329828262329}, {"X": 0.5278128981590271, "Y": 0.29922977089881897}, {"X": 0.47053226828575134, "Y": 0.2992304861545563}]}, "Id": "2e8235a4-d547-4c43-af1d-d80ad4fd5bd0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CERTIFICATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08967792242765427, "Height": 0.008884044364094734, "Left": 0.5327039361000061, "Top": 0.2903496026992798}, "Polygon": [{"X": 0.5327039957046509, "Y": 0.2903507351875305}, {"X": 0.622381865978241, "Y": 0.2903496026992798}, {"X": 0.6223817467689514, "Y": 0.29923248291015625}, {"X": 0.5327039361000061, "Y": 0.29923364520072937}]}, "Id": "4be4eb10-0d9a-49db-824c-079f8b5170a3"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Pro", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021388905122876167, "Height": 0.008751227520406246, "Left": 0.15433162450790405, "Top": 0.3165864050388336}, "Polygon": [{"X": 0.15433166921138763, "Y": 0.3165866732597351}, {"X": 0.17572052776813507, "Y": 0.3165864050388336}, {"X": 0.1757204681634903, "Y": 0.32533735036849976}, {"X": 0.15433162450790405, "Y": 0.32533761858940125}]}, "Id": "941f81c8-cbfb-4344-9da7-ca17774a26c2"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05895177647471428, "Height": 0.009268013760447502, "Left": 0.18463827669620514, "Top": 0.3161730170249939}, "Polygon": [{"X": 0.18463833630084991, "Y": 0.316173791885376}, {"X": 0.24359004199504852, "Y": 0.3161730170249939}, {"X": 0.24358998239040375, "Y": 0.32544025778770447}, {"X": 0.18463827669620514, "Y": 0.32544103264808655}]}, "Id": "8fc34952-47db-4cbc-96b8-48eea3611fa8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07552546262741089, "Height": 0.009020472876727581, "Left": 0.2525319457054138, "Top": 0.31638628244400024}, "Polygon": [{"X": 0.252532035112381, "Y": 0.3163872957229614}, {"X": 0.3280574083328247, "Y": 0.31638628244400024}, {"X": 0.32805734872817993, "Y": 0.3254057765007019}, {"X": 0.2525319457054138, "Y": 0.3254067599773407}]}, "Id": "5c705409-c2ff-44d5-9622-83726e4be7a6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01808108016848564, "Height": 0.009077761322259903, "Left": 0.6965249180793762, "Top": 0.3161868751049042}, "Polygon": [{"X": 0.6965250372886658, "Y": 0.3161871135234833}, {"X": 0.714605987071991, "Y": 0.3161868751049042}, {"X": 0.7146058678627014, "Y": 0.3252643942832947}, {"X": 0.6965249180793762, "Y": 0.3252646327018738}]}, "Id": "47b6fc37-1b93-4ec8-910e-cdc2419f5488"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014402875676751137, "Height": 0.009438899345695972, "Left": 0.7203461527824402, "Top": 0.31584814190864563}, "Polygon": [{"X": 0.7203463315963745, "Y": 0.31584832072257996}, {"X": 0.7347490787506104, "Y": 0.31584814190864563}, {"X": 0.734748899936676, "Y": 0.3252868354320526}, {"X": 0.7203461527824402, "Y": 0.3252870440483093}]}, "Id": "de329712-c38b-4fd5-8465-9d53b9910684"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Lading", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04320087283849716, "Height": 0.011417851783335209, "Left": 0.7399371862411499, "Top": 0.3161904215812683}, "Polygon": [{"X": 0.7399373650550842, "Y": 0.31619101762771606}, {"X": 0.78313809633255, "Y": 0.3161904215812683}, {"X": 0.7831379175186157, "Y": 0.3276077210903168}, {"X": 0.7399371862411499, "Y": 0.32760828733444214}]}, "Id": "a35353c0-8718-4b30-8e5f-4bb0316ca8ea"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03578847274184227, "Height": 0.008917699567973614, "Left": 0.15446539223194122, "Top": 0.3295671343803406}, "Polygon": [{"X": 0.154465451836586, "Y": 0.3295676112174988}, {"X": 0.1902538686990738, "Y": 0.3295671343803406}, {"X": 0.19025380909442902, "Y": 0.33848437666893005}, {"X": 0.15446539223194122, "Y": 0.33848485350608826}]}, "Id": "299fff1f-dd5b-4aa5-8bc1-a0c45af9fe76"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07925864309072495, "Height": 0.010597198270261288, "Left": 0.19703388214111328, "Top": 0.32907816767692566}, "Polygon": [{"X": 0.19703395664691925, "Y": 0.32907921075820923}, {"X": 0.2762925326824188, "Y": 0.32907816767692566}, {"X": 0.27629244327545166, "Y": 0.33967429399490356}, {"X": 0.19703388214111328, "Y": 0.33967533707618713}]}, "Id": "a7236a80-57f9-4c93-b256-e9d4c0bf5a75"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DIAMOND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04778068885207176, "Height": 0.006365248467773199, "Left": 0.16249734163284302, "Top": 0.368204265832901}, "Polygon": [{"X": 0.1624973714351654, "Y": 0.3682049512863159}, {"X": 0.21027801930904388, "Y": 0.368204265832901}, {"X": 0.2102779746055603, "Y": 0.3745688498020172}, {"X": 0.16249734163284302, "Y": 0.37456953525543213}]}, "Id": "6a1b538b-990c-4a8b-9410-ab92c00ae888"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "ENVELOPE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05010327696800232, "Height": 0.00638725608587265, "Left": 0.21331366896629333, "Top": 0.3682202994823456}, "Polygon": [{"X": 0.21331371366977692, "Y": 0.3682210147380829}, {"X": 0.26341694593429565, "Y": 0.3682202994823456}, {"X": 0.2634168863296509, "Y": 0.3746068477630615}, {"X": 0.21331366896629333, "Y": 0.37460756301879883}]}, "Id": "76416d46-33c0-444d-8848-ee88a52f27d6"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03317679092288017, "Height": 0.008311101235449314, "Left": 0.7232305407524109, "Top": 0.3676694929599762}, "Polygon": [{"X": 0.7232306599617004, "Y": 0.3676699697971344}, {"X": 0.7564073204994202, "Y": 0.3676694929599762}, {"X": 0.7564072012901306, "Y": 0.3759801387786865}, {"X": 0.7232305407524109, "Y": 0.37598058581352234}]}, "Id": "7449e7a4-0b68-46b2-8e2e-39ee6e059509"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Certificate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04864736273884773, "Height": 0.006783640012145042, "Left": 0.7593495845794678, "Top": 0.3677155077457428}, "Polygon": [{"X": 0.7593496441841125, "Y": 0.3677161931991577}, {"X": 0.807996928691864, "Y": 0.3677155077457428}, {"X": 0.8079968094825745, "Y": 0.3744984567165375}, {"X": 0.7593495845794678, "Y": 0.3744991421699524}]}, "Id": "7cf69c21-7686-4c81-933e-1d9548554068"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2270", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02383694052696228, "Height": 0.006322705652564764, "Left": 0.16246169805526733, "Top": 0.37672942876815796}, "Polygon": [{"X": 0.16246172785758972, "Y": 0.3767297565937042}, {"X": 0.18629863858222961, "Y": 0.37672942876815796}, {"X": 0.18629859387874603, "Y": 0.3830517828464508}, {"X": 0.16246169805526733, "Y": 0.38305214047431946}]}, "Id": "e627b598-d1ba-4a4c-a3d8-ac54e74c2f2e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "WHITE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032461635768413544, "Height": 0.006380218081176281, "Left": 0.18896152079105377, "Top": 0.3766777217388153}, "Polygon": [{"X": 0.18896156549453735, "Y": 0.3766781687736511}, {"X": 0.22142314910888672, "Y": 0.3766777217388153}, {"X": 0.22142310440540314, "Y": 0.38305747509002686}, {"X": 0.18896152079105377, "Y": 0.38305795192718506}]}, "Id": "1555b32b-6ca1-49b3-9eef-ebdeb19b6d9d"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "OAK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02111927978694439, "Height": 0.006493091583251953, "Left": 0.22455410659313202, "Top": 0.3767055571079254}, "Polygon": [{"X": 0.2245541661977768, "Y": 0.3767058551311493}, {"X": 0.24567338824272156, "Y": 0.3767055571079254}, {"X": 0.24567334353923798, "Y": 0.3831983506679535}, {"X": 0.22455410659313202, "Y": 0.38319864869117737}]}, "Id": "f6bea819-ac69-4eb9-bc3c-96546e027f87"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "CIR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017902487888932228, "Height": 0.00622687628492713, "Left": 0.2486133575439453, "Top": 0.3768872916698456}, "Polygon": [{"X": 0.2486134171485901, "Y": 0.3768875300884247}, {"X": 0.266515851020813, "Y": 0.3768872916698456}, {"X": 0.2665157914161682, "Y": 0.38311392068862915}, {"X": 0.2486133575439453, "Y": 0.38311415910720825}]}, "Id": "4b09747a-a8f5-4bb1-a456-407b224859ec"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "We", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014652658253908157, "Height": 0.006238427013158798, "Left": 0.7073789238929749, "Top": 0.37680166959762573}, "Polygon": [{"X": 0.7073789834976196, "Y": 0.37680187821388245}, {"X": 0.7220315337181091, "Y": 0.37680166959762573}, {"X": 0.7220314741134644, "Y": 0.3830398917198181}, {"X": 0.7073789238929749, "Y": 0.38304010033607483}]}, "Id": "773f322c-fadf-4e56-99d8-88eabdbc9fcd"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Certify", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03157906234264374, "Height": 0.008171954192221165, "Left": 0.7253687977790833, "Top": 0.3762212097644806}, "Polygon": [{"X": 0.7253689169883728, "Y": 0.3762216567993164}, {"X": 0.7569478750228882, "Y": 0.3762212097644806}, {"X": 0.7569477558135986, "Y": 0.38439270853996277}, {"X": 0.7253687977790833, "Y": 0.3843931555747986}]}, "Id": "4e052189-2325-4ed0-b3c3-308f3296109d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019612392410635948, "Height": 0.006630921270698309, "Left": 0.7600220441818237, "Top": 0.37648865580558777}, "Polygon": [{"X": 0.7600221633911133, "Y": 0.37648892402648926}, {"X": 0.7796344757080078, "Y": 0.37648865580558777}, {"X": 0.7796343564987183, "Y": 0.38311928510665894}, {"X": 0.7600220441818237, "Y": 0.3831195831298828}]}, "Id": "02a52d4c-4d77-4190-9031-d441cf91a9c3"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0170767679810524, "Height": 0.00658020656555891, "Left": 0.7818982601165771, "Top": 0.37639084458351135}, "Polygon": [{"X": 0.7818983793258667, "Y": 0.37639108300209045}, {"X": 0.7989750504493713, "Y": 0.37639084458351135}, {"X": 0.7989749312400818, "Y": 0.38297080993652344}, {"X": 0.7818982601165771, "Y": 0.38297104835510254}]}, "Id": "f6fcdaa2-8c39-44ee-955f-96190f1ea8c0"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044950954616069794, "Height": 0.008148849941790104, "Left": 0.8024011850357056, "Top": 0.37618619203567505}, "Polygon": [{"X": 0.8024013042449951, "Y": 0.3761868178844452}, {"X": 0.847352147102356, "Y": 0.37618619203567505}, {"X": 0.8473520278930664, "Y": 0.38433441519737244}, {"X": 0.8024011850357056, "Y": 0.3843350410461426}]}, "Id": "a051dfd5-3d8c-43ff-96c0-e03669578abb"}, {"BlockType": "WORD", "Confidence": 99.2930908203125, "Text": "AURORA,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04494357109069824, "Height": 0.0072541264817118645, "Left": 0.16181287169456482, "Top": 0.385231077671051}, "Polygon": [{"X": 0.1618129163980484, "Y": 0.38523170351982117}, {"X": 0.20675644278526306, "Y": 0.385231077671051}, {"X": 0.2067563831806183, "Y": 0.39248454570770264}, {"X": 0.16181287169456482, "Y": 0.39248520135879517}]}, "Id": "9c4731d8-0fc7-43a0-8fa8-faefac091c5e"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009762809611856937, "Height": 0.006594124715775251, "Left": 0.21053430438041687, "Top": 0.3850327432155609}, "Polygon": [{"X": 0.21053434908390045, "Y": 0.38503286242485046}, {"X": 0.22029711306095123, "Y": 0.3850327432155609}, {"X": 0.22029706835746765, "Y": 0.3916267156600952}, {"X": 0.21053430438041687, "Y": 0.39162686467170715}]}, "Id": "e459a0b3-ad3c-4f8b-9610-dc51fcdb710d"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "60502", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029458606615662575, "Height": 0.006240761373192072, "Left": 0.22320081293582916, "Top": 0.38528716564178467}, "Polygon": [{"X": 0.22320085763931274, "Y": 0.3852875828742981}, {"X": 0.252659410238266, "Y": 0.38528716564178467}, {"X": 0.2526593804359436, "Y": 0.3915275037288666}, {"X": 0.22320081293582916, "Y": 0.39152792096138}]}, "Id": "fe9997e2-513e-45fa-8e51-d1f23828dc55"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "has", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016709955409169197, "Height": 0.006623478606343269, "Left": 0.7077588438987732, "Top": 0.38500329852104187}, "Polygon": [{"X": 0.7077589631080627, "Y": 0.38500353693962097}, {"X": 0.7244688272476196, "Y": 0.38500329852104187}, {"X": 0.7244687080383301, "Y": 0.3916265368461609}, {"X": 0.7077588438987732, "Y": 0.39162677526474}]}, "Id": "45150398-5485-49e4-8cf9-e710d2379851"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "been", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023208731785416603, "Height": 0.006726184394210577, "Left": 0.7273632884025574, "Top": 0.3849447965621948}, "Polygon": [{"X": 0.7273633480072021, "Y": 0.3849451243877411}, {"X": 0.7505720257759094, "Y": 0.3849447965621948}, {"X": 0.7505719065666199, "Y": 0.3916706442832947}, {"X": 0.7273632884025574, "Y": 0.39167100191116333}]}, "Id": "e0c206c5-80fb-4837-8607-b0b9b7a67ddd"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "reweighed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05010595545172691, "Height": 0.00796002522110939, "Left": 0.75407475233078, "Top": 0.38496842980384827}, "Polygon": [{"X": 0.7540748715400696, "Y": 0.38496914505958557}, {"X": 0.8041807413101196, "Y": 0.38496842980384827}, {"X": 0.8041806221008301, "Y": 0.39292773604393005}, {"X": 0.75407475233078, "Y": 0.39292845129966736}]}, "Id": "e5ee7ab1-7310-427f-9d02-e391d8090884"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01778884045779705, "Height": 0.006604108493775129, "Left": 0.806389570236206, "Top": 0.3849635720252991}, "Polygon": [{"X": 0.8063896894454956, "Y": 0.38496384024620056}, {"X": 0.8241783976554871, "Y": 0.3849635720252991}, {"X": 0.8241783380508423, "Y": 0.3915674388408661}, {"X": 0.806389570236206, "Y": 0.3915676772594452}]}, "Id": "3daf2ee5-6e4d-4aa7-b83f-b025a545521e"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018977243453264236, "Height": 0.006806911434978247, "Left": 0.8267665505409241, "Top": 0.3848668932914734}, "Polygon": [{"X": 0.8267666697502136, "Y": 0.3848671615123749}, {"X": 0.8457438349723816, "Y": 0.3848668932914734}, {"X": 0.845743715763092, "Y": 0.3916735351085663}, {"X": 0.8267665505409241, "Y": 0.3916738033294678}]}, "Id": "5481917c-f6b1-4937-911d-065644b9ac66"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015432396903634071, "Height": 0.006619512569159269, "Left": 0.707314670085907, "Top": 0.39335307478904724}, "Polygon": [{"X": 0.7073147296905518, "Y": 0.39335328340530396}, {"X": 0.7227470874786377, "Y": 0.39335307478904724}, {"X": 0.7227469682693481, "Y": 0.3999723792076111}, {"X": 0.707314670085907, "Y": 0.3999725878238678}]}, "Id": "d104628f-1f42-45af-a4df-99023b0c1018"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "corrected", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045688241720199585, "Height": 0.006469257641583681, "Left": 0.7260426878929138, "Top": 0.3935489356517792}, "Polygon": [{"X": 0.7260427474975586, "Y": 0.3935495913028717}, {"X": 0.771730899810791, "Y": 0.3935489356517792}, {"X": 0.7717308402061462, "Y": 0.40001752972602844}, {"X": 0.7260426878929138, "Y": 0.40001818537712097}]}, "Id": "39080325-9256-4da5-b5b0-51f95150c113"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031995803117752075, "Height": 0.008181402459740639, "Left": 0.7740893959999084, "Top": 0.39328134059906006}, "Polygon": [{"X": 0.774089515209198, "Y": 0.3932817876338959}, {"X": 0.8060852289199829, "Y": 0.39328134059906006}, {"X": 0.8060850501060486, "Y": 0.40146228671073914}, {"X": 0.7740893959999084, "Y": 0.40146273374557495}]}, "Id": "d0257aa7-232f-4c17-8d97-00a18655033d"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "as", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010669945739209652, "Height": 0.004868826363235712, "Left": 0.8092235326766968, "Top": 0.394987553358078}, "Polygon": [{"X": 0.8092235922813416, "Y": 0.39498770236968994}, {"X": 0.819893479347229, "Y": 0.394987553358078}, {"X": 0.8198934197425842, "Y": 0.39985620975494385}, {"X": 0.8092235326766968, "Y": 0.3998563587665558}]}, "Id": "d904a262-e09f-41bb-b05e-f8593d21226c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "stated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029793880879878998, "Height": 0.006427379325032234, "Left": 0.8228550553321838, "Top": 0.39359018206596375}, "Polygon": [{"X": 0.8228551745414734, "Y": 0.39359062910079956}, {"X": 0.8526489734649658, "Y": 0.39359018206596375}, {"X": 0.8526488542556763, "Y": 0.4000171422958374}, {"X": 0.8228550553321838, "Y": 0.40001755952835083}]}, "Id": "535572be-9be4-48c9-a540-e9a4bbb83649"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007551476359367371, "Height": 0.0070420317351818085, "Left": 0.7076213955879211, "Top": 0.4017404317855835}, "Polygon": [{"X": 0.7076214551925659, "Y": 0.40174052119255066}, {"X": 0.7151728868484497, "Y": 0.4017404317855835}, {"X": 0.7151727676391602, "Y": 0.40878233313560486}, {"X": 0.7076213955879211, "Y": 0.4087824523448944}]}, "Id": "914e4f81-5a38-4d54-84d3-c9e2946407a0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015589390881359577, "Height": 0.006525528617203236, "Left": 0.717993974685669, "Top": 0.40196681022644043}, "Polygon": [{"X": 0.7179940938949585, "Y": 0.40196701884269714}, {"X": 0.733583390712738, "Y": 0.40196681022644043}, {"X": 0.7335832715034485, "Y": 0.4084920883178711}, {"X": 0.717993974685669, "Y": 0.4084923267364502}]}, "Id": "9f4eedea-9a92-4309-8b15-39e9e2c658d8"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "true", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019698061048984528, "Height": 0.00635173823684454, "Left": 0.7363592386245728, "Top": 0.40228596329689026}, "Polygon": [{"X": 0.7363593578338623, "Y": 0.40228626132011414}, {"X": 0.7560573220252991, "Y": 0.40228596329689026}, {"X": 0.7560572028160095, "Y": 0.4086374044418335}, {"X": 0.7363592386245728, "Y": 0.4086377024650574}]}, "Id": "9b2b236c-5428-4e6a-b1f2-a16c13ddae34"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01766594685614109, "Height": 0.006636554840952158, "Left": 0.7591797113418579, "Top": 0.4019549787044525}, "Polygon": [{"X": 0.7591797709465027, "Y": 0.401955246925354}, {"X": 0.7768456339836121, "Y": 0.4019549787044525}, {"X": 0.7768455743789673, "Y": 0.40859127044677734}, {"X": 0.7591797113418579, "Y": 0.40859153866767883}]}, "Id": "35b828b6-a8cc-492d-b12c-5619b7197832"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "accurate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0413362979888916, "Height": 0.0063436212949454784, "Left": 0.7800097465515137, "Top": 0.40249454975128174}, "Polygon": [{"X": 0.7800098657608032, "Y": 0.4024951756000519}, {"X": 0.8213460445404053, "Y": 0.40249454975128174}, {"X": 0.8213459253311157, "Y": 0.40883758664131165}, {"X": 0.7800097465515137, "Y": 0.4088381826877594}]}, "Id": "28d3fef4-1ddf-4972-869b-8dc847c1c78d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Declared", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04313170164823532, "Height": 0.0064966813661158085, "Left": 0.16241343319416046, "Top": 0.4106127619743347}, "Polygon": [{"X": 0.16241347789764404, "Y": 0.41061341762542725}, {"X": 0.20554512739181519, "Y": 0.4106127619743347}, {"X": 0.2055450826883316, "Y": 0.41710883378982544}, {"X": 0.16241343319416046, "Y": 0.4171094596385956}]}, "Id": "e8582662-adcf-49ac-be66-b5ea05077c4a"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03596487268805504, "Height": 0.008118771947920322, "Left": 0.20843079686164856, "Top": 0.4103333055973053}, "Polygon": [{"X": 0.20843085646629333, "Y": 0.4103338122367859}, {"X": 0.2443956732749939, "Y": 0.4103333055973053}, {"X": 0.24439561367034912, "Y": 0.41845154762268066}, {"X": 0.20843079686164856, "Y": 0.41845208406448364}]}, "Id": "3ca506f7-610b-41b8-9af4-5b4382cfed7f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1148", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023435501381754875, "Height": 0.006363362539559603, "Left": 0.2486116588115692, "Top": 0.4108577370643616}, "Polygon": [{"X": 0.2486117035150528, "Y": 0.4108580946922302}, {"X": 0.27204716205596924, "Y": 0.4108577370643616}, {"X": 0.27204710245132446, "Y": 0.4172207713127136}, {"X": 0.2486116588115692, "Y": 0.4172210991382599}]}, "Id": "52ec0b00-75c2-4fa4-9ee2-65dd22da9780"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03254435956478119, "Height": 0.008135346695780754, "Left": 0.7069318294525146, "Top": 0.4102950394153595}, "Polygon": [{"X": 0.7069319486618042, "Y": 0.4102955162525177}, {"X": 0.739476203918457, "Y": 0.4102950394153595}, {"X": 0.7394760847091675, "Y": 0.4184299111366272}, {"X": 0.7069318294525146, "Y": 0.4184303879737854}]}, "Id": "c69ff903-4ee1-4d6f-bb7d-75bf6e26325d"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009893991984426975, "Height": 0.006705030333250761, "Left": 0.7423079609870911, "Top": 0.410263329744339}, "Polygon": [{"X": 0.7423080205917358, "Y": 0.4102634787559509}, {"X": 0.7522019147872925, "Y": 0.410263329744339}, {"X": 0.7522018551826477, "Y": 0.4169681966304779}, {"X": 0.7423079609870911, "Y": 0.41696834564208984}]}, "Id": "d49e73eb-ccfe-4791-b264-0d62e52b7d39"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015859805047512054, "Height": 0.0065754735842347145, "Left": 0.7543373107910156, "Top": 0.4106237292289734}, "Polygon": [{"X": 0.7543374300003052, "Y": 0.4106239676475525}, {"X": 0.7701971530914307, "Y": 0.4106237292289734}, {"X": 0.7701970338821411, "Y": 0.4171989858150482}, {"X": 0.7543373107910156, "Y": 0.4171992242336273}]}, "Id": "5dc2a84c-1e6f-4c56-88b6-27f28b252198"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04508283734321594, "Height": 0.007687367964535952, "Left": 0.7730361223220825, "Top": 0.41048917174339294}, "Polygon": [{"X": 0.7730362415313721, "Y": 0.4104898273944855}, {"X": 0.8181189894676208, "Y": 0.41048917174339294}, {"X": 0.8181188702583313, "Y": 0.4181758761405945}, {"X": 0.7730361223220825, "Y": 0.418176531791687}]}, "Id": "d42f3e98-fc53-4cb0-bcd4-3a40d9e5461e"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Corrected", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04748039320111275, "Height": 0.006810094229876995, "Left": 0.16247409582138062, "Top": 0.4188801050186157}, "Polygon": [{"X": 0.1624741405248642, "Y": 0.41888079047203064}, {"X": 0.20995448529720306, "Y": 0.4188801050186157}, {"X": 0.20995444059371948, "Y": 0.4256894886493683}, {"X": 0.16247409582138062, "Y": 0.4256902039051056}]}, "Id": "cce99f27-708b-4b8b-8aec-d278234dd0d5"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036091309040784836, "Height": 0.008014409802854061, "Left": 0.21280618011951447, "Top": 0.41898638010025024}, "Polygon": [{"X": 0.21280623972415924, "Y": 0.4189869165420532}, {"X": 0.2488974928855896, "Y": 0.41898638010025024}, {"X": 0.24889743328094482, "Y": 0.4270002543926239}, {"X": 0.21280618011951447, "Y": 0.4270007908344269}]}, "Id": "65473b38-a6b2-4267-88f3-3ff90edbbbad"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "1425", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023578299209475517, "Height": 0.006388806272298098, "Left": 0.253194659948349, "Top": 0.419284462928772}, "Polygon": [{"X": 0.2531947195529938, "Y": 0.41928479075431824}, {"X": 0.27677297592163086, "Y": 0.419284462928772}, {"X": 0.2767729163169861, "Y": 0.4256729185581207}, {"X": 0.253194659948349, "Y": 0.425673246383667}]}, "Id": "f9899cbd-8921-4316-80f1-4d65801a2a66"}, {"BlockType": "WORD", "Confidence": 99.7120132446289, "Text": "HU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017055867239832878, "Height": 0.006777491886168718, "Left": 0.16441786289215088, "Top": 0.4541013836860657}, "Polygon": [{"X": 0.16441790759563446, "Y": 0.45410165190696716}, {"X": 0.1814737319946289, "Y": 0.4541013836860657}, {"X": 0.18147367238998413, "Y": 0.4608786106109619}, {"X": 0.16441786289215088, "Y": 0.4608788788318634}]}, "Id": "8d4d3c9c-cdaf-4c5e-8811-b84b4377829b"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027246743440628052, "Height": 0.007028465624898672, "Left": 0.19829589128494263, "Top": 0.4539552330970764}, "Polygon": [{"X": 0.1982959508895874, "Y": 0.45395565032958984}, {"X": 0.22554263472557068, "Y": 0.4539552330970764}, {"X": 0.2255425900220871, "Y": 0.4609832763671875}, {"X": 0.19829589128494263, "Y": 0.4609836935997009}]}, "Id": "55f47607-2c08-40dc-b20a-ae3a28b64a6c"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02633608691394329, "Height": 0.007158621214330196, "Left": 0.2319745570421219, "Top": 0.45388081669807434}, "Polygon": [{"X": 0.23197461664676666, "Y": 0.4538812041282654}, {"X": 0.2583106458187103, "Y": 0.45388081669807434}, {"X": 0.25831058621406555, "Y": 0.4610390365123749}, {"X": 0.2319745570421219, "Y": 0.4610394239425659}]}, "Id": "e7a1d074-7f71-471c-a9c6-d4e587c6650e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02890188619494438, "Height": 0.007359205279499292, "Left": 0.2874515652656555, "Top": 0.4534839987754822}, "Polygon": [{"X": 0.2874516248703003, "Y": 0.453484445810318}, {"X": 0.316353440284729, "Y": 0.4534839987754822}, {"X": 0.31635338068008423, "Y": 0.4608427584171295}, {"X": 0.2874515652656555, "Y": 0.46084320545196533}]}, "Id": "9113960b-69fe-4abf-8da8-c558e397ca35"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040659863501787186, "Height": 0.009368103928864002, "Left": 0.3337138891220093, "Top": 0.4535251557826996}, "Polygon": [{"X": 0.33371397852897644, "Y": 0.4535257816314697}, {"X": 0.37437376379966736, "Y": 0.4535251557826996}, {"X": 0.3743736743927002, "Y": 0.4628926217556}, {"X": 0.3337138891220093, "Y": 0.4628932476043701}]}, "Id": "b64b6674-4ab6-454d-8393-7db13999ee74"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Terminal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0507369227707386, "Height": 0.007401087787002325, "Left": 0.3780301809310913, "Top": 0.45351311564445496}, "Polygon": [{"X": 0.3780302405357361, "Y": 0.4535139203071594}, {"X": 0.4287670850753784, "Y": 0.45351311564445496}, {"X": 0.42876702547073364, "Y": 0.46091344952583313}, {"X": 0.3780301809310913, "Y": 0.4609142243862152}]}, "Id": "f8d6c41a-7803-4417-b269-751ad822b94f"}, {"BlockType": "WORD", "Confidence": 99.7217788696289, "Text": "Pro", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01893853209912777, "Height": 0.007110124453902245, "Left": 0.46933484077453613, "Top": 0.45394328236579895}, "Polygon": [{"X": 0.4693349301815033, "Y": 0.4539435803890228}, {"X": 0.48827338218688965, "Y": 0.45394328236579895}, {"X": 0.4882732927799225, "Y": 0.46105310320854187}, {"X": 0.46933484077453613, "Y": 0.46105340123176575}]}, "Id": "30965192-dae6-4c3a-b528-d8119cf56155"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044754479080438614, "Height": 0.00717869633808732, "Left": 0.4923553168773651, "Top": 0.4536535441875458}, "Polygon": [{"X": 0.4923553764820099, "Y": 0.4536542296409607}, {"X": 0.5371097922325134, "Y": 0.4536535441875458}, {"X": 0.5371096730232239, "Y": 0.46083155274391174}, {"X": 0.4923553168773651, "Y": 0.46083223819732666}]}, "Id": "65e6aec6-05ff-414f-9b95-255049b9d621"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "33136199", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058709219098091125, "Height": 0.007329114247113466, "Left": 0.1687587946653366, "Top": 0.4650166928768158}, "Polygon": [{"X": 0.1687588393688202, "Y": 0.4650176167488098}, {"X": 0.22746801376342773, "Y": 0.4650166928768158}, {"X": 0.22746795415878296, "Y": 0.47234490513801575}, {"X": 0.1687587946653366, "Y": 0.47234582901000977}]}, "Id": "5c77223c-dce8-49c8-a3f0-d407e87dc5e9"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0687561109662056, "Height": 0.008462921716272831, "Left": 0.24354542791843414, "Top": 0.46471866965293884}, "Polygon": [{"X": 0.24354548752307892, "Y": 0.4647197127342224}, {"X": 0.31230154633522034, "Y": 0.46471866965293884}, {"X": 0.3123014569282532, "Y": 0.47318050265312195}, {"X": 0.24354542791843414, "Y": 0.4731815755367279}]}, "Id": "8815062f-3fc2-4bd3-aaaf-d6a175ff608c"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "00:02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03399926424026489, "Height": 0.007215920835733414, "Left": 0.3340480327606201, "Top": 0.4650706946849823}, "Polygon": [{"X": 0.3340480923652649, "Y": 0.4650712311267853}, {"X": 0.368047297000885, "Y": 0.4650706946849823}, {"X": 0.36804723739624023, "Y": 0.4722861051559448}, {"X": 0.3340480327606201, "Y": 0.4722866117954254}]}, "Id": "5326459f-fe7e-41d4-8358-5ffa055b6192"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "880", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02212643437087536, "Height": 0.006782433483749628, "Left": 0.3790387511253357, "Top": 0.46533575654029846}, "Polygon": [{"X": 0.37903881072998047, "Y": 0.4653360843658447}, {"X": 0.4011651873588562, "Y": 0.46533575654029846}, {"X": 0.4011651277542114, "Y": 0.4721178412437439}, {"X": 0.3790387511253357, "Y": 0.47211816906929016}]}, "Id": "1b244ae0-a4a0-4b2e-806d-4badce7fbe38"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "463", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021867085248231888, "Height": 0.00682410504668951, "Left": 0.43186819553375244, "Top": 0.46527284383773804}, "Polygon": [{"X": 0.4318682551383972, "Y": 0.4652732014656067}, {"X": 0.45373526215553284, "Y": 0.46527284383773804}, {"X": 0.45373520255088806, "Y": 0.47209662199020386}, {"X": 0.43186819553375244, "Y": 0.4720969498157501}]}, "Id": "60de7d43-1b08-4d08-a17d-d265dac2b6ee"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060560133308172226, "Height": 0.007165390066802502, "Left": 0.5148974657058716, "Top": 0.4650382697582245}, "Polygon": [{"X": 0.5148975253105164, "Y": 0.4650391936302185}, {"X": 0.5754575729370117, "Y": 0.4650382697582245}, {"X": 0.5754575133323669, "Y": 0.47220271825790405}, {"X": 0.5148974657058716, "Y": 0.47220367193222046}]}, "Id": "461a0331-9e70-45c3-924f-077f5500d143"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "33136200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058386050164699554, "Height": 0.006965055596083403, "Left": 0.16896452009677887, "Top": 0.47567445039749146}, "Polygon": [{"X": 0.16896456480026245, "Y": 0.4756753742694855}, {"X": 0.22735057771205902, "Y": 0.47567445039749146}, {"X": 0.22735051810741425, "Y": 0.4826385974884033}, {"X": 0.16896452009677887, "Y": 0.48263952136039734}]}, "Id": "08c2ca8d-a745-4b83-aa4f-d286ff4c1596"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06862763315439224, "Height": 0.008570958860218525, "Left": 0.24351204931735992, "Top": 0.47528213262557983}, "Polygon": [{"X": 0.2435121089220047, "Y": 0.4752832055091858}, {"X": 0.31213968992233276, "Y": 0.47528213262557983}, {"X": 0.3121396005153656, "Y": 0.48385199904441833}, {"X": 0.24351204931735992, "Y": 0.4838530719280243}]}, "Id": "2287e76c-f613-4910-b723-c703e2b9d299"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "00:03", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033893387764692307, "Height": 0.0071488418616354465, "Left": 0.3341608941555023, "Top": 0.4757031202316284}, "Polygon": [{"X": 0.3341609537601471, "Y": 0.475703626871109}, {"X": 0.3680543005466461, "Y": 0.4757031202316284}, {"X": 0.36805421113967896, "Y": 0.48285141587257385}, {"X": 0.3341608941555023, "Y": 0.48285195231437683}]}, "Id": "46a3e37d-8997-40e7-b8c8-fe99787f006b"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "545", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02147347666323185, "Height": 0.006957860663533211, "Left": 0.3794381320476532, "Top": 0.4757300317287445}, "Polygon": [{"X": 0.379438191652298, "Y": 0.47573035955429077}, {"X": 0.4009115993976593, "Y": 0.4757300317287445}, {"X": 0.4009115397930145, "Y": 0.4826875627040863}, {"X": 0.3794381320476532, "Y": 0.48268789052963257}]}, "Id": "34f0bd91-e79a-4a6b-8055-1fe539a7f27e"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "463", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021909311413764954, "Height": 0.006979272235184908, "Left": 0.43184787034988403, "Top": 0.4756060838699341}, "Polygon": [{"X": 0.4318479299545288, "Y": 0.47560641169548035}, {"X": 0.4537571966648102, "Y": 0.4756060838699341}, {"X": 0.453757107257843, "Y": 0.48258501291275024}, {"X": 0.43184787034988403, "Y": 0.4825853407382965}]}, "Id": "b96efeee-4bf4-454d-8819-667a354db43a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06058836355805397, "Height": 0.006870943587273359, "Left": 0.514869749546051, "Top": 0.4756676256656647}, "Polygon": [{"X": 0.5148698091506958, "Y": 0.4756685793399811}, {"X": 0.5754581093788147, "Y": 0.4756676256656647}, {"X": 0.5754579901695251, "Y": 0.4825376272201538}, {"X": 0.514869749546051, "Y": 0.4825385808944702}]}, "Id": "30760787-f6de-45ae-859b-1d96f19e7471"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0063613103702664375, "Height": 0.006829916033893824, "Left": 0.16887255012989044, "Top": 0.48619070649147034}, "Polygon": [{"X": 0.16887259483337402, "Y": 0.4861908257007599}, {"X": 0.175233855843544, "Y": 0.48619070649147034}, {"X": 0.17523381114006042, "Y": 0.49302053451538086}, {"X": 0.16887255012989044, "Y": 0.493020623922348}]}, "Id": "82dd8436-e85f-4544-9718-2e825b90f3bc"}, {"BlockType": "WORD", "Confidence": 99.68511199951172, "Text": "4", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0070258076302707195, "Height": 0.006691001355648041, "Left": 0.16855113208293915, "Top": 0.49654772877693176}, "Polygon": [{"X": 0.16855117678642273, "Y": 0.4965478479862213}, {"X": 0.17557694017887115, "Y": 0.49654772877693176}, {"X": 0.17557689547538757, "Y": 0.5032386183738708}, {"X": 0.16855113208293915, "Y": 0.5032387375831604}]}, "Id": "0a9ad415-7565-44a7-9198-6f24ce1880e7"}, {"BlockType": "WORD", "Confidence": 99.79252624511719, "Text": "5", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006197942886501551, "Height": 0.006747648119926453, "Left": 0.1691218912601471, "Top": 0.5072013139724731}, "Polygon": [{"X": 0.16912193596363068, "Y": 0.5072013735771179}, {"X": 0.1753198355436325, "Y": 0.5072013139724731}, {"X": 0.17531979084014893, "Y": 0.5139488577842712}, {"X": 0.1691218912601471, "Y": 0.5139489769935608}]}, "Id": "6354ac64-1396-4bf3-9f1d-605223b08e86"}, {"BlockType": "WORD", "Confidence": 99.71281433105469, "Text": "6", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00636710412800312, "Height": 0.0069289072416722775, "Left": 0.16888143122196198, "Top": 0.5180972814559937}, "Polygon": [{"X": 0.16888147592544556, "Y": 0.5180974006652832}, {"X": 0.17524853348731995, "Y": 0.5180972814559937}, {"X": 0.17524848878383636, "Y": 0.5250260829925537}, {"X": 0.16888143122196198, "Y": 0.5250262022018433}]}, "Id": "3476ffdf-88dd-484d-be29-42cad8ea9d08"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "7", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006649814546108246, "Height": 0.006885642185807228, "Left": 0.16893725097179413, "Top": 0.5284852385520935}, "Polygon": [{"X": 0.1689372956752777, "Y": 0.5284853577613831}, {"X": 0.17558707296848297, "Y": 0.5284852385520935}, {"X": 0.1755870282649994, "Y": 0.5353707671165466}, {"X": 0.16893725097179413, "Y": 0.5353708863258362}]}, "Id": "7bc18e2e-7668-4ca4-b1e8-0c2f24439637"}, {"BlockType": "WORD", "Confidence": 99.67375183105469, "Text": "8", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006595203652977943, "Height": 0.006734260357916355, "Left": 0.1688777208328247, "Top": 0.5392356514930725}, "Polygon": [{"X": 0.1688777655363083, "Y": 0.5392357707023621}, {"X": 0.1754729151725769, "Y": 0.5392356514930725}, {"X": 0.17547287046909332, "Y": 0.5459697842597961}, {"X": 0.1688777208328247, "Y": 0.5459699034690857}]}, "Id": "31115a1e-4a3f-4e75-ba0f-c48bd1908e66"}, {"BlockType": "WORD", "Confidence": 99.60299682617188, "Text": "9", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006715078372508287, "Height": 0.007085907738655806, "Left": 0.16874417662620544, "Top": 0.5495967268943787}, "Polygon": [{"X": 0.16874422132968903, "Y": 0.5495968461036682}, {"X": 0.17545926570892334, "Y": 0.5495967268943787}, {"X": 0.17545920610427856, "Y": 0.5566825270652771}, {"X": 0.16874417662620544, "Y": 0.5566826462745667}]}, "Id": "4e7b07e7-c6b8-4320-bb94-8531ff631ce9"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "10", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012692058458924294, "Height": 0.006892743520438671, "Left": 0.1696051061153412, "Top": 0.5602093935012817}, "Polygon": [{"X": 0.16960515081882477, "Y": 0.5602095723152161}, {"X": 0.18229715526103973, "Y": 0.5602093935012817}, {"X": 0.18229711055755615, "Y": 0.5671018958091736}, {"X": 0.1696051061153412, "Y": 0.5671021342277527}]}, "Id": "8862a2a7-0907-4529-9c2b-f956f3951028"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027860941365361214, "Height": 0.007223647087812424, "Left": 0.16397152841091156, "Top": 0.5709370970726013}, "Polygon": [{"X": 0.16397157311439514, "Y": 0.5709375739097595}, {"X": 0.19183246791362762, "Y": 0.5709370970726013}, {"X": 0.19183242321014404, "Y": 0.5781602263450623}, {"X": 0.16397152841091156, "Y": 0.5781607031822205}]}, "Id": "8913c35e-fdd8-46d3-9477-ed04ed09c3c0"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "1425", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02627403661608696, "Height": 0.006929267197847366, "Left": 0.33493518829345703, "Top": 0.5713433623313904}, "Polygon": [{"X": 0.3349352478981018, "Y": 0.5713438391685486}, {"X": 0.3612092137336731, "Y": 0.5713433623313904}, {"X": 0.3612091541290283, "Y": 0.5782721638679504}, {"X": 0.33493518829345703, "Y": 0.5782726407051086}]}, "Id": "33009cf3-9dd7-4d00-817e-0dab297e5d73"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Page", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030416401103138924, "Height": 0.010070300661027431, "Left": 0.8169759511947632, "Top": 0.9619089365005493}, "Polygon": [{"X": 0.8169761300086975, "Y": 0.9619096517562866}, {"X": 0.8473923802375793, "Y": 0.9619089365005493}, {"X": 0.847392201423645, "Y": 0.9719785451889038}, {"X": 0.8169759511947632, "Y": 0.9719792604446411}]}, "Id": "3ef228ff-d433-4375-a834-ad9bf99a99d7"}, {"BlockType": "WORD", "Confidence": 99.63548278808594, "Text": "3/3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01798633299767971, "Height": 0.008184125646948814, "Left": 0.8512584567070007, "Top": 0.9621924757957458}, "Polygon": [{"X": 0.8512585759162903, "Y": 0.9621928930282593}, {"X": 0.8692447543144226, "Y": 0.9621924757957458}, {"X": 0.8692446351051331, "Y": 0.970376193523407}, {"X": 0.8512584567070007, "Y": 0.9703766107559204}]}, "Id": "3de7233f-4d22-439a-a97b-136e8f2736a8"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "c9bd556c-5720-41da-9b94-6d46fd9f951f", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "c9bd556c-5720-41da-9b94-6d46fd9f951f", "content-type": "application/x-amz-json-1.1", "content-length": "78383", "date": "Wed, 24 Sep 2025 08:35:55 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "c1441a61-2ce4-4724-acea-41aa78c5b01e", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 08:35:59 GMT", "content-type": "application/json", "content-length": "1591", "connection": "keep-alive", "x-amzn-requestid": "c1441a61-2ce4-4724-acea-41aa78c5b01e"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page with \"Weight & Inspection\" and \"Certified Weight Certificate\" etc. The doc seems to be a \"weight_and_inspection_cert\" or \"nmfc_cert\". It contains \"corrected weight\" and \"certified weight certificate\". The description: weight_and_inspection_cert vs nmfc_cert. The difference: if it has weight mention, weight_and_inspection_cert. If it also has nmfc correction indicators. The content: \"Weight Certificate\" \"Corrected Weight: 1425\" \"Declared Weight: 1148\". It seems a weight certificate. Does it have NMFC-related terms? Not seeing NMFC Code, class #, or correction words like \"Corrected\". It does say \"reweighed\" and \"corrected weight\". But does that qualify as nmfc_cert? The instruction: \"Strickly check if weight_and_inspection_cert is having keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\" The nmfc_cert keywords: \"Inspected against original\", \"Corrected against Actual\", \"Correction information\", \"NMFC Code\", \"class #\". There's no mention of those. So classify as weight_and_inspection_cert.\n\nWe have only one page. We need to output: array of objects with page_no 1 and doc_type \"weight_and_inspection_cert\". Use the tool."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"weight_and_inspection_cert\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2682, "outputTokens": 308, "totalTokens": 2990}, "metrics": {"latencyMs": 1390}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/ed3c3359_W_DFY1VDZWR7NBDLJV02G2.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nCentral Transport\n08/31/2025\nPRO#: 227407699\nWeight & Inspection\nCENTRAL TRANSPORT\n12225 Stephens Road\nWarren, MI 48089\nCERTIFIED WEIGHT CERTIFICATE\nPro Number: 227407699\nBill of Lading\nDate: 08/26/2025\nDIAMOND ENVELOPE\nWeight Certificate\n2270 WHITE OAK CIR\nWe Certify that this shipment\nAURORA, IL 60502\nhas been reweighed and that\nthe corrected weight as stated\nis the true and accurate\nDeclared Weight: 1148\nweight of the shipment\nCorrected Weight: 1425\nHU\nScan Date\nTime\nWeight Terminal\nPro Number\n33136199\n08/26/2025\n00:02 880\n463\n227407699\n33136200\n08/26/2025\n00:03\n545\n463\n227407699\n3\n4\n5\n6\n7\n8\n9\n10\nTotal\n1425\nPage 3/3\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}