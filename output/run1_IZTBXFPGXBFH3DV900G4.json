{"classification_result": {"documents": [{"page_no": 1, "doc_type": "weight_and_inspection_cert"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 1.6966780203020448e-09, "Y": 6.882381597961285e-08}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "bc9a6a1f-7073-4baa-a66e-9cfb379bd147", "Relationships": [{"Type": "CHILD", "Ids": ["1993b420-7c75-429b-83aa-cedcf8727b85", "b13edebc-c823-4766-ac1f-b6efe2a54b5c", "7261bf91-4170-4f8a-8ed1-c9f8b1eea6b0", "47889fe0-c1f9-477c-afdf-39995d2a5701", "6b0e0c14-0ab9-480c-8880-1a5bb714dee5", "bdbf4b60-8144-4492-a3e0-513652c9e769", "d6aee025-95a4-4304-a275-744a18aa5209", "7c473a6a-504a-43fa-bc9d-ac04f0d3928c", "cd502f9a-add5-4e1e-a689-413ec2e5dddc", "0e38ec58-c5e6-4160-a4b4-ac3ed0785989", "24bfb661-293a-4063-b8dc-a730d27b6399", "cd406238-fa60-4c87-92c4-499373efdaa8", "b7a5e51c-d69e-4f7f-8e96-de7f2ac0208f", "94924be7-7b39-4a80-a3b7-2acc1e35b641", "3faf208c-b024-4cb3-919a-f1db31923ae3", "9a8aa558-75f0-463c-ae33-533a6eab7a99", "31bd7418-7ed3-421a-946e-cc4bc5eafb85", "07c84c93-e9a3-4f29-8d28-4906e16eef1b", "8d4fecbf-3e0d-4cbc-85b7-fabef8db210d", "b96dce2f-db24-49fd-bcaf-aa770e095aa8", "be6c9ac1-eba6-466e-8917-b836f88745e9", "0fa33a0c-9204-4167-9230-d03efa46ef6f", "ff372de6-f521-4d0b-93ed-37ea227fb20b", "becf9f10-36ea-4ef6-8170-ee13aa647ced", "6ffdb31a-7879-4750-bdc7-2f0208a3097f", "76793fe4-3727-42fb-abed-78d835d5b61d", "2ba7e421-ec55-4427-84ae-a718079bc59b", "1b786b94-f6fb-43f8-acce-220476a8f50d", "a30dd878-78ba-42d0-837f-b0b808a8a982", "d391ff79-2b25-4bc0-9f11-5583f708c8cf", "264764f3-b153-491b-a91f-a1c70bfab4d2", "3bd71475-d4e3-4291-9ddf-380b57a902eb", "8dc3473b-e3bc-44bb-9b1e-f1c94be5d2e4", "8acbd06b-e1f9-4696-b0d4-7c3d44dcad41", "74604a3b-3479-4f21-b156-d2b2a013fdfe", "5ce0dc12-b713-484c-b2b3-b1bce81fffd6", "61f6e77b-c58b-4081-86a8-add4664adb25"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Central Transport", "Geometry": {"BoundingBox": {"Width": 0.1152677908539772, "Height": 0.012035544030368328, "Left": 0.05695206671953201, "Top": 0.05596514791250229}, "Polygon": [{"X": 0.05695214495062828, "Y": 0.05596514791250229}, {"X": 0.17221985757350922, "Y": 0.05596515163779259}, {"X": 0.17221978306770325, "Y": 0.06800069659948349}, {"X": 0.05695206671953201, "Y": 0.0680006816983223}]}, "Id": "1993b420-7c75-429b-83aa-cedcf8727b85", "Relationships": [{"Type": "CHILD", "Ids": ["dd0e890f-ca9d-4675-9348-6f3dbef0161f", "dbfe88ae-a093-43bf-8e64-5c64ca2ed2c6"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "09/11/2025", "Geometry": {"BoundingBox": {"Width": 0.07392847537994385, "Height": 0.00983159989118576, "Left": 0.8653674125671387, "Top": 0.05613705515861511}, "Polygon": [{"X": 0.8653674721717834, "Y": 0.05613705515861511}, {"X": 0.9392958879470825, "Y": 0.05613705515861511}, {"X": 0.939295768737793, "Y": 0.06596865504980087}, {"X": 0.8653674125671387, "Y": 0.06596864759922028}]}, "Id": "b13edebc-c823-4766-ac1f-b6efe2a54b5c", "Relationships": [{"Type": "CHILD", "Ids": ["cbbaf161-c041-4d1b-ace1-68f3bed822ed"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "PRO#: 333467728", "Geometry": {"BoundingBox": {"Width": 0.12174462527036667, "Height": 0.00962787214666605, "Left": 0.05740179121494293, "Top": 0.0713518038392067}, "Polygon": [{"X": 0.057401854544878006, "Y": 0.0713518038392067}, {"X": 0.179146409034729, "Y": 0.07135181874036789}, {"X": 0.17914634943008423, "Y": 0.08097967505455017}, {"X": 0.05740179121494293, "Y": 0.08097965270280838}]}, "Id": "7261bf91-4170-4f8a-8ed1-c9f8b1eea6b0", "Relationships": [{"Type": "CHILD", "Ids": ["069fd921-d71a-44bb-8c89-e4d914fb18be", "056f2f71-5ba4-4bf7-acbe-2a9c0709dac3"]}]}, {"BlockType": "LINE", "Confidence": 99.66935729980469, "Text": "Weight & Inspection", "Geometry": {"BoundingBox": {"Width": 0.1313006579875946, "Height": 0.011971949599683285, "Left": 0.8078534603118896, "Top": 0.07120402157306671}, "Polygon": [{"X": 0.8078535795211792, "Y": 0.07120402157306671}, {"X": 0.9391541481018066, "Y": 0.0712040439248085}, {"X": 0.9391540288925171, "Y": 0.08317597210407257}, {"X": 0.8078534603118896, "Y": 0.08317594230175018}]}, "Id": "47889fe0-c1f9-477c-afdf-39995d2a5701", "Relationships": [{"Type": "CHILD", "Ids": ["9c4c83b3-9728-49b6-bd14-3edfb43e6f8c", "df9f1f18-75a1-4e83-aff1-1c639adf8a60", "243c8b50-4328-479e-b6d7-d51fa542a508"]}]}, {"BlockType": "LINE", "Confidence": 99.93708801269531, "Text": "Freight Class Certificate", "Geometry": {"BoundingBox": {"Width": 0.22567972540855408, "Height": 0.015263679437339306, "Left": 0.10531498491764069, "Top": 0.11901228129863739}, "Polygon": [{"X": 0.10531508922576904, "Y": 0.11901228129863739}, {"X": 0.33099472522735596, "Y": 0.11901238560676575}, {"X": 0.3309946060180664, "Y": 0.13427595794200897}, {"X": 0.10531498491764069, "Y": 0.13427582383155823}]}, "Id": "6b0e0c14-0ab9-480c-8880-1a5bb714dee5", "Relationships": [{"Type": "CHILD", "Ids": ["85bd1482-4ebe-4cb8-a9e0-442cb34d91f6", "3ef74add-1784-4243-930e-56f56ed32102", "b3c8ed3d-ac94-4bb5-8bb6-33f9bb3131d3"]}]}, {"BlockType": "LINE", "Confidence": 20.509706497192383, "Text": "H", "Geometry": {"BoundingBox": {"Width": 0.03562415763735771, "Height": 0.01114694681018591, "Left": 0.7024396657943726, "Top": 0.11773242056369781}, "Polygon": [{"X": 0.7024397253990173, "Y": 0.11773242056369781}, {"X": 0.7380638122558594, "Y": 0.11773243546485901}, {"X": 0.7380636930465698, "Y": 0.1288793683052063}, {"X": 0.7024396657943726, "Y": 0.1288793534040451}]}, "Id": "bdbf4b60-8144-4492-a3e0-513652c9e769", "Relationships": [{"Type": "CHILD", "Ids": ["18f1b3a7-86d5-4a17-abd7-dd6f9b2169d4"]}]}, {"BlockType": "LINE", "Confidence": 99.9755859375, "Text": "CENTRAL TRANSPORT", "Geometry": {"BoundingBox": {"Width": 0.13068844377994537, "Height": 0.009223283268511295, "Left": 0.7392116785049438, "Top": 0.11986402422189713}, "Polygon": [{"X": 0.7392117381095886, "Y": 0.11986402422189713}, {"X": 0.869900107383728, "Y": 0.1198640838265419}, {"X": 0.8699000477790833, "Y": 0.12908731400966644}, {"X": 0.7392116785049438, "Y": 0.12908723950386047}]}, "Id": "d6aee025-95a4-4304-a275-744a18aa5209", "Relationships": [{"Type": "CHILD", "Ids": ["e6026cf5-283c-4845-8aeb-fb6b66d613aa", "c098a598-7588-4dd8-84a4-59522bda3902"]}]}, {"BlockType": "LINE", "Confidence": 93.29407501220703, "Text": "Simple. Affordable. Reliable.", "Geometry": {"BoundingBox": {"Width": 0.15165145695209503, "Height": 0.0073478384874761105, "Left": 0.7174856662750244, "Top": 0.1304204910993576}, "Polygon": [{"X": 0.7174857258796692, "Y": 0.1304204910993576}, {"X": 0.869137167930603, "Y": 0.13042058050632477}, {"X": 0.8691371083259583, "Y": 0.13776832818984985}, {"X": 0.7174856662750244, "Y": 0.1377682387828827}]}, "Id": "7c473a6a-504a-43fa-bc9d-ac04f0d3928c", "Relationships": [{"Type": "CHILD", "Ids": ["0bcb4f0a-29c9-4692-b3ed-ac059203040b", "d7465d3f-4075-4f94-84ce-9812c5861c5e", "06f7db47-1f48-4063-b129-dbd0684ccc9a"]}]}, {"BlockType": "LINE", "Confidence": 99.90563201904297, "Text": "DATE: 9/4/2025", "Geometry": {"BoundingBox": {"Width": 0.13304851949214935, "Height": 0.010409236885607243, "Left": 0.10492746531963348, "Top": 0.15054762363433838}, "Polygon": [{"X": 0.10492753237485886, "Y": 0.15054762363433838}, {"X": 0.23797598481178284, "Y": 0.15054772794246674}, {"X": 0.23797592520713806, "Y": 0.16095685958862305}, {"X": 0.10492746531963348, "Y": 0.1609567552804947}]}, "Id": "cd502f9a-add5-4e1e-a689-413ec2e5dddc", "Relationships": [{"Type": "CHILD", "Ids": ["b41784c2-2772-4a4a-b4a6-b24c46fed9ea", "ec487bb1-482f-4ca3-82b6-d665429988f6"]}]}, {"BlockType": "LINE", "Confidence": 99.99349212646484, "Text": "PRO NUMBER: 333467728", "Geometry": {"BoundingBox": {"Width": 0.22464296221733093, "Height": 0.009727295488119125, "Left": 0.5012646913528442, "Top": 0.15051504969596863}, "Polygon": [{"X": 0.501264750957489, "Y": 0.15051504969596863}, {"X": 0.7259076237678528, "Y": 0.15051521360874176}, {"X": 0.725907564163208, "Y": 0.16024233400821686}, {"X": 0.5012646913528442, "Y": 0.16024215519428253}]}, "Id": "0e38ec58-c5e6-4160-a4b4-ac3ed0785989", "Relationships": [{"Type": "CHILD", "Ids": ["db59c46b-1407-4a07-bfa3-89ad493950c2", "042fe249-0892-44ee-ae3f-76256d939aaf", "514ee209-f9e4-41a5-8436-48822661b312"]}]}, {"BlockType": "LINE", "Confidence": 99.86776733398438, "Text": "PCS: 1", "Geometry": {"BoundingBox": {"Width": 0.06889920681715012, "Height": 0.009539088234305382, "Left": 0.5015285611152649, "Top": 0.16919778287410736}, "Polygon": [{"X": 0.5015286207199097, "Y": 0.16919778287410736}, {"X": 0.5704277157783508, "Y": 0.16919784247875214}, {"X": 0.570427656173706, "Y": 0.1787368655204773}, {"X": 0.5015285611152649, "Y": 0.17873680591583252}]}, "Id": "24bfb661-293a-4063-b8dc-a730d27b6399", "Relationships": [{"Type": "CHILD", "Ids": ["3227036b-8832-4330-8c15-cc77f42adde2", "ef6cf1af-fa1b-448f-b389-fcbf081f42dc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "WEIGHT: 258", "Geometry": {"BoundingBox": {"Width": 0.11821137368679047, "Height": 0.00966525450348854, "Left": 0.6460837721824646, "Top": 0.16906513273715973}, "Polygon": [{"X": 0.6460838317871094, "Y": 0.16906513273715973}, {"X": 0.7642951011657715, "Y": 0.16906523704528809}, {"X": 0.7642950415611267, "Y": 0.17873039841651917}, {"X": 0.6460837721824646, "Y": 0.17873027920722961}]}, "Id": "cd406238-fa60-4c87-92c4-499373efdaa8", "Relationships": [{"Type": "CHILD", "Ids": ["fa6d5ea3-1cf4-48e9-8a33-1e910ffa2183", "f98aa30d-7880-4447-93cf-3cd1b221fcee"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "PCS", "Geometry": {"BoundingBox": {"Width": 0.025304477661848068, "Height": 0.009142337366938591, "Left": 0.12018635123968124, "Top": 0.2044692039489746}, "Polygon": [{"X": 0.12018641084432602, "Y": 0.2044692039489746}, {"X": 0.145490825176239, "Y": 0.204469233751297}, {"X": 0.14549076557159424, "Y": 0.21361154317855835}, {"X": 0.12018635123968124, "Y": 0.21361151337623596}]}, "Id": "b7a5e51c-d69e-4f7f-8e96-de7f2ac0208f", "Relationships": [{"Type": "CHILD", "Ids": ["e42129da-8491-4d08-8a7b-095feb6c0c0e"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "LENGTH", "Geometry": {"BoundingBox": {"Width": 0.05518924817442894, "Height": 0.009179559536278248, "Left": 0.17731845378875732, "Top": 0.20444194972515106}, "Polygon": [{"X": 0.1773185133934021, "Y": 0.20444194972515106}, {"X": 0.23250770568847656, "Y": 0.20444200932979584}, {"X": 0.2325076311826706, "Y": 0.21362151205539703}, {"X": 0.17731845378875732, "Y": 0.21362143754959106}]}, "Id": "94924be7-7b39-4a80-a3b7-2acc1e35b641", "Relationships": [{"Type": "CHILD", "Ids": ["d18af7e2-e859-4a7e-81d2-5888f5583e8e"]}]}, {"BlockType": "LINE", "Confidence": 99.92107391357422, "Text": "WIDTH", "Geometry": {"BoundingBox": {"Width": 0.04962138086557388, "Height": 0.009267519228160381, "Left": 0.263185054063797, "Top": 0.20441551506519318}, "Polygon": [{"X": 0.2631851136684418, "Y": 0.20441551506519318}, {"X": 0.3128064274787903, "Y": 0.20441555976867676}, {"X": 0.3128063678741455, "Y": 0.21368302404880524}, {"X": 0.263185054063797, "Y": 0.21368296444416046}]}, "Id": "3faf208c-b024-4cb3-919a-f1db31923ae3", "Relationships": [{"Type": "CHILD", "Ids": ["a6a1a940-96d7-487d-a2bd-eb03e3024841"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "HEIGHT", "Geometry": {"BoundingBox": {"Width": 0.05327567085623741, "Height": 0.008920793421566486, "Left": 0.34610122442245483, "Top": 0.20454640686511993}, "Polygon": [{"X": 0.3461012840270996, "Y": 0.20454640686511993}, {"X": 0.39937689900398254, "Y": 0.2045464664697647}, {"X": 0.3993768095970154, "Y": 0.21346719563007355}, {"X": 0.34610122442245483, "Y": 0.21346713602542877}]}, "Id": "9a8aa558-75f0-463c-ae33-533a6eab7a99", "Relationships": [{"Type": "CHILD", "Ids": ["67702988-9521-4e74-85bf-7e94b4b55da2"]}]}, {"BlockType": "LINE", "Confidence": 99.68710327148438, "Text": "CUBIC FT.", "Geometry": {"BoundingBox": {"Width": 0.06744743138551712, "Height": 0.009071297943592072, "Left": 0.42208072543144226, "Top": 0.20393900573253632}, "Polygon": [{"X": 0.4220808148384094, "Y": 0.20393900573253632}, {"X": 0.48952817916870117, "Y": 0.20393908023834229}, {"X": 0.489528089761734, "Y": 0.2130102962255478}, {"X": 0.42208072543144226, "Y": 0.21301022171974182}]}, "Id": "31bd7418-7ed3-421a-946e-cc4bc5eafb85", "Relationships": [{"Type": "CHILD", "Ids": ["b8a49cc0-cfff-4945-a860-1be63281739b", "49f0db96-406b-4db8-ae70-33bd8435a5c3"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "PCS", "Geometry": {"BoundingBox": {"Width": 0.026000680401921272, "Height": 0.009130418300628662, "Left": 0.5161558389663696, "Top": 0.2044910341501236}, "Polygon": [{"X": 0.5161559581756592, "Y": 0.2044910341501236}, {"X": 0.5421565175056458, "Y": 0.20449106395244598}, {"X": 0.542156457901001, "Y": 0.21362145245075226}, {"X": 0.5161558389663696, "Y": 0.21362142264842987}]}, "Id": "07c84c93-e9a3-4f29-8d28-4906e16eef1b", "Relationships": [{"Type": "CHILD", "Ids": ["d42a4e23-b0fb-4a5b-b542-ce62291edd7f"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "LENGTH", "Geometry": {"BoundingBox": {"Width": 0.05558815598487854, "Height": 0.009279107674956322, "Left": 0.5738120079040527, "Top": 0.20439359545707703}, "Polygon": [{"X": 0.5738120675086975, "Y": 0.20439359545707703}, {"X": 0.6294001340866089, "Y": 0.2043936550617218}, {"X": 0.6294000744819641, "Y": 0.2136726975440979}, {"X": 0.5738120079040527, "Y": 0.21367263793945312}]}, "Id": "8d4fecbf-3e0d-4cbc-85b7-fabef8db210d", "Relationships": [{"Type": "CHILD", "Ids": ["e624ada6-4470-498b-8399-19a012e7ca93"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "WIDTH", "Geometry": {"BoundingBox": {"Width": 0.049718040972948074, "Height": 0.009183656424283981, "Left": 0.6605287790298462, "Top": 0.20442785322666168}, "Polygon": [{"X": 0.660528838634491, "Y": 0.20442785322666168}, {"X": 0.7102468013763428, "Y": 0.20442791283130646}, {"X": 0.710246741771698, "Y": 0.21361151337623596}, {"X": 0.6605287790298462, "Y": 0.2136114537715912}]}, "Id": "b96dce2f-db24-49fd-bcaf-aa770e095aa8", "Relationships": [{"Type": "CHILD", "Ids": ["25d53d4c-5b58-4f3e-8a7d-a4b1718e5a73"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "HEIGHT", "Geometry": {"BoundingBox": {"Width": 0.05343520641326904, "Height": 0.009186433628201485, "Left": 0.7431590557098389, "Top": 0.2044721096754074}, "Polygon": [{"X": 0.7431591749191284, "Y": 0.2044721096754074}, {"X": 0.7965942621231079, "Y": 0.20447216928005219}, {"X": 0.7965942025184631, "Y": 0.21365854144096375}, {"X": 0.7431590557098389, "Y": 0.21365848183631897}]}, "Id": "be6c9ac1-eba6-466e-8917-b836f88745e9", "Relationships": [{"Type": "CHILD", "Ids": ["ebbc7df6-6134-473e-a71b-d3d99dceffdd"]}]}, {"BlockType": "LINE", "Confidence": 99.78475952148438, "Text": "CUBIC FT.", "Geometry": {"BoundingBox": {"Width": 0.06753571331501007, "Height": 0.008937346749007702, "Left": 0.8190454244613647, "Top": 0.20396967232227325}, "Polygon": [{"X": 0.8190454840660095, "Y": 0.20396967232227325}, {"X": 0.8865811228752136, "Y": 0.20396974682807922}, {"X": 0.8865810632705688, "Y": 0.21290701627731323}, {"X": 0.8190454244613647, "Y": 0.21290694177150726}]}, "Id": "0fa33a0c-9204-4167-9230-d03efa46ef6f", "Relationships": [{"Type": "CHILD", "Ids": ["a325931a-ace6-4d73-a7b4-438131384777", "5881ed00-7c61-41db-b62c-23233e275364"]}]}, {"BlockType": "LINE", "Confidence": 99.46050262451172, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.007979909889400005, "Height": 0.009115271270275116, "Left": 0.15138088166713715, "Top": 0.22276099026203156}, "Polygon": [{"X": 0.15138094127178192, "Y": 0.22276099026203156}, {"X": 0.15936078131198883, "Y": 0.22276100516319275}, {"X": 0.15936072170734406, "Y": 0.23187626898288727}, {"X": 0.15138088166713715, "Y": 0.23187625408172607}]}, "Id": "ff372de6-f521-4d0b-93ed-37ea227fb20b", "Relationships": [{"Type": "CHILD", "Ids": ["45ef0a09-315a-410a-9b35-ac7428d2e555"]}]}, {"BlockType": "LINE", "Confidence": 99.88121795654297, "Text": "48", "Geometry": {"BoundingBox": {"Width": 0.018341289833188057, "Height": 0.009036527946591377, "Left": 0.2246977537870407, "Top": 0.22287967801094055}, "Polygon": [{"X": 0.22469781339168549, "Y": 0.22287967801094055}, {"X": 0.24303904175758362, "Y": 0.22287970781326294}, {"X": 0.24303898215293884, "Y": 0.23191620409488678}, {"X": 0.2246977537870407, "Y": 0.2319161742925644}]}, "Id": "becf9f10-36ea-4ef6-8170-ee13aa647ced", "Relationships": [{"Type": "CHILD", "Ids": ["fd584b20-9f64-4f9b-ad56-54d112b75ea7"]}]}, {"BlockType": "LINE", "Confidence": 99.84056091308594, "Text": "40", "Geometry": {"BoundingBox": {"Width": 0.018311206251382828, "Height": 0.00909879058599472, "Left": 0.3082852065563202, "Top": 0.22284206748008728}, "Polygon": [{"X": 0.30828526616096497, "Y": 0.22284206748008728}, {"X": 0.3265964090824127, "Y": 0.22284209728240967}, {"X": 0.32659634947776794, "Y": 0.2319408655166626}, {"X": 0.3082852065563202, "Y": 0.2319408357143402}]}, "Id": "6ffdb31a-7879-4750-bdc7-2f0208a3097f", "Relationships": [{"Type": "CHILD", "Ids": ["667bf0af-272c-44e4-af52-9fda06031333"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "68", "Geometry": {"BoundingBox": {"Width": 0.01791040226817131, "Height": 0.009093320928514004, "Left": 0.3924364745616913, "Top": 0.222781240940094}, "Polygon": [{"X": 0.39243653416633606, "Y": 0.222781240940094}, {"X": 0.4103468656539917, "Y": 0.22278127074241638}, {"X": 0.4103468060493469, "Y": 0.23187457025051117}, {"X": 0.3924364745616913, "Y": 0.23187454044818878}]}, "Id": "76793fe4-3727-42fb-abed-78d835d5b61d", "Relationships": [{"Type": "CHILD", "Ids": ["97534064-c012-4545-845b-67b403c4c62a"]}]}, {"BlockType": "LINE", "Confidence": 99.89178466796875, "Text": "75.56", "Geometry": {"BoundingBox": {"Width": 0.05056563392281532, "Height": 0.009573346935212612, "Left": 0.44390609860420227, "Top": 0.2226729542016983}, "Polygon": [{"X": 0.44390615820884705, "Y": 0.2226729542016983}, {"X": 0.4944717288017273, "Y": 0.22267301380634308}, {"X": 0.4944716691970825, "Y": 0.2322462946176529}, {"X": 0.44390609860420227, "Y": 0.23224623501300812}]}, "Id": "2ba7e421-ec55-4427-84ae-a718079bc59b", "Relationships": [{"Type": "CHILD", "Ids": ["bef6f9ed-8323-45e2-8b2a-8257695cd9af"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Total Cube:", "Geometry": {"BoundingBox": {"Width": 0.08554671704769135, "Height": 0.010194528847932816, "Left": 0.5004209876060486, "Top": 0.44538721442222595}, "Polygon": [{"X": 0.5004211068153381, "Y": 0.44538721442222595}, {"X": 0.5859677195549011, "Y": 0.44538745284080505}, {"X": 0.5859676599502563, "Y": 0.45558175444602966}, {"X": 0.5004209876060486, "Y": 0.4555814862251282}]}, "Id": "1b786b94-f6fb-43f8-acce-220476a8f50d", "Relationships": [{"Type": "CHILD", "Ids": ["bc50ca82-418e-4ee0-9e92-f2e7def3d3a3", "94f7655c-5a23-4670-a93d-1c9a03738a02"]}]}, {"BlockType": "LINE", "Confidence": 99.92107391357422, "Text": "75.56", "Geometry": {"BoundingBox": {"Width": 0.05023062974214554, "Height": 0.009511829353868961, "Left": 0.6349213719367981, "Top": 0.44435691833496094}, "Polygon": [{"X": 0.6349214315414429, "Y": 0.44435691833496094}, {"X": 0.685151994228363, "Y": 0.4443570375442505}, {"X": 0.6851518750190735, "Y": 0.4538687467575073}, {"X": 0.6349213719367981, "Y": 0.4538685977458954}]}, "Id": "a30dd878-78ba-42d0-837f-b0b808a8a982", "Relationships": [{"Type": "CHILD", "Ids": ["03127b52-14cb-400f-9479-2eceb4bda59a"]}]}, {"BlockType": "LINE", "Confidence": 99.59407806396484, "Text": "CORRECT TO CLASS: 250", "Geometry": {"BoundingBox": {"Width": 0.19874390959739685, "Height": 0.009949538856744766, "Left": 0.10434383898973465, "Top": 0.4629785120487213}, "Polygon": [{"X": 0.10434390604496002, "Y": 0.4629785120487213}, {"X": 0.3030877411365509, "Y": 0.46297910809516907}, {"X": 0.30308768153190613, "Y": 0.4729280471801758}, {"X": 0.10434383898973465, "Y": 0.47292742133140564}]}, "Id": "d391ff79-2b25-4bc0-9f11-5583f708c8cf", "Relationships": [{"Type": "CHILD", "Ids": ["3411532c-d188-4a62-bd53-1ed4c825eccf", "9a0bebf2-4378-4d2d-85e5-3f10fdad7cef", "aac3c330-8721-4d3a-aae7-c661e26ba4d5", "536c6a66-bd22-4dc2-98eb-9e8de5c67b94"]}]}, {"BlockType": "LINE", "Confidence": 99.90074920654297, "Text": "Density PCF:", "Geometry": {"BoundingBox": {"Width": 0.09240284562110901, "Height": 0.011997252702713013, "Left": 0.5012312531471252, "Top": 0.46287453174591064}, "Polygon": [{"X": 0.5012313723564148, "Y": 0.46287453174591064}, {"X": 0.5936341285705566, "Y": 0.46287479996681213}, {"X": 0.5936340093612671, "Y": 0.47487178444862366}, {"X": 0.5012312531471252, "Y": 0.4748714864253998}]}, "Id": "264764f3-b153-491b-a91f-a1c70bfab4d2", "Relationships": [{"Type": "CHILD", "Ids": ["9164d55c-94ab-40df-89a4-0b657968ac54", "032d5bec-b545-4c7f-a62f-c92820015b20"]}]}, {"BlockType": "LINE", "Confidence": 99.37420654296875, "Text": "3.41", "Geometry": {"BoundingBox": {"Width": 0.03914634883403778, "Height": 0.009273439645767212, "Left": 0.6349996328353882, "Top": 0.46365195512771606}, "Polygon": [{"X": 0.634999692440033, "Y": 0.46365195512771606}, {"X": 0.6741459965705872, "Y": 0.4636520743370056}, {"X": 0.6741459369659424, "Y": 0.4729253947734833}, {"X": 0.6349996328353882, "Y": 0.4729252755641937}]}, "Id": "3bd71475-d4e3-4291-9ddf-380b57a902eb", "Relationships": [{"Type": "CHILD", "Ids": ["a4e5a5f5-61b3-44b4-b1df-d19eb5bd37b7"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "REMARKS:", "Geometry": {"BoundingBox": {"Width": 0.07860101014375687, "Height": 0.009322216734290123, "Left": 0.10469871759414673, "Top": 0.4818367660045624}, "Polygon": [{"X": 0.1046987771987915, "Y": 0.4818367660045624}, {"X": 0.183299720287323, "Y": 0.48183703422546387}, {"X": 0.18329966068267822, "Y": 0.49115899205207825}, {"X": 0.10469871759414673, "Y": 0.49115872383117676}]}, "Id": "8dc3473b-e3bc-44bb-9b1e-f1c94be5d2e4", "Relationships": [{"Type": "CHILD", "Ids": ["404cfd06-f054-4d69-a087-70191157f74f"]}]}, {"BlockType": "LINE", "Confidence": 99.8445816040039, "Text": "CORRECT CLASS PER DEFAULT DENSITY RATING & DIMENSIONS OBTAINED AT PICK UP", "Geometry": {"BoundingBox": {"Width": 0.7563537955284119, "Height": 0.008942029438912868, "Left": 0.10374454408884048, "Top": 0.5020927786827087}, "Polygon": [{"X": 0.10374460369348526, "Y": 0.5020927786827087}, {"X": 0.8600983619689941, "Y": 0.5020952820777893}, {"X": 0.8600983023643494, "Y": 0.5110347867012024}, {"X": 0.10374454408884048, "Y": 0.511032223701477}]}, "Id": "8acbd06b-e1f9-4696-b0d4-7c3d44dcad41", "Relationships": [{"Type": "CHILD", "Ids": ["488e572b-eb15-4614-b4b6-bea4de70a9bb", "8b030fd3-578b-40b5-bce3-e6ca106ee815", "22cac7a6-4c34-4d80-b4eb-55a69d854b0f", "e5a207b4-f978-4df4-85c8-ad0d0326b433", "d4933768-f004-4eea-a1e9-b51d9fdd89f6", "149ca713-6333-4cb2-84d0-ea1153d1441d", "19461951-365d-4ce8-ba63-848a86e34027", "9dd6126c-314a-4c65-8e3e-d98bd4a51fc2", "acacc02f-5c56-4473-894d-a9266654fa96", "ee42544a-b9bd-46b4-bfd7-ba901f7983df", "40b1e241-afad-4dd9-be53-95feaea741fc", "59d6c7fd-82fd-422d-839f-d61d6578eacb"]}]}, {"BlockType": "LINE", "Confidence": 85.23590850830078, "Text": "Mileage = = 898.00", "Geometry": {"BoundingBox": {"Width": 0.16469141840934753, "Height": 0.011554524302482605, "Left": 0.10296478867530823, "Top": 0.5143582224845886}, "Polygon": [{"X": 0.1029648631811142, "Y": 0.5143582224845886}, {"X": 0.26765620708465576, "Y": 0.5143588185310364}, {"X": 0.2676561176776886, "Y": 0.5259127616882324}, {"X": 0.10296478867530823, "Y": 0.5259122252464294}]}, "Id": "74604a3b-3479-4f21-b156-d2b2a013fdfe", "Relationships": [{"Type": "CHILD", "Ids": ["0d2ab19a-8465-4d2e-8ad3-2e4a29bd2478", "80401657-5d50-4251-964e-ccbbb8bd9567", "4c87f585-62df-4d67-a419-271384523505"]}]}, {"BlockType": "LINE", "Confidence": 99.92107391357422, "Text": "FCCW: 15352309", "Geometry": {"BoundingBox": {"Width": 0.1427854299545288, "Height": 0.009590022265911102, "Left": 0.7366762161254883, "Top": 0.848368763923645}, "Polygon": [{"X": 0.7366763353347778, "Y": 0.848368763923645}, {"X": 0.8794616460800171, "Y": 0.8483696579933167}, {"X": 0.8794615864753723, "Y": 0.8579587936401367}, {"X": 0.7366762161254883, "Y": 0.8579579591751099}]}, "Id": "5ce0dc12-b713-484c-b2b3-b1bce81fffd6", "Relationships": [{"Type": "CHILD", "Ids": ["3ff516d0-38b6-4364-8e33-aa875eb1fe6e", "1b701625-4152-4b74-a95d-0fcdab26749c"]}]}, {"BlockType": "LINE", "Confidence": 99.81774139404297, "Text": "Page 3/3", "Geometry": {"BoundingBox": {"Width": 0.0520952083170414, "Height": 0.010967516340315342, "Left": 0.8171693682670593, "Top": 0.96134352684021}, "Polygon": [{"X": 0.8171694278717041, "Y": 0.96134352684021}, {"X": 0.869264543056488, "Y": 0.9613438844680786}, {"X": 0.8692644834518433, "Y": 0.9723110795021057}, {"X": 0.8171693682670593, "Y": 0.9723107218742371}]}, "Id": "61f6e77b-c58b-4081-86a8-add4664adb25", "Relationships": [{"Type": "CHILD", "Ids": ["14b421e2-ad6a-4b56-9d87-9a5100192018", "ac0cde4a-2644-43d0-b1a5-d56622dad21c"]}]}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Central", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04795985296368599, "Height": 0.009998388588428497, "Left": 0.05695208162069321, "Top": 0.05596514791250229}, "Polygon": [{"X": 0.05695214495062828, "Y": 0.05596514791250229}, {"X": 0.1049119308590889, "Y": 0.05596515163779259}, {"X": 0.10491186380386353, "Y": 0.06596353650093079}, {"X": 0.05695208162069321, "Y": 0.06596353650093079}]}, "Id": "dd0e890f-ca9d-4675-9348-6f3dbef0161f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Transport", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06414023041725159, "Height": 0.011780579574406147, "Left": 0.10807962715625763, "Top": 0.05622011423110962}, "Polygon": [{"X": 0.1080797091126442, "Y": 0.05622011423110962}, {"X": 0.17221985757350922, "Y": 0.05622011423110962}, {"X": 0.17221978306770325, "Y": 0.06800069659948349}, {"X": 0.10807962715625763, "Y": 0.0680006891489029}]}, "Id": "dbfe88ae-a093-43bf-8e64-5c64ca2ed2c6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "09/11/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07392847537994385, "Height": 0.00983159989118576, "Left": 0.8653674125671387, "Top": 0.05613705515861511}, "Polygon": [{"X": 0.8653674721717834, "Y": 0.05613705515861511}, {"X": 0.9392958879470825, "Y": 0.05613705515861511}, {"X": 0.939295768737793, "Y": 0.06596865504980087}, {"X": 0.8653674125671387, "Y": 0.06596864759922028}]}, "Id": "cbbaf161-c041-4d1b-ace1-68f3bed822ed"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "PRO#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04353134334087372, "Height": 0.009601536206901073, "Left": 0.05740179121494293, "Top": 0.0713518038392067}, "Polygon": [{"X": 0.057401854544878006, "Y": 0.0713518038392067}, {"X": 0.10093313455581665, "Y": 0.07135181128978729}, {"X": 0.10093307495117188, "Y": 0.08095334470272064}, {"X": 0.05740179121494293, "Y": 0.08095332980155945}]}, "Id": "069fd921-d71a-44bb-8c89-e4d914fb18be"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "333467728", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07384199649095535, "Height": 0.009395544417202473, "Left": 0.10530441254377365, "Top": 0.07158413529396057}, "Polygon": [{"X": 0.10530447214841843, "Y": 0.07158413529396057}, {"X": 0.179146409034729, "Y": 0.07158414274454117}, {"X": 0.17914634943008423, "Y": 0.08097967505455017}, {"X": 0.10530441254377365, "Y": 0.08097966015338898}]}, "Id": "056f2f71-5ba4-4bf7-acbe-2a9c0709dac3"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047338441014289856, "Height": 0.011971930973231792, "Left": 0.8078534603118896, "Top": 0.07120402157306671}, "Polygon": [{"X": 0.8078535795211792, "Y": 0.07120402157306671}, {"X": 0.8551918864250183, "Y": 0.07120402902364731}, {"X": 0.8551918268203735, "Y": 0.08317595720291138}, {"X": 0.8078534603118896, "Y": 0.08317594230175018}]}, "Id": "9c4c83b3-9728-49b6-bd14-3edfb43e6f8c"}, {"BlockType": "WORD", "Confidence": 99.345703125, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010766486637294292, "Height": 0.00950479693710804, "Left": 0.858883798122406, "Top": 0.07144937664270401}, "Polygon": [{"X": 0.8588838577270508, "Y": 0.07144937664270401}, {"X": 0.8696503043174744, "Y": 0.0714493840932846}, {"X": 0.8696502447128296, "Y": 0.0809541791677475}, {"X": 0.858883798122406, "Y": 0.0809541717171669}]}, "Id": "df9f1f18-75a1-4e83-aff1-1c639adf8a60"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06642972677946091, "Height": 0.011567450128495693, "Left": 0.8727244138717651, "Top": 0.07149267941713333}, "Polygon": [{"X": 0.8727244734764099, "Y": 0.07149267941713333}, {"X": 0.9391541481018066, "Y": 0.07149268686771393}, {"X": 0.9391540288925171, "Y": 0.0830601304769516}, {"X": 0.8727244138717651, "Y": 0.0830601155757904}]}, "Id": "243c8b50-4328-479e-b6d7-d51fa542a508"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Freight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06964773684740067, "Height": 0.01526358537375927, "Left": 0.10531498491764069, "Top": 0.11901228129863739}, "Polygon": [{"X": 0.10531508922576904, "Y": 0.11901228129863739}, {"X": 0.17496271431446075, "Y": 0.11901231110095978}, {"X": 0.1749626100063324, "Y": 0.1342758685350418}, {"X": 0.10531498491764069, "Y": 0.13427582383155823}]}, "Id": "85bd1482-4ebe-4cb8-a9e0-442cb34d91f6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Class", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04789445549249649, "Height": 0.012011123821139336, "Left": 0.17939336597919464, "Top": 0.11916538327932358}, "Polygon": [{"X": 0.1793934404850006, "Y": 0.11916538327932358}, {"X": 0.22728781402111053, "Y": 0.11916540563106537}, {"X": 0.22728773951530457, "Y": 0.13117650151252747}, {"X": 0.17939336597919464, "Y": 0.13117647171020508}]}, "Id": "3ef74add-1784-4243-930e-56f56ed32102"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Certificate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09866927564144135, "Height": 0.012011727318167686, "Left": 0.23232543468475342, "Top": 0.11909950524568558}, "Polygon": [{"X": 0.23232552409172058, "Y": 0.11909950524568558}, {"X": 0.33099472522735596, "Y": 0.11909955739974976}, {"X": 0.3309946358203888, "Y": 0.1311112344264984}, {"X": 0.23232543468475342, "Y": 0.13111117482185364}]}, "Id": "b3c8ed3d-ac94-4bb5-8bb6-33f9bb3131d3"}, {"BlockType": "WORD", "Confidence": 20.509706497192383, "Text": "H", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03562415763735771, "Height": 0.01114694681018591, "Left": 0.7024396657943726, "Top": 0.11773242056369781}, "Polygon": [{"X": 0.7380638122558594, "Y": 0.11773243546485901}, {"X": 0.7380636930465698, "Y": 0.1288793683052063}, {"X": 0.7024396657943726, "Y": 0.1288793534040451}, {"X": 0.7024397253990173, "Y": 0.11773242056369781}]}, "Id": "18f1b3a7-86d5-4a17-abd7-dd6f9b2169d4"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "CENTRAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054278336465358734, "Height": 0.009054278954863548, "Left": 0.7392116785049438, "Top": 0.12003298848867416}, "Polygon": [{"X": 0.7392117381095886, "Y": 0.12003298848867416}, {"X": 0.7934899926185608, "Y": 0.12003301084041595}, {"X": 0.793489933013916, "Y": 0.12908726930618286}, {"X": 0.7392116785049438, "Y": 0.12908723950386047}]}, "Id": "e6026cf5-283c-4845-8aeb-fb6b66d613aa"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "TRANSPORT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07245767116546631, "Height": 0.009214869700372219, "Left": 0.7974424362182617, "Top": 0.11986405402421951}, "Polygon": [{"X": 0.7974424958229065, "Y": 0.11986405402421951}, {"X": 0.869900107383728, "Y": 0.1198640838265419}, {"X": 0.8699000477790833, "Y": 0.1290789246559143}, {"X": 0.7974424362182617, "Y": 0.12907887995243073}]}, "Id": "c098a598-7588-4dd8-84a4-59522bda3902"}, {"BlockType": "WORD", "Confidence": 96.57206726074219, "Text": "Simple.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0381915420293808, "Height": 0.007254535332322121, "Left": 0.7174856662750244, "Top": 0.1305137276649475}, "Polygon": [{"X": 0.7174857258796692, "Y": 0.1305137276649475}, {"X": 0.7556772232055664, "Y": 0.1305137425661087}, {"X": 0.7556771636009216, "Y": 0.13776825368404388}, {"X": 0.7174856662750244, "Y": 0.1377682387828827}]}, "Id": "0bcb4f0a-29c9-4692-b3ed-ac059203040b"}, {"BlockType": "WORD", "Confidence": 88.49191284179688, "Text": "Affordable.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058804575353860855, "Height": 0.006123814266175032, "Left": 0.7599806189537048, "Top": 0.1304205060005188}, "Polygon": [{"X": 0.7599806785583496, "Y": 0.1304205060005188}, {"X": 0.8187851905822754, "Y": 0.13042055070400238}, {"X": 0.8187851309776306, "Y": 0.136544331908226}, {"X": 0.7599806189537048, "Y": 0.13654428720474243}]}, "Id": "d7465d3f-4075-4f94-84ce-9812c5861c5e"}, {"BlockType": "WORD", "Confidence": 94.8182373046875, "Text": "Reliable.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04578729346394539, "Height": 0.006212838925421238, "Left": 0.8233498334884644, "Top": 0.13045810163021088}, "Polygon": [{"X": 0.8233498930931091, "Y": 0.13045810163021088}, {"X": 0.869137167930603, "Y": 0.13045813143253326}, {"X": 0.8691371083259583, "Y": 0.13667093217372894}, {"X": 0.8233498334884644, "Y": 0.13667090237140656}]}, "Id": "06f7db47-1f48-4063-b129-dbd0684ccc9a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04426981136202812, "Height": 0.009514547884464264, "Left": 0.10492747277021408, "Top": 0.15057140588760376}, "Polygon": [{"X": 0.10492753237485886, "Y": 0.15057140588760376}, {"X": 0.1491972804069519, "Y": 0.15057143568992615}, {"X": 0.14919722080230713, "Y": 0.16008596122264862}, {"X": 0.10492747277021408, "Y": 0.16008591651916504}]}, "Id": "b41784c2-2772-4a4a-b4a6-b24c46fed9ea"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "9/4/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08082001656293869, "Height": 0.010409199632704258, "Left": 0.15715597569942474, "Top": 0.15054766833782196}, "Polygon": [{"X": 0.1571560502052307, "Y": 0.15054766833782196}, {"X": 0.23797598481178284, "Y": 0.15054772794246674}, {"X": 0.23797592520713806, "Y": 0.16095685958862305}, {"X": 0.15715597569942474, "Y": 0.16095679998397827}]}, "Id": "ec487bb1-482f-4ca3-82b6-d665429988f6"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "PRO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031438522040843964, "Height": 0.009678022935986519, "Left": 0.5012646913528442, "Top": 0.15051504969596863}, "Polygon": [{"X": 0.501264750957489, "Y": 0.15051504969596863}, {"X": 0.532703161239624, "Y": 0.15051506459712982}, {"X": 0.5327031016349792, "Y": 0.16019307076931}, {"X": 0.5012646913528442, "Y": 0.1601930409669876}]}, "Id": "db59c46b-1407-4a07-bfa3-89ad493950c2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NUMBER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0732349157333374, "Height": 0.009426834061741829, "Left": 0.5375746488571167, "Top": 0.15068058669567108}, "Polygon": [{"X": 0.5375747084617615, "Y": 0.15068058669567108}, {"X": 0.6108095645904541, "Y": 0.15068064630031586}, {"X": 0.6108095049858093, "Y": 0.16010743379592896}, {"X": 0.5375746488571167, "Y": 0.16010737419128418}]}, "Id": "042fe249-0892-44ee-ae3f-76256d939aaf"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "333467728", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09099726378917694, "Height": 0.009231791831552982, "Left": 0.6349103450775146, "Top": 0.15101054310798645}, "Polygon": [{"X": 0.6349104046821594, "Y": 0.15101054310798645}, {"X": 0.7259076237678528, "Y": 0.15101061761379242}, {"X": 0.725907564163208, "Y": 0.16024233400821686}, {"X": 0.6349103450775146, "Y": 0.16024227440357208}]}, "Id": "514ee209-f9e4-41a5-8436-48822661b312"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "PCS:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03172596916556358, "Height": 0.00950954295694828, "Left": 0.5015285611152649, "Top": 0.16922728717327118}, "Polygon": [{"X": 0.5015286207199097, "Y": 0.16922728717327118}, {"X": 0.5332545042037964, "Y": 0.16922731697559357}, {"X": 0.5332544445991516, "Y": 0.1787368357181549}, {"X": 0.5015285611152649, "Y": 0.17873680591583252}]}, "Id": "3227036b-8832-4330-8c15-cc77f42adde2"}, {"BlockType": "WORD", "Confidence": 99.77458953857422, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0077645364217460155, "Height": 0.009092330001294613, "Left": 0.562663197517395, "Top": 0.16919782757759094}, "Polygon": [{"X": 0.5626632571220398, "Y": 0.16919782757759094}, {"X": 0.5704277157783508, "Y": 0.16919784247875214}, {"X": 0.570427656173706, "Y": 0.17829015851020813}, {"X": 0.562663197517395, "Y": 0.17829015851020813}]}, "Id": "ef6cf1af-fa1b-448f-b389-fcbf081f42dc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WEIGHT:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06595709919929504, "Height": 0.00966520607471466, "Left": 0.6460837721824646, "Top": 0.16906513273715973}, "Polygon": [{"X": 0.6460838317871094, "Y": 0.16906513273715973}, {"X": 0.7120408415794373, "Y": 0.1690651923418045}, {"X": 0.7120407819747925, "Y": 0.1787303388118744}, {"X": 0.6460837721824646, "Y": 0.17873027920722961}]}, "Id": "fa6d5ea3-1cf4-48e9-8a33-1e910ffa2183"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "258", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029063787311315536, "Height": 0.009244340471923351, "Left": 0.735231339931488, "Top": 0.16911081969738007}, "Polygon": [{"X": 0.7352313995361328, "Y": 0.16911081969738007}, {"X": 0.7642951011657715, "Y": 0.16911084949970245}, {"X": 0.7642950415611267, "Y": 0.1783551573753357}, {"X": 0.735231339931488, "Y": 0.1783551275730133}]}, "Id": "f98aa30d-7880-4447-93cf-3cd1b221fcee"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "PCS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025304477661848068, "Height": 0.009142337366938591, "Left": 0.12018635123968124, "Top": 0.2044692039489746}, "Polygon": [{"X": 0.12018641084432602, "Y": 0.2044692039489746}, {"X": 0.145490825176239, "Y": 0.204469233751297}, {"X": 0.14549076557159424, "Y": 0.21361154317855835}, {"X": 0.12018635123968124, "Y": 0.21361151337623596}]}, "Id": "e42129da-8491-4d08-8a7b-095feb6c0c0e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LENGTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05518924817442894, "Height": 0.009179559536278248, "Left": 0.17731845378875732, "Top": 0.20444194972515106}, "Polygon": [{"X": 0.1773185133934021, "Y": 0.20444194972515106}, {"X": 0.23250770568847656, "Y": 0.20444200932979584}, {"X": 0.2325076311826706, "Y": 0.21362151205539703}, {"X": 0.17731845378875732, "Y": 0.21362143754959106}]}, "Id": "d18af7e2-e859-4a7e-81d2-5888f5583e8e"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "WIDTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04962138086557388, "Height": 0.009267519228160381, "Left": 0.263185054063797, "Top": 0.20441551506519318}, "Polygon": [{"X": 0.2631851136684418, "Y": 0.20441551506519318}, {"X": 0.3128064274787903, "Y": 0.20441555976867676}, {"X": 0.3128063678741455, "Y": 0.21368302404880524}, {"X": 0.263185054063797, "Y": 0.21368296444416046}]}, "Id": "a6a1a940-96d7-487d-a2bd-eb03e3024841"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "HEIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05327567085623741, "Height": 0.008920793421566486, "Left": 0.34610122442245483, "Top": 0.20454640686511993}, "Polygon": [{"X": 0.3461012840270996, "Y": 0.20454640686511993}, {"X": 0.39937689900398254, "Y": 0.2045464664697647}, {"X": 0.3993768095970154, "Y": 0.21346719563007355}, {"X": 0.34610122442245483, "Y": 0.21346713602542877}]}, "Id": "67702988-9521-4e74-85bf-7e94b4b55da2"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "CUBIC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043446023017168045, "Height": 0.009071269072592258, "Left": 0.42208072543144226, "Top": 0.20393900573253632}, "Polygon": [{"X": 0.4220808148384094, "Y": 0.20393900573253632}, {"X": 0.4655267596244812, "Y": 0.2039390504360199}, {"X": 0.4655267000198364, "Y": 0.2130102664232254}, {"X": 0.42208072543144226, "Y": 0.21301022171974182}]}, "Id": "b8a49cc0-cfff-4945-a860-1be63281739b"}, {"BlockType": "WORD", "Confidence": 99.63787078857422, "Text": "FT.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020412515848875046, "Height": 0.008997428230941296, "Left": 0.46911564469337463, "Top": 0.2039756029844284}, "Polygon": [{"X": 0.4691157042980194, "Y": 0.2039756029844284}, {"X": 0.48952817916870117, "Y": 0.2039756327867508}, {"X": 0.489528089761734, "Y": 0.21297304332256317}, {"X": 0.46911564469337463, "Y": 0.21297301352024078}]}, "Id": "49f0db96-406b-4db8-ae70-33bd8435a5c3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PCS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026000680401921272, "Height": 0.009130418300628662, "Left": 0.5161558389663696, "Top": 0.2044910341501236}, "Polygon": [{"X": 0.5161559581756592, "Y": 0.2044910341501236}, {"X": 0.5421565175056458, "Y": 0.20449106395244598}, {"X": 0.542156457901001, "Y": 0.21362145245075226}, {"X": 0.5161558389663696, "Y": 0.21362142264842987}]}, "Id": "d42a4e23-b0fb-4a5b-b542-ce62291edd7f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LENGTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05558815598487854, "Height": 0.009279107674956322, "Left": 0.5738120079040527, "Top": 0.20439359545707703}, "Polygon": [{"X": 0.5738120675086975, "Y": 0.20439359545707703}, {"X": 0.6294001340866089, "Y": 0.2043936550617218}, {"X": 0.6294000744819641, "Y": 0.2136726975440979}, {"X": 0.5738120079040527, "Y": 0.21367263793945312}]}, "Id": "e624ada6-4470-498b-8399-19a012e7ca93"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "WIDTH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049718040972948074, "Height": 0.009183656424283981, "Left": 0.6605287790298462, "Top": 0.20442785322666168}, "Polygon": [{"X": 0.660528838634491, "Y": 0.20442785322666168}, {"X": 0.7102468013763428, "Y": 0.20442791283130646}, {"X": 0.710246741771698, "Y": 0.21361151337623596}, {"X": 0.6605287790298462, "Y": 0.2136114537715912}]}, "Id": "25d53d4c-5b58-4f3e-8a7d-a4b1718e5a73"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "HEIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05343520641326904, "Height": 0.009186433628201485, "Left": 0.7431590557098389, "Top": 0.2044721096754074}, "Polygon": [{"X": 0.7431591749191284, "Y": 0.2044721096754074}, {"X": 0.7965942621231079, "Y": 0.20447216928005219}, {"X": 0.7965942025184631, "Y": 0.21365854144096375}, {"X": 0.7431590557098389, "Y": 0.21365848183631897}]}, "Id": "ebbc7df6-6134-473e-a71b-d3d99dceffdd"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "CUBIC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04335566610097885, "Height": 0.008937318809330463, "Left": 0.8190454244613647, "Top": 0.20396967232227325}, "Polygon": [{"X": 0.8190454840660095, "Y": 0.20396967232227325}, {"X": 0.8624010682106018, "Y": 0.20396971702575684}, {"X": 0.862401008605957, "Y": 0.21290698647499084}, {"X": 0.8190454244613647, "Y": 0.21290694177150726}]}, "Id": "a325931a-ace6-4d73-a7b4-438131384777"}, {"BlockType": "WORD", "Confidence": 99.69647216796875, "Text": "FT.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020353317260742188, "Height": 0.008803988806903362, "Left": 0.8662278056144714, "Top": 0.20406940579414368}, "Polygon": [{"X": 0.8662278652191162, "Y": 0.20406940579414368}, {"X": 0.8865811228752136, "Y": 0.20406942069530487}, {"X": 0.8865810632705688, "Y": 0.2128733992576599}, {"X": 0.8662278056144714, "Y": 0.21287336945533752}]}, "Id": "5881ed00-7c61-41db-b62c-23233e275364"}, {"BlockType": "WORD", "Confidence": 99.46050262451172, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007979909889400005, "Height": 0.009115271270275116, "Left": 0.15138088166713715, "Top": 0.22276099026203156}, "Polygon": [{"X": 0.15138094127178192, "Y": 0.22276099026203156}, {"X": 0.15936078131198883, "Y": 0.22276100516319275}, {"X": 0.15936072170734406, "Y": 0.23187626898288727}, {"X": 0.15138088166713715, "Y": 0.23187625408172607}]}, "Id": "45ef0a09-315a-410a-9b35-ac7428d2e555"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "48", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018341289833188057, "Height": 0.009036527946591377, "Left": 0.2246977537870407, "Top": 0.22287967801094055}, "Polygon": [{"X": 0.22469781339168549, "Y": 0.22287967801094055}, {"X": 0.24303904175758362, "Y": 0.22287970781326294}, {"X": 0.24303898215293884, "Y": 0.23191620409488678}, {"X": 0.2246977537870407, "Y": 0.2319161742925644}]}, "Id": "fd584b20-9f64-4f9b-ad56-54d112b75ea7"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018311206251382828, "Height": 0.00909879058599472, "Left": 0.3082852065563202, "Top": 0.22284206748008728}, "Polygon": [{"X": 0.30828526616096497, "Y": 0.22284206748008728}, {"X": 0.3265964090824127, "Y": 0.22284209728240967}, {"X": 0.32659634947776794, "Y": 0.2319408655166626}, {"X": 0.3082852065563202, "Y": 0.2319408357143402}]}, "Id": "667bf0af-272c-44e4-af52-9fda06031333"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "68", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01791040226817131, "Height": 0.009093320928514004, "Left": 0.3924364745616913, "Top": 0.222781240940094}, "Polygon": [{"X": 0.39243653416633606, "Y": 0.222781240940094}, {"X": 0.4103468656539917, "Y": 0.22278127074241638}, {"X": 0.4103468060493469, "Y": 0.23187457025051117}, {"X": 0.3924364745616913, "Y": 0.23187454044818878}]}, "Id": "97534064-c012-4545-845b-67b403c4c62a"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "75.56", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05056563392281532, "Height": 0.009573346935212612, "Left": 0.44390609860420227, "Top": 0.2226729542016983}, "Polygon": [{"X": 0.44390615820884705, "Y": 0.2226729542016983}, {"X": 0.4944717288017273, "Y": 0.22267301380634308}, {"X": 0.4944716691970825, "Y": 0.2322462946176529}, {"X": 0.44390609860420227, "Y": 0.23224623501300812}]}, "Id": "bef6f9ed-8323-45e2-8b2a-8257695cd9af"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038519956171512604, "Height": 0.00994637981057167, "Left": 0.5004209876060486, "Top": 0.4455810487270355}, "Polygon": [{"X": 0.5004211068153381, "Y": 0.4455810487270355}, {"X": 0.538940966129303, "Y": 0.4455811679363251}, {"X": 0.5389409065246582, "Y": 0.4555274248123169}, {"X": 0.5004209876060486, "Y": 0.45552730560302734}]}, "Id": "bc50ca82-418e-4ee0-9e92-f2e7def3d3a3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Cube:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04239191487431526, "Height": 0.010194403119385242, "Left": 0.5435758233070374, "Top": 0.4453873336315155}, "Polygon": [{"X": 0.5435758829116821, "Y": 0.4453873336315155}, {"X": 0.5859677195549011, "Y": 0.44538745284080505}, {"X": 0.5859676599502563, "Y": 0.45558175444602966}, {"X": 0.5435758233070374, "Y": 0.4555816352367401}]}, "Id": "94f7655c-5a23-4670-a93d-1c9a03738a02"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "75.56", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05023062974214554, "Height": 0.009511829353868961, "Left": 0.6349213719367981, "Top": 0.44435691833496094}, "Polygon": [{"X": 0.6349214315414429, "Y": 0.44435691833496094}, {"X": 0.685151994228363, "Y": 0.4443570375442505}, {"X": 0.6851518750190735, "Y": 0.4538687467575073}, {"X": 0.6349213719367981, "Y": 0.4538685977458954}]}, "Id": "03127b52-14cb-400f-9479-2eceb4bda59a"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CORRECT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07033875584602356, "Height": 0.009580405429005623, "Left": 0.10434383898973465, "Top": 0.4629785120487213}, "Polygon": [{"X": 0.10434390604496002, "Y": 0.4629785120487213}, {"X": 0.1746826022863388, "Y": 0.462978720664978}, {"X": 0.17468252778053284, "Y": 0.4725589156150818}, {"X": 0.10434383898973465, "Y": 0.4725586771965027}]}, "Id": "3411532c-d188-4a62-bd53-1ed4c825eccf"}, {"BlockType": "WORD", "Confidence": 98.40560913085938, "Text": "TO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021302776411175728, "Height": 0.009454199112951756, "Left": 0.17798158526420593, "Top": 0.46307939291000366}, "Polygon": [{"X": 0.1779816448688507, "Y": 0.46307939291000366}, {"X": 0.1992843598127365, "Y": 0.46307945251464844}, {"X": 0.19928430020809174, "Y": 0.47253358364105225}, {"X": 0.17798158526420593, "Y": 0.47253352403640747}]}, "Id": "9a0bebf2-4378-4d2d-85e5-3f10fdad7cef"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CLASS:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050392329692840576, "Height": 0.009441469796001911, "Left": 0.2038508802652359, "Top": 0.4631277024745941}, "Polygon": [{"X": 0.20385095477104187, "Y": 0.4631277024745941}, {"X": 0.25424322485923767, "Y": 0.46312785148620605}, {"X": 0.2542431354522705, "Y": 0.47256916761398315}, {"X": 0.2038508802652359, "Y": 0.4725690186023712}]}, "Id": "aac3c330-8721-4d3a-aae7-c661e26ba4d5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "250", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029381779953837395, "Height": 0.009097177535295486, "Left": 0.27370595932006836, "Top": 0.4638308584690094}, "Polygon": [{"X": 0.27370601892471313, "Y": 0.4638308584690094}, {"X": 0.3030877411365509, "Y": 0.46383094787597656}, {"X": 0.30308768153190613, "Y": 0.4729280471801758}, {"X": 0.27370595932006836, "Y": 0.4729279577732086}]}, "Id": "536c6a66-bd22-4dc2-98eb-9e8de5c67b94"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Density", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056950733065605164, "Height": 0.011997141875326633, "Left": 0.5012312531471252, "Top": 0.46287453174591064}, "Polygon": [{"X": 0.5012313723564148, "Y": 0.46287453174591064}, {"X": 0.5581820011138916, "Y": 0.4628746807575226}, {"X": 0.558181881904602, "Y": 0.4748716652393341}, {"X": 0.5012312531471252, "Y": 0.4748714864253998}]}, "Id": "9164d55c-94ab-40df-89a4-0b657968ac54"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "PCF:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03150828555226326, "Height": 0.009494351223111153, "Left": 0.5621258020401001, "Top": 0.46303585171699524}, "Polygon": [{"X": 0.5621259212493896, "Y": 0.46303585171699524}, {"X": 0.5936341285705566, "Y": 0.4630359411239624}, {"X": 0.5936340093612671, "Y": 0.47253021597862244}, {"X": 0.5621258020401001, "Y": 0.4725300967693329}]}, "Id": "032d5bec-b545-4c7f-a62f-c92820015b20"}, {"BlockType": "WORD", "Confidence": 99.37420654296875, "Text": "3.41", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03914634883403778, "Height": 0.009273439645767212, "Left": 0.6349996328353882, "Top": 0.46365195512771606}, "Polygon": [{"X": 0.634999692440033, "Y": 0.46365195512771606}, {"X": 0.6741459965705872, "Y": 0.4636520743370056}, {"X": 0.6741459369659424, "Y": 0.4729253947734833}, {"X": 0.6349996328353882, "Y": 0.4729252755641937}]}, "Id": "a4e5a5f5-61b3-44b4-b1df-d19eb5bd37b7"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "REMARKS:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07860101014375687, "Height": 0.009322216734290123, "Left": 0.10469871759414673, "Top": 0.4818367660045624}, "Polygon": [{"X": 0.1046987771987915, "Y": 0.4818367660045624}, {"X": 0.183299720287323, "Y": 0.48183703422546387}, {"X": 0.18329966068267822, "Y": 0.49115899205207825}, {"X": 0.10469871759414673, "Y": 0.49115872383117676}]}, "Id": "404cfd06-f054-4d69-a087-70191157f74f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CORRECT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07137658447027206, "Height": 0.008692923933267593, "Left": 0.10374454408884048, "Top": 0.5022498369216919}, "Polygon": [{"X": 0.10374459624290466, "Y": 0.5022498369216919}, {"X": 0.17512112855911255, "Y": 0.502250075340271}, {"X": 0.17512106895446777, "Y": 0.5109427571296692}, {"X": 0.10374454408884048, "Y": 0.5109425187110901}]}, "Id": "488e572b-eb15-4614-b4b6-bea4de70a9bb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CLASS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.050675369799137115, "Height": 0.008843055926263332, "Left": 0.18604741990566254, "Top": 0.5021525025367737}, "Polygon": [{"X": 0.1860474795103073, "Y": 0.5021525025367737}, {"X": 0.23672279715538025, "Y": 0.502152681350708}, {"X": 0.23672272264957428, "Y": 0.5109955668449402}, {"X": 0.18604741990566254, "Y": 0.5109953880310059}]}, "Id": "8b030fd3-578b-40b5-bce3-e6ca106ee815"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030521390959620476, "Height": 0.008853086270391941, "Left": 0.24889764189720154, "Top": 0.5021637678146362}, "Polygon": [{"X": 0.2488977015018463, "Y": 0.5021637678146362}, {"X": 0.27941903471946716, "Y": 0.502163827419281}, {"X": 0.2794189751148224, "Y": 0.511016845703125}, {"X": 0.24889764189720154, "Y": 0.5110167264938354}]}, "Id": "22cac7a6-4c34-4d80-b4eb-55a69d854b0f"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "DEFAULT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07194489240646362, "Height": 0.008803733624517918, "Left": 0.29021862149238586, "Top": 0.5021615028381348}, "Polygon": [{"X": 0.29021868109703064, "Y": 0.5021615028381348}, {"X": 0.3621635138988495, "Y": 0.5021617412567139}, {"X": 0.3621634542942047, "Y": 0.5109652280807495}, {"X": 0.29021862149238586, "Y": 0.5109649896621704}]}, "Id": "e5a207b4-f978-4df4-85c8-ad0d0326b433"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "DENSITY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07151822745800018, "Height": 0.008825310505926609, "Left": 0.37301191687583923, "Top": 0.502148449420929}, "Polygon": [{"X": 0.373011976480484, "Y": 0.502148449420929}, {"X": 0.4445301294326782, "Y": 0.5021486878395081}, {"X": 0.44453006982803345, "Y": 0.5109737515449524}, {"X": 0.37301191687583923, "Y": 0.5109735131263733}]}, "Id": "d4933768-f004-4eea-a1e9-b51d9fdd89f6"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "RATING", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06217070296406746, "Height": 0.008927247487008572, "Left": 0.4559295177459717, "Top": 0.5020939111709595}, "Polygon": [{"X": 0.45592960715293884, "Y": 0.5020939111709595}, {"X": 0.5181002616882324, "Y": 0.5020941495895386}, {"X": 0.5181001424789429, "Y": 0.5110211968421936}, {"X": 0.4559295177459717, "Y": 0.5110209584236145}]}, "Id": "149ca713-6333-4cb2-84d0-ea1153d1441d"}, {"BlockType": "WORD", "Confidence": 98.65972137451172, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007831797003746033, "Height": 0.007835106924176216, "Left": 0.5298142433166504, "Top": 0.5030677914619446}, "Polygon": [{"X": 0.5298143029212952, "Y": 0.5030677914619446}, {"X": 0.5376460552215576, "Y": 0.5030677914619446}, {"X": 0.5376459956169128, "Y": 0.5109028816223145}, {"X": 0.5298142433166504, "Y": 0.5109028220176697}]}, "Id": "19461951-365d-4ce8-ba63-848a86e34027"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "DIMENSIONS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10196076333522797, "Height": 0.00887516513466835, "Left": 0.5499444603919983, "Top": 0.5021589398384094}, "Polygon": [{"X": 0.5499445199966431, "Y": 0.5021589398384094}, {"X": 0.6519051790237427, "Y": 0.5021592378616333}, {"X": 0.6519051194190979, "Y": 0.5110340714454651}, {"X": 0.5499444603919983, "Y": 0.5110337138175964}]}, "Id": "9dd6126c-314a-4c65-8e3e-d98bd4a51fc2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "OBTAINED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08300315588712692, "Height": 0.008748804219067097, "Left": 0.6634860038757324, "Top": 0.5022082328796387}, "Polygon": [{"X": 0.6634860634803772, "Y": 0.5022082328796387}, {"X": 0.7464891672134399, "Y": 0.5022084712982178}, {"X": 0.7464891076087952, "Y": 0.5109570026397705}, {"X": 0.6634860038757324, "Y": 0.5109567642211914}]}, "Id": "acacc02f-5c56-4473-894d-a9266654fa96"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "AT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020555000752210617, "Height": 0.008659619837999344, "Left": 0.7564439177513123, "Top": 0.5022602677345276}, "Polygon": [{"X": 0.756443977355957, "Y": 0.5022602677345276}, {"X": 0.7769989371299744, "Y": 0.5022603273391724}, {"X": 0.7769988179206848, "Y": 0.5109198689460754}, {"X": 0.7564439177513123, "Y": 0.5109198093414307}]}, "Id": "ee42544a-b9bd-46b4-bfd7-ba901f7983df"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "PICK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04088004305958748, "Height": 0.008757665753364563, "Left": 0.7886013984680176, "Top": 0.5021665096282959}, "Polygon": [{"X": 0.7886014580726624, "Y": 0.5021665096282959}, {"X": 0.8294814229011536, "Y": 0.5021666288375854}, {"X": 0.8294813632965088, "Y": 0.5109241604804993}, {"X": 0.7886013984680176, "Y": 0.5109240412712097}]}, "Id": "40b1e241-afad-4dd9-be53-95feaea741fc"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020275481045246124, "Height": 0.008792197331786156, "Left": 0.8398228883743286, "Top": 0.5021519660949707}, "Polygon": [{"X": 0.8398229479789734, "Y": 0.5021519660949707}, {"X": 0.8600983619689941, "Y": 0.5021520256996155}, {"X": 0.8600983023643494, "Y": 0.5109441876411438}, {"X": 0.8398228883743286, "Y": 0.510944128036499}]}, "Id": "59d6c7fd-82fd-422d-839f-d61d6578eacb"}, {"BlockType": "WORD", "Confidence": 59.71240997314453, "Text": "Mileage =", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08635326474905014, "Height": 0.011504551395773888, "Left": 0.10296478867530823, "Top": 0.5144079327583313}, "Polygon": [{"X": 0.1029648631811142, "Y": 0.5144079327583313}, {"X": 0.18931806087493896, "Y": 0.5144082307815552}, {"X": 0.1893179714679718, "Y": 0.5259125232696533}, {"X": 0.10296478867530823, "Y": 0.5259122252464294}]}, "Id": "0d2ab19a-8465-4d2e-8ad3-2e4a29bd2478"}, {"BlockType": "WORD", "Confidence": 97.35272216796875, "Text": "=", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00971254426985979, "Height": 0.003711928380653262, "Left": 0.18607324361801147, "Top": 0.5176010727882385}, "Polygon": [{"X": 0.18607327342033386, "Y": 0.5176010727882385}, {"X": 0.195785790681839, "Y": 0.5176011323928833}, {"X": 0.1957857608795166, "Y": 0.5213130116462708}, {"X": 0.18607324361801147, "Y": 0.521312952041626}]}, "Id": "80401657-5d50-4251-964e-ccbbb8bd9567"}, {"BlockType": "WORD", "Confidence": 98.642578125, "Text": "898.00", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.059903889894485474, "Height": 0.009357751347124577, "Left": 0.2077523171901703, "Top": 0.5143585801124573}, "Polygon": [{"X": 0.20775237679481506, "Y": 0.5143585801124573}, {"X": 0.26765620708465576, "Y": 0.5143588185310364}, {"X": 0.267656147480011, "Y": 0.5237163305282593}, {"X": 0.2077523171901703, "Y": 0.523716151714325}]}, "Id": "4c87f585-62df-4d67-a419-271384523505"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "FCCW:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048180680721998215, "Height": 0.008986961096525192, "Left": 0.7366762161254883, "Top": 0.8489713072776794}, "Polygon": [{"X": 0.7366763353347778, "Y": 0.8489713072776794}, {"X": 0.784856915473938, "Y": 0.8489715456962585}, {"X": 0.7848568558692932, "Y": 0.8579582571983337}, {"X": 0.7366762161254883, "Y": 0.8579579591751099}]}, "Id": "3ff516d0-38b6-4364-8e33-aa875eb1fe6e"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "15352309", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08029888570308685, "Height": 0.009520621038973331, "Left": 0.7991628050804138, "Top": 0.8483691811561584}, "Polygon": [{"X": 0.7991628646850586, "Y": 0.8483691811561584}, {"X": 0.8794616460800171, "Y": 0.8483696579933167}, {"X": 0.8794615864753723, "Y": 0.8578897714614868}, {"X": 0.7991628050804138, "Y": 0.8578892946243286}]}, "Id": "1b701625-4152-4b74-a95d-0fcdab26749c"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "Page", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030556363984942436, "Height": 0.01067348849028349, "Left": 0.8171693682670593, "Top": 0.9616374373435974}, "Polygon": [{"X": 0.8171694278717041, "Y": 0.9616374373435974}, {"X": 0.8477257490158081, "Y": 0.9616376161575317}, {"X": 0.8477256298065186, "Y": 0.9723109006881714}, {"X": 0.8171693682670593, "Y": 0.9723107218742371}]}, "Id": "14b421e2-ad6a-4b56-9d87-9a5100192018"}, {"BlockType": "WORD", "Confidence": 99.72417449951172, "Text": "3/3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01875460334122181, "Height": 0.009069597348570824, "Left": 0.8505099415779114, "Top": 0.9613437652587891}, "Polygon": [{"X": 0.8505100607872009, "Y": 0.9613437652587891}, {"X": 0.869264543056488, "Y": 0.9613438844680786}, {"X": 0.8692644834518433, "Y": 0.9704133868217468}, {"X": 0.8505099415779114, "Y": 0.9704132080078125}]}, "Id": "ac0cde4a-2644-43d0-b1a5-d56622dad21c"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "67ad627d-2c65-4352-a2c9-173519b576e6", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "67ad627d-2c65-4352-a2c9-173519b576e6", "content-type": "application/x-amz-json-1.1", "content-length": "57898", "date": "Wed, 24 Sep 2025 09:15:40 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "954d2cd9-1fef-4d87-b5a0-72b619b2cceb", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 09:15:43 GMT", "content-type": "application/json", "content-length": "1521", "connection": "keep-alive", "x-amzn-requestid": "954d2cd9-1fef-4d87-b5a0-72b619b2cceb"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's only page1. We need to determine document type.\n\nThe page shows:\n\n- Header: \"Weight & Inspection Freight Class Certificate\"\n- \"Weight & Inspection\"\n- \"Freight Class Certificate\"\n- Detailed weight and dimensions, cube, density, etc.\n- \"CORRECT TO CLASS: 250\"\n- \"CORRECT CLASS PER DEFAULT DENSITY RATING & DIMENSIONS OBTAINED AT PICK UP\"\n- \"Mileage = 898.00\"\n- \"FCCW: 15352309\"\n\nIt seems like a weight and inspection certificate. It contains weight and dimensions, likely for freight classification. So likely \"weight_and_inspection_cert\".\n\nCheck if nmfc_cert? There's no mention of NMFC code or class #. \"CORRECT TO CLASS: 250\" that's the freight class. But no NMFC code. So should be weight_and_inspection_cert.\n\nThus output one object: page_no: 1, doc_type: \"weight_and_inspection_cert\".\n\nWe need to use tool classify_logistics_doc_type.\n\nLet's produce JSON array? The spec says return exactly one object describing that page. But tool expects array of documents. The input to function: documents: array of objects each with page_no and doc_type. So we pass array with one element.\n\nThus call function."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"weight_and_inspection_cert\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2634, "outputTokens": 291, "totalTokens": 2925}, "metrics": {"latencyMs": 1451}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/01c63dd5_IZTBXFPGXBFH3DV900G4.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nCentral Transport\n09/11/2025\nPRO#: 333467728\nWeight & Inspection\nFreight Class Certificate\nH\nCENTRAL TRANSPORT\nSimple. Affordable. Reliable.\nDATE: 9/4/2025\nPRO NUMBER: 333467728\nPCS: 1\nWEIGHT: 258\nPCS\nLENGTH\nWIDTH\nHEIGHT\nCUBIC FT.\nPCS\nLENGTH\nWIDTH\nHEIGHT\nCUBIC FT.\n1\n48\n40\n68\n75.56\nTotal Cube:\n75.56\nCORRECT TO CLASS: 250\nDensity PCF:\n3.41\nREMARKS:\nCORRECT CLASS PER DEFAULT DENSITY RATING & DIMENSIONS OBTAINED AT PICK UP\nMileage = = 898.00\nFCCW: 15352309\nPage 3/3\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}