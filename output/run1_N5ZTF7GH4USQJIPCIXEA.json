{"classification_result": {"documents": [{"page_no": 1, "doc_type": "po"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 4.152822441483295e-08}, {"X": 1.0, "Y": 1.0}, {"X": 4.059767988451313e-08, "Y": 1.0}]}, "Id": "a785fdfc-3ef8-4d22-b0c2-47b0e46508c1", "Relationships": [{"Type": "CHILD", "Ids": ["ba508243-ae18-4b95-a9a0-904448d3ff86", "320f702a-701d-465f-8a4f-a308fc5ecba4", "59c2b9fa-9232-4997-8f5b-76dae021d99d", "c4733734-b620-433e-826e-ed9f4c33e93a", "6dde2a25-8d50-457b-9dd2-b9984efdfc6b", "eb561597-8fde-401f-b574-75915616e484", "9e79768c-59a0-4427-a571-3f5200e3caa5", "8cf70dd2-58f5-4ebc-93cf-eea33280f571", "74184623-be4e-4c8d-be51-561727cca9e3", "c9e52da4-f15f-4101-b05c-b955ab347c8d", "4a4ab7de-af69-41c5-9299-b37747c97710", "18847834-7307-48ec-a2e4-8b982a9056e3", "f9654c3b-cc3f-46fc-b4f3-585c427af42f", "d55472c7-d8ed-43c7-903b-4ad01b92fa89", "a9534500-4dcf-47cb-b3c2-ef2cbb3527f6", "07d4122a-1df1-456c-b4bc-ed48a8fa84a5", "55e0b91d-f903-4b77-8d49-fefd0c27d383", "08e30e37-a4af-4885-91c0-0fb59144f4f4", "d396d2a7-ef88-4950-bbe3-efbff0d017e8", "8e5e3e5a-cd5e-4af3-9881-fbab21d96add", "595f7d0c-2a12-4365-8002-672f45011732", "95af79e3-f453-4cf2-bba3-354303a20c3f", "a5d09836-7ad5-4005-a03f-272a69c6a745"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "MRB Logistics, LLC", "Geometry": {"BoundingBox": {"Width": 0.17662590742111206, "Height": 0.014372901991009712, "Left": 0.06952004134654999, "Top": 0.05209829658269882}, "Polygon": [{"X": 0.06952004134654999, "Y": 0.05209829658269882}, {"X": 0.24614594876766205, "Y": 0.05209888890385628}, {"X": 0.24614596366882324, "Y": 0.06647119671106339}, {"X": 0.06952009350061417, "Y": 0.06647057086229324}]}, "Id": "ba508243-ae18-4b95-a9a0-904448d3ff86", "Relationships": [{"Type": "CHILD", "Ids": ["15f7025d-a61b-49db-91c7-f8de20ad3a92", "1fb3794b-fc9d-419a-8c29-a405c362aafc", "c6eea196-c826-4dad-b21b-879393796377"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Integris Lighting", "Geometry": {"BoundingBox": {"Width": 0.16946162283420563, "Height": 0.016677796840667725, "Left": 0.7981922626495361, "Top": 0.05258569121360779}, "Polygon": [{"X": 0.7981924414634705, "Y": 0.05258569121360779}, {"X": 0.9676539301872253, "Y": 0.05258626118302345}, {"X": 0.9676536917686462, "Y": 0.06926348805427551}, {"X": 0.7981922626495361, "Y": 0.06926288455724716}]}, "Id": "320f702a-701d-465f-8a4f-a308fc5ecba4", "Relationships": [{"Type": "CHILD", "Ids": ["bb23ec50-c5b0-49ca-84b8-329c83c8773b", "ded3fb86-a8ea-41c6-b772-92f0e4138f21"]}]}, {"BlockType": "LINE", "Confidence": 99.88058471679688, "Text": "620 S Belt Line Rd", "Geometry": {"BoundingBox": {"Width": 0.1289346069097519, "Height": 0.009800959378480911, "Left": 0.06909505277872086, "Top": 0.07568375766277313}, "Polygon": [{"X": 0.06909505277872086, "Y": 0.07568375766277313}, {"X": 0.19802965223789215, "Y": 0.07568423449993134}, {"X": 0.19802966713905334, "Y": 0.08548472076654434}, {"X": 0.06909508258104324, "Y": 0.08548422902822495}]}, "Id": "59c2b9fa-9232-4997-8f5b-76dae021d99d", "Relationships": [{"Type": "CHILD", "Ids": ["f1d5dd50-1ab2-44cc-bc4b-2401886375ee", "90f15b76-5311-4fcf-83d5-286b2fb928fd", "e4ebb4ff-842d-4f0b-af2d-376f185646d6", "09182e2c-d1f3-4637-9d8e-27f1ba324c93", "00204199-e350-4d4f-b86e-756a093c6227"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Purchase Order", "Geometry": {"BoundingBox": {"Width": 0.1316094547510147, "Height": 0.01171786617487669, "Left": 0.837257444858551, "Top": 0.07957417517900467}, "Polygon": [{"X": 0.8372575640678406, "Y": 0.07957417517900467}, {"X": 0.9688669443130493, "Y": 0.07957465946674347}, {"X": 0.968866765499115, "Y": 0.09129203855991364}, {"X": 0.837257444858551, "Y": 0.09129153192043304}]}, "Id": "c4733734-b620-433e-826e-ed9f4c33e93a", "Relationships": [{"Type": "CHILD", "Ids": ["7d381fc4-d58c-403e-8910-c748c2e724a9", "9ae0cff6-3aeb-4e32-bb97-390136ac0c47"]}]}, {"BlockType": "LINE", "Confidence": 99.94359588623047, "Text": "Irving TX 75060", "Geometry": {"BoundingBox": {"Width": 0.11337011307477951, "Height": 0.012100649997591972, "Left": 0.07005190849304199, "Top": 0.10688509792089462}, "Polygon": [{"X": 0.07005190849304199, "Y": 0.10688509792089462}, {"X": 0.1834220141172409, "Y": 0.10688555985689163}, {"X": 0.1834220290184021, "Y": 0.11898574233055115}, {"X": 0.07005195319652557, "Y": 0.11898526549339294}]}, "Id": "6dde2a25-8d50-457b-9dd2-b9984efdfc6b", "Relationships": [{"Type": "CHILD", "Ids": ["ef98bd9e-e2cf-4aca-888c-a1cff4bc27df", "5681bd2a-2179-4ab6-a7f1-24586311ecf3", "f6111d7a-73a3-4eab-9cf2-861b895c40e7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "44501", "Geometry": {"BoundingBox": {"Width": 0.05749541148543358, "Height": 0.01297085452824831, "Left": 0.9070103168487549, "Top": 0.10119577497243881}, "Polygon": [{"X": 0.9070104360580444, "Y": 0.10119577497243881}, {"X": 0.9645057320594788, "Y": 0.10119600594043732}, {"X": 0.9645055532455444, "Y": 0.11416663229465485}, {"X": 0.9070103168487549, "Y": 0.11416639387607574}]}, "Id": "eb561597-8fde-401f-b574-75915616e484", "Relationships": [{"Type": "CHILD", "Ids": ["f75f6c08-bc4f-4ca6-a2f2-68dcbb8199ec"]}]}, {"BlockType": "LINE", "Confidence": 99.8336410522461, "Text": "Terms: V Net 60", "Geometry": {"BoundingBox": {"Width": 0.1276843249797821, "Height": 0.009852087125182152, "Left": 0.0687548816204071, "Top": 0.12376299500465393}, "Polygon": [{"X": 0.0687548816204071, "Y": 0.12376299500465393}, {"X": 0.19643919169902802, "Y": 0.1237635463476181}, {"X": 0.19643919169902802, "Y": 0.13361507654190063}, {"X": 0.06875491142272949, "Y": 0.13361452519893646}]}, "Id": "9e79768c-59a0-4427-a571-3f5200e3caa5", "Relationships": [{"Type": "CHILD", "Ids": ["e89b1924-9abe-40c3-9a94-83ce4239b986", "e1427f83-7a05-453f-99a6-6b48e18bbd1f", "78b6e624-b227-4d6e-8d93-410df55a0205", "255aef7d-b391-4ac4-b161-e43c44f470cc"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.03244440257549286, "Height": 0.009833811782300472, "Left": 0.818942666053772, "Top": 0.12386162579059601}, "Polygon": [{"X": 0.8189427852630615, "Y": 0.12386162579059601}, {"X": 0.851387083530426, "Y": 0.12386176735162735}, {"X": 0.8513869643211365, "Y": 0.13369543850421906}, {"X": 0.818942666053772, "Y": 0.13369528949260712}]}, "Id": "8cf70dd2-58f5-4ebc-93cf-eea33280f571", "Relationships": [{"Type": "CHILD", "Ids": ["3d009b01-7132-43d3-939a-ac008c4aed88"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "09/22/25", "Geometry": {"BoundingBox": {"Width": 0.06055694445967674, "Height": 0.009717236272990704, "Left": 0.9081063866615295, "Top": 0.12390786409378052}, "Polygon": [{"X": 0.9081065058708191, "Y": 0.12390786409378052}, {"X": 0.9686633348464966, "Y": 0.12390812486410141}, {"X": 0.968663215637207, "Y": 0.1336251050233841}, {"X": 0.9081063866615295, "Y": 0.1336248368024826}]}, "Id": "74184623-be4e-4c8d-be51-561727cca9e3", "Relationships": [{"Type": "CHILD", "Ids": ["93ab50ec-40a0-4677-b609-6c674bb1c697"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "# Description", "Geometry": {"BoundingBox": {"Width": 0.11006943136453629, "Height": 0.011921562254428864, "Left": 0.06131520867347717, "Top": 0.21392247080802917}, "Polygon": [{"X": 0.06131520867347717, "Y": 0.21392247080802917}, {"X": 0.17138461768627167, "Y": 0.21392306685447693}, {"X": 0.17138463258743286, "Y": 0.22584402561187744}, {"X": 0.061315249651670456, "Y": 0.2258434146642685}]}, "Id": "c9e52da4-f15f-4101-b05c-b955ab347c8d", "Relationships": [{"Type": "CHILD", "Ids": ["ada4f988-c570-4caf-b822-13524a4eb3ca", "03491ac4-15d5-449f-a99f-01caab01aa2c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Price", "Geometry": {"BoundingBox": {"Width": 0.037085775285959244, "Height": 0.009657526388764381, "Left": 0.6918363571166992, "Top": 0.21383725106716156}, "Polygon": [{"X": 0.691836416721344, "Y": 0.21383725106716156}, {"X": 0.7289221286773682, "Y": 0.21383745968341827}, {"X": 0.7289220690727234, "Y": 0.2234947830438614}, {"X": 0.6918363571166992, "Y": 0.22349457442760468}]}, "Id": "4a4ab7de-af69-41c5-9299-b37747c97710", "Relationships": [{"Type": "CHILD", "Ids": ["db686457-01de-4f29-b0ab-ee5c34ea3baa"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.036739200353622437, "Height": 0.009809191338717937, "Left": 0.8679670691490173, "Top": 0.21377190947532654}, "Polygon": [{"X": 0.8679671883583069, "Y": 0.21377190947532654}, {"X": 0.9047062993049622, "Y": 0.21377211809158325}, {"X": 0.9047061800956726, "Y": 0.22358110547065735}, {"X": 0.8679670691490173, "Y": 0.22358089685440063}]}, "Id": "18847834-7307-48ec-a2e4-8b982a9056e3", "Relationships": [{"Type": "CHILD", "Ids": ["e986ac27-44f1-42ba-acb3-bbb6b70874d8"]}]}, {"BlockType": "LINE", "Confidence": 99.873046875, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.004661079030483961, "Height": 0.009204750880599022, "Left": 0.06267775595188141, "Top": 0.23781445622444153}, "Polygon": [{"X": 0.06267775595188141, "Y": 0.23781445622444153}, {"X": 0.06733880192041397, "Y": 0.23781448602676392}, {"X": 0.06733883172273636, "Y": 0.2470192164182663}, {"X": 0.0626777857542038, "Y": 0.2470191866159439}]}, "Id": "f9654c3b-cc3f-46fc-b4f3-585c427af42f", "Relationships": [{"Type": "CHILD", "Ids": ["8187d1d2-01ec-4656-817f-66e6a42952f9"]}]}, {"BlockType": "LINE", "Confidence": 99.26438903808594, "Text": "PS-INT2025-T-2884", "Geometry": {"BoundingBox": {"Width": 0.1408074051141739, "Height": 0.009879780933260918, "Left": 0.0865197703242302, "Top": 0.23745325207710266}, "Polygon": [{"X": 0.0865197703242302, "Y": 0.23745325207710266}, {"X": 0.2273271679878235, "Y": 0.23745405673980713}, {"X": 0.22732718288898468, "Y": 0.24733303487300873}, {"X": 0.08651980012655258, "Y": 0.24733220040798187}]}, "Id": "d55472c7-d8ed-43c7-903b-4ad01b92fa89", "Relationships": [{"Type": "CHILD", "Ids": ["0f0c856e-296a-47c0-be1e-0c9cd8af8ea8"]}]}, {"BlockType": "LINE", "Confidence": 91.94893646240234, "Text": "$ 624.00000", "Geometry": {"BoundingBox": {"Width": 0.08607494086027145, "Height": 0.011511018499732018, "Left": 0.7128667235374451, "Top": 0.23709988594055176}, "Polygon": [{"X": 0.7128668427467346, "Y": 0.23709988594055176}, {"X": 0.7989416718482971, "Y": 0.23710037767887115}, {"X": 0.7989415526390076, "Y": 0.24861089885234833}, {"X": 0.7128667235374451, "Y": 0.24861039221286774}]}, "Id": "a9534500-4dcf-47cb-b3c2-ef2cbb3527f6", "Relationships": [{"Type": "CHILD", "Ids": ["8d2a0bf1-e597-45d9-82cb-2b84b0d95c4d"]}]}, {"BlockType": "LINE", "Confidence": 88.12739562988281, "Text": "$ 624.00000", "Geometry": {"BoundingBox": {"Width": 0.0864727646112442, "Height": 0.01139231026172638, "Left": 0.881791889667511, "Top": 0.23715341091156006}, "Polygon": [{"X": 0.8817920088768005, "Y": 0.23715341091156006}, {"X": 0.968264639377594, "Y": 0.23715390264987946}, {"X": 0.9682645201683044, "Y": 0.24854572117328644}, {"X": 0.881791889667511, "Y": 0.24854521453380585}]}, "Id": "07d4122a-1df1-456c-b4bc-ed48a8fa84a5", "Relationships": [{"Type": "CHILD", "Ids": ["4cb90ad0-83e7-4c01-9b51-ec1a9236cc47"]}]}, {"BlockType": "LINE", "Confidence": 99.88932037353516, "Text": "1 Day LTL", "Geometry": {"BoundingBox": {"Width": 0.07062921673059464, "Height": 0.012169376946985722, "Left": 0.05436711385846138, "Top": 0.26895880699157715}, "Polygon": [{"X": 0.05436711385846138, "Y": 0.26895880699157715}, {"X": 0.12499629706144333, "Y": 0.2689592242240906}, {"X": 0.12499632686376572, "Y": 0.2811281681060791}, {"X": 0.05436715856194496, "Y": 0.2811277210712433}]}, "Id": "55e0b91d-f903-4b77-8d49-fefd0c27d383", "Relationships": [{"Type": "CHILD", "Ids": ["c0c52bbe-6e40-42c8-bdd9-f3dcf8c23fe3", "f05f93d6-e529-4c2d-ab82-0350de55d2a0", "fc9f1ef2-da27-4e3c-9823-57543b291458"]}]}, {"BlockType": "LINE", "Confidence": 99.45072937011719, "Text": "LAX-<PERSON><PERSON>", "Geometry": {"BoundingBox": {"Width": 0.10646981745958328, "Height": 0.009709703736007214, "Left": 0.054080963134765625, "Top": 0.28478386998176575}, "Polygon": [{"X": 0.054080963134765625, "Y": 0.28478386998176575}, {"X": 0.1605507731437683, "Y": 0.2847845256328583}, {"X": 0.1605507880449295, "Y": 0.29449355602264404}, {"X": 0.05408100038766861, "Y": 0.2944928705692291}]}, "Id": "08e30e37-a4af-4885-91c0-0fb59144f4f4", "Relationships": [{"Type": "CHILD", "Ids": ["5120a6c9-0f22-4ca3-bb26-5b690a90d577"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.0367107130587101, "Height": 0.009904111735522747, "Left": 0.6054815649986267, "Top": 0.29830440878868103}, "Polygon": [{"X": 0.6054816246032715, "Y": 0.29830440878868103}, {"X": 0.6421922445297241, "Y": 0.29830464720726013}, {"X": 0.6421921849250793, "Y": 0.30820849537849426}, {"X": 0.6054815649986267, "Y": 0.30820825695991516}]}, "Id": "d396d2a7-ef88-4950-bbe3-efbff0d017e8", "Relationships": [{"Type": "CHILD", "Ids": ["773e6dae-3b25-45d1-864c-5f714ae117d4"]}]}, {"BlockType": "LINE", "Confidence": 89.6436538696289, "Text": "$ 624.00000", "Geometry": {"BoundingBox": {"Width": 0.08621582388877869, "Height": 0.01136825606226921, "Left": 0.8816708326339722, "Top": 0.2977845072746277}, "Polygon": [{"X": 0.8816709518432617, "Y": 0.2977845072746277}, {"X": 0.9678866863250732, "Y": 0.29778507351875305}, {"X": 0.9678865075111389, "Y": 0.309152752161026}, {"X": 0.8816708326339722, "Y": 0.30915218591690063}]}, "Id": "8e5e3e5a-cd5e-4af3-9881-fbab21d96add", "Relationships": [{"Type": "CHILD", "Ids": ["2c69ca3b-28d0-454c-9937-f57e5eb4e854"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Page", "Geometry": {"BoundingBox": {"Width": 0.034737151116132736, "Height": 0.011954426765441895, "Left": 0.8081532716751099, "Top": 0.8821245431900024}, "Polygon": [{"X": 0.8081533908843994, "Y": 0.8821245431900024}, {"X": 0.8428904414176941, "Y": 0.8821250796318054}, {"X": 0.8428903222084045, "Y": 0.8940789699554443}, {"X": 0.8081532716751099, "Y": 0.8940784931182861}]}, "Id": "595f7d0c-2a12-4365-8002-672f45011732", "Relationships": [{"Type": "CHILD", "Ids": ["1ce44d01-02d4-4c1c-b67d-0bf95e7cf380"]}]}, {"BlockType": "LINE", "Confidence": 99.35061645507812, "Text": "1 / 1", "Geometry": {"BoundingBox": {"Width": 0.054762136191129684, "Height": 0.014648335985839367, "Left": 0.8929982781410217, "Top": 0.881847620010376}, "Polygon": [{"X": 0.892998456954956, "Y": 0.881847620010376}, {"X": 0.9477604031562805, "Y": 0.8818483948707581}, {"X": 0.9477602243423462, "Y": 0.8964959383010864}, {"X": 0.8929982781410217, "Y": 0.8964951634407043}]}, "Id": "95af79e3-f453-4cf2-bba3-354303a20c3f", "Relationships": [{"Type": "CHILD", "Ids": ["6aab1f12-9f37-47f1-acb2-ef02999f7896", "50702148-d498-4217-bf44-32d9e3c5f821", "41236e7a-90a4-4785-8a26-a8cdbaa30716"]}]}, {"BlockType": "LINE", "Confidence": 99.99003601074219, "Text": "Created By: <PERSON>", "Geometry": {"BoundingBox": {"Width": 0.19217130541801453, "Height": 0.012439604848623276, "Left": 0.08243472874164581, "Top": 0.9061403274536133}, "Polygon": [{"X": 0.08243472874164581, "Y": 0.9061403274536133}, {"X": 0.27460601925849915, "Y": 0.9061431288719177}, {"X": 0.27460601925849915, "Y": 0.9185799360275269}, {"X": 0.0824347659945488, "Y": 0.9185771346092224}]}, "Id": "a5d09836-7ad5-4005-a03f-272a69c6a745", "Relationships": [{"Type": "CHILD", "Ids": ["4c2b1f02-7a50-406d-a74e-fb79d17a0221", "ce119337-7e2c-48eb-bfdb-a29e34b78557", "73284ebc-630f-40bf-beba-fc6978a762ba", "ad33104e-04f7-4eae-b781-2476927e2bd2"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MRB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04114783555269241, "Height": 0.011276520788669586, "Left": 0.06952004134654999, "Top": 0.052195917814970016}, "Polygon": [{"X": 0.06952004134654999, "Y": 0.052195917814970016}, {"X": 0.11066784709692001, "Y": 0.05219605565071106}, {"X": 0.110667884349823, "Y": 0.0634724348783493}, {"X": 0.06952008605003357, "Y": 0.06347229331731796}]}, "Id": "15f7025d-a61b-49db-91c7-f8de20ad3a92"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Logistics,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08623550087213516, "Height": 0.014372589997947216, "Left": 0.11760592460632324, "Top": 0.052098456770181656}, "Polygon": [{"X": 0.11760592460632324, "Y": 0.052098456770181656}, {"X": 0.2038414180278778, "Y": 0.052098747342824936}, {"X": 0.203841432929039, "Y": 0.06647104769945145}, {"X": 0.11760596185922623, "Y": 0.06647074222564697}]}, "Id": "1fb3794b-fc9d-419a-8c29-a405c362aafc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03505390137434006, "Height": 0.011382886208593845, "Left": 0.2110920548439026, "Top": 0.05210902914404869}, "Polygon": [{"X": 0.2110920548439026, "Y": 0.05210902914404869}, {"X": 0.24614594876766205, "Y": 0.05210914835333824}, {"X": 0.24614596366882324, "Y": 0.06349191814661026}, {"X": 0.21109206974506378, "Y": 0.06349179148674011}]}, "Id": "c6eea196-c826-4dad-b21b-879393796377"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Integris", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07821988314390182, "Height": 0.01661679521203041, "Left": 0.7981922626495361, "Top": 0.052614305168390274}, "Polygon": [{"X": 0.7981924414634705, "Y": 0.052614305168390274}, {"X": 0.8764121532440186, "Y": 0.052614569664001465}, {"X": 0.8764119744300842, "Y": 0.06923110038042068}, {"X": 0.7981922626495361, "Y": 0.069230817258358}]}, "Id": "bb23ec50-c5b0-49ca-84b8-329c83c8773b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Lighting", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08468230068683624, "Height": 0.016677511855959892, "Left": 0.8829715847969055, "Top": 0.05258597433567047}, "Polygon": [{"X": 0.8829717636108398, "Y": 0.05258597433567047}, {"X": 0.9676539301872253, "Y": 0.05258626118302345}, {"X": 0.9676536917686462, "Y": 0.06926348805427551}, {"X": 0.8829715847969055, "Y": 0.06926318258047104}]}, "Id": "ded3fb86-a8ea-41c6-b772-92f0e4138f21"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "620", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02525533363223076, "Height": 0.009558583609759808, "Left": 0.06909505277872086, "Top": 0.0758558064699173}, "Polygon": [{"X": 0.06909505277872086, "Y": 0.0758558064699173}, {"X": 0.09435035288333893, "Y": 0.07585589587688446}, {"X": 0.09435038268566132, "Y": 0.08541438728570938}, {"X": 0.06909508258104324, "Y": 0.08541429042816162}]}, "Id": "f1d5dd50-1ab2-44cc-bc4b-2401886375ee"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "S", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009455087594687939, "Height": 0.009651745669543743, "Left": 0.09952929615974426, "Top": 0.07568645477294922}, "Polygon": [{"X": 0.09952929615974426, "Y": 0.07568645477294922}, {"X": 0.10898435115814209, "Y": 0.0756864845752716}, {"X": 0.10898438096046448, "Y": 0.08533819764852524}, {"X": 0.09952931851148605, "Y": 0.08533816039562225}]}, "Id": "90f15b76-5311-4fcf-83d5-286b2fb928fd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Belt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026339568197727203, "Height": 0.009798835963010788, "Left": 0.11452654749155045, "Top": 0.07568392902612686}, "Polygon": [{"X": 0.11452654749155045, "Y": 0.07568392902612686}, {"X": 0.14086610078811646, "Y": 0.07568402588367462}, {"X": 0.14086611568927765, "Y": 0.08548276126384735}, {"X": 0.11452657729387283, "Y": 0.08548266440629959}]}, "Id": "e4ebb4ff-842d-4f0b-af2d-376f185646d6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Line", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.028352851048111916, "Height": 0.009705288335680962, "Left": 0.14565104246139526, "Top": 0.07569129019975662}, "Polygon": [{"X": 0.14565104246139526, "Y": 0.07569129019975662}, {"X": 0.17400386929512024, "Y": 0.07569139450788498}, {"X": 0.17400388419628143, "Y": 0.08539657294750214}, {"X": 0.14565105736255646, "Y": 0.08539646863937378}]}, "Id": "09182e2c-d1f3-4637-9d8e-27f1ba324c93"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Rd", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018444806337356567, "Height": 0.009770087897777557, "Left": 0.17958486080169678, "Top": 0.07571463286876678}, "Polygon": [{"X": 0.17958486080169678, "Y": 0.07571463286876678}, {"X": 0.19802965223789215, "Y": 0.07571469992399216}, {"X": 0.19802966713905334, "Y": 0.08548472076654434}, {"X": 0.17958487570285797, "Y": 0.08548465371131897}]}, "Id": "00204199-e350-4d4f-b86e-756a093c6227"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07743191719055176, "Height": 0.011604761704802513, "Left": 0.837257444858551, "Top": 0.07968706637620926}, "Polygon": [{"X": 0.8372575640678406, "Y": 0.07968706637620926}, {"X": 0.9146893620491028, "Y": 0.07968735694885254}, {"X": 0.9146892428398132, "Y": 0.09129182994365692}, {"X": 0.837257444858551, "Y": 0.09129153192043304}]}, "Id": "7d381fc4-d58c-403e-8910-c748c2e724a9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04815039411187172, "Height": 0.011708937585353851, "Left": 0.9207165241241455, "Top": 0.07957448065280914}, "Polygon": [{"X": 0.9207166433334351, "Y": 0.07957448065280914}, {"X": 0.9688669443130493, "Y": 0.07957465946674347}, {"X": 0.968866765499115, "Y": 0.091283418238163}, {"X": 0.9207165241241455, "Y": 0.09128323197364807}]}, "Id": "9ae0cff6-3aeb-4e32-bb97-390136ac0c47"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036501139402389526, "Height": 0.012030364014208317, "Left": 0.07005190849304199, "Top": 0.10695505887269974}, "Polygon": [{"X": 0.07005190849304199, "Y": 0.10695505887269974}, {"X": 0.10655301809310913, "Y": 0.10695520788431168}, {"X": 0.10655304789543152, "Y": 0.11898542195558548}, {"X": 0.07005195319652557, "Y": 0.11898526549339294}]}, "Id": "ef98bd9e-e2cf-4aca-888c-a1cff4bc27df"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "TX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019984493032097816, "Height": 0.009608704596757889, "Left": 0.11153022944927216, "Top": 0.10688526183366776}, "Polygon": [{"X": 0.11153022944927216, "Y": 0.10688526183366776}, {"X": 0.13151469826698303, "Y": 0.10688534379005432}, {"X": 0.13151472806930542, "Y": 0.11649397015571594}, {"X": 0.11153025180101395, "Y": 0.11649388074874878}]}, "Id": "5681bd2a-2179-4ab6-a7f1-24586311ecf3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "75060", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042606763541698456, "Height": 0.009434363804757595, "Left": 0.14081525802612305, "Top": 0.10712803900241852}, "Polygon": [{"X": 0.14081525802612305, "Y": 0.10712803900241852}, {"X": 0.1834220141172409, "Y": 0.10712821781635284}, {"X": 0.1834220290184021, "Y": 0.11656240373849869}, {"X": 0.14081527292728424, "Y": 0.11656222492456436}]}, "Id": "f6111d7a-73a3-4eab-9cf2-861b895c40e7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "44501", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05749541148543358, "Height": 0.01297085452824831, "Left": 0.9070103168487549, "Top": 0.10119577497243881}, "Polygon": [{"X": 0.9070104360580444, "Y": 0.10119577497243881}, {"X": 0.9645057320594788, "Y": 0.10119600594043732}, {"X": 0.9645055532455444, "Y": 0.11416663229465485}, {"X": 0.9070103168487549, "Y": 0.11416639387607574}]}, "Id": "f75f6c08-bc4f-4ca6-a2f2-68dcbb8199ec"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Terms:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04881138727068901, "Height": 0.00952536053955555, "Left": 0.0687548816204071, "Top": 0.12396541237831116}, "Polygon": [{"X": 0.0687548816204071, "Y": 0.12396541237831116}, {"X": 0.11756624281406403, "Y": 0.12396562099456787}, {"X": 0.11756626516580582, "Y": 0.13349077105522156}, {"X": 0.06875491142272949, "Y": 0.13349054753780365}]}, "Id": "e89b1924-9abe-40c3-9a94-83ce4239b986"}, {"BlockType": "WORD", "Confidence": 99.43299102783203, "Text": "V", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010329606011509895, "Height": 0.009642946533858776, "Left": 0.13610784709453583, "Top": 0.12376328557729721}, "Polygon": [{"X": 0.13610784709453583, "Y": 0.12376328557729721}, {"X": 0.14643742144107819, "Y": 0.12376333028078079}, {"X": 0.14643745124340057, "Y": 0.13340623676776886}, {"X": 0.13610786199569702, "Y": 0.13340619206428528}]}, "Id": "e1427f83-7a05-453f-99a6-6b48e18bbd1f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023804571479558945, "Height": 0.009711157530546188, "Left": 0.15140485763549805, "Top": 0.12390383332967758}, "Polygon": [{"X": 0.15140485763549805, "Y": 0.12390383332967758}, {"X": 0.1752094179391861, "Y": 0.12390393763780594}, {"X": 0.1752094328403473, "Y": 0.13361498713493347}, {"X": 0.15140487253665924, "Y": 0.13361488282680511}]}, "Id": "78b6e624-b227-4d6e-8d93-410df55a0205"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "60", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01682082749903202, "Height": 0.009489738382399082, "Left": 0.17961837351322174, "Top": 0.1240481585264206}, "Polygon": [{"X": 0.17961837351322174, "Y": 0.1240481585264206}, {"X": 0.19643919169902802, "Y": 0.12404823303222656}, {"X": 0.19643919169902802, "Y": 0.1335379034280777}, {"X": 0.17961838841438293, "Y": 0.13353782892227173}]}, "Id": "255aef7d-b391-4ac4-b161-e43c44f470cc"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03244440257549286, "Height": 0.009833811782300472, "Left": 0.818942666053772, "Top": 0.12386162579059601}, "Polygon": [{"X": 0.8189427852630615, "Y": 0.12386162579059601}, {"X": 0.851387083530426, "Y": 0.12386176735162735}, {"X": 0.8513869643211365, "Y": 0.13369543850421906}, {"X": 0.818942666053772, "Y": 0.13369528949260712}]}, "Id": "3d009b01-7132-43d3-939a-ac008c4aed88"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "09/22/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06055694445967674, "Height": 0.009717236272990704, "Left": 0.9081063866615295, "Top": 0.12390786409378052}, "Polygon": [{"X": 0.9081065058708191, "Y": 0.12390786409378052}, {"X": 0.9686633348464966, "Y": 0.12390812486410141}, {"X": 0.968663215637207, "Y": 0.1336251050233841}, {"X": 0.9081063866615295, "Y": 0.1336248368024826}]}, "Id": "93ab50ec-40a0-4677-b609-6c674bb1c697"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008926678448915482, "Height": 0.009571810252964497, "Left": 0.06131520867347717, "Top": 0.21392247080802917}, "Polygon": [{"X": 0.06131520867347717, "Y": 0.21392247080802917}, {"X": 0.07024185359477997, "Y": 0.21392251551151276}, {"X": 0.07024188339710236, "Y": 0.2234942764043808}, {"X": 0.06131524220108986, "Y": 0.22349423170089722}]}, "Id": "ada4f988-c570-4caf-b822-13524a4eb3ca"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Description", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08464311063289642, "Height": 0.011891118250787258, "Left": 0.08674152940511703, "Top": 0.2139529138803482}, "Polygon": [{"X": 0.08674152940511703, "Y": 0.2139529138803482}, {"X": 0.17138461768627167, "Y": 0.21395337581634521}, {"X": 0.17138463258743286, "Y": 0.22584402561187744}, {"X": 0.08674156665802002, "Y": 0.22584354877471924}]}, "Id": "03491ac4-15d5-449f-a99f-01caab01aa2c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037085775285959244, "Height": 0.009657526388764381, "Left": 0.6918363571166992, "Top": 0.21383725106716156}, "Polygon": [{"X": 0.691836416721344, "Y": 0.21383725106716156}, {"X": 0.7289221286773682, "Y": 0.21383745968341827}, {"X": 0.7289220690727234, "Y": 0.2234947830438614}, {"X": 0.6918363571166992, "Y": 0.22349457442760468}]}, "Id": "db686457-01de-4f29-b0ab-ee5c34ea3baa"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036739200353622437, "Height": 0.009809191338717937, "Left": 0.8679670691490173, "Top": 0.21377190947532654}, "Polygon": [{"X": 0.8679671883583069, "Y": 0.21377190947532654}, {"X": 0.9047062993049622, "Y": 0.21377211809158325}, {"X": 0.9047061800956726, "Y": 0.22358110547065735}, {"X": 0.8679670691490173, "Y": 0.22358089685440063}]}, "Id": "e986ac27-44f1-42ba-acb3-bbb6b70874d8"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004661079030483961, "Height": 0.009204750880599022, "Left": 0.06267775595188141, "Top": 0.23781445622444153}, "Polygon": [{"X": 0.06267775595188141, "Y": 0.23781445622444153}, {"X": 0.06733880192041397, "Y": 0.23781448602676392}, {"X": 0.06733883172273636, "Y": 0.2470192164182663}, {"X": 0.0626777857542038, "Y": 0.2470191866159439}]}, "Id": "8187d1d2-01ec-4656-817f-66e6a42952f9"}, {"BlockType": "WORD", "Confidence": 99.26438903808594, "Text": "PS-INT2025-T-2884", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1408074051141739, "Height": 0.009879780933260918, "Left": 0.0865197703242302, "Top": 0.23745325207710266}, "Polygon": [{"X": 0.0865197703242302, "Y": 0.23745325207710266}, {"X": 0.2273271679878235, "Y": 0.23745405673980713}, {"X": 0.22732718288898468, "Y": 0.24733303487300873}, {"X": 0.08651980012655258, "Y": 0.24733220040798187}]}, "Id": "0f0c856e-296a-47c0-be1e-0c9cd8af8ea8"}, {"BlockType": "WORD", "Confidence": 91.94893646240234, "Text": "$ 624.00000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08607494086027145, "Height": 0.011511018499732018, "Left": 0.7128667235374451, "Top": 0.23709988594055176}, "Polygon": [{"X": 0.7128668427467346, "Y": 0.23709988594055176}, {"X": 0.7989416718482971, "Y": 0.23710037767887115}, {"X": 0.7989415526390076, "Y": 0.24861089885234833}, {"X": 0.7128667235374451, "Y": 0.24861039221286774}]}, "Id": "8d2a0bf1-e597-45d9-82cb-2b84b0d95c4d"}, {"BlockType": "WORD", "Confidence": 88.12739562988281, "Text": "$ 624.00000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0864727646112442, "Height": 0.01139231026172638, "Left": 0.881791889667511, "Top": 0.23715341091156006}, "Polygon": [{"X": 0.8817920088768005, "Y": 0.23715341091156006}, {"X": 0.968264639377594, "Y": 0.23715390264987946}, {"X": 0.9682645201683044, "Y": 0.24854572117328644}, {"X": 0.881791889667511, "Y": 0.24854521453380585}]}, "Id": "4cb90ad0-83e7-4c01-9b51-ec1a9236cc47"}, {"BlockType": "WORD", "Confidence": 99.697265625, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004697942640632391, "Height": 0.009206084534525871, "Left": 0.05436711758375168, "Top": 0.2692490220069885}, "Polygon": [{"X": 0.05436711758375168, "Y": 0.2692490220069885}, {"X": 0.05906502529978752, "Y": 0.2692490518093109}, {"X": 0.05906505882740021, "Y": 0.27845510840415955}, {"X": 0.054367151111364365, "Y": 0.27845507860183716}]}, "Id": "c0c52bbe-6e40-42c8-bdd9-f3dcf8c23fe3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Day", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027023643255233765, "Height": 0.012052444741129875, "Left": 0.06687995791435242, "Top": 0.26907554268836975}, "Polygon": [{"X": 0.06687995791435242, "Y": 0.26907554268836975}, {"X": 0.0939035713672638, "Y": 0.2690756916999817}, {"X": 0.09390360116958618, "Y": 0.2811279892921448}, {"X": 0.066880002617836, "Y": 0.28112781047821045}]}, "Id": "f05f93d6-e529-4c2d-ab82-0350de55d2a0"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LTL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02609592117369175, "Height": 0.009738492779433727, "Left": 0.09890040010213852, "Top": 0.26895907521247864}, "Polygon": [{"X": 0.09890040010213852, "Y": 0.26895907521247864}, {"X": 0.12499629706144333, "Y": 0.2689592242240906}, {"X": 0.12499631941318512, "Y": 0.27869758009910583}, {"X": 0.09890042990446091, "Y": 0.2786974012851715}]}, "Id": "fc9f1ef2-da27-4e3c-9823-57543b291458"}, {"BlockType": "WORD", "Confidence": 99.45072937011719, "Text": "LAX-<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10646981745958328, "Height": 0.009709703736007214, "Left": 0.054080963134765625, "Top": 0.28478386998176575}, "Polygon": [{"X": 0.054080963134765625, "Y": 0.28478386998176575}, {"X": 0.1605507731437683, "Y": 0.2847845256328583}, {"X": 0.1605507880449295, "Y": 0.29449355602264404}, {"X": 0.05408100038766861, "Y": 0.2944928705692291}]}, "Id": "5120a6c9-0f22-4ca3-bb26-5b690a90d577"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0367107130587101, "Height": 0.009904111735522747, "Left": 0.6054815649986267, "Top": 0.29830440878868103}, "Polygon": [{"X": 0.6054816246032715, "Y": 0.29830440878868103}, {"X": 0.6421922445297241, "Y": 0.29830464720726013}, {"X": 0.6421921849250793, "Y": 0.30820849537849426}, {"X": 0.6054815649986267, "Y": 0.30820825695991516}]}, "Id": "773e6dae-3b25-45d1-864c-5f714ae117d4"}, {"BlockType": "WORD", "Confidence": 89.6436538696289, "Text": "$ 624.00000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08621582388877869, "Height": 0.01136825606226921, "Left": 0.8816708326339722, "Top": 0.2977845072746277}, "Polygon": [{"X": 0.8816709518432617, "Y": 0.2977845072746277}, {"X": 0.9678866863250732, "Y": 0.29778507351875305}, {"X": 0.9678865075111389, "Y": 0.309152752161026}, {"X": 0.8816708326339722, "Y": 0.30915218591690063}]}, "Id": "2c69ca3b-28d0-454c-9937-f57e5eb4e854"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Page", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034737151116132736, "Height": 0.011954426765441895, "Left": 0.8081532716751099, "Top": 0.8821245431900024}, "Polygon": [{"X": 0.8081533908843994, "Y": 0.8821245431900024}, {"X": 0.8428904414176941, "Y": 0.8821250796318054}, {"X": 0.8428903222084045, "Y": 0.8940789699554443}, {"X": 0.8081532716751099, "Y": 0.8940784931182861}]}, "Id": "1ce44d01-02d4-4c1c-b67d-0bf95e7cf380"}, {"BlockType": "WORD", "Confidence": 99.326171875, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004728925414383411, "Height": 0.009338960982859135, "Left": 0.8929983377456665, "Top": 0.881847620010376}, "Polygon": [{"X": 0.892998456954956, "Y": 0.881847620010376}, {"X": 0.8977272510528564, "Y": 0.8818476796150208}, {"X": 0.8977271318435669, "Y": 0.891186535358429}, {"X": 0.8929983377456665, "Y": 0.8911864757537842}]}, "Id": "6aab1f12-9f37-47f1-acb2-ef02999f7896"}, {"BlockType": "WORD", "Confidence": 99.3213882446289, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.005502783227711916, "Height": 0.009946680627763271, "Left": 0.9239775538444519, "Top": 0.8865489959716797}, "Polygon": [{"X": 0.9239776730537415, "Y": 0.8865489959716797}, {"X": 0.9294803142547607, "Y": 0.8865490555763245}, {"X": 0.9294801950454712, "Y": 0.8964956402778625}, {"X": 0.9239775538444519, "Y": 0.8964955806732178}]}, "Id": "50702148-d498-4217-bf44-32d9e3c5f821"}, {"BlockType": "WORD", "Confidence": 99.404296875, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00485169468447566, "Height": 0.009170617908239365, "Left": 0.9429087042808533, "Top": 0.8821667432785034}, "Polygon": [{"X": 0.9429088234901428, "Y": 0.8821667432785034}, {"X": 0.9477604031562805, "Y": 0.882166862487793}, {"X": 0.947760283946991, "Y": 0.8913373947143555}, {"X": 0.9429087042808533, "Y": 0.8913373351097107}]}, "Id": "41236e7a-90a4-4785-8a26-a8cdbaa30716"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Created", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05470871552824974, "Height": 0.00965129118412733, "Left": 0.08243472874164581, "Top": 0.9061403274536133}, "Polygon": [{"X": 0.08243472874164581, "Y": 0.9061403274536133}, {"X": 0.13714341819286346, "Y": 0.9061411619186401}, {"X": 0.13714344799518585, "Y": 0.9157916307449341}, {"X": 0.0824347585439682, "Y": 0.915790855884552}]}, "Id": "4c2b1f02-7a50-406d-a74e-fb79d17a0221"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "By:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02137078158557415, "Height": 0.012234004214406013, "Left": 0.14241638779640198, "Top": 0.9063443541526794}, "Polygon": [{"X": 0.14241638779640198, "Y": 0.9063443541526794}, {"X": 0.16378715634346008, "Y": 0.9063446521759033}, {"X": 0.16378717124462128, "Y": 0.9185783267021179}, {"X": 0.14241641759872437, "Y": 0.918578028678894}]}, "Id": "ce119337-7e2c-48eb-bfdb-a29e34b78557"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03801864758133888, "Height": 0.009378183633089066, "Left": 0.17119929194450378, "Top": 0.9062064290046692}, "Polygon": [{"X": 0.17119929194450378, "Y": 0.9062064290046692}, {"X": 0.20921792089939117, "Y": 0.9062069654464722}, {"X": 0.20921793580055237, "Y": 0.9155846238136292}, {"X": 0.17119930684566498, "Y": 0.9155840873718262}]}, "Id": "73284ebc-630f-40bf-beba-fc6978a762ba"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06056200712919235, "Height": 0.009497697465121746, "Left": 0.2140440195798874, "Top": 0.9062657356262207}, "Polygon": [{"X": 0.2140440195798874, "Y": 0.9062657356262207}, {"X": 0.27460601925849915, "Y": 0.9062666296958923}, {"X": 0.27460601925849915, "Y": 0.9157634377479553}, {"X": 0.21404403448104858, "Y": 0.9157625436782837}]}, "Id": "ad33104e-04f7-4eae-b781-2476927e2bd2"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "70aa42fb-e215-442f-9ac4-86e897adbe75", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "70aa42fb-e215-442f-9ac4-86e897adbe75", "content-type": "application/x-amz-json-1.1", "content-length": "35387", "date": "Wed, 24 Sep 2025 09:00:14 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "266a887f-3a34-4d18-aeac-084f6ee3f67f", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 09:00:16 GMT", "content-type": "application/json", "content-length": "639", "connection": "keep-alive", "x-amzn-requestid": "266a887f-3a34-4d18-aeac-084f6ee3f67f"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. We have only page1. It's a purchase order. Keywords: \"Purchase Order\". So doc_type is \"po\". No continuation. So output array with one object: {page_no:1, doc_type:\"po\"}. Use tool classify_logistics_doc_type."}}}, {"toolUse": {"toolUseId": "tooluse_o3W88N1FRWS-3GKVdpu0lg", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "po"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2570, "outputTokens": 78, "totalTokens": 2648}, "metrics": {"latencyMs": 552}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/29a65973_N5ZTF7GH4USQJIPCIXEA.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nMRB Logistics, LLC\nIntegris Lighting\n620 S Belt Line Rd\nPurchase Order\nIrving TX 75060\n44501\nTerms: V Net 60\nDate\n09/22/25\n# Description\nPrice\nTotal\n1\nPS-INT2025-T-2884\n$ 624.00000\n$ 624.00000\n1 Day LTL\nLAX-Scottsdale\nTotal\n$ 624.00000\nPage\n1 / 1\nCreated By: <PERSON>\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}