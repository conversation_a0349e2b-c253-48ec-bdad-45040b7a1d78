{"classification_result": {"documents": [{"page_no": 1, "doc_type": "po"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 4.3079435130266575e-08}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 4.5622932987043896e-08, "Y": 1.0}]}, "Id": "75a27652-4f82-4860-8825-347478abb384", "Relationships": [{"Type": "CHILD", "Ids": ["9b79b87c-99b5-4e9a-b370-839afe2f5977", "93e77fad-3b68-4ed0-9bda-3756571aec0b", "7c7e0e6e-7bef-4260-8445-b1c516f34591", "52620608-99a4-4cbd-b141-babaf7220d17", "dd762572-b181-4ebf-92e9-2e0db19941c4", "97b82954-75b6-4239-a67f-1af0f8307846", "ba5d1f05-b89d-44e2-bf2b-6b801e7e3be4", "e235adc1-ac89-4d69-8606-abe4ed784ad9", "1a45fcd6-6104-41f0-967a-f408c5ade91f", "54aa6fe1-728f-4a74-b6e7-25a3cf4d8908", "844e9d3e-6939-4a3a-b4b0-9706b9f842ea", "87b079ca-b6f9-4cd5-911d-6f57d1870526", "e5000b8e-60d4-4e36-aed5-2631b8806eda", "2f6beaa6-6d1a-408a-a730-761ec3964a10", "5dbe6b4b-e0da-46fd-947e-ca10d304af98", "0f695915-76f6-47a6-a1e3-058d86f2a700", "41c4dadc-9237-4d9c-b33c-0b76c6edb487", "cafba16a-b42c-455d-a217-69bd06df5ea2", "2588acde-ac33-4944-b599-60441964c671", "7ea5d5f9-10d8-4a19-af00-35174c2a11c0", "3b855ecf-2997-4897-99df-34664ea6a44e", "73e540f1-46c7-4819-bd43-69f59cb48e9f", "8066a97d-3578-453c-9a47-9d2e23eae9fe", "ac62f366-c7ea-4897-bdf9-b49c1785322e", "8adac5cf-723c-4af1-be67-c2dd96090bfa", "57f76c5a-f00f-49d8-8937-c30023d77e85", "b1a3b971-fc29-43e5-a7fb-f3ec9a47dbe8", "839190e5-162b-42fb-82fc-49ff9d9c5a37", "443aa928-6eb3-404c-8cd0-deac4da29ede", "33d4f6a6-e5cf-45d1-bb37-a2d11a44d869", "5ff2fb4a-5606-4096-a161-447290b502ac", "6854b3c6-2d75-49db-bc21-01f6b9732169", "24dd585a-704c-4181-9706-71aec7f73586", "2a79ee4c-360c-4799-8236-8d2dd7083897", "14a9178e-8d67-4ae9-834a-08566918c495", "fe2a3544-f478-47fd-9140-a52bb23c8e84", "a74c8e86-d943-4fef-956c-2c720b05d815", "f3788c4b-f1b5-436c-ac07-955d415b36d4", "4dddc628-2153-4b5e-a141-cb8c96630a3e", "dd7bd307-4b22-4637-b0f8-74242727c5e8", "92aceea3-c3c7-413f-a407-60813020f919"]}]}, {"BlockType": "LINE", "Confidence": 99.76593017578125, "Text": "Cox Industrial Equipment, Inc.", "Geometry": {"BoundingBox": {"Width": 0.3101713955402374, "Height": 0.014696154743432999, "Left": 0.23314613103866577, "Top": 0.03953803703188896}, "Polygon": [{"X": 0.23314613103866577, "Y": 0.03953884541988373}, {"X": 0.5433174967765808, "Y": 0.03953803703188896}, {"X": 0.5433175563812256, "Y": 0.05423341691493988}, {"X": 0.23314616084098816, "Y": 0.05423419177532196}]}, "Id": "9b79b87c-99b5-4e9a-b370-839afe2f5977", "Relationships": [{"Type": "CHILD", "Ids": ["82c468aa-bc20-4af0-b01c-90e24413b1d5", "952ce86a-c904-4e13-b621-e341a56903b2", "44b6a481-0e2a-4d2d-a45c-e88c5f244101", "f7264e3b-863c-423e-9d12-24d7446fd809"]}]}, {"BlockType": "LINE", "Confidence": 83.30516815185547, "Text": "COX", "Geometry": {"BoundingBox": {"Width": 0.07932434976100922, "Height": 0.020324241369962692, "Left": 0.09038420766592026, "Top": 0.04915319010615349}, "Polygon": [{"X": 0.09038420766592026, "Y": 0.049153391271829605}, {"X": 0.1697085052728653, "Y": 0.04915319010615349}, {"X": 0.16970854997634888, "Y": 0.06947724521160126}, {"X": 0.09038424491882324, "Y": 0.06947743147611618}]}, "Id": "93e77fad-3b68-4ed0-9bda-3756571aec0b", "Relationships": [{"Type": "CHILD", "Ids": ["6f2a8434-47d6-4269-bff7-05a21beb6af8"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "PURCHASE ORDER", "Geometry": {"BoundingBox": {"Width": 0.25996434688568115, "Height": 0.014127202332019806, "Left": 0.6127174496650696, "Top": 0.044121816754341125}, "Polygon": [{"X": 0.6127174496650696, "Y": 0.04412248730659485}, {"X": 0.872681736946106, "Y": 0.044121816754341125}, {"X": 0.8726817965507507, "Y": 0.058248382061719894}, {"X": 0.6127175092697144, "Y": 0.05824901908636093}]}, "Id": "7c7e0e6e-7bef-4260-8445-b1c516f34591", "Relationships": [{"Type": "CHILD", "Ids": ["fd90a138-b969-4d5d-b5f0-078bfbc998f5", "b32eddd2-a53a-4510-8a25-a6c0c7fe9f48"]}]}, {"BlockType": "LINE", "Confidence": 99.77300262451172, "Text": "PO Box 9", "Geometry": {"BoundingBox": {"Width": 0.06462922692298889, "Height": 0.008222517557442188, "Left": 0.23312528431415558, "Top": 0.05555158481001854}, "Polygon": [{"X": 0.23312528431415558, "Y": 0.05555174499750137}, {"X": 0.2977544963359833, "Y": 0.05555158481001854}, {"X": 0.2977544963359833, "Y": 0.06377394497394562}, {"X": 0.23312529921531677, "Y": 0.06377410143613815}]}, "Id": "52620608-99a4-4cbd-b141-babaf7220d17", "Relationships": [{"Type": "CHILD", "Ids": ["21ec8d15-65fc-4ee9-984f-a867eb1a035b", "6c505bbb-a5d1-4a79-8a78-a63cd9f6733c", "11d70bac-fc1c-4508-b29f-1b5c5f4160cb"]}]}, {"BlockType": "LINE", "Confidence": 99.3264389038086, "Text": "Chesterfield MO 63006", "Geometry": {"BoundingBox": {"Width": 0.1613871306180954, "Height": 0.008218285627663136, "Left": 0.2329384833574295, "Top": 0.06765231490135193}, "Polygon": [{"X": 0.2329384833574295, "Y": 0.06765270233154297}, {"X": 0.3943255841732025, "Y": 0.06765231490135193}, {"X": 0.3943256139755249, "Y": 0.07587023079395294}, {"X": 0.2329384982585907, "Y": 0.07587060332298279}]}, "Id": "dd762572-b181-4ebf-92e9-2e0db19941c4", "Relationships": [{"Type": "CHILD", "Ids": ["264ec718-9677-4f7d-a210-ec7537d724d8", "6bb0a0cd-457b-4e29-8d25-badf5c75f9a5", "1130f138-e0f4-400d-b74f-ed651347a1cd"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "INDUSTRIAL", "Geometry": {"BoundingBox": {"Width": 0.11448425054550171, "Height": 0.010182628408074379, "Left": 0.07825879007577896, "Top": 0.08406809717416763}, "Polygon": [{"X": 0.07825879007577896, "Y": 0.08406835794448853}, {"X": 0.19274303317070007, "Y": 0.08406809717416763}, {"X": 0.19274304807186127, "Y": 0.09425047785043716}, {"X": 0.07825881242752075, "Y": 0.09425073117017746}]}, "Id": "97b82954-75b6-4239-a67f-1af0f8307846", "Relationships": [{"Type": "CHILD", "Ids": ["24ad598a-0f5e-4622-a54a-5a5bd8a64988"]}]}, {"BlockType": "LINE", "Confidence": 99.8779296875, "Text": "Phone ************ Fax ************", "Geometry": {"BoundingBox": {"Width": 0.2817501723766327, "Height": 0.00815017893910408, "Left": 0.23322619497776031, "Top": 0.07988974452018738}, "Polygon": [{"X": 0.23322619497776031, "Y": 0.07989039272069931}, {"X": 0.5149763822555542, "Y": 0.07988974452018738}, {"X": 0.5149763822555542, "Y": 0.08803929388523102}, {"X": 0.2332262098789215, "Y": 0.08803992718458176}]}, "Id": "ba5d1f05-b89d-44e2-bf2b-6b801e7e3be4", "Relationships": [{"Type": "CHILD", "Ids": ["931490cf-2370-4ec9-9265-12001909a5a4", "b81b8053-679a-45be-be1b-c5285d993122", "38950985-a01d-4986-9055-ee19f4a29f33", "2e5afa93-2af6-465c-8858-6245e173fe0b"]}]}, {"BlockType": "LINE", "Confidence": 99.96040344238281, "Text": "Purchase Order No.", "Geometry": {"BoundingBox": {"Width": 0.1374572515487671, "Height": 0.008361195214092731, "Left": 0.6696064472198486, "Top": 0.07547479122877121}, "Polygon": [{"X": 0.6696064472198486, "Y": 0.07547511160373688}, {"X": 0.807063639163971, "Y": 0.07547479122877121}, {"X": 0.8070636987686157, "Y": 0.083835668861866}, {"X": 0.6696064472198486, "Y": 0.08383598178625107}]}, "Id": "e235adc1-ac89-4d69-8606-abe4ed784ad9", "Relationships": [{"Type": "CHILD", "Ids": ["bbad920d-7c62-4b70-8951-0ae473dd8e3e", "46fa4dc7-eb39-448a-a646-c203741e186b", "ab703b3f-ad5f-45e0-a5e9-a38607a77452"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "EQUIPMENT", "Geometry": {"BoundingBox": {"Width": 0.11124782264232635, "Height": 0.00997584592550993, "Left": 0.07994891703128815, "Top": 0.09697677195072174}, "Polygon": [{"X": 0.07994891703128815, "Y": 0.09697701036930084}, {"X": 0.1911967247724533, "Y": 0.09697677195072174}, {"X": 0.1911967396736145, "Y": 0.10695239156484604}, {"X": 0.07994893938302994, "Y": 0.10695262253284454}]}, "Id": "1a45fcd6-6104-41f0-967a-f408c5ade91f", "Relationships": [{"Type": "CHILD", "Ids": ["690c9a2b-791a-4d15-8f64-c7de148ef2ef"]}]}, {"BlockType": "LINE", "Confidence": 93.01069641113281, "Text": "Email <EMAIL>", "Geometry": {"BoundingBox": {"Width": 0.17525887489318848, "Height": 0.009878273122012615, "Left": 0.2333293855190277, "Top": 0.09192012995481491}, "Polygon": [{"X": 0.2333293855190277, "Y": 0.09192050993442535}, {"X": 0.4085882306098938, "Y": 0.09192012995481491}, {"X": 0.4085882604122162, "Y": 0.10179802775382996}, {"X": 0.2333294004201889, "Y": 0.1017984002828598}]}, "Id": "54aa6fe1-728f-4a74-b6e7-25a3cf4d8908", "Relationships": [{"Type": "CHILD", "Ids": ["d76b8a46-cd08-4c48-ae70-4f5e3035753f", "f047dd8f-bc40-4803-8f2c-7ab6a84fc391"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "5762", "Geometry": {"BoundingBox": {"Width": 0.08402890712022781, "Height": 0.01922101154923439, "Left": 0.6968002319335938, "Top": 0.0929069072008133}, "Polygon": [{"X": 0.6968002319335938, "Y": 0.09290709346532822}, {"X": 0.7808290719985962, "Y": 0.0929069072008133}, {"X": 0.780829131603241, "Y": 0.11212775111198425}, {"X": 0.6968002915382385, "Y": 0.11212792247533798}]}, "Id": "844e9d3e-6939-4a3a-b4b0-9706b9f842ea", "Relationships": [{"Type": "CHILD", "Ids": ["9182bab0-5656-47f8-9647-470418750985"]}]}, {"BlockType": "LINE", "Confidence": 99.78157043457031, "Text": "Vendor Info", "Geometry": {"BoundingBox": {"Width": 0.08059120923280716, "Height": 0.00835880171507597, "Left": 0.06325451284646988, "Top": 0.1379248946905136}, "Polygon": [{"X": 0.06325451284646988, "Y": 0.13792504370212555}, {"X": 0.14384570717811584, "Y": 0.1379248946905136}, {"X": 0.14384572207927704, "Y": 0.14628355205059052}, {"X": 0.06325452774763107, "Y": 0.14628370106220245}]}, "Id": "87b079ca-b6f9-4cd5-911d-6f57d1870526", "Relationships": [{"Type": "CHILD", "Ids": ["8769a8f8-3640-41e8-96d9-6cc852cfba96", "2264d7bc-1137-46ec-949d-560c0646f87f"]}]}, {"BlockType": "LINE", "Confidence": 99.582275390625, "Text": "Ship To", "Geometry": {"BoundingBox": {"Width": 0.05215274542570114, "Height": 0.010362830013036728, "Left": 0.5123528242111206, "Top": 0.13777747750282288}, "Polygon": [{"X": 0.5123528242111206, "Y": 0.13777756690979004}, {"X": 0.5645055174827576, "Y": 0.13777747750282288}, {"X": 0.5645055770874023, "Y": 0.14814020693302155}, {"X": 0.5123528242111206, "Y": 0.1481402963399887}]}, "Id": "e5000b8e-60d4-4e36-aed5-2631b8806eda", "Relationships": [{"Type": "CHILD", "Ids": ["ed685c83-ca00-45cc-a4ad-db13edf8a424", "266a29b8-c18f-420c-ac89-233948ab92a8"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "NASHVILLE WIRE", "Geometry": {"BoundingBox": {"Width": 0.18385472893714905, "Height": 0.011930369772017002, "Left": 0.06452081352472305, "Top": 0.15920042991638184}, "Polygon": [{"X": 0.06452081352472305, "Y": 0.1592007428407669}, {"X": 0.2483755201101303, "Y": 0.15920042991638184}, {"X": 0.2483755499124527, "Y": 0.17113052308559418}, {"X": 0.06452083587646484, "Y": 0.17113080620765686}]}, "Id": "2f6beaa6-6d1a-408a-a730-761ec3964a10", "Relationships": [{"Type": "CHILD", "Ids": ["41d7d216-25a9-4e5b-a871-0ea2b1b77806", "5dd4fee6-a45a-464d-9838-9e29ad71b58e"]}]}, {"BlockType": "LINE", "Confidence": 99.62173461914062, "Text": "HONEYWELL INTELLIGRATED", "Geometry": {"BoundingBox": {"Width": 0.3134736716747284, "Height": 0.01208474300801754, "Left": 0.5135259628295898, "Top": 0.15908433496952057}, "Polygon": [{"X": 0.5135259628295898, "Y": 0.15908485651016235}, {"X": 0.8269995450973511, "Y": 0.15908433496952057}, {"X": 0.8269996047019958, "Y": 0.17116858065128326}, {"X": 0.5135259628295898, "Y": 0.17116907238960266}]}, "Id": "5dbe6b4b-e0da-46fd-947e-ca10d304af98", "Relationships": [{"Type": "CHILD", "Ids": ["6e0fadcd-c6ad-4c37-82ef-34afd55385bb", "b6b64e26-9d45-4774-bd24-971bbde7ef01"]}]}, {"BlockType": "LINE", "Confidence": 99.80448913574219, "Text": "PO Box 637935", "Geometry": {"BoundingBox": {"Width": 0.1606305092573166, "Height": 0.011782728135585785, "Left": 0.06473373621702194, "Top": 0.17745962738990784}, "Polygon": [{"X": 0.06473373621702194, "Y": 0.17745988070964813}, {"X": 0.22536422312259674, "Y": 0.17745962738990784}, {"X": 0.22536423802375793, "Y": 0.18924212455749512}, {"X": 0.06473375856876373, "Y": 0.18924236297607422}]}, "Id": "0f695915-76f6-47a6-a1e3-058d86f2a700", "Relationships": [{"Type": "CHILD", "Ids": ["c50e2ad5-2b1d-4331-a64b-f517f17b8557", "4250ac75-1feb-4186-a71c-921570900535", "cff9afe7-b863-47b3-8f2a-04cbc24d24d7"]}]}, {"BlockType": "LINE", "Confidence": 99.94386291503906, "Text": "9301 Olive Blvd.", "Geometry": {"BoundingBox": {"Width": 0.16552844643592834, "Height": 0.01199608575552702, "Left": 0.5126782059669495, "Top": 0.1772560477256775}, "Polygon": [{"X": 0.5126782059669495, "Y": 0.17725630104541779}, {"X": 0.6782066226005554, "Y": 0.1772560477256775}, {"X": 0.6782066226005554, "Y": 0.18925189971923828}, {"X": 0.5126782059669495, "Y": 0.18925213813781738}]}, "Id": "41c4dadc-9237-4d9c-b33c-0b76c6edb487", "Relationships": [{"Type": "CHILD", "Ids": ["018e4ade-20c8-4fb2-ac78-67e00fd7f5c4", "8088e8ff-1b8f-4131-885b-401bb2a0f755", "379dd685-3ec2-49ec-ab4b-72a7dcc1640d"]}]}, {"BlockType": "LINE", "Confidence": 96.86815643310547, "Text": "Cincinnati OH 45263-7935", "Geometry": {"BoundingBox": {"Width": 0.2741466462612152, "Height": 0.012023726478219032, "Left": 0.06432931125164032, "Top": 0.19541403651237488}, "Polygon": [{"X": 0.06432931125164032, "Y": 0.19541440904140472}, {"X": 0.33847591280937195, "Y": 0.19541403651237488}, {"X": 0.33847594261169434, "Y": 0.2074374109506607}, {"X": 0.06432933360338211, "Y": 0.20743776857852936}]}, "Id": "cafba16a-b42c-455d-a217-69bd06df5ea2", "Relationships": [{"Type": "CHILD", "Ids": ["7bcbe1f1-7732-455d-bc7d-a355e805ab3a", "bea5536e-8dbd-40c3-9d30-8c9cc53a025c", "8fc9582f-8169-4bbd-896e-a768ede096e9"]}]}, {"BlockType": "LINE", "Confidence": 99.82122802734375, "Text": "St Louis MO 63132", "Geometry": {"BoundingBox": {"Width": 0.19795048236846924, "Height": 0.011770933866500854, "Left": 0.5127675533294678, "Top": 0.19561651349067688}, "Polygon": [{"X": 0.5127675533294678, "Y": 0.19561678171157837}, {"X": 0.7107179760932922, "Y": 0.19561651349067688}, {"X": 0.710718035697937, "Y": 0.20738719403743744}, {"X": 0.5127675533294678, "Y": 0.20738744735717773}]}, "Id": "2588acde-ac33-4944-b599-60441964c671", "Relationships": [{"Type": "CHILD", "Ids": ["4f2efaa3-af7e-45b1-b482-3327b3b0683a", "dc113d77-c7fe-41b5-b8a8-44abd0d9f4ec", "88ea143f-c570-4fda-8bbc-225affb32e96", "c2db282b-e003-4f0c-ae87-7fa7d3c1876f"]}]}, {"BlockType": "LINE", "Confidence": 99.5777816772461, "Text": "ATTN; PO#A002142163", "Geometry": {"BoundingBox": {"Width": 0.2485823631286621, "Height": 0.013699106872081757, "Left": 0.5119361281394958, "Top": 0.21381890773773193}, "Polygon": [{"X": 0.5119361281394958, "Y": 0.213819220662117}, {"X": 0.7605184316635132, "Y": 0.21381890773773193}, {"X": 0.760518491268158, "Y": 0.2275177389383316}, {"X": 0.5119361877441406, "Y": 0.2275180220603943}]}, "Id": "7ea5d5f9-10d8-4a19-af00-35174c2a11c0", "Relationships": [{"Type": "CHILD", "Ids": ["555e9a27-0a37-4182-8f1a-48eb6af6ebca", "61cfd34b-9863-451f-9884-db4f72a3b128"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "LEE CROSS", "Geometry": {"BoundingBox": {"Width": 0.12306507676839828, "Height": 0.011841662228107452, "Left": 0.5133993625640869, "Top": 0.2319340854883194}, "Polygon": [{"X": 0.5133993625640869, "Y": 0.23193421959877014}, {"X": 0.6364643573760986, "Y": 0.2319340854883194}, {"X": 0.6364644169807434, "Y": 0.2437756210565567}, {"X": 0.5133993625640869, "Y": 0.24377574026584625}]}, "Id": "3b855ecf-2997-4897-99df-34664ea6a44e", "Relationships": [{"Type": "CHILD", "Ids": ["b065a245-ccde-40e0-b11d-302c0e19fc8e", "0ffee009-97e2-4af6-b70c-76ef73fc3eeb"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.033149354159832, "Height": 0.008266945369541645, "Left": 0.14894694089889526, "Top": 0.2631278932094574}, "Polygon": [{"X": 0.14894694089889526, "Y": 0.2631279230117798}, {"X": 0.18209627270698547, "Y": 0.2631278932094574}, {"X": 0.18209628760814667, "Y": 0.271394819021225}, {"X": 0.14894695580005646, "Y": 0.271394819021225}]}, "Id": "73e540f1-46c7-4819-bd43-69f59cb48e9f", "Relationships": [{"Type": "CHILD", "Ids": ["2f303fd6-1929-4cf7-9457-78e83830c989"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Ship Schedule", "Geometry": {"BoundingBox": {"Width": 0.10035085678100586, "Height": 0.010328740812838078, "Left": 0.33935311436653137, "Top": 0.26300954818725586}, "Polygon": [{"X": 0.33935311436653137, "Y": 0.263009637594223}, {"X": 0.43970394134521484, "Y": 0.26300954818725586}, {"X": 0.43970397114753723, "Y": 0.2733381986618042}, {"X": 0.33935314416885376, "Y": 0.27333828806877136}]}, "Id": "8066a97d-3578-453c-9a47-9d2e23eae9fe", "Relationships": [{"Type": "CHILD", "Ids": ["87bd40c1-6a57-4244-9f9e-4fe107ecbeb6", "b0880d36-2251-424f-847c-ad32f1de237f"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "Ship Via", "Geometry": {"BoundingBox": {"Width": 0.0571637786924839, "Height": 0.010298359207808971, "Left": 0.5852105617523193, "Top": 0.26303932070732117}, "Polygon": [{"X": 0.5852105617523193, "Y": 0.26303938031196594}, {"X": 0.6423742771148682, "Y": 0.26303932070732117}, {"X": 0.6423743367195129, "Y": 0.2733376622200012}, {"X": 0.5852105617523193, "Y": 0.2733376920223236}]}, "Id": "ac62f366-c7ea-4897-bdf9-b49c1785322e", "Relationships": [{"Type": "CHILD", "Ids": ["171a4c55-0352-459c-9cbe-99bcee59ec22", "05405f3f-d1a2-43a9-966d-da3fae22e858"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "Payment Terms", "Geometry": {"BoundingBox": {"Width": 0.10977251827716827, "Height": 0.01012459397315979, "Left": 0.7795626521110535, "Top": 0.26319295167922974}, "Polygon": [{"X": 0.7795626521110535, "Y": 0.2631930410861969}, {"X": 0.8893351554870605, "Y": 0.26319295167922974}, {"X": 0.8893351554870605, "Y": 0.27331745624542236}, {"X": 0.7795626521110535, "Y": 0.2733175456523895}]}, "Id": "8adac5cf-723c-4af1-be67-c2dd96090bfa", "Relationships": [{"Type": "CHILD", "Ids": ["7bbc876f-353d-4cef-a50a-e27c32973f4e", "4d664b60-068b-4f95-9d1a-9d7a55b29948"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "9/16/2025", "Geometry": {"BoundingBox": {"Width": 0.07618224620819092, "Height": 0.010062672197818756, "Left": 0.12693561613559723, "Top": 0.2852950096130371}, "Polygon": [{"X": 0.12693561613559723, "Y": 0.2852950692176819}, {"X": 0.20311783254146576, "Y": 0.2852950096130371}, {"X": 0.20311786234378815, "Y": 0.2953576445579529}, {"X": 0.12693563103675842, "Y": 0.29535770416259766}]}, "Id": "57f76c5a-f00f-49d8-8937-c30023d77e85", "Relationships": [{"Type": "CHILD", "Ids": ["c01d2027-580a-4f98-ba65-f8e03a3481e1"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "NOW", "Geometry": {"BoundingBox": {"Width": 0.04214417561888695, "Height": 0.00887185987085104, "Left": 0.3690221607685089, "Top": 0.28541994094848633}, "Polygon": [{"X": 0.3690221607685089, "Y": 0.2854199707508087}, {"X": 0.41116631031036377, "Y": 0.28541994094848633}, {"X": 0.41116634011268616, "Y": 0.29429179430007935}, {"X": 0.3690221905708313, "Y": 0.29429179430007935}]}, "Id": "b1a3b971-fc29-43e5-a7fb-f3ec9a47dbe8", "Relationships": [{"Type": "CHILD", "Ids": ["10862bb2-1804-47ca-8eee-b4fd65c9280c"]}]}, {"BlockType": "LINE", "Confidence": 99.90234375, "Text": "DAN MORRISON", "Geometry": {"BoundingBox": {"Width": 0.13118495047092438, "Height": 0.008896496146917343, "Left": 0.5478318333625793, "Top": 0.2854684591293335}, "Polygon": [{"X": 0.5478318333625793, "Y": 0.28546854853630066}, {"X": 0.6790167689323425, "Y": 0.2854684591293335}, {"X": 0.6790167689323425, "Y": 0.29436489939689636}, {"X": 0.5478318333625793, "Y": 0.29436495900154114}]}, "Id": "839190e5-162b-42fb-82fc-49ff9d9c5a37", "Relationships": [{"Type": "CHILD", "Ids": ["34cd82d8-746c-4f63-846f-344c41e6cda8", "b15ef82f-ee1c-43a4-b169-fc980fd49e69"]}]}, {"BlockType": "LINE", "Confidence": 99.60027313232422, "Text": "NET 30 DAYS", "Geometry": {"BoundingBox": {"Width": 0.10624167323112488, "Height": 0.009106121025979519, "Left": 0.7811560034751892, "Top": 0.28533509373664856}, "Polygon": [{"X": 0.7811560034751892, "Y": 0.28533515334129333}, {"X": 0.8873976469039917, "Y": 0.28533509373664856}, {"X": 0.8873977065086365, "Y": 0.2944411337375641}, {"X": 0.781156063079834, "Y": 0.29444119334220886}]}, "Id": "443aa928-6eb3-404c-8cd0-deac4da29ede", "Relationships": [{"Type": "CHILD", "Ids": ["b63dd8bc-cda7-43d3-94f6-305b51857cba", "69926b35-fc8f-48f8-89ca-9950575586be", "d7507afc-67e5-4f39-ae6d-d8daadd978aa"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Quantity", "Geometry": {"BoundingBox": {"Width": 0.05822797119617462, "Height": 0.010288609191775322, "Left": 0.07023660838603973, "Top": 0.3139788508415222}, "Polygon": [{"X": 0.07023660838603973, "Y": 0.3139788806438446}, {"X": 0.12846456468105316, "Y": 0.3139788508415222}, {"X": 0.12846457958221436, "Y": 0.3242674469947815}, {"X": 0.07023662328720093, "Y": 0.3242674767971039}]}, "Id": "33d4f6a6-e5cf-45d1-bb37-a2d11a44d869", "Relationships": [{"Type": "CHILD", "Ids": ["d4eaea20-151b-41b0-9109-efa7564606c4"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Description", "Geometry": {"BoundingBox": {"Width": 0.07881976664066315, "Height": 0.010238233022391796, "Left": 0.3905395269393921, "Top": 0.313981294631958}, "Polygon": [{"X": 0.3905395269393921, "Y": 0.3139813244342804}, {"X": 0.46935927867889404, "Y": 0.313981294631958}, {"X": 0.46935927867889404, "Y": 0.3242194950580597}, {"X": 0.3905395567417145, "Y": 0.3242195248603821}]}, "Id": "5ff2fb4a-5606-4096-a161-447290b502ac", "Relationships": [{"Type": "CHILD", "Ids": ["f5b29746-71e9-4bd9-bfef-f3ce397ec428"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Each", "Geometry": {"BoundingBox": {"Width": 0.03444110229611397, "Height": 0.008385381661355495, "Left": 0.7583204507827759, "Top": 0.31398385763168335}, "Polygon": [{"X": 0.7583204507827759, "Y": 0.31398388743400574}, {"X": 0.792761504650116, "Y": 0.31398385763168335}, {"X": 0.792761504650116, "Y": 0.322369247674942}, {"X": 0.7583204507827759, "Y": 0.322369247674942}]}, "Id": "6854b3c6-2d75-49db-bc21-01f6b9732169", "Relationships": [{"Type": "CHILD", "Ids": ["f7eb7f5d-697c-4db8-b666-79e6dce6ee76"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Amount", "Geometry": {"BoundingBox": {"Width": 0.056096576154232025, "Height": 0.008261536248028278, "Left": 0.8578738570213318, "Top": 0.3140263855457306}, "Polygon": [{"X": 0.8578738570213318, "Y": 0.314026415348053}, {"X": 0.913970410823822, "Y": 0.3140263855457306}, {"X": 0.9139704704284668, "Y": 0.322287917137146}, {"X": 0.8578739166259766, "Y": 0.322287917137146}]}, "Id": "24dd585a-704c-4181-9706-71aec7f73586", "Relationships": [{"Type": "CHILD", "Ids": ["4e98a57c-a6a9-4cdd-80d6-105058af8e42"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "60", "Geometry": {"BoundingBox": {"Width": 0.019222203642129898, "Height": 0.008913577534258366, "Left": 0.08950583636760712, "Top": 0.3617657423019409}, "Polygon": [{"X": 0.08950583636760712, "Y": 0.3617657721042633}, {"X": 0.10872802138328552, "Y": 0.3617657423019409}, {"X": 0.10872803628444672, "Y": 0.3706793189048767}, {"X": 0.08950585126876831, "Y": 0.3706793189048767}]}, "Id": "2a79ee4c-360c-4799-8236-8d2dd7083897", "Relationships": [{"Type": "CHILD", "Ids": ["7a77c384-7b52-40c3-9d13-9c5d2ba2d489"]}]}, {"BlockType": "LINE", "Confidence": 98.37196350097656, "Text": "48\" X 46\" wire deck 2500#", "Geometry": {"BoundingBox": {"Width": 0.20077534019947052, "Height": 0.00917788315564394, "Left": 0.14477938413619995, "Top": 0.36174502968788147}, "Polygon": [{"X": 0.14477938413619995, "Y": 0.36174502968788147}, {"X": 0.3455547094345093, "Y": 0.36174502968788147}, {"X": 0.34555473923683167, "Y": 0.37092289328575134}, {"X": 0.14477939903736115, "Y": 0.37092289328575134}]}, "Id": "14a9178e-8d67-4ae9-834a-08566918c495", "Relationships": [{"Type": "CHILD", "Ids": ["fbcfac82-ae38-4610-9bdd-57d193a5775b", "5c326127-a9a5-43a8-b05b-176ffaa15a21", "2bb06c31-7893-4c35-aff2-c698072ed6f5", "5e761b7e-182a-4d4e-aeea-3f143abe7241", "b9d63f17-56cc-489b-b761-28b013d3a3c9", "834b0a8f-5301-4f42-972f-37d6eb7792f3"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "22.79", "Geometry": {"BoundingBox": {"Width": 0.04404080659151077, "Height": 0.0090416818857193, "Left": 0.781628429889679, "Top": 0.36179301142692566}, "Polygon": [{"X": 0.781628429889679, "Y": 0.36179301142692566}, {"X": 0.8256692290306091, "Y": 0.36179301142692566}, {"X": 0.8256692290306091, "Y": 0.37083470821380615}, {"X": 0.781628429889679, "Y": 0.37083470821380615}]}, "Id": "fe2a3544-f478-47fd-9140-a52bb23c8e84", "Relationships": [{"Type": "CHILD", "Ids": ["b2aa7656-dff1-4f76-a83f-6f599854de25"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "1,367.40", "Geometry": {"BoundingBox": {"Width": 0.06737089157104492, "Height": 0.01056645903736353, "Left": 0.8588917851448059, "Top": 0.36164259910583496}, "Polygon": [{"X": 0.8588917851448059, "Y": 0.36164262890815735}, {"X": 0.9262626767158508, "Y": 0.36164259910583496}, {"X": 0.9262626767158508, "Y": 0.3722090721130371}, {"X": 0.8588918447494507, "Y": 0.3722090721130371}]}, "Id": "a74c8e86-d943-4fef-956c-2c720b05d815", "Relationships": [{"Type": "CHILD", "Ids": ["3bf64ec0-fb98-43cc-b97b-630e92750293"]}]}, {"BlockType": "LINE", "Confidence": 99.97265625, "Text": "CALL DAN FOR PICK UP", "Geometry": {"BoundingBox": {"Width": 0.1914394348859787, "Height": 0.009074180386960506, "Left": 0.14479376375675201, "Top": 0.4298575520515442}, "Polygon": [{"X": 0.14479376375675201, "Y": 0.4298575520515442}, {"X": 0.3362331688404083, "Y": 0.42985764145851135}, {"X": 0.3362331986427307, "Y": 0.43893173336982727}, {"X": 0.1447937786579132, "Y": 0.4389316141605377}]}, "Id": "f3788c4b-f1b5-436c-ac07-955d415b36d4", "Relationships": [{"Type": "CHILD", "Ids": ["feb9b97a-b1fb-4d74-91f7-206590f84ae1", "52110e5f-696b-4da8-9b7e-12b7c1b6975d", "9668bfc6-e08e-4b7f-a1c1-84239f9c6dfa", "3fffdf21-8b2a-43b4-a1db-3888fcd40cb1", "e323d8cf-8128-4a94-9080-4b70dadf8433"]}]}, {"BlockType": "LINE", "Confidence": 99.81126403808594, "Text": "636-493-0555", "Geometry": {"BoundingBox": {"Width": 0.11081942915916443, "Height": 0.00917322188615799, "Left": 0.14477454125881195, "Top": 0.44352343678474426}, "Polygon": [{"X": 0.14477454125881195, "Y": 0.44352343678474426}, {"X": 0.2555939555168152, "Y": 0.4435235261917114}, {"X": 0.2555939853191376, "Y": 0.45269668102264404}, {"X": 0.14477457106113434, "Y": 0.4526965916156769}]}, "Id": "4dddc628-2153-4b5e-a141-cb8c96630a3e", "Relationships": [{"Type": "CHILD", "Ids": ["6830ff92-8002-4a65-a8a4-d763b28763a3"]}]}, {"BlockType": "LINE", "Confidence": 99.98046875, "Text": "Total Amount", "Geometry": {"BoundingBox": {"Width": 0.09435579180717468, "Height": 0.008495435118675232, "Left": 0.7248082756996155, "Top": 0.9478293657302856}, "Polygon": [{"X": 0.7248082756996155, "Y": 0.9478293657302856}, {"X": 0.8191640377044678, "Y": 0.9478297829627991}, {"X": 0.8191640377044678, "Y": 0.9563248157501221}, {"X": 0.7248082756996155, "Y": 0.9563243389129639}]}, "Id": "dd7bd307-4b22-4637-b0f8-74242727c5e8", "Relationships": [{"Type": "CHILD", "Ids": ["d5254e58-4d39-4c1b-a997-aef374b0ebbc", "e2990bd1-4c9f-419a-8903-19d2998ef36a"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "$1,367.40", "Geometry": {"BoundingBox": {"Width": 0.07800514996051788, "Height": 0.010975057259202003, "Left": 0.8577622771263123, "Top": 0.9459840059280396}, "Polygon": [{"X": 0.8577622771263123, "Y": 0.9459840059280396}, {"X": 0.935767412185669, "Y": 0.9459843635559082}, {"X": 0.935767412185669, "Y": 0.956959068775177}, {"X": 0.857762336730957, "Y": 0.9569587111473083}]}, "Id": "92aceea3-c3c7-413f-a407-60813020f919", "Relationships": [{"Type": "CHILD", "Ids": ["76c6b238-b563-477f-b890-d6756a151fdf"]}]}, {"BlockType": "WORD", "Confidence": 99.3126220703125, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04109543561935425, "Height": 0.011731727980077267, "Left": 0.23314613103866577, "Top": 0.03960544615983963}, "Polygon": [{"X": 0.23314613103866577, "Y": 0.039605554193258286}, {"X": 0.27424153685569763, "Y": 0.03960544615983963}, {"X": 0.27424156665802, "Y": 0.051337070763111115}, {"X": 0.23314616084098816, "Y": 0.05133717507123947}]}, "Id": "82c468aa-bc20-4af0-b01c-90e24413b1d5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Industrial", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09293148666620255, "Height": 0.011774599552154541, "Left": 0.2812250852584839, "Top": 0.039682887494564056}, "Polygon": [{"X": 0.2812250852584839, "Y": 0.039683129638433456}, {"X": 0.37415656447410583, "Y": 0.039682887494564056}, {"X": 0.3741565942764282, "Y": 0.051457252353429794}, {"X": 0.2812251150608063, "Y": 0.0514574833214283}]}, "Id": "952ce86a-c904-4e13-b621-e341a56903b2"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "Equipment,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11811450123786926, "Height": 0.014695670455694199, "Left": 0.3821136951446533, "Top": 0.039538148790597916}, "Polygon": [{"X": 0.3821136951446533, "Y": 0.03953845798969269}, {"X": 0.5002281665802002, "Y": 0.039538148790597916}, {"X": 0.500228226184845, "Y": 0.054233524948358536}, {"X": 0.3821137249469757, "Y": 0.054233819246292114}]}, "Id": "44b6a481-0e2a-4d2d-a45c-e88c5f244101"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Inc.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.034616973251104355, "Height": 0.011701957322657108, "Left": 0.5087005496025085, "Top": 0.039700087159872055}, "Polygon": [{"X": 0.5087005496025085, "Y": 0.03970017656683922}, {"X": 0.5433174967765808, "Y": 0.039700087159872055}, {"X": 0.5433174967765808, "Y": 0.051401957869529724}, {"X": 0.5087006092071533, "Y": 0.05140204355120659}]}, "Id": "f7264e3b-863c-423e-9d12-24d7446fd809"}, {"BlockType": "WORD", "Confidence": 83.30516815185547, "Text": "COX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07932434976100922, "Height": 0.020324241369962692, "Left": 0.09038420766592026, "Top": 0.04915319010615349}, "Polygon": [{"X": 0.09038420766592026, "Y": 0.049153391271829605}, {"X": 0.1697085052728653, "Y": 0.04915319010615349}, {"X": 0.16970854997634888, "Y": 0.06947724521160126}, {"X": 0.09038424491882324, "Y": 0.06947743147611618}]}, "Id": "6f2a8434-47d6-4269-bff7-05a21beb6af8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PURCHASE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.15272048115730286, "Height": 0.014067483134567738, "Left": 0.6127174496650696, "Top": 0.04416971281170845}, "Polygon": [{"X": 0.6127174496650696, "Y": 0.04417010769248009}, {"X": 0.76543790102005, "Y": 0.04416971281170845}, {"X": 0.7654379606246948, "Y": 0.05823681876063347}, {"X": 0.6127175092697144, "Y": 0.05823719501495361}]}, "Id": "fd90a138-b969-4d5d-b5f0-078bfbc998f5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0984698012471199, "Height": 0.014126804657280445, "Left": 0.7742120027542114, "Top": 0.044121816754341125}, "Polygon": [{"X": 0.7742120027542114, "Y": 0.04412207379937172}, {"X": 0.872681736946106, "Y": 0.044121816754341125}, {"X": 0.8726817965507507, "Y": 0.058248382061719894}, {"X": 0.7742120623588562, "Y": 0.058248624205589294}]}, "Id": "b32eddd2-a53a-4510-8a25-a6c0c7fe9f48"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02063683606684208, "Height": 0.008222407661378384, "Left": 0.23312528431415558, "Top": 0.055551692843437195}, "Polygon": [{"X": 0.23312528431415558, "Y": 0.05555174499750137}, {"X": 0.2537620961666107, "Y": 0.055551692843437195}, {"X": 0.2537621259689331, "Y": 0.06377404928207397}, {"X": 0.23312529921531677, "Y": 0.06377410143613815}]}, "Id": "21ec8d15-65fc-4ee9-984f-a867eb1a035b"}, {"BlockType": "WORD", "Confidence": 99.67215728759766, "Text": "Box", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026373140513896942, "Height": 0.008099758066236973, "Left": 0.25866425037384033, "Top": 0.055649664252996445}, "Polygon": [{"X": 0.25866425037384033, "Y": 0.05564973130822182}, {"X": 0.2850373685359955, "Y": 0.055649664252996445}, {"X": 0.2850373685359955, "Y": 0.06374935805797577}, {"X": 0.25866425037384033, "Y": 0.06374942511320114}]}, "Id": "6c505bbb-a5d1-4a79-8a78-a63cd9f6733c"}, {"BlockType": "WORD", "Confidence": 99.73552703857422, "Text": "9", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008603223599493504, "Height": 0.007825194858014584, "Left": 0.28915128111839294, "Top": 0.05577031150460243}, "Polygon": [{"X": 0.28915128111839294, "Y": 0.055770330131053925}, {"X": 0.2977544963359833, "Y": 0.05577031150460243}, {"X": 0.2977544963359833, "Y": 0.0635954812169075}, {"X": 0.28915131092071533, "Y": 0.06359550356864929}]}, "Id": "11d70bac-fc1c-4508-b29f-1b5c5f4160cb"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Chesterfield", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08387114107608795, "Height": 0.008218100294470787, "Left": 0.2329384833574295, "Top": 0.06765250116586685}, "Polygon": [{"X": 0.2329384833574295, "Y": 0.06765270233154297}, {"X": 0.31680959463119507, "Y": 0.06765250116586685}, {"X": 0.31680962443351746, "Y": 0.07587040960788727}, {"X": 0.2329384982585907, "Y": 0.07587060332298279}]}, "Id": "264ec718-9677-4f7d-a210-ec7537d724d8"}, {"BlockType": "WORD", "Confidence": 98.12818908691406, "Text": "MO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024637946859002113, "Height": 0.008134283125400543, "Left": 0.32155758142471313, "Top": 0.06768596917390823}, "Polygon": [{"X": 0.32155758142471313, "Y": 0.06768602877855301}, {"X": 0.3461955189704895, "Y": 0.06768596917390823}, {"X": 0.3461955487728119, "Y": 0.075820192694664}, {"X": 0.3215576112270355, "Y": 0.07582025229930878}]}, "Id": "6bb0a0cd-457b-4e29-8d25-badf5c75f9a5"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "63006", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0436566136777401, "Height": 0.008063887245953083, "Left": 0.3506689965724945, "Top": 0.06774433702230453}, "Polygon": [{"X": 0.3506689965724945, "Y": 0.06774444133043289}, {"X": 0.3943255841732025, "Y": 0.06774433702230453}, {"X": 0.3943256139755249, "Y": 0.07580812275409698}, {"X": 0.3506690263748169, "Y": 0.07580822706222534}]}, "Id": "1130f138-e0f4-400d-b74f-ed651347a1cd"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "INDUSTRIAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11448425054550171, "Height": 0.010182628408074379, "Left": 0.07825879007577896, "Top": 0.08406809717416763}, "Polygon": [{"X": 0.07825879007577896, "Y": 0.08406835794448853}, {"X": 0.19274303317070007, "Y": 0.08406809717416763}, {"X": 0.19274304807186127, "Y": 0.09425047785043716}, {"X": 0.07825881242752075, "Y": 0.09425073117017746}]}, "Id": "24ad598a-0f5e-4622-a54a-5a5bd8a64988"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Phone", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043610669672489166, "Height": 0.008088572882115841, "Left": 0.23322619497776031, "Top": 0.07989269495010376}, "Polygon": [{"X": 0.23322619497776031, "Y": 0.07989279925823212}, {"X": 0.2768368422985077, "Y": 0.07989269495010376}, {"X": 0.2768368721008301, "Y": 0.08798117190599442}, {"X": 0.2332262098789215, "Y": 0.08798126876354218}]}, "Id": "931490cf-2370-4ec9-9265-12001909a5a4"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10044800490140915, "Height": 0.007994664832949638, "Left": 0.2813663184642792, "Top": 0.07990244776010513}, "Polygon": [{"X": 0.2813663184642792, "Y": 0.07990267872810364}, {"X": 0.38181430101394653, "Y": 0.07990244776010513}, {"X": 0.3818143308162689, "Y": 0.08789688348770142}, {"X": 0.28136634826660156, "Y": 0.08789711445569992}]}, "Id": "b81b8053-679a-45be-be1b-c5285d993122"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "Fax", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024574488401412964, "Height": 0.00814959965646267, "Left": 0.38643965125083923, "Top": 0.07988998293876648}, "Polygon": [{"X": 0.38643965125083923, "Y": 0.07989004254341125}, {"X": 0.4110141098499298, "Y": 0.07988998293876648}, {"X": 0.4110141396522522, "Y": 0.08803953230381012}, {"X": 0.3864396810531616, "Y": 0.0880395844578743}]}, "Id": "38950985-a01d-4986-9055-ee19f4a29f33"}, {"BlockType": "WORD", "Confidence": 99.794921875, "Text": "************", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09965600073337555, "Height": 0.008047230541706085, "Left": 0.41532039642333984, "Top": 0.07994414865970612}, "Polygon": [{"X": 0.41532039642333984, "Y": 0.07994437217712402}, {"X": 0.5149763822555542, "Y": 0.07994414865970612}, {"X": 0.5149763822555542, "Y": 0.0879911556839943}, {"X": 0.41532039642333984, "Y": 0.0879913792014122}]}, "Id": "2e5afa93-2af6-465c-8858-6245e173fe0b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06492284685373306, "Height": 0.00825192965567112, "Left": 0.6696064472198486, "Top": 0.0755840539932251}, "Polygon": [{"X": 0.6696064472198486, "Y": 0.07558420300483704}, {"X": 0.7345292568206787, "Y": 0.0755840539932251}, {"X": 0.7345293164253235, "Y": 0.08383583277463913}, {"X": 0.6696064472198486, "Y": 0.08383598178625107}]}, "Id": "bbad920d-7c62-4b70-8951-0ae473dd8e3e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04026202857494354, "Height": 0.008288830518722534, "Left": 0.7391868233680725, "Top": 0.07547485083341599}, "Polygon": [{"X": 0.7391868233680725, "Y": 0.07547494769096375}, {"X": 0.7794488072395325, "Y": 0.07547485083341599}, {"X": 0.7794488668441772, "Y": 0.08376359194517136}, {"X": 0.7391868233680725, "Y": 0.08376368135213852}]}, "Id": "46fa4dc7-eb39-448a-a646-c203741e186b"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "No.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022882379591464996, "Height": 0.008159182034432888, "Left": 0.7841812968254089, "Top": 0.07563402503728867}, "Polygon": [{"X": 0.7841812968254089, "Y": 0.07563407719135284}, {"X": 0.807063639163971, "Y": 0.07563402503728867}, {"X": 0.8070636987686157, "Y": 0.08379314839839935}, {"X": 0.7841813564300537, "Y": 0.08379320055246353}]}, "Id": "ab703b3f-ad5f-45e0-a5e9-a38607a77452"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "EQUIPMENT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11124782264232635, "Height": 0.00997584592550993, "Left": 0.07994891703128815, "Top": 0.09697677195072174}, "Polygon": [{"X": 0.07994891703128815, "Y": 0.09697701036930084}, {"X": 0.1911967247724533, "Y": 0.09697677195072174}, {"X": 0.1911967396736145, "Y": 0.10695239156484604}, {"X": 0.07994893938302994, "Y": 0.10695262253284454}]}, "Id": "690c9a2b-791a-4d15-8f64-c7de148ef2ef"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Email", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03819451481103897, "Height": 0.008272344246506691, "Left": 0.2333293855190277, "Top": 0.09192042797803879}, "Polygon": [{"X": 0.2333293855190277, "Y": 0.09192050993442535}, {"X": 0.2715238630771637, "Y": 0.09192042797803879}, {"X": 0.2715238928794861, "Y": 0.10019268840551376}, {"X": 0.2333294004201889, "Y": 0.10019277036190033}]}, "Id": "d76b8a46-cd08-4c48-ae70-4f5e3035753f"}, {"BlockType": "WORD", "Confidence": 86.05070495605469, "Text": "<EMAIL>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13221792876720428, "Height": 0.009814572520554066, "Left": 0.2763703167438507, "Top": 0.09198373556137085}, "Polygon": [{"X": 0.2763703167438507, "Y": 0.09198402613401413}, {"X": 0.4085882306098938, "Y": 0.09198373556137085}, {"X": 0.4085882604122162, "Y": 0.10179802775382996}, {"X": 0.2763703465461731, "Y": 0.10179831087589264}]}, "Id": "f047dd8f-bc40-4803-8f2c-7ab6a84fc391"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "5762", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08402890712022781, "Height": 0.01922101154923439, "Left": 0.6968002319335938, "Top": 0.0929069072008133}, "Polygon": [{"X": 0.6968002319335938, "Y": 0.09290709346532822}, {"X": 0.7808290719985962, "Y": 0.0929069072008133}, {"X": 0.780829131603241, "Y": 0.11212775111198425}, {"X": 0.6968002915382385, "Y": 0.11212792247533798}]}, "Id": "9182bab0-5656-47f8-9647-470418750985"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05133288353681564, "Height": 0.008280638605356216, "Left": 0.06325451284646988, "Top": 0.13800306618213654}, "Polygon": [{"X": 0.06325451284646988, "Y": 0.1380031555891037}, {"X": 0.11458738148212433, "Y": 0.13800306618213654}, {"X": 0.11458739638328552, "Y": 0.1462836116552353}, {"X": 0.06325452774763107, "Y": 0.14628370106220245}]}, "Id": "8769a8f8-3640-41e8-96d9-6cc852cfba96"}, {"BlockType": "WORD", "Confidence": 99.63229370117188, "Text": "Info", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.024921732023358345, "Height": 0.008320859633386135, "Left": 0.11892399191856384, "Top": 0.1379248946905136}, "Polygon": [{"X": 0.11892399191856384, "Y": 0.1379249393939972}, {"X": 0.14384570717811584, "Y": 0.1379248946905136}, {"X": 0.14384572207927704, "Y": 0.14624571800231934}, {"X": 0.11892400681972504, "Y": 0.14624576270580292}]}, "Id": "2264d7bc-1137-46ec-949d-560c0646f87f"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030740326270461082, "Height": 0.010362790897488594, "Left": 0.5123528242111206, "Top": 0.13777750730514526}, "Polygon": [{"X": 0.5123528242111206, "Y": 0.13777756690979004}, {"X": 0.5430930852890015, "Y": 0.13777750730514526}, {"X": 0.5430931448936462, "Y": 0.14814025163650513}, {"X": 0.5123528242111206, "Y": 0.1481402963399887}]}, "Id": "ed685c83-ca00-45cc-a4ad-db13edf8a424"}, {"BlockType": "WORD", "Confidence": 99.20360565185547, "Text": "To", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017520824447274208, "Height": 0.008285166695713997, "Left": 0.5469847321510315, "Top": 0.13795049488544464}, "Polygon": [{"X": 0.5469847321510315, "Y": 0.13795052468776703}, {"X": 0.5645055174827576, "Y": 0.13795049488544464}, {"X": 0.5645055770874023, "Y": 0.1462356299161911}, {"X": 0.5469847321510315, "Y": 0.1462356597185135}]}, "Id": "266a29b8-c18f-420c-ac89-233948ab92a8"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "NASHVILLE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12030748277902603, "Height": 0.011848398484289646, "Left": 0.06452081352472305, "Top": 0.15926623344421387}, "Polygon": [{"X": 0.06452081352472305, "Y": 0.1592664271593094}, {"X": 0.1848282814025879, "Y": 0.15926623344421387}, {"X": 0.18482829630374908, "Y": 0.17111444473266602}, {"X": 0.06452083587646484, "Y": 0.17111462354660034}]}, "Id": "41d7d216-25a9-4e5b-a871-0ea2b1b77806"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "WIRE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.057900626212358475, "Height": 0.011930172331631184, "Left": 0.19047491252422333, "Top": 0.15920042991638184}, "Polygon": [{"X": 0.19047491252422333, "Y": 0.1592005342245102}, {"X": 0.2483755201101303, "Y": 0.15920042991638184}, {"X": 0.2483755499124527, "Y": 0.17113052308559418}, {"X": 0.19047494232654572, "Y": 0.17113061249256134}]}, "Id": "5dd4fee6-a45a-464d-9838-9e29ad71b58e"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "HONEYWELL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1376698911190033, "Height": 0.01178958173841238, "Left": 0.5135259628295898, "Top": 0.15918688476085663}, "Polygon": [{"X": 0.5135259628295898, "Y": 0.15918710827827454}, {"X": 0.6511958241462708, "Y": 0.15918688476085663}, {"X": 0.6511958241462708, "Y": 0.17097625136375427}, {"X": 0.5135259628295898, "Y": 0.170976459980011}]}, "Id": "6e0fadcd-c6ad-4c37-82ef-34afd55385bb"}, {"BlockType": "WORD", "Confidence": 99.27275848388672, "Text": "INTELLIGRATED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.16836580634117126, "Height": 0.01208451483398676, "Left": 0.658633828163147, "Top": 0.15908433496952057}, "Polygon": [{"X": 0.658633828163147, "Y": 0.15908461809158325}, {"X": 0.8269995450973511, "Y": 0.15908433496952057}, {"X": 0.8269996047019958, "Y": 0.17116858065128326}, {"X": 0.658633828163147, "Y": 0.17116884887218475}]}, "Id": "b6b64e26-9d45-4774-bd24-971bbde7ef01"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "PO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02968522533774376, "Height": 0.01175853330641985, "Left": 0.06473373621702194, "Top": 0.17745983600616455}, "Polygon": [{"X": 0.06473373621702194, "Y": 0.17745988070964813}, {"X": 0.09441893547773361, "Y": 0.17745983600616455}, {"X": 0.0944189578294754, "Y": 0.18921832740306854}, {"X": 0.06473375856876373, "Y": 0.18921835720539093}]}, "Id": "c50e2ad5-2b1d-4331-a64b-f517f17b8557"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Box", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038935620337724686, "Height": 0.011756902560591698, "Left": 0.10220921784639359, "Top": 0.1774854063987732}, "Polygon": [{"X": 0.10220921784639359, "Y": 0.17748546600341797}, {"X": 0.14114481210708618, "Y": 0.1774854063987732}, {"X": 0.14114484190940857, "Y": 0.18924224376678467}, {"X": 0.10220924019813538, "Y": 0.18924230337142944}]}, "Id": "4250ac75-1feb-4186-a71c-921570900535"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "637935", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07739529013633728, "Height": 0.011557558551430702, "Left": 0.14796894788742065, "Top": 0.17753198742866516}, "Polygon": [{"X": 0.14796894788742065, "Y": 0.1775321066379547}, {"X": 0.22536422312259674, "Y": 0.17753198742866516}, {"X": 0.22536423802375793, "Y": 0.18908943235874176}, {"X": 0.14796897768974304, "Y": 0.18908953666687012}]}, "Id": "cff9afe7-b863-47b3-8f2a-04cbc24d24d7"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "9301", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04796299338340759, "Height": 0.011829162947833538, "Left": 0.5126782059669495, "Top": 0.17739498615264893}, "Polygon": [{"X": 0.5126782059669495, "Y": 0.1773950606584549}, {"X": 0.5606411695480347, "Y": 0.17739498615264893}, {"X": 0.5606411695480347, "Y": 0.18922407925128937}, {"X": 0.5126782059669495, "Y": 0.18922415375709534}]}, "Id": "018e4ade-20c8-4fb2-ac78-67e00fd7f5c4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05050898343324661, "Height": 0.011977721005678177, "Left": 0.5715740919113159, "Top": 0.17725613713264465}, "Polygon": [{"X": 0.5715740919113159, "Y": 0.17725621163845062}, {"X": 0.6220830678939819, "Y": 0.17725613713264465}, {"X": 0.6220830678939819, "Y": 0.18923379480838776}, {"X": 0.5715741515159607, "Y": 0.18923385441303253}]}, "Id": "8088e8ff-1b8f-4131-885b-401bb2a0f755"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Blvd.", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04830627515912056, "Height": 0.011878344230353832, "Left": 0.6299003958702087, "Top": 0.17737363278865814}, "Polygon": [{"X": 0.6299003958702087, "Y": 0.1773737072944641}, {"X": 0.6782066226005554, "Y": 0.17737363278865814}, {"X": 0.6782066226005554, "Y": 0.18925189971923828}, {"X": 0.6299003958702087, "Y": 0.18925197422504425}]}, "Id": "379dd685-3ec2-49ec-ab4b-72a7dcc1640d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Cincinnati", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10177657753229141, "Height": 0.011972344480454922, "Left": 0.06432931125164032, "Top": 0.19541427493095398}, "Polygon": [{"X": 0.06432931125164032, "Y": 0.19541440904140472}, {"X": 0.16610586643218994, "Y": 0.19541427493095398}, {"X": 0.16610589623451233, "Y": 0.20738649368286133}, {"X": 0.06432933360338211, "Y": 0.20738661289215088}]}, "Id": "7bcbe1f1-7732-455d-bc7d-a355e805ab3a"}, {"BlockType": "WORD", "Confidence": 90.72325134277344, "Text": "OH", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03163931518793106, "Height": 0.011681796051561832, "Left": 0.17308539152145386, "Top": 0.19564737379550934}, "Polygon": [{"X": 0.17308539152145386, "Y": 0.19564741849899292}, {"X": 0.20472468435764313, "Y": 0.19564737379550934}, {"X": 0.20472469925880432, "Y": 0.20732912421226501}, {"X": 0.17308540642261505, "Y": 0.2073291689157486}]}, "Id": "bea5536e-8dbd-40c3-9d30-8c9cc53a025c"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "45263-7935", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1263013631105423, "Height": 0.01183080393821001, "Left": 0.21217457950115204, "Top": 0.1956067681312561}, "Polygon": [{"X": 0.21217457950115204, "Y": 0.19560694694519043}, {"X": 0.33847591280937195, "Y": 0.1956067681312561}, {"X": 0.33847594261169434, "Y": 0.2074374109506607}, {"X": 0.21217460930347443, "Y": 0.20743757486343384}]}, "Id": "8fc9582f-8169-4bbd-896e-a768ede096e9"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "St", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021654369309544563, "Height": 0.011584877967834473, "Left": 0.5127675533294678, "Top": 0.195643350481987}, "Polygon": [{"X": 0.5127675533294678, "Y": 0.1956433802843094}, {"X": 0.5344218611717224, "Y": 0.195643350481987}, {"X": 0.5344219207763672, "Y": 0.20722819864749908}, {"X": 0.5127675533294678, "Y": 0.20722822844982147}]}, "Id": "4f2efaa3-af7e-45b1-b482-3327b3b0683a"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Louis", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053792960941791534, "Height": 0.011572000570595264, "Left": 0.5415478944778442, "Top": 0.19572117924690247}, "Polygon": [{"X": 0.5415478944778442, "Y": 0.19572125375270844}, {"X": 0.5953408479690552, "Y": 0.19572117924690247}, {"X": 0.5953409075737, "Y": 0.20729310810565948}, {"X": 0.541547954082489, "Y": 0.20729318261146545}]}, "Id": "dc113d77-c7fe-41b5-b8a8-44abd0d9f4ec"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "MO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03646200895309448, "Height": 0.011627808213233948, "Left": 0.6026508808135986, "Top": 0.19561661779880524}, "Polygon": [{"X": 0.6026508808135986, "Y": 0.19561666250228882}, {"X": 0.6391128301620483, "Y": 0.19561661779880524}, {"X": 0.6391128897666931, "Y": 0.2072443664073944}, {"X": 0.6026508808135986, "Y": 0.20724442601203918}]}, "Id": "88ea143f-c570-4fda-8bbc-225affb32e96"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "63132", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06445574015378952, "Height": 0.011677448637783527, "Left": 0.6462622880935669, "Top": 0.19570982456207275}, "Polygon": [{"X": 0.6462622880935669, "Y": 0.19570991396903992}, {"X": 0.7107179760932922, "Y": 0.19570982456207275}, {"X": 0.710718035697937, "Y": 0.20738719403743744}, {"X": 0.6462623476982117, "Y": 0.2073872685432434}]}, "Id": "c2db282b-e003-4f0c-ae87-7fa7d3c1876f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "ATTN;", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06229759007692337, "Height": 0.013698876835405827, "Left": 0.5119361281394958, "Top": 0.21381914615631104}, "Polygon": [{"X": 0.5119361281394958, "Y": 0.213819220662117}, {"X": 0.5742336511611938, "Y": 0.21381914615631104}, {"X": 0.5742337107658386, "Y": 0.22751794755458832}, {"X": 0.5119361877441406, "Y": 0.2275180220603943}]}, "Id": "555e9a27-0a37-4182-8f1a-48eb6af6ebca"}, {"BlockType": "WORD", "Confidence": 99.23529052734375, "Text": "PO#A002142163", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.17734761536121368, "Height": 0.011909564957022667, "Left": 0.5831708908081055, "Top": 0.2138548046350479}, "Polygon": [{"X": 0.5831708908081055, "Y": 0.21385502815246582}, {"X": 0.7605184316635132, "Y": 0.2138548046350479}, {"X": 0.760518491268158, "Y": 0.2257641702890396}, {"X": 0.5831708908081055, "Y": 0.22576437890529633}]}, "Id": "61cfd34b-9863-451f-9884-db4f72a3b128"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "LEE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03910114988684654, "Height": 0.011824413202702999, "Left": 0.5133993625640869, "Top": 0.23195132613182068}, "Polygon": [{"X": 0.5133993625640869, "Y": 0.23195137083530426}, {"X": 0.5525004267692566, "Y": 0.23195132613182068}, {"X": 0.5525004863739014, "Y": 0.24377569556236267}, {"X": 0.5133993625640869, "Y": 0.24377574026584625}]}, "Id": "b065a245-ccde-40e0-b11d-302c0e19fc8e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "CROSS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07729648798704147, "Height": 0.011765662580728531, "Left": 0.5591679215431213, "Top": 0.2319340854883194}, "Polygon": [{"X": 0.5591679215431213, "Y": 0.23193415999412537}, {"X": 0.6364643573760986, "Y": 0.2319340854883194}, {"X": 0.6364644169807434, "Y": 0.24369966983795166}, {"X": 0.5591679811477661, "Y": 0.24369974434375763}]}, "Id": "0ffee009-97e2-4af6-b70c-76ef73fc3eeb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033149354159832, "Height": 0.008266945369541645, "Left": 0.14894694089889526, "Top": 0.2631278932094574}, "Polygon": [{"X": 0.14894694089889526, "Y": 0.2631279230117798}, {"X": 0.18209627270698547, "Y": 0.2631278932094574}, {"X": 0.18209628760814667, "Y": 0.271394819021225}, {"X": 0.14894695580005646, "Y": 0.271394819021225}]}, "Id": "2f303fd6-1929-4cf7-9457-78e83830c989"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03080175630748272, "Height": 0.010328682139515877, "Left": 0.33935311436653137, "Top": 0.26300960779190063}, "Polygon": [{"X": 0.33935311436653137, "Y": 0.263009637594223}, {"X": 0.37015485763549805, "Y": 0.26300960779190063}, {"X": 0.37015488743782043, "Y": 0.273338258266449}, {"X": 0.33935314416885376, "Y": 0.27333828806877136}]}, "Id": "87bd40c1-6a57-4244-9f9e-4fe107ecbeb6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Schedule", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06510575115680695, "Height": 0.008433934301137924, "Left": 0.3745982348918915, "Top": 0.2630249261856079}, "Polygon": [{"X": 0.3745982348918915, "Y": 0.2630249559879303}, {"X": 0.43970394134521484, "Y": 0.2630249261856079}, {"X": 0.43970397114753723, "Y": 0.27145880460739136}, {"X": 0.3745982348918915, "Y": 0.27145883440971375}]}, "Id": "b0880d36-2251-424f-847c-ad32f1de237f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030870817601680756, "Height": 0.01029833685606718, "Left": 0.5852105617523193, "Top": 0.26303935050964355}, "Polygon": [{"X": 0.5852105617523193, "Y": 0.26303938031196594}, {"X": 0.6160813570022583, "Y": 0.26303935050964355}, {"X": 0.6160813570022583, "Y": 0.2733376622200012}, {"X": 0.5852105617523193, "Y": 0.2733376920223236}]}, "Id": "171a4c55-0352-459c-9cbe-99bcee59ec22"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "Via", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.022260751575231552, "Height": 0.008360077627003193, "Left": 0.6201135516166687, "Top": 0.26313674449920654}, "Polygon": [{"X": 0.6201135516166687, "Y": 0.26313674449920654}, {"X": 0.6423742771148682, "Y": 0.26313674449920654}, {"X": 0.6423743367195129, "Y": 0.27149680256843567}, {"X": 0.6201136112213135, "Y": 0.27149680256843567}]}, "Id": "05405f3f-d1a2-43a9-966d-da3fae22e858"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Payment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06188274547457695, "Height": 0.010092438198626041, "Left": 0.7795626521110535, "Top": 0.26322510838508606}, "Polygon": [{"X": 0.7795626521110535, "Y": 0.26322513818740845}, {"X": 0.8414453864097595, "Y": 0.26322510838508606}, {"X": 0.8414453864097595, "Y": 0.27331748604774475}, {"X": 0.7795626521110535, "Y": 0.2733175456523895}]}, "Id": "7bbc876f-353d-4cef-a50a-e27c32973f4e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Terms", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04454730451107025, "Height": 0.008166482672095299, "Left": 0.8447878360748291, "Top": 0.26319295167922974}, "Polygon": [{"X": 0.8447878360748291, "Y": 0.2631929814815521}, {"X": 0.8893351554870605, "Y": 0.26319295167922974}, {"X": 0.8893351554870605, "Y": 0.271359384059906}, {"X": 0.8447878956794739, "Y": 0.2713594138622284}]}, "Id": "4d664b60-068b-4f95-9d1a-9d7a55b29948"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "9/16/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07618224620819092, "Height": 0.010062672197818756, "Left": 0.12693561613559723, "Top": 0.2852950096130371}, "Polygon": [{"X": 0.12693561613559723, "Y": 0.2852950692176819}, {"X": 0.20311783254146576, "Y": 0.2852950096130371}, {"X": 0.20311786234378815, "Y": 0.2953576445579529}, {"X": 0.12693563103675842, "Y": 0.29535770416259766}]}, "Id": "c01d2027-580a-4f98-ba65-f8e03a3481e1"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "NOW", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04214417561888695, "Height": 0.00887185987085104, "Left": 0.3690221607685089, "Top": 0.28541994094848633}, "Polygon": [{"X": 0.3690221607685089, "Y": 0.2854199707508087}, {"X": 0.41116631031036377, "Y": 0.28541994094848633}, {"X": 0.41116634011268616, "Y": 0.29429179430007935}, {"X": 0.3690221905708313, "Y": 0.29429179430007935}]}, "Id": "10862bb2-1804-47ca-8eee-b4fd65c9280c"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "DAN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03489665314555168, "Height": 0.00885515846312046, "Left": 0.5478318333625793, "Top": 0.28550979495048523}, "Polygon": [{"X": 0.5478318333625793, "Y": 0.2855098247528076}, {"X": 0.5827284455299377, "Y": 0.28550979495048523}, {"X": 0.5827285051345825, "Y": 0.29436492919921875}, {"X": 0.5478318333625793, "Y": 0.29436495900154114}]}, "Id": "34cd82d8-746c-4f63-846f-344c41e6cda8"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "MORRISON", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0900375247001648, "Height": 0.008861955255270004, "Left": 0.5889792442321777, "Top": 0.2854684591293335}, "Polygon": [{"X": 0.5889792442321777, "Y": 0.28546851873397827}, {"X": 0.6790167689323425, "Y": 0.2854684591293335}, {"X": 0.6790167689323425, "Y": 0.294330358505249}, {"X": 0.5889793038368225, "Y": 0.2943304181098938}]}, "Id": "b15ef82f-ee1c-43a4-b169-fc980fd49e69"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "NET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03232449293136597, "Height": 0.009037505835294724, "Left": 0.7811560034751892, "Top": 0.28540369868278503}, "Polygon": [{"X": 0.7811560034751892, "Y": 0.2854037284851074}, {"X": 0.8134804964065552, "Y": 0.28540369868278503}, {"X": 0.8134804964065552, "Y": 0.29444119334220886}, {"X": 0.781156063079834, "Y": 0.29444119334220886}]}, "Id": "b63dd8bc-cda7-43d3-94f6-305b51857cba"}, {"BlockType": "WORD", "Confidence": 99.05473327636719, "Text": "30", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01907322183251381, "Height": 0.00866579171270132, "Left": 0.8181293606758118, "Top": 0.2855701148509979}, "Polygon": [{"X": 0.8181293606758118, "Y": 0.2855701446533203}, {"X": 0.8372025489807129, "Y": 0.2855701148509979}, {"X": 0.8372025489807129, "Y": 0.2942359149456024}, {"X": 0.8181293606758118, "Y": 0.2942359149456024}]}, "Id": "69926b35-fc8f-48f8-89ca-9950575586be"}, {"BlockType": "WORD", "Confidence": 99.814453125, "Text": "DAYS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0449114665389061, "Height": 0.008899267762899399, "Left": 0.8424862027168274, "Top": 0.28533509373664856}, "Polygon": [{"X": 0.8424862027168274, "Y": 0.28533512353897095}, {"X": 0.8873976469039917, "Y": 0.28533509373664856}, {"X": 0.8873977065086365, "Y": 0.29423433542251587}, {"X": 0.8424862623214722, "Y": 0.29423436522483826}]}, "Id": "d7507afc-67e5-4f39-ae6d-d8daadd978aa"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Quantity", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05822797119617462, "Height": 0.010288609191775322, "Left": 0.07023660838603973, "Top": 0.3139788508415222}, "Polygon": [{"X": 0.07023660838603973, "Y": 0.3139788806438446}, {"X": 0.12846456468105316, "Y": 0.3139788508415222}, {"X": 0.12846457958221436, "Y": 0.3242674469947815}, {"X": 0.07023662328720093, "Y": 0.3242674767971039}]}, "Id": "d4eaea20-151b-41b0-9109-efa7564606c4"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Description", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07881976664066315, "Height": 0.010238233022391796, "Left": 0.3905395269393921, "Top": 0.313981294631958}, "Polygon": [{"X": 0.3905395269393921, "Y": 0.3139813244342804}, {"X": 0.46935927867889404, "Y": 0.313981294631958}, {"X": 0.46935927867889404, "Y": 0.3242194950580597}, {"X": 0.3905395567417145, "Y": 0.3242195248603821}]}, "Id": "f5b29746-71e9-4bd9-bfef-f3ce397ec428"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Each", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03444110229611397, "Height": 0.008385381661355495, "Left": 0.7583204507827759, "Top": 0.31398385763168335}, "Polygon": [{"X": 0.7583204507827759, "Y": 0.31398388743400574}, {"X": 0.792761504650116, "Y": 0.31398385763168335}, {"X": 0.792761504650116, "Y": 0.322369247674942}, {"X": 0.7583204507827759, "Y": 0.322369247674942}]}, "Id": "f7eb7f5d-697c-4db8-b666-79e6dce6ee76"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.056096576154232025, "Height": 0.008261536248028278, "Left": 0.8578738570213318, "Top": 0.3140263855457306}, "Polygon": [{"X": 0.8578738570213318, "Y": 0.314026415348053}, {"X": 0.913970410823822, "Y": 0.3140263855457306}, {"X": 0.9139704704284668, "Y": 0.322287917137146}, {"X": 0.8578739166259766, "Y": 0.322287917137146}]}, "Id": "4e98a57c-a6a9-4cdd-80d6-105058af8e42"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "60", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019222203642129898, "Height": 0.008913577534258366, "Left": 0.08950583636760712, "Top": 0.3617657423019409}, "Polygon": [{"X": 0.08950583636760712, "Y": 0.3617657721042633}, {"X": 0.10872802138328552, "Y": 0.3617657423019409}, {"X": 0.10872803628444672, "Y": 0.3706793189048767}, {"X": 0.08950585126876831, "Y": 0.3706793189048767}]}, "Id": "7a77c384-7b52-40c3-9d13-9c5d2ba2d489"}, {"BlockType": "WORD", "Confidence": 98.60192108154297, "Text": "48\"", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02404395118355751, "Height": 0.008853502571582794, "Left": 0.14477938413619995, "Top": 0.36179953813552856}, "Polygon": [{"X": 0.14477938413619995, "Y": 0.36179953813552856}, {"X": 0.16882331669330597, "Y": 0.36179953813552856}, {"X": 0.16882334649562836, "Y": 0.37065303325653076}, {"X": 0.14477939903736115, "Y": 0.37065303325653076}]}, "Id": "fbcfac82-ae38-4610-9bdd-57d193a5775b"}, {"BlockType": "WORD", "Confidence": 93.37052917480469, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009197543375194073, "Height": 0.00715424632653594, "Left": 0.17324134707450867, "Top": 0.36376866698265076}, "Polygon": [{"X": 0.17324134707450867, "Y": 0.36376866698265076}, {"X": 0.18243886530399323, "Y": 0.36376866698265076}, {"X": 0.18243888020515442, "Y": 0.37092289328575134}, {"X": 0.17324136197566986, "Y": 0.37092289328575134}]}, "Id": "5c326127-a9a5-43a8-b05b-176ffaa15a21"}, {"BlockType": "WORD", "Confidence": 98.35698699951172, "Text": "46\"", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023802055045962334, "Height": 0.008779384195804596, "Left": 0.18714381754398346, "Top": 0.3618525564670563}, "Polygon": [{"X": 0.18714381754398346, "Y": 0.36185258626937866}, {"X": 0.21094584465026855, "Y": 0.3618525564670563}, {"X": 0.21094587445259094, "Y": 0.37063196301460266}, {"X": 0.18714383244514465, "Y": 0.37063196301460266}]}, "Id": "2bb06c31-7893-4c35-aff2-c698072ed6f5"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "wire", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03202865272760391, "Height": 0.008910615928471088, "Left": 0.21546746790409088, "Top": 0.36174634099006653}, "Polygon": [{"X": 0.21546746790409088, "Y": 0.36174634099006653}, {"X": 0.2474961131811142, "Y": 0.36174634099006653}, {"X": 0.2474961280822754, "Y": 0.37065693736076355}, {"X": 0.21546749770641327, "Y": 0.37065693736076355}]}, "Id": "5e761b7e-182a-4d4e-aeea-3f143abe7241"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "deck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03836830332875252, "Height": 0.008920306339859962, "Left": 0.2528722584247589, "Top": 0.36174502968788147}, "Polygon": [{"X": 0.2528722584247589, "Y": 0.36174502968788147}, {"X": 0.29124054312705994, "Y": 0.36174502968788147}, {"X": 0.29124054312705994, "Y": 0.3706653416156769}, {"X": 0.2528722584247589, "Y": 0.3706653416156769}]}, "Id": "b9d63f17-56cc-489b-b761-28b013d3a3c9"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "2500#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.049802266061306, "Height": 0.008919740095734596, "Left": 0.29575246572494507, "Top": 0.36183494329452515}, "Polygon": [{"X": 0.29575246572494507, "Y": 0.36183494329452515}, {"X": 0.3455547094345093, "Y": 0.36183494329452515}, {"X": 0.34555473923683167, "Y": 0.3707546889781952}, {"X": 0.29575249552726746, "Y": 0.3707546889781952}]}, "Id": "834b0a8f-5301-4f42-972f-37d6eb7792f3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "22.79", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04404080659151077, "Height": 0.0090416818857193, "Left": 0.781628429889679, "Top": 0.36179301142692566}, "Polygon": [{"X": 0.781628429889679, "Y": 0.36179301142692566}, {"X": 0.8256692290306091, "Y": 0.36179301142692566}, {"X": 0.8256692290306091, "Y": 0.37083470821380615}, {"X": 0.781628429889679, "Y": 0.37083470821380615}]}, "Id": "b2aa7656-dff1-4f76-a83f-6f599854de25"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "1,367.40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06737089157104492, "Height": 0.01056645903736353, "Left": 0.8588917851448059, "Top": 0.36164259910583496}, "Polygon": [{"X": 0.8588917851448059, "Y": 0.36164262890815735}, {"X": 0.9262626767158508, "Y": 0.36164259910583496}, {"X": 0.9262626767158508, "Y": 0.3722090721130371}, {"X": 0.8588918447494507, "Y": 0.3722090721130371}]}, "Id": "3bf64ec0-fb98-43cc-b97b-630e92750293"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CALL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04226493090391159, "Height": 0.008983492851257324, "Left": 0.14479376375675201, "Top": 0.42989781498908997}, "Polygon": [{"X": 0.14479376375675201, "Y": 0.42989781498908997}, {"X": 0.18705867230892181, "Y": 0.42989781498908997}, {"X": 0.1870587021112442, "Y": 0.4388813078403473}, {"X": 0.1447937786579132, "Y": 0.4388812780380249}]}, "Id": "feb9b97a-b1fb-4d74-91f7-206590f84ae1"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "DAN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035093214362859726, "Height": 0.009074099361896515, "Left": 0.1924898773431778, "Top": 0.4298575818538666}, "Polygon": [{"X": 0.1924898773431778, "Y": 0.4298575818538666}, {"X": 0.22758308053016663, "Y": 0.4298575818538666}, {"X": 0.22758309543132782, "Y": 0.4389316737651825}, {"X": 0.19248990714550018, "Y": 0.4389316439628601}]}, "Id": "52110e5f-696b-4da8-9b7e-12b7c1b6975d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FOR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033075232058763504, "Height": 0.008988948538899422, "Left": 0.2334105670452118, "Top": 0.4299232065677643}, "Polygon": [{"X": 0.2334105670452118, "Y": 0.4299232065677643}, {"X": 0.2664857804775238, "Y": 0.4299232065677643}, {"X": 0.2664858102798462, "Y": 0.43891215324401855}, {"X": 0.23341058194637299, "Y": 0.43891212344169617}]}, "Id": "9668bfc6-e08e-4b7f-a1c1-84239f9c6dfa"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "PICK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037423525005578995, "Height": 0.008755111135542393, "Left": 0.2720748782157898, "Top": 0.42998746037483215}, "Polygon": [{"X": 0.2720748782157898, "Y": 0.42998746037483215}, {"X": 0.3094983696937561, "Y": 0.42998746037483215}, {"X": 0.3094983994960785, "Y": 0.4387425482273102}, {"X": 0.2720749080181122, "Y": 0.4387425482273102}]}, "Id": "3fffdf21-8b2a-43b4-a1db-3888fcd40cb1"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021586889401078224, "Height": 0.00883264560252428, "Left": 0.31464630365371704, "Top": 0.4299592673778534}, "Polygon": [{"X": 0.31464630365371704, "Y": 0.4299592673778534}, {"X": 0.3362331688404083, "Y": 0.4299592673778534}, {"X": 0.3362331986427307, "Y": 0.4387919008731842}, {"X": 0.31464633345603943, "Y": 0.4387919008731842}]}, "Id": "e323d8cf-8128-4a94-9080-4b70dadf8433"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "636-493-0555", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11081942915916443, "Height": 0.00917322188615799, "Left": 0.14477454125881195, "Top": 0.44352343678474426}, "Polygon": [{"X": 0.14477454125881195, "Y": 0.44352343678474426}, {"X": 0.2555939555168152, "Y": 0.4435235261917114}, {"X": 0.2555939853191376, "Y": 0.45269668102264404}, {"X": 0.14477457106113434, "Y": 0.4526965916156769}]}, "Id": "6830ff92-8002-4a65-a8a4-d763b28763a3"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03447505086660385, "Height": 0.008495158515870571, "Left": 0.7248082756996155, "Top": 0.9478293657302856}, "Polygon": [{"X": 0.7248082756996155, "Y": 0.9478293657302856}, {"X": 0.7592833042144775, "Y": 0.9478294849395752}, {"X": 0.7592833042144775, "Y": 0.9563245177268982}, {"X": 0.7248082756996155, "Y": 0.9563243389129639}]}, "Id": "d5254e58-4d39-4c1b-a997-aef374b0ebbc"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Amount", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.055805470794439316, "Height": 0.00805060938000679, "Left": 0.7633585929870605, "Top": 0.9478656053543091}, "Polygon": [{"X": 0.7633585929870605, "Y": 0.9478656053543091}, {"X": 0.8191640377044678, "Y": 0.9478658437728882}, {"X": 0.8191640377044678, "Y": 0.9559162259101868}, {"X": 0.7633585929870605, "Y": 0.9559159278869629}]}, "Id": "e2990bd1-4c9f-419a-8903-19d2998ef36a"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "$1,367.40", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07800514996051788, "Height": 0.010975057259202003, "Left": 0.8577622771263123, "Top": 0.9459840059280396}, "Polygon": [{"X": 0.8577622771263123, "Y": 0.9459840059280396}, {"X": 0.935767412185669, "Y": 0.9459843635559082}, {"X": 0.935767412185669, "Y": 0.956959068775177}, {"X": 0.857762336730957, "Y": 0.9569587111473083}]}, "Id": "76c6b238-b563-477f-b890-d6756a151fdf"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "bab4ab7e-3079-4fac-96e9-d64e31db4e91", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "bab4ab7e-3079-4fac-96e9-d64e31db4e91", "content-type": "application/x-amz-json-1.1", "content-length": "66797", "date": "Wed, 24 Sep 2025 09:00:20 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "8abb7213-1b77-4b02-913c-9cb16dd7e78b", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 09:00:22 GMT", "content-type": "application/json", "content-length": "669", "connection": "keep-alive", "x-amzn-requestid": "8abb7213-1b77-4b02-913c-9cb16dd7e78b"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify this page. It's a Purchase Order. So doc_type \"po\". Need to output using classify_logistics_doc_type tool. For page 1, page_no 1. Provide array with one object? The function expects documents: array of objects. For each page, produce one object. So we call tool with documents: [ {page_no:1, doc_type:\"po\"} ].\n\nLet's ensure correct."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"po\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2674, "outputTokens": 104, "totalTokens": 2778}, "metrics": {"latencyMs": 635}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/6f963017_OBUNADUYEXKR65OJ917C.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nCox Industrial Equipment, Inc.\nCOX\nPURCHASE ORDER\nPO Box 9\nChesterfield MO 63006\nINDUSTRIAL\nPhone ************ Fax ************\nPurchase Order No.\nEQUIPMENT\nEmail <EMAIL>\n5762\nVendor Info\nShip To\nNASHVILLE WIRE\nHONEYWELL INTELLIGRATED\nPO Box 637935\n9301 Olive Blvd.\nCincinnati OH 45263-7935\nSt Louis MO 63132\nATTN; PO#A002142163\nLEE CROSS\nDate\nShip Schedule\nShip Via\nPayment Terms\n9/16/2025\nNOW\nDAN MORRISON\nNET 30 DAYS\nQuantity\nDescription\nEach\nAmount\n60\n48\" X 46\" wire deck 2500#\n22.79\n1,367.40\nCALL DAN FOR PICK UP\n636-493-0555\nTotal Amount\n$1,367.40\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}