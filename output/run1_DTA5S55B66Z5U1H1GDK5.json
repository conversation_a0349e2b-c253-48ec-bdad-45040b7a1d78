{"classification_result": {"documents": [{"page_no": 1, "doc_type": "log"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 9.278808283852413e-05}, {"X": 1.0, "Y": 1.0}, {"X": 5.838295464855037e-07, "Y": 1.0}]}, "Id": "1e78a233-18fe-4f5c-b1ef-f837a9c40785", "Relationships": [{"Type": "CHILD", "Ids": ["0de0f2fe-c66b-47d9-998c-21e3479e030d", "5879efe8-8241-4725-bf83-adab7bde82c2", "f27dda79-4d24-4ae0-819f-309bdb1606ad", "3243999f-f9e0-426c-8e72-287720361cf6", "7c591722-e252-480e-95ff-258f962afd14", "df07b5ce-60e7-4d33-a859-2c2ed21e4279", "29cb0e67-5749-4f69-8d29-09f60f91030b", "caef76b6-7eef-41b5-90bd-4fc73ef9c645", "4c77b79e-93af-4bee-8b24-6719089d30a4", "8cf8c43a-e892-4158-94c0-10ec3bc6f2c8", "236c0612-b750-4981-8463-fc9ac69836f2", "21a4de0b-27cc-4264-aa15-e6febbbc8f71", "e126ed2b-fb8b-4e64-bf0f-387959a7f755", "39b99aaf-f94b-4a4c-923c-eeb95c3b37ed", "cb7b06f5-a8dd-4985-a804-f604d2cf9590", "d9d92ef3-fc1f-4a61-9518-8f95088bd923", "5bcab2e2-23b7-45c7-85d9-13a64ac782b1", "ecf2a7c5-8646-407a-a61b-75289206a477", "af1d891f-fe01-44d5-a02d-9d9a007f2cee", "1d4eb713-4b25-4370-821d-4d6da9238e7d", "5adffc68-7254-455c-b5a7-f2bb3a58f342", "d659f559-e89b-4c63-859e-b958470c1c75", "0c48aa4b-789b-4bde-8676-50442af83720", "13065bc8-ad1a-4e49-97f5-0c69088b4ff3", "caaaa5db-e18e-49cb-b7a7-25b75ce6ba3e", "cc4164f7-6650-4b09-bd66-31a68db2fdb2", "fef053ad-1a25-4388-bd0d-35433bf3fc76", "0f9fb01c-3523-457b-9645-dafa64f735e8", "09da269d-2e17-4a0a-a401-7d972423e942", "a6dcae5f-6335-4f41-9fae-134697c911de", "a2f9e512-f69b-4270-8d7a-38dd7b07d6f8", "41188844-0d5a-46e3-8b8b-b12ca7775ce3", "1414d723-2b81-467a-a865-7f75c44e9abe", "0401eeba-a801-469f-bb79-b20a2fcfd491", "7297122f-769c-41b3-bcd5-15af4b7544e3", "64d53e16-67ce-4e9c-9277-8c4137a947f4", "ef429def-76a6-45ed-b26c-cf882987dc86"]}]}, {"BlockType": "LINE", "Confidence": 99.48406982421875, "Text": "DATE: 16-Jun Metal Finishing", "Geometry": {"BoundingBox": {"Width": 0.5784920454025269, "Height": 0.04258476570248604, "Left": 0.24103447794914246, "Top": 0.044791046530008316}, "Polygon": [{"X": 0.24103447794914246, "Y": 0.044791046530008316}, {"X": 0.8194988369941711, "Y": 0.04757129028439522}, {"X": 0.8195264935493469, "Y": 0.08737581223249435}, {"X": 0.24106118083000183, "Y": 0.08472535014152527}]}, "Id": "0de0f2fe-c66b-47d9-998c-21e3479e030d", "Relationships": [{"Type": "CHILD", "Ids": ["218af2d2-a216-43ce-a0bf-60524a66ac10", "af8e6f2e-ca2e-4901-a378-248f0f52e789", "b450afb7-ce7e-4e38-95b0-81e30905e9f7", "5284f8ff-d73a-44fb-8f94-4fdb68ddae66"]}]}, {"BlockType": "LINE", "Confidence": 94.455810546875, "Text": "UNIT #: 6063/283420 Receipt", "Geometry": {"BoundingBox": {"Width": 0.43151766061782837, "Height": 0.023051977157592773, "Left": 0.2222989797592163, "Top": 0.09197241812944412}, "Polygon": [{"X": 0.2222989797592163, "Y": 0.09197241812944412}, {"X": 0.6538021564483643, "Y": 0.09393174201250076}, {"X": 0.6538166403770447, "Y": 0.1150243952870369}, {"X": 0.22231309115886688, "Y": 0.1131163239479065}]}, "Id": "5879efe8-8241-4725-bf83-adab7bde82c2", "Relationships": [{"Type": "CHILD", "Ids": ["7d6a6c8e-d9e2-437c-b223-6fd7c8e6d06a", "0cdd98f3-f3d8-4b13-95bd-e7e2f506180b", "cfb6ba46-e140-411f-85ef-24fc37041376", "78c8bccb-322e-49e8-a58b-9458edb2e981"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Truck Ready Time:", "Geometry": {"BoundingBox": {"Width": 0.18300969898700714, "Height": 0.01806115172803402, "Left": 0.4778715670108795, "Top": 0.11589237302541733}, "Polygon": [{"X": 0.4778715670108795, "Y": 0.11589237302541733}, {"X": 0.6608694195747375, "Y": 0.11669988185167313}, {"X": 0.6608812808990479, "Y": 0.1339535266160965}, {"X": 0.47788330912590027, "Y": 0.13316379487514496}]}, "Id": "f27dda79-4d24-4ae0-819f-309bdb1606ad", "Relationships": [{"Type": "CHILD", "Ids": ["d9b26482-5ef9-498f-a1b2-bd0cde5d38f5", "f7334df4-240c-4817-8dc6-2660c1e612c8", "e39e100e-f529-466d-a788-2cfac0bbccf7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "DRIVER: KARRA", "Geometry": {"BoundingBox": {"Width": 0.18543614447116852, "Height": 0.014749633148312569, "Left": 0.21138796210289001, "Top": 0.13396279513835907}, "Polygon": [{"X": 0.21138796210289001, "Y": 0.13396279513835907}, {"X": 0.3968146741390228, "Y": 0.13476097583770752}, {"X": 0.3968241214752197, "Y": 0.1487124264240265}, {"X": 0.21139727532863617, "Y": 0.14792880415916443}]}, "Id": "3243999f-f9e0-426c-8e72-287720361cf6", "Relationships": [{"Type": "CHILD", "Ids": ["12431205-19a7-44cb-90ad-51d62ea9ce4d", "3c1424a8-47c3-47b3-804c-62134ed4a056"]}]}, {"BlockType": "LINE", "Confidence": 99.98007202148438, "Text": "Truck Departure:", "Geometry": {"BoundingBox": {"Width": 0.16578447818756104, "Height": 0.017503205686807632, "Left": 0.47829118371009827, "Top": 0.15436407923698425}, "Polygon": [{"X": 0.47829118371009827, "Y": 0.15436407923698425}, {"X": 0.6440641283988953, "Y": 0.15505969524383545}, {"X": 0.6440756320953369, "Y": 0.1718672811985016}, {"X": 0.47830259799957275, "Y": 0.17118734121322632}]}, "Id": "7c591722-e252-480e-95ff-258f962afd14", "Relationships": [{"Type": "CHILD", "Ids": ["404d7965-6d25-415f-b750-923b9ee861c5", "cd8e257c-9db3-4084-8d9c-372ca55b278a"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "DEPARTURE TIME: 5am", "Geometry": {"BoundingBox": {"Width": 0.26424476504325867, "Height": 0.015759959816932678, "Left": 0.10690374672412872, "Top": 0.17184032499790192}, "Polygon": [{"X": 0.10690374672412872, "Y": 0.17184032499790192}, {"X": 0.3711386024951935, "Y": 0.17292088270187378}, {"X": 0.3711484968662262, "Y": 0.1876002848148346}, {"X": 0.1069134846329689, "Y": 0.18654154241085052}]}, "Id": "df07b5ce-60e7-4d33-a859-2c2ed21e4279", "Relationships": [{"Type": "CHILD", "Ids": ["84d0e936-ad24-4031-bbbd-cb4a278af61b", "2571f27b-846a-417c-890d-2efaa6fa0109", "3dfcb3fd-c4ea-43b7-860b-c8dc9fd290f7"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "PICK", "Geometry": {"BoundingBox": {"Width": 0.04759252443909645, "Height": 0.012118658982217312, "Left": 0.34071293473243713, "Top": 0.2065531313419342}, "Polygon": [{"X": 0.34071293473243713, "Y": 0.2065531313419342}, {"X": 0.3882974088191986, "Y": 0.2067386955022812}, {"X": 0.3883054554462433, "Y": 0.2186717987060547}, {"X": 0.3407209813594818, "Y": 0.21848942339420319}]}, "Id": "29cb0e67-5749-4f69-8d29-09f60f91030b", "Relationships": [{"Type": "CHILD", "Ids": ["987f054d-94c7-4d04-b3f6-9cc443787414"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "UP DEL", "Geometry": {"BoundingBox": {"Width": 0.10688525438308716, "Height": 0.01430320180952549, "Left": 0.35068249702453613, "Top": 0.22607578337192535}, "Polygon": [{"X": 0.35068249702453613, "Y": 0.22607578337192535}, {"X": 0.4575583040714264, "Y": 0.2264808565378189}, {"X": 0.4575677514076233, "Y": 0.2403789907693863}, {"X": 0.35069185495376587, "Y": 0.23998227715492249}]}, "Id": "caef76b6-7eef-41b5-90bd-4fc73ef9c645", "Relationships": [{"Type": "CHILD", "Ids": ["f7d52a90-cb61-437e-aa25-756aa942142f", "b1b1a823-d74f-495b-8bf1-3a2d889c0288"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Dock Time", "Geometry": {"BoundingBox": {"Width": 0.1077544167637825, "Height": 0.013697322458028793, "Left": 0.8886743187904358, "Top": 0.22138719260692596}, "Polygon": [{"X": 0.8886743187904358, "Y": 0.22138719260692596}, {"X": 0.996419370174408, "Y": 0.22179964184761047}, {"X": 0.9964287281036377, "Y": 0.23508451879024506}, {"X": 0.888683557510376, "Y": 0.2346801459789276}]}, "Id": "4c77b79e-93af-4bee-8b24-6719089d30a4", "Relationships": [{"Type": "CHILD", "Ids": ["8a595527-95b3-491d-b637-702d7bd44ceb", "6c5bf1b0-1e10-4836-80a8-2d7ca9f5cd32"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CUSTOMER", "Geometry": {"BoundingBox": {"Width": 0.11353284120559692, "Height": 0.01488695852458477, "Left": 0.14350344240665436, "Top": 0.22728955745697021}, "Polygon": [{"X": 0.14350344240665436, "Y": 0.22728955745697021}, {"X": 0.2570266127586365, "Y": 0.2277185320854187}, {"X": 0.2570362985134125, "Y": 0.24217650294303894}, {"X": 0.1435130536556244, "Y": 0.24175673723220825}]}, "Id": "8cf8c43a-e892-4158-94c0-10ec3bc6f2c8", "Relationships": [{"Type": "CHILD", "Ids": ["89fd31e8-6db0-4ca9-87a4-b726fea185c5"]}]}, {"BlockType": "LINE", "Confidence": 99.86653900146484, "Text": "WORK ORDER #", "Geometry": {"BoundingBox": {"Width": 0.162194162607193, "Height": 0.014337273314595222, "Left": 0.5946463942527771, "Top": 0.22804084420204163}, "Polygon": [{"X": 0.5946463942527771, "Y": 0.22804084420204163}, {"X": 0.7568311095237732, "Y": 0.22865459322929382}, {"X": 0.7568405866622925, "Y": 0.2423781156539917}, {"X": 0.5946558117866516, "Y": 0.24177691340446472}]}, "Id": "236c0612-b750-4981-8463-fc9ac69836f2", "Relationships": [{"Type": "CHILD", "Ids": ["0614a9f0-1874-4a0b-9f9b-52dd5c462084", "9e36a8bb-db33-4018-8bb5-bfc84ed0b4fc", "c82e6b02-6e86-4657-b4c0-293682f4a9f6"]}]}, {"BlockType": "LINE", "Confidence": 94.54081726074219, "Text": "ISK", "Geometry": {"BoundingBox": {"Width": 0.06304258108139038, "Height": 0.026615964248776436, "Left": 0.4711299538612366, "Top": 0.2555736005306244}, "Polygon": [{"X": 0.4711299538612366, "Y": 0.2555736005306244}, {"X": 0.5341545343399048, "Y": 0.255802184343338}, {"X": 0.534172534942627, "Y": 0.2821895480155945}, {"X": 0.4711478650569916, "Y": 0.281970351934433}]}, "Id": "21a4de0b-27cc-4264-aa15-e6febbbc8f71", "Relationships": [{"Type": "CHILD", "Ids": ["79f85b34-81c7-4b29-afc5-da7cf62ba32a"]}]}, {"BlockType": "LINE", "Confidence": 99.77140808105469, "Text": "1020", "Geometry": {"BoundingBox": {"Width": 0.07413194328546524, "Height": 0.01809423230588436, "Left": 0.8994511961936951, "Top": 0.25593405961990356}, "Polygon": [{"X": 0.8994511961936951, "Y": 0.25593405961990356}, {"X": 0.9735706448554993, "Y": 0.25620338320732117}, {"X": 0.9735831618309021, "Y": 0.27402830123901367}, {"X": 0.8994636535644531, "Y": 0.2737664580345154}]}, "Id": "e126ed2b-fb8b-4e64-bf0f-387959a7f755", "Relationships": [{"Type": "CHILD", "Ids": ["d2d54ac6-b0a8-4fab-b193-0f5ceb0bf83c"]}]}, {"BlockType": "LINE", "Confidence": 99.9609375, "Text": "2", "Geometry": {"BoundingBox": {"Width": 0.03494357317686081, "Height": 0.0365309901535511, "Left": 0.012326158583164215, "Top": 0.26877906918525696}, "Polygon": [{"X": 0.012326158583164215, "Y": 0.26877906918525696}, {"X": 0.04724571481347084, "Y": 0.26890280842781067}, {"X": 0.047269731760025024, "Y": 0.30531007051467896}, {"X": 0.01235012337565422, "Y": 0.3051934838294983}]}, "Id": "39b99aaf-f94b-4a4c-923c-eeb95c3b37ed", "Relationships": [{"Type": "CHILD", "Ids": ["53a50d34-d455-40b6-ba52-e87449e364ac"]}]}, {"BlockType": "LINE", "Confidence": 76.5587158203125, "Text": "1sm Box", "Geometry": {"BoundingBox": {"Width": 0.17747357487678528, "Height": 0.029952935874462128, "Left": 0.09863375127315521, "Top": 0.263430118560791}, "Polygon": [{"X": 0.09863375127315521, "Y": 0.263430118560791}, {"X": 0.2760876715183258, "Y": 0.2640645205974579}, {"X": 0.2761073112487793, "Y": 0.29338306188583374}, {"X": 0.09865318238735199, "Y": 0.2927778959274292}]}, "Id": "cb7b06f5-a8dd-4985-a804-f604d2cf9590", "Relationships": [{"Type": "CHILD", "Ids": ["ed403e32-16ce-4522-a9f7-cd9fa3d7270d", "d52f6960-5b6f-4eef-8ae5-c0364eaed8f8"]}]}, {"BlockType": "LINE", "Confidence": 99.8396987915039, "Text": "Automation & Modular", "Geometry": {"BoundingBox": {"Width": 0.22368553280830383, "Height": 0.01609792374074459, "Left": 0.07606939226388931, "Top": 0.28632867336273193}, "Polygon": [{"X": 0.07606939226388931, "Y": 0.28632867336273193}, {"X": 0.2997446358203888, "Y": 0.2870994508266449}, {"X": 0.29975494742393494, "Y": 0.30242660641670227}, {"X": 0.07607953995466232, "Y": 0.30167508125305176}]}, "Id": "d9d92ef3-fc1f-4a61-9518-8f95088bd923", "Relationships": [{"Type": "CHILD", "Ids": ["5dd93331-9d8d-4c67-b389-d5231e79a784", "c95ccd77-c4c9-4528-b48e-bdbbd6ca5f58", "01854019-5444-4b33-89bd-c66105d49e40"]}]}, {"BlockType": "LINE", "Confidence": 97.45296478271484, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.012313039042055607, "Height": 0.011039203964173794, "Left": 0.3577520251274109, "Top": 0.2902805209159851}, "Polygon": [{"X": 0.3577520251274109, "Y": 0.2902805209159851}, {"X": 0.3700576424598694, "Y": 0.2903227210044861}, {"X": 0.3700650632381439, "Y": 0.3013197183609009}, {"X": 0.35775941610336304, "Y": 0.301278293132782}]}, "Id": "5bcab2e2-23b7-45c7-85d9-13a64ac782b1", "Relationships": [{"Type": "CHILD", "Ids": ["1253b475-9645-4730-9ef5-a65f81d93668"]}]}, {"BlockType": "LINE", "Confidence": 97.02716827392578, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.15388645231723785, "Height": 0.013512854464352131, "Left": 0.43144533038139343, "Top": 0.2887952923774719}, "Polygon": [{"X": 0.43144533038139343, "Y": 0.2887952923774719}, {"X": 0.585322916507721, "Y": 0.28932449221611023}, {"X": 0.5853317975997925, "Y": 0.3023081421852112}, {"X": 0.4314541518688202, "Y": 0.3017902076244354}]}, "Id": "ecf2a7c5-8646-407a-a61b-75289206a477", "Relationships": [{"Type": "CHILD", "Ids": ["59622719-96ec-4b6c-a618-003ee6c075a3", "327254e7-b862-44dc-aceb-41bd0894410c"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "1030", "Geometry": {"BoundingBox": {"Width": 0.07263769209384918, "Height": 0.019073421135544777, "Left": 0.8952206969261169, "Top": 0.3086911737918854}, "Polygon": [{"X": 0.8952206969261169, "Y": 0.3086911737918854}, {"X": 0.9678452014923096, "Y": 0.30893343687057495}, {"X": 0.9678583741188049, "Y": 0.3277646005153656}, {"X": 0.8952338695526123, "Y": 0.32753005623817444}]}, "Id": "af1d891f-fe01-44d5-a02d-9d9a007f2cee", "Relationships": [{"Type": "CHILD", "Ids": ["faeb3a7c-99a1-4dfd-b82e-eaae15594ed7"]}]}, {"BlockType": "LINE", "Confidence": 66.3318099975586, "Text": "Ism SR", "Geometry": {"BoundingBox": {"Width": 0.14051853120326996, "Height": 0.04179235175251961, "Left": 0.10696327686309814, "Top": 0.3491291403770447}, "Polygon": [{"X": 0.10696327686309814, "Y": 0.3491291403770447}, {"X": 0.24745413661003113, "Y": 0.3495638370513916}, {"X": 0.2474817931652069, "Y": 0.3909215033054352}, {"X": 0.10699068754911423, "Y": 0.39051946997642517}]}, "Id": "1d4eb713-4b25-4370-821d-4d6da9238e7d", "Relationships": [{"Type": "CHILD", "Ids": ["9387227d-f7ae-46ed-9902-7dcf9ce90156", "154c4b9d-a7f7-4dbd-b0df-98e2e1174b80"]}]}, {"BlockType": "LINE", "Confidence": 42.938255310058594, "Text": "Isk", "Geometry": {"BoundingBox": {"Width": 0.06696676462888718, "Height": 0.03149253502488136, "Left": 0.4840385615825653, "Top": 0.35507461428642273}, "Polygon": [{"X": 0.4840385615825653, "Y": 0.35507461428642273}, {"X": 0.5509839653968811, "Y": 0.35527995228767395}, {"X": 0.5510053038597107, "Y": 0.3865671455860138}, {"X": 0.4840598404407501, "Y": 0.3863736093044281}]}, "Id": "5adffc68-7254-455c-b5a7-f2bb3a58f342", "Relationships": [{"Type": "CHILD", "Ids": ["87691ae8-3797-4fe2-8528-edb993c612a2"]}]}, {"BlockType": "LINE", "Confidence": 99.79093933105469, "Text": "1105", "Geometry": {"BoundingBox": {"Width": 0.06733477860689163, "Height": 0.020313508808612823, "Left": 0.897701621055603, "Top": 0.36111822724342346}, "Polygon": [{"X": 0.897701621055603, "Y": 0.36111822724342346}, {"X": 0.9650223255157471, "Y": 0.36132287979125977}, {"X": 0.9650363922119141, "Y": 0.3814317286014557}, {"X": 0.89771568775177, "Y": 0.38123467564582825}]}, "Id": "d659f559-e89b-4c63-859e-b958470c1c75", "Relationships": [{"Type": "CHILD", "Ids": ["b5645586-b83f-47b0-a64e-71598df7d2c2"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "3", "Geometry": {"BoundingBox": {"Width": 0.029292328283190727, "Height": 0.03867295756936073, "Left": 0.015774134546518326, "Top": 0.37743908166885376}, "Polygon": [{"X": 0.015774134546518326, "Y": 0.37743908166885376}, {"X": 0.04504100978374481, "Y": 0.3775249421596527}, {"X": 0.045066460967063904, "Y": 0.4161120355129242}, {"X": 0.015799539163708687, "Y": 0.4160325229167938}]}, "Id": "0c48aa4b-789b-4bde-8676-50442af83720", "Relationships": [{"Type": "CHILD", "Ids": ["e2828f10-610f-4120-af46-0659c52105bd"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "<PERSON>", "Geometry": {"BoundingBox": {"Width": 0.17783896625041962, "Height": 0.01794452965259552, "Left": 0.0775209292769432, "Top": 0.3900987505912781}, "Polygon": [{"X": 0.0775209292769432, "Y": 0.3900987505912781}, {"X": 0.25534823536872864, "Y": 0.3906079828739166}, {"X": 0.2553598880767822, "Y": 0.4080432653427124}, {"X": 0.07753246277570724, "Y": 0.40755146741867065}]}, "Id": "13065bc8-ad1a-4e49-97f5-0c69088b4ff3", "Relationships": [{"Type": "CHILD", "Ids": ["4ed7b1e0-c2b5-4ad3-8edd-d376dff1afad", "457e6821-87ed-4764-9136-a5d024e95d64"]}]}, {"BlockType": "LINE", "Confidence": 93.89568328857422, "Text": "X", "Geometry": {"BoundingBox": {"Width": 0.012405311688780785, "Height": 0.01091103907674551, "Left": 0.3587411046028137, "Top": 0.3937020003795624}, "Polygon": [{"X": 0.3587411046028137, "Y": 0.3937020003795624}, {"X": 0.3711390793323517, "Y": 0.3937372863292694}, {"X": 0.37114641070365906, "Y": 0.40461301803588867}, {"X": 0.3587484359741211, "Y": 0.40457847714424133}]}, "Id": "caaaa5db-e18e-49cb-b7a7-25b75ce6ba3e", "Relationships": [{"Type": "CHILD", "Ids": ["6b70503e-6dc9-433d-a75d-81038c217d63"]}]}, {"BlockType": "LINE", "Confidence": 97.9308853149414, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.15276016294956207, "Height": 0.013504955917596817, "Left": 0.43235671520233154, "Top": 0.3918451964855194}, "Polygon": [{"X": 0.43235671520233154, "Y": 0.3918451964855194}, {"X": 0.5851079225540161, "Y": 0.39228197932243347}, {"X": 0.5851168632507324, "Y": 0.4053501486778259}, {"X": 0.4323655664920807, "Y": 0.404924601316452}]}, "Id": "cc4164f7-6650-4b09-bd66-31a68db2fdb2", "Relationships": [{"Type": "CHILD", "Ids": ["0180c18d-c887-459d-8010-703f7ad2bc82", "ffff36e5-53df-4b5a-a796-e2fe2cd00217"]}]}, {"BlockType": "LINE", "Confidence": 99.392333984375, "Text": "1110", "Geometry": {"BoundingBox": {"Width": 0.06288564950227737, "Height": 0.02031942456960678, "Left": 0.8937227129936218, "Top": 0.4221348166465759}, "Polygon": [{"X": 0.8937227129936218, "Y": 0.4221348166465759}, {"X": 0.9565942287445068, "Y": 0.42230433225631714}, {"X": 0.9566083550453186, "Y": 0.4424542486667633}, {"X": 0.8937367796897888, "Y": 0.44229188561439514}]}, "Id": "fef053ad-1a25-4388-bd0d-35433bf3fc76", "Relationships": [{"Type": "CHILD", "Ids": ["4a83787e-2d76-420d-9443-91c389ce8c5f"]}]}, {"BlockType": "LINE", "Confidence": 61.700416564941406, "Text": "ISK", "Geometry": {"BoundingBox": {"Width": 0.06083182618021965, "Height": 0.027103105559945107, "Left": 0.49607476592063904, "Top": 0.4672451317310333}, "Polygon": [{"X": 0.49607476592063904, "Y": 0.4672451317310333}, {"X": 0.556888222694397, "Y": 0.46739327907562256}, {"X": 0.5569065809249878, "Y": 0.4943482279777527}, {"X": 0.49609312415122986, "Y": 0.49420931935310364}]}, "Id": "0f9fb01c-3523-457b-9645-dafa64f735e8", "Relationships": [{"Type": "CHILD", "Ids": ["e3ec030c-5556-4ca9-a670-b9d05504ee0a"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "0920", "Geometry": {"BoundingBox": {"Width": 0.07250090688467026, "Height": 0.015798991546034813, "Left": 0.9011507034301758, "Top": 0.4685002863407135}, "Polygon": [{"X": 0.9011507034301758, "Y": 0.4685002863407135}, {"X": 0.9736406207084656, "Y": 0.46867677569389343}, {"X": 0.9736515879631042, "Y": 0.48429927229881287}, {"X": 0.9011616110801697, "Y": 0.4841291606426239}]}, "Id": "09da269d-2e17-4a0a-a401-7d972423e942", "Relationships": [{"Type": "CHILD", "Ids": ["6972664e-9e46-4f76-94dc-c3a699d537ff"]}]}, {"BlockType": "LINE", "Confidence": 91.32672882080078, "Text": "D", "Geometry": {"BoundingBox": {"Width": 0.0748872384428978, "Height": 0.03442820534110069, "Left": 0.14307034015655518, "Top": 0.4735470414161682}, "Polygon": [{"X": 0.14307034015655518, "Y": 0.4735470414161682}, {"X": 0.2179347276687622, "Y": 0.47372642159461975}, {"X": 0.21795758605003357, "Y": 0.5079752802848816}, {"X": 0.14309309422969818, "Y": 0.5078102946281433}]}, "Id": "a6dcae5f-6335-4f41-9fae-134697c911de", "Relationships": [{"Type": "CHILD", "Ids": ["2feab0b0-4bef-4762-8f70-4dcd8016ddeb"]}]}, {"BlockType": "LINE", "Confidence": 53.952484130859375, "Text": "I", "Geometry": {"BoundingBox": {"Width": 0.01868954673409462, "Height": 0.02958555333316326, "Left": 0.018603550270199776, "Top": 0.4993377923965454}, "Polygon": [{"X": 0.018603550270199776, "Y": 0.4993377923965454}, {"X": 0.037273623049259186, "Y": 0.49937981367111206}, {"X": 0.037293098866939545, "Y": 0.5289233326911926}, {"X": 0.018623003736138344, "Y": 0.5288844704627991}]}, "Id": "a2f9e512-f69b-4270-8d7a-38dd7b07d6f8", "Relationships": [{"Type": "CHILD", "Ids": ["425e66f3-25a7-4b94-9f0d-01e005524ee9"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Ranger Products", "Geometry": {"BoundingBox": {"Width": 0.16239435970783234, "Height": 0.018141280859708786, "Left": 0.08001936227083206, "Top": 0.5012462139129639}, "Polygon": [{"X": 0.08001936227083206, "Y": 0.5012462139129639}, {"X": 0.2424018383026123, "Y": 0.5016099214553833}, {"X": 0.242413729429245, "Y": 0.5193874835968018}, {"X": 0.0800311267375946, "Y": 0.5190399885177612}]}, "Id": "41188844-0d5a-46e3-8b8b-b12ca7775ce3", "Relationships": [{"Type": "CHILD", "Ids": ["d0b81115-1f75-4678-8fc9-611f6ff3ab35", "75684b0c-1d2d-461e-b474-f48cbc54f174"]}]}, {"BlockType": "LINE", "Confidence": 95.47791290283203, "Text": "X ***********", "Geometry": {"BoundingBox": {"Width": 0.15327268838882446, "Height": 0.014440145343542099, "Left": 0.43216419219970703, "Top": 0.500661313533783}, "Polygon": [{"X": 0.43216419219970703, "Y": 0.500661313533783}, {"X": 0.5854272246360779, "Y": 0.5010057687759399}, {"X": 0.5854368805885315, "Y": 0.515101432800293}, {"X": 0.4321737587451935, "Y": 0.5147691965103149}]}, "Id": "1414d723-2b81-467a-a865-7f75c44e9abe", "Relationships": [{"Type": "CHILD", "Ids": ["81bc081b-63de-480e-acf9-711364199136", "45f1ae60-fd1a-4ece-ab18-bc682131f8d9"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "0925", "Geometry": {"BoundingBox": {"Width": 0.08217073976993561, "Height": 0.0181865356862545, "Left": 0.89959317445755, "Top": 0.5201439261436462}, "Polygon": [{"X": 0.89959317445755, "Y": 0.5201439261436462}, {"X": 0.9817513227462769, "Y": 0.5203200578689575}, {"X": 0.9817639589309692, "Y": 0.5383304953575134}, {"X": 0.8996057510375977, "Y": 0.5381627082824707}]}, "Id": "0401eeba-a801-469f-bb79-b20a2fcfd491", "Relationships": [{"Type": "CHILD", "Ids": ["8e4ac249-4e26-4d11-984a-91b146af2690"]}]}, {"BlockType": "LINE", "Confidence": 88.05464172363281, "Text": "Number", "Geometry": {"BoundingBox": {"Width": 0.09842003136873245, "Height": 0.02030169405043125, "Left": 0.10205579549074173, "Top": 0.5365812182426453}, "Polygon": [{"X": 0.10205579549074173, "Y": 0.5365812182426453}, {"X": 0.2004624307155609, "Y": 0.5367821455001831}, {"X": 0.20047582685947418, "Y": 0.556882917881012}, {"X": 0.10206911712884903, "Y": 0.5566931366920471}]}, "Id": "7297122f-769c-41b3-bcd5-15af4b7544e3", "Relationships": [{"Type": "CHILD", "Ids": ["7639f9d9-44b1-4b53-aff0-ece94401dfb2"]}]}, {"BlockType": "LINE", "Confidence": 89.17888641357422, "Text": "Received", "Geometry": {"BoundingBox": {"Width": 0.12128117680549622, "Height": 0.02038852497935295, "Left": 0.09971673041582108, "Top": 0.5894327759742737}, "Polygon": [{"X": 0.09971673041582108, "Y": 0.5894327759742737}, {"X": 0.22098444402217865, "Y": 0.5896443724632263}, {"X": 0.2209979146718979, "Y": 0.6098212599754333}, {"X": 0.09973010420799255, "Y": 0.6096234321594238}]}, "Id": "64d53e16-67ce-4e9c-9277-8c4137a947f4", "Relationships": [{"Type": "CHILD", "Ids": ["03c09be1-a021-40f8-872a-ecd17d87f4a9"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "Detroit", "Geometry": {"BoundingBox": {"Width": 0.0661182627081871, "Height": 0.013595602475106716, "Left": 0.49973756074905396, "Top": 0.9587586522102356}, "Polygon": [{"X": 0.49973756074905396, "Y": 0.9587804079055786}, {"X": 0.5658465623855591, "Y": 0.9587586522102356}, {"X": 0.5658558011054993, "Y": 0.9723274111747742}, {"X": 0.49974679946899414, "Y": 0.9723542332649231}]}, "Id": "ef429def-76a6-45ed-b26c-cf882987dc86", "Relationships": [{"Type": "CHILD", "Ids": ["4b336bfd-97de-476d-afd0-e49c1f6c9420"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DATE:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06306497007608414, "Height": 0.014264742843806744, "Left": 0.2410423904657364, "Top": 0.056620508432388306}, "Polygon": [{"X": 0.2410423904657364, "Y": 0.056620508432388306}, {"X": 0.304097980260849, "Y": 0.05691937729716301}, {"X": 0.3041073679924011, "Y": 0.07088524848222733}, {"X": 0.24105173349380493, "Y": 0.07059133052825928}]}, "Id": "218af2d2-a216-43ce-a0bf-60524a66ac10"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "16-Jun", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06518691033124924, "Height": 0.01432514376938343, "Left": 0.32997673749923706, "Top": 0.057563357055187225}, "Polygon": [{"X": 0.32997673749923706, "Y": 0.057563357055187225}, {"X": 0.3951541781425476, "Y": 0.057872094213962555}, {"X": 0.3951636254787445, "Y": 0.0718884989619255}, {"X": 0.3299861550331116, "Y": 0.07158490270376205}]}, "Id": "af8e6f2e-ca2e-4901-a378-248f0f52e789"}, {"BlockType": "WORD", "Confidence": 98.61985778808594, "Text": "Metal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.13313019275665283, "Height": 0.029575800523161888, "Left": 0.4547988772392273, "Top": 0.04649011418223381}, "Polygon": [{"X": 0.4547988772392273, "Y": 0.04649011418223381}, {"X": 0.5879092812538147, "Y": 0.047129370272159576}, {"X": 0.5879290699958801, "Y": 0.07606591284275055}, {"X": 0.4548185169696808, "Y": 0.07544833421707153}]}, "Id": "b450afb7-ce7e-4e38-95b0-81e30905e9f7"}, {"BlockType": "WORD", "Confidence": 99.423828125, "Text": "Finishing", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.2208331972360611, "Height": 0.04086576774716377, "Left": 0.598693311214447, "Top": 0.04651004448533058}, "Polygon": [{"X": 0.598693311214447, "Y": 0.04651004448533058}, {"X": 0.8194988369941711, "Y": 0.04757129028439522}, {"X": 0.8195264935493469, "Y": 0.08737581223249435}, {"X": 0.5987206101417542, "Y": 0.08636410534381866}]}, "Id": "5284f8ff-d73a-44fb-8f94-4fdb68ddae66"}, {"BlockType": "WORD", "Confidence": 99.63787078857422, "Text": "UNIT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0550232008099556, "Height": 0.014172817580401897, "Left": 0.22230124473571777, "Top": 0.09536857157945633}, "Polygon": [{"X": 0.22230124473571777, "Y": 0.09536857157945633}, {"X": 0.2773151099681854, "Y": 0.09561731666326523}, {"X": 0.2773244380950928, "Y": 0.1095413863658905}, {"X": 0.22231054306030273, "Y": 0.10929694026708603}]}, "Id": "7d6a6c8e-d9e2-437c-b223-6fd7c8e6d06a"}, {"BlockType": "WORD", "Confidence": 99.482421875, "Text": "#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023738034069538116, "Height": 0.013923609629273415, "Left": 0.2802562117576599, "Top": 0.09558170288801193}, "Polygon": [{"X": 0.2802562117576599, "Y": 0.09558170288801193}, {"X": 0.30398496985435486, "Y": 0.09568899869918823}, {"X": 0.30399423837661743, "Y": 0.1095053106546402}, {"X": 0.2802654802799225, "Y": 0.10939985513687134}]}, "Id": "0cdd98f3-f3d8-4b13-95bd-e7e2f506180b"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "6063/283420", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.1311330497264862, "Height": 0.016931181773543358, "Left": 0.328524649143219, "Top": 0.09573021531105042}, "Polygon": [{"X": 0.328524649143219, "Y": 0.09573021531105042}, {"X": 0.459646612405777, "Y": 0.09632318466901779}, {"X": 0.4596576690673828, "Y": 0.11266139149665833}, {"X": 0.32853564620018005, "Y": 0.11208047717809677}]}, "Id": "cfb6ba46-e140-411f-85ef-24fc37041376"}, {"BlockType": "WORD", "Confidence": 78.73226165771484, "Text": "Receipt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12082139402627945, "Height": 0.02164120227098465, "Left": 0.5329952239990234, "Top": 0.09338319301605225}, "Polygon": [{"X": 0.5329952239990234, "Y": 0.09338319301605225}, {"X": 0.6538021564483643, "Y": 0.09393174201250076}, {"X": 0.6538166403770447, "Y": 0.1150243952870369}, {"X": 0.5330096483230591, "Y": 0.11449019610881805}]}, "Id": "78c8bccb-322e-49e8-a58b-9458edb2e981"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054961513727903366, "Height": 0.014361806213855743, "Left": 0.4778715670108795, "Top": 0.11589861661195755}, "Polygon": [{"X": 0.4778715670108795, "Y": 0.11589861661195755}, {"X": 0.5328234434127808, "Y": 0.11614110320806503}, {"X": 0.5328330993652344, "Y": 0.1302604228258133}, {"X": 0.47788116335868835, "Y": 0.1300223171710968}]}, "Id": "d9b26482-5ef9-498f-a1b2-bd0cde5d38f5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Ready", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06326800584793091, "Height": 0.0175386443734169, "Left": 0.5371868014335632, "Top": 0.11615411192178726}, "Polygon": [{"X": 0.5371868014335632, "Y": 0.11615411192178726}, {"X": 0.6004430055618286, "Y": 0.11643324047327042}, {"X": 0.6004548072814941, "Y": 0.13369275629520416}, {"X": 0.5371986031532288, "Y": 0.13341978192329407}]}, "Id": "f7334df4-240c-4817-8dc6-2660c1e612c8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05460979789495468, "Height": 0.013918382115662098, "Left": 0.606269359588623, "Top": 0.11698733270168304}, "Polygon": [{"X": 0.606269359588623, "Y": 0.11698733270168304}, {"X": 0.6608697772026062, "Y": 0.11722810566425323}, {"X": 0.6608791947364807, "Y": 0.13090571761131287}, {"X": 0.6062787175178528, "Y": 0.13066914677619934}]}, "Id": "e39e100e-f529-466d-a788-2cfac0bbccf7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DRIVER:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08578747510910034, "Height": 0.014140261337161064, "Left": 0.21138809621334076, "Top": 0.13415104150772095}, "Polygon": [{"X": 0.21138809621334076, "Y": 0.13415104150772095}, {"X": 0.2971663177013397, "Y": 0.13452018797397614}, {"X": 0.2971755564212799, "Y": 0.14829130470752716}, {"X": 0.21139727532863617, "Y": 0.14792880415916443}]}, "Id": "12431205-19a7-44cb-90ad-51d62ea9ce4d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "KARRA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06762655079364777, "Height": 0.013936483301222324, "Left": 0.32919734716415405, "Top": 0.1344699114561081}, "Polygon": [{"X": 0.32919734716415405, "Y": 0.1344699114561081}, {"X": 0.3968146741390228, "Y": 0.13476097583770752}, {"X": 0.396823912858963, "Y": 0.14840640127658844}, {"X": 0.32920652627944946, "Y": 0.14812052249908447}]}, "Id": "3c1424a8-47c3-47b3-804c-62134ed4a056"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Truck", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054722610861063004, "Height": 0.014246786013245583, "Left": 0.47829118371009827, "Top": 0.15436407923698425}, "Polygon": [{"X": 0.47829118371009827, "Y": 0.15436407923698425}, {"X": 0.5330042243003845, "Y": 0.15459366142749786}, {"X": 0.5330138206481934, "Y": 0.1686108559370041}, {"X": 0.47830069065093994, "Y": 0.1683855801820755}]}, "Id": "404d7965-6d25-415f-b750-923b9ee861c5"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Departure:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10634840279817581, "Height": 0.0170254185795784, "Left": 0.5377272367477417, "Top": 0.1548418551683426}, "Polygon": [{"X": 0.5377272367477417, "Y": 0.1548418551683426}, {"X": 0.6440642476081848, "Y": 0.1552879363298416}, {"X": 0.6440756320953369, "Y": 0.1718672811985016}, {"X": 0.537738561630249, "Y": 0.17143112421035767}]}, "Id": "cd8e257c-9db3-4084-8d9c-372ca55b278a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEPARTURE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12694238126277924, "Height": 0.015209810808300972, "Left": 0.10690374672412872, "Top": 0.17184032499790192}, "Polygon": [{"X": 0.10690374672412872, "Y": 0.17184032499790192}, {"X": 0.2338363081216812, "Y": 0.1723594069480896}, {"X": 0.23384612798690796, "Y": 0.18705014884471893}, {"X": 0.1069134846329689, "Y": 0.18654154241085052}]}, "Id": "84d0e936-ad24-4031-bbbd-cb4a278af61b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "TIME:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05687609314918518, "Height": 0.013824240304529667, "Left": 0.2397363930940628, "Top": 0.17291611433029175}, "Polygon": [{"X": 0.2397363930940628, "Y": 0.17291611433029175}, {"X": 0.29660338163375854, "Y": 0.1731484979391098}, {"X": 0.2966125011444092, "Y": 0.18674035370349884}, {"X": 0.23974548280239105, "Y": 0.1865123212337494}]}, "Id": "2571f27b-846a-417c-890d-2efaa6fa0109"}, {"BlockType": "WORD", "Confidence": 99.765625, "Text": "5am", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04241609573364258, "Height": 0.01403665728867054, "Left": 0.3287319242954254, "Top": 0.17282900214195251}, "Polygon": [{"X": 0.3287319242954254, "Y": 0.17282900214195251}, {"X": 0.37113866209983826, "Y": 0.17300240695476532}, {"X": 0.371148020029068, "Y": 0.1868656575679779}, {"X": 0.32874125242233276, "Y": 0.18669557571411133}]}, "Id": "3dfcb3fd-c4ea-43b7-860b-c8dc9fd290f7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PICK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04759252443909645, "Height": 0.012118658982217312, "Left": 0.34071293473243713, "Top": 0.2065531313419342}, "Polygon": [{"X": 0.34071293473243713, "Y": 0.2065531313419342}, {"X": 0.3882974088191986, "Y": 0.2067386955022812}, {"X": 0.3883054554462433, "Y": 0.2186717987060547}, {"X": 0.3407209813594818, "Y": 0.21848942339420319}]}, "Id": "987f054d-94c7-4d04-b3f6-9cc443787414"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "UP", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02702987566590309, "Height": 0.013917472213506699, "Left": 0.35068249702453613, "Top": 0.22607578337192535}, "Polygon": [{"X": 0.35068249702453613, "Y": 0.22607578337192535}, {"X": 0.3777030408382416, "Y": 0.22617819905281067}, {"X": 0.3777123689651489, "Y": 0.23999325931072235}, {"X": 0.3506917953491211, "Y": 0.23989294469356537}]}, "Id": "f7d52a90-cb61-437e-aa25-756aa942142f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "DEL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.039036087691783905, "Height": 0.013744252733886242, "Left": 0.4185316562652588, "Top": 0.22663474082946777}, "Polygon": [{"X": 0.4185316562652588, "Y": 0.22663474082946777}, {"X": 0.4575585126876831, "Y": 0.22678259015083313}, {"X": 0.4575677514076233, "Y": 0.2403789907693863}, {"X": 0.4185408651828766, "Y": 0.2402341216802597}]}, "Id": "b1b1a823-d74f-495b-8bf1-3a2d889c0288"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Dock", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05175027623772621, "Height": 0.013383576646447182, "Left": 0.8886743783950806, "Top": 0.2214907556772232}, "Polygon": [{"X": 0.8886743783950806, "Y": 0.2214907556772232}, {"X": 0.9404154419898987, "Y": 0.22168879210948944}, {"X": 0.9404246807098389, "Y": 0.23487433791160583}, {"X": 0.888683557510376, "Y": 0.2346801459789276}]}, "Id": "8a595527-95b3-491d-b637-702d7bd44ceb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05237412080168724, "Height": 0.0130130834877491, "Left": 0.9440542459487915, "Top": 0.22159919142723083}, "Polygon": [{"X": 0.9440542459487915, "Y": 0.22159919142723083}, {"X": 0.996419370174408, "Y": 0.22179964184761047}, {"X": 0.996428370475769, "Y": 0.23461227118968964}, {"X": 0.9440632462501526, "Y": 0.23441560566425323}]}, "Id": "6c5bf1b0-1e10-4836-80a8-2d7ca9f5cd32"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CUSTOMER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11353284120559692, "Height": 0.01488695852458477, "Left": 0.14350344240665436, "Top": 0.22728955745697021}, "Polygon": [{"X": 0.14350344240665436, "Y": 0.22728955745697021}, {"X": 0.2570266127586365, "Y": 0.2277185320854187}, {"X": 0.2570362985134125, "Y": 0.24217650294303894}, {"X": 0.1435130536556244, "Y": 0.24175673723220825}]}, "Id": "89fd31e8-6db0-4ca9-87a4-b726fea185c5"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WORK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06397752463817596, "Height": 0.013774982653558254, "Left": 0.5946465730667114, "Top": 0.2282390594482422}, "Polygon": [{"X": 0.5946465730667114, "Y": 0.2282390594482422}, {"X": 0.6586147546768188, "Y": 0.22848105430603027}, {"X": 0.6586240530014038, "Y": 0.2420140504837036}, {"X": 0.5946558117866516, "Y": 0.24177691340446472}]}, "Id": "0614a9f0-1874-4a0b-9f9b-52dd5c462084"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07157399505376816, "Height": 0.013838260434567928, "Left": 0.6646826267242432, "Top": 0.22842003405094147}, "Polygon": [{"X": 0.6646826267242432, "Y": 0.22842003405094147}, {"X": 0.7362472414970398, "Y": 0.22869080305099487}, {"X": 0.7362566590309143, "Y": 0.24225829541683197}, {"X": 0.6646919846534729, "Y": 0.2419929951429367}]}, "Id": "9e36a8bb-db33-4018-8bb5-bfc84ed0b4fc"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014006056822836399, "Height": 0.013430176302790642, "Left": 0.7428342700004578, "Top": 0.2286016196012497}, "Polygon": [{"X": 0.7428342700004578, "Y": 0.2286016196012497}, {"X": 0.7568311095237732, "Y": 0.22865459322929382}, {"X": 0.7568403482437134, "Y": 0.24203179776668549}, {"X": 0.742843508720398, "Y": 0.24197988212108612}]}, "Id": "c82e6b02-6e86-4657-b4c0-293682f4a9f6"}, {"BlockType": "WORD", "Confidence": 94.54081726074219, "Text": "ISK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06304258108139038, "Height": 0.026615964248776436, "Left": 0.4711299538612366, "Top": 0.2555736005306244}, "Polygon": [{"X": 0.4711299538612366, "Y": 0.2555736005306244}, {"X": 0.5341545343399048, "Y": 0.255802184343338}, {"X": 0.534172534942627, "Y": 0.2821895480155945}, {"X": 0.4711478650569916, "Y": 0.281970351934433}]}, "Id": "79f85b34-81c7-4b29-afc5-da7cf62ba32a"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "1020", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07413194328546524, "Height": 0.01809423230588436, "Left": 0.8994511961936951, "Top": 0.25593405961990356}, "Polygon": [{"X": 0.8994511961936951, "Y": 0.25593405961990356}, {"X": 0.9735706448554993, "Y": 0.25620338320732117}, {"X": 0.9735831618309021, "Y": 0.27402830123901367}, {"X": 0.8994636535644531, "Y": 0.2737664580345154}]}, "Id": "d2d54ac6-b0a8-4fab-b193-0f5ceb0bf83c"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "2", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.03494357317686081, "Height": 0.0365309901535511, "Left": 0.012326158583164215, "Top": 0.26877906918525696}, "Polygon": [{"X": 0.012326158583164215, "Y": 0.26877906918525696}, {"X": 0.04724571481347084, "Y": 0.26890280842781067}, {"X": 0.047269731760025024, "Y": 0.30531007051467896}, {"X": 0.01235012337565422, "Y": 0.3051934838294983}]}, "Id": "53a50d34-d455-40b6-ba52-e87449e364ac"}, {"BlockType": "WORD", "Confidence": 55.59450912475586, "Text": "1sm", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08360423147678375, "Height": 0.029632816091179848, "Left": 0.09863375127315521, "Top": 0.263430118560791}, "Polygon": [{"X": 0.09863375127315521, "Y": 0.263430118560791}, {"X": 0.18221844732761383, "Y": 0.26372894644737244}, {"X": 0.18223798274993896, "Y": 0.2930629253387451}, {"X": 0.09865318238735199, "Y": 0.2927778959274292}]}, "Id": "ed403e32-16ce-4522-a9f7-cd9fa3d7270d"}, {"BlockType": "WORD", "Confidence": 97.52291870117188, "Text": "Box", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07984447479248047, "Height": 0.02683817595243454, "Left": 0.19626189768314362, "Top": 0.2651192247867584}, "Polygon": [{"X": 0.19626189768314362, "Y": 0.2651192247867584}, {"X": 0.27608856558799744, "Y": 0.26540398597717285}, {"X": 0.2761063575744629, "Y": 0.29195737838745117}, {"X": 0.1962796002626419, "Y": 0.29168450832366943}]}, "Id": "d52f6960-5b6f-4eef-8ae5-c0364eaed8f8"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Automation", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11445844918489456, "Height": 0.01437823660671711, "Left": 0.07607028633356094, "Top": 0.28768137097358704}, "Polygon": [{"X": 0.07607028633356094, "Y": 0.28768137097358704}, {"X": 0.19051942229270935, "Y": 0.28807491064071655}, {"X": 0.1905287355184555, "Y": 0.3020596206188202}, {"X": 0.07607953995466232, "Y": 0.30167508125305176}]}, "Id": "5dd93331-9d8d-4c67-b389-d5231e79a784"}, {"BlockType": "WORD", "Confidence": 99.736328125, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016658298671245575, "Height": 0.01389613002538681, "Left": 0.1974191516637802, "Top": 0.28755438327789307}, "Polygon": [{"X": 0.1974191516637802, "Y": 0.28755438327789307}, {"X": 0.214068204164505, "Y": 0.2876116931438446}, {"X": 0.2140774428844452, "Y": 0.3014505207538605}, {"X": 0.1974283754825592, "Y": 0.301394522190094}]}, "Id": "c95ccd77-c4c9-4528-b48e-bdbbd6ca5f58"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "Modular", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07967866957187653, "Height": 0.014451124705374241, "Left": 0.22007548809051514, "Top": 0.28682491183280945}, "Polygon": [{"X": 0.22007548809051514, "Y": 0.28682491183280945}, {"X": 0.2997446358203888, "Y": 0.2870994508266449}, {"X": 0.29975417256355286, "Y": 0.3012760579586029}, {"X": 0.22008496522903442, "Y": 0.30100783705711365}]}, "Id": "01854019-5444-4b33-89bd-c66105d49e40"}, {"BlockType": "WORD", "Confidence": 97.45296478271484, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012313039042055607, "Height": 0.011039203964173794, "Left": 0.3577520251274109, "Top": 0.2902805209159851}, "Polygon": [{"X": 0.3577520251274109, "Y": 0.2902805209159851}, {"X": 0.3700576424598694, "Y": 0.2903227210044861}, {"X": 0.3700650632381439, "Y": 0.3013197183609009}, {"X": 0.35775941610336304, "Y": 0.301278293132782}]}, "Id": "1253b475-9645-4730-9ef5-a65f81d93668"}, {"BlockType": "WORD", "Confidence": 94.6619873046875, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012255001813173294, "Height": 0.01109025627374649, "Left": 0.4314466714859009, "Top": 0.2907411754131317}, "Polygon": [{"X": 0.4314466714859009, "Y": 0.2907411754131317}, {"X": 0.44369417428970337, "Y": 0.290783166885376}, {"X": 0.4437016546726227, "Y": 0.3018314242362976}, {"X": 0.4314541518688202, "Y": 0.3017902076244354}]}, "Id": "59622719-96ec-4b6c-a618-003ee6c075a3"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11055707931518555, "Height": 0.01222818810492754, "Left": 0.47477394342422485, "Top": 0.2889443039894104}, "Polygon": [{"X": 0.47477394342422485, "Y": 0.2889443039894104}, {"X": 0.585322916507721, "Y": 0.28932449221611023}, {"X": 0.5853310227394104, "Y": 0.30117249488830566}, {"X": 0.47478199005126953, "Y": 0.300799697637558}]}, "Id": "327254e7-b862-44dc-aceb-41bd0894410c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "1030", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07263769209384918, "Height": 0.019073421135544777, "Left": 0.8952206969261169, "Top": 0.3086911737918854}, "Polygon": [{"X": 0.8952206969261169, "Y": 0.3086911737918854}, {"X": 0.9678452014923096, "Y": 0.30893343687057495}, {"X": 0.9678583741188049, "Y": 0.3277646005153656}, {"X": 0.8952338695526123, "Y": 0.32753005623817444}]}, "Id": "faeb3a7c-99a1-4dfd-b82e-eaae15594ed7"}, {"BlockType": "WORD", "Confidence": 78.18897247314453, "Text": "Ism", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06915627419948578, "Height": 0.041588135063648224, "Left": 0.10696327686309814, "Top": 0.3491291403770447}, "Polygon": [{"X": 0.10696327686309814, "Y": 0.3491291403770447}, {"X": 0.1760920137166977, "Y": 0.34934303164482117}, {"X": 0.17611955106258392, "Y": 0.3907172977924347}, {"X": 0.10699068754911423, "Y": 0.39051946997642517}]}, "Id": "9387227d-f7ae-46ed-9902-7dcf9ce90156"}, {"BlockType": "WORD", "Confidence": 54.47465133666992, "Text": "SR", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.05944705754518509, "Height": 0.03462570160627365, "Left": 0.18803462386131287, "Top": 0.3561254143714905}, "Polygon": [{"X": 0.18803462386131287, "Y": 0.3561254143714905}, {"X": 0.24745865166187286, "Y": 0.3563070297241211}, {"X": 0.24748168885707855, "Y": 0.3907511234283447}, {"X": 0.1880575716495514, "Y": 0.39058101177215576}]}, "Id": "154c4b9d-a7f7-4dbd-b0df-98e2e1174b80"}, {"BlockType": "WORD", "Confidence": 42.938255310058594, "Text": "Isk", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06696676462888718, "Height": 0.03149253502488136, "Left": 0.4840385615825653, "Top": 0.35507461428642273}, "Polygon": [{"X": 0.4840385615825653, "Y": 0.35507461428642273}, {"X": 0.5509839653968811, "Y": 0.35527995228767395}, {"X": 0.5510053038597107, "Y": 0.3865671455860138}, {"X": 0.4840598404407501, "Y": 0.3863736093044281}]}, "Id": "87691ae8-3797-4fe2-8528-edb993c612a2"}, {"BlockType": "WORD", "Confidence": 99.79093933105469, "Text": "1105", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06733477860689163, "Height": 0.020313508808612823, "Left": 0.897701621055603, "Top": 0.36111822724342346}, "Polygon": [{"X": 0.897701621055603, "Y": 0.36111822724342346}, {"X": 0.9650223255157471, "Y": 0.36132287979125977}, {"X": 0.9650363922119141, "Y": 0.3814317286014557}, {"X": 0.89771568775177, "Y": 0.38123467564582825}]}, "Id": "b5645586-b83f-47b0-a64e-71598df7d2c2"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "3", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.029292328283190727, "Height": 0.03867295756936073, "Left": 0.015774134546518326, "Top": 0.37743908166885376}, "Polygon": [{"X": 0.015774134546518326, "Y": 0.37743908166885376}, {"X": 0.04504100978374481, "Y": 0.3775249421596527}, {"X": 0.045066460967063904, "Y": 0.4161120355129242}, {"X": 0.015799539163708687, "Y": 0.4160325229167938}]}, "Id": "e2828f10-610f-4120-af46-0659c52105bd"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08305372297763824, "Height": 0.014071094803512096, "Left": 0.07752134650945663, "Top": 0.3907388150691986}, "Polygon": [{"X": 0.07752134650945663, "Y": 0.3907388150691986}, {"X": 0.16056586802005768, "Y": 0.39097633957862854}, {"X": 0.16057507693767548, "Y": 0.4048099219799042}, {"X": 0.07753049582242966, "Y": 0.4045788645744324}]}, "Id": "4ed7b1e0-c2b5-4ad3-8edd-d376dff1afad"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Gunsight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08847473561763763, "Height": 0.017688628286123276, "Left": 0.1668851524591446, "Top": 0.3903546631336212}, "Polygon": [{"X": 0.1668851524591446, "Y": 0.3903546631336212}, {"X": 0.25534823536872864, "Y": 0.3906079828739166}, {"X": 0.2553598880767822, "Y": 0.4080432653427124}, {"X": 0.1668967604637146, "Y": 0.4077986180782318}]}, "Id": "457e6821-87ed-4764-9136-a5d024e95d64"}, {"BlockType": "WORD", "Confidence": 93.89568328857422, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012405311688780785, "Height": 0.01091103907674551, "Left": 0.3587411046028137, "Top": 0.3937020003795624}, "Polygon": [{"X": 0.3587411046028137, "Y": 0.3937020003795624}, {"X": 0.3711390793323517, "Y": 0.3937372863292694}, {"X": 0.37114641070365906, "Y": 0.40461301803588867}, {"X": 0.3587484359741211, "Y": 0.40457847714424133}]}, "Id": "6b70503e-6dc9-433d-a75d-81038c217d63"}, {"BlockType": "WORD", "Confidence": 96.62886810302734, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012235797010362148, "Height": 0.010933266021311283, "Left": 0.4323582053184509, "Top": 0.39402541518211365}, "Polygon": [{"X": 0.4323582053184509, "Y": 0.39402541518211365}, {"X": 0.4445866048336029, "Y": 0.3940602242946625}, {"X": 0.44459399580955505, "Y": 0.40495866537094116}, {"X": 0.4323655664920807, "Y": 0.404924601316452}]}, "Id": "0180c18d-c887-459d-8010-703f7ad2bc82"}, {"BlockType": "WORD", "Confidence": 99.23290252685547, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10945416241884232, "Height": 0.012276295572519302, "Left": 0.475661963224411, "Top": 0.3919690251350403}, "Polygon": [{"X": 0.475661963224411, "Y": 0.3919690251350403}, {"X": 0.5851079225540161, "Y": 0.39228197932243347}, {"X": 0.5851161479949951, "Y": 0.4042453169822693}, {"X": 0.47567006945610046, "Y": 0.40393972396850586}]}, "Id": "ffff36e5-53df-4b5a-a796-e2fe2cd00217"}, {"BlockType": "WORD", "Confidence": 99.392333984375, "Text": "1110", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06288564950227737, "Height": 0.02031942456960678, "Left": 0.8937227129936218, "Top": 0.4221348166465759}, "Polygon": [{"X": 0.8937227129936218, "Y": 0.4221348166465759}, {"X": 0.9565942287445068, "Y": 0.42230433225631714}, {"X": 0.9566083550453186, "Y": 0.4424542486667633}, {"X": 0.8937367796897888, "Y": 0.44229188561439514}]}, "Id": "4a83787e-2d76-420d-9443-91c389ce8c5f"}, {"BlockType": "WORD", "Confidence": 61.700416564941406, "Text": "ISK", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.06083182618021965, "Height": 0.027103105559945107, "Left": 0.49607476592063904, "Top": 0.4672451317310333}, "Polygon": [{"X": 0.49607476592063904, "Y": 0.4672451317310333}, {"X": 0.556888222694397, "Y": 0.46739327907562256}, {"X": 0.5569065809249878, "Y": 0.4943482279777527}, {"X": 0.49609312415122986, "Y": 0.49420931935310364}]}, "Id": "e3ec030c-5556-4ca9-a670-b9d05504ee0a"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "0920", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.07250090688467026, "Height": 0.015798991546034813, "Left": 0.9011507034301758, "Top": 0.4685002863407135}, "Polygon": [{"X": 0.9011507034301758, "Y": 0.4685002863407135}, {"X": 0.9736406207084656, "Y": 0.46867677569389343}, {"X": 0.9736515879631042, "Y": 0.48429927229881287}, {"X": 0.9011616110801697, "Y": 0.4841291606426239}]}, "Id": "6972664e-9e46-4f76-94dc-c3a699d537ff"}, {"BlockType": "WORD", "Confidence": 91.32672882080078, "Text": "D", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.0748872384428978, "Height": 0.03442820534110069, "Left": 0.14307034015655518, "Top": 0.4735470414161682}, "Polygon": [{"X": 0.14307034015655518, "Y": 0.4735470414161682}, {"X": 0.2179347276687622, "Y": 0.47372642159461975}, {"X": 0.21795758605003357, "Y": 0.5079752802848816}, {"X": 0.14309309422969818, "Y": 0.5078102946281433}]}, "Id": "2feab0b0-4bef-4762-8f70-4dcd8016ddeb"}, {"BlockType": "WORD", "Confidence": 53.952484130859375, "Text": "I", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.01868954673409462, "Height": 0.02958555333316326, "Left": 0.018603550270199776, "Top": 0.4993377923965454}, "Polygon": [{"X": 0.018603550270199776, "Y": 0.4993377923965454}, {"X": 0.037273623049259186, "Y": 0.49937981367111206}, {"X": 0.037293098866939545, "Y": 0.5289233326911926}, {"X": 0.018623003736138344, "Y": 0.5288844704627991}]}, "Id": "425e66f3-25a7-4b94-9f0d-01e005524ee9"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07123996317386627, "Height": 0.017066454514861107, "Left": 0.08001994341611862, "Top": 0.5021259784698486}, "Polygon": [{"X": 0.08001994341611862, "Y": 0.5021259784698486}, {"X": 0.15124867856502533, "Y": 0.5022851228713989}, {"X": 0.1512598991394043, "Y": 0.5191923975944519}, {"X": 0.0800311267375946, "Y": 0.5190399885177612}]}, "Id": "d0b81115-1f75-4678-8fc9-611f6ff3ab35"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Products", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08344366401433945, "Height": 0.01432795636355877, "Left": 0.15896762907505035, "Top": 0.5014230012893677}, "Polygon": [{"X": 0.15896762907505035, "Y": 0.5014230012893677}, {"X": 0.2424018383026123, "Y": 0.5016099214553833}, {"X": 0.2424112856388092, "Y": 0.5157510042190552}, {"X": 0.15897703170776367, "Y": 0.5155707597732544}]}, "Id": "75684b0c-1d2d-461e-b474-f48cbc54f174"}, {"BlockType": "WORD", "Confidence": 91.60335540771484, "Text": "X", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014586550183594227, "Height": 0.014139457605779171, "Left": 0.43216419219970703, "Top": 0.500661313533783}, "Polygon": [{"X": 0.43216419219970703, "Y": 0.500661313533783}, {"X": 0.4467411935329437, "Y": 0.5006940960884094}, {"X": 0.4467507302761078, "Y": 0.5148007869720459}, {"X": 0.4321737587451935, "Y": 0.5147691965103149}]}, "Id": "81bc081b-63de-480e-acf9-711364199136"}, {"BlockType": "WORD", "Confidence": 99.35247802734375, "Text": "***********", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10901995003223419, "Height": 0.01177925430238247, "Left": 0.4764157235622406, "Top": 0.5015275478363037}, "Polygon": [{"X": 0.4764157235622406, "Y": 0.5015275478363037}, {"X": 0.5854277610778809, "Y": 0.5017720460891724}, {"X": 0.585435688495636, "Y": 0.5133067965507507}, {"X": 0.47642356157302856, "Y": 0.5130693316459656}]}, "Id": "45f1ae60-fd1a-4ece-ab18-bc682131f8d9"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "0925", "TextType": "HANDWRITING", "Geometry": {"BoundingBox": {"Width": 0.08217073976993561, "Height": 0.0181865356862545, "Left": 0.89959317445755, "Top": 0.5201439261436462}, "Polygon": [{"X": 0.89959317445755, "Y": 0.5201439261436462}, {"X": 0.9817513227462769, "Y": 0.5203200578689575}, {"X": 0.9817639589309692, "Y": 0.5383304953575134}, {"X": 0.8996057510375977, "Y": 0.5381627082824707}]}, "Id": "8e4ac249-4e26-4d11-984a-91b146af2690"}, {"BlockType": "WORD", "Confidence": 88.05464172363281, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09842003136873245, "Height": 0.02030169405043125, "Left": 0.10205579549074173, "Top": 0.5365812182426453}, "Polygon": [{"X": 0.10205579549074173, "Y": 0.5365812182426453}, {"X": 0.2004624307155609, "Y": 0.5367821455001831}, {"X": 0.20047582685947418, "Y": 0.556882917881012}, {"X": 0.10206911712884903, "Y": 0.5566931366920471}]}, "Id": "7639f9d9-44b1-4b53-aff0-ece94401dfb2"}, {"BlockType": "WORD", "Confidence": 89.17888641357422, "Text": "Received", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.12128117680549622, "Height": 0.02038852497935295, "Left": 0.09971673041582108, "Top": 0.5894327759742737}, "Polygon": [{"X": 0.09971673041582108, "Y": 0.5894327759742737}, {"X": 0.22098444402217865, "Y": 0.5896443724632263}, {"X": 0.2209979146718979, "Y": 0.6098212599754333}, {"X": 0.09973010420799255, "Y": 0.6096234321594238}]}, "Id": "03c09be1-a021-40f8-872a-ecd17d87f4a9"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "Detroit", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0661182627081871, "Height": 0.013595602475106716, "Left": 0.49973756074905396, "Top": 0.9587586522102356}, "Polygon": [{"X": 0.49973756074905396, "Y": 0.9587804079055786}, {"X": 0.5658465623855591, "Y": 0.9587586522102356}, {"X": 0.5658558011054993, "Y": 0.9723274111747742}, {"X": 0.49974679946899414, "Y": 0.9723542332649231}]}, "Id": "4b336bfd-97de-476d-afd0-e49c1f6c9420"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "c45d3259-8585-483a-9656-d1e02ac2ff5a", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "c45d3259-8585-483a-9656-d1e02ac2ff5a", "content-type": "application/x-amz-json-1.1", "content-length": "52093", "date": "Wed, 24 Sep 2025 09:13:40 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "a6db4a0b-39cc-4147-901a-2b52b36b764a", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 09:13:45 GMT", "content-type": "application/json", "content-length": "1847", "connection": "keep-alive", "x-amzn-requestid": "a6db4a0b-39cc-4147-901a-2b52b36b764a"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. Need to classify the document type. Let's examine content. It looks like a log of truck arrival times and work orders. It's a log or ingate/outgate? Let's parse.\n\nPage content: \"DATE: 16-Jun Metal Finishing\" then \"UNIT #: 6063/283420 Receipt\" then \"Truck Ready Time:\" etc. There's \"Driver: KARRA\". \"Truck Departure:\" \"DEPARTURE TIME: 5am\". Then \"PICK UP DEL Dock Time\" maybe columns. Then customer/work order numbers, etc. It seems like a log of arrival/departure times for trucks that delivered items. Possibly a \"log\" document: Activity record. It includes driver name, truck ready, departure time, pick up, etc. Could be ingate/outgate? Ingates: record of vehicle entering facility, with time in. Outgate: exit. This page shows arrival times and departure times? It shows \"Truck Ready Time\" and \"Truck Departure\" maybe departure time. Could be a gate log. The type \"log\" seems appropriate.\n\nCheck for ingate/outgate: Keywords: \"Truck Ready Time\", \"Truck Departure\", \"Dock Time\". This looks like an arrival/departure log for equipment at a facility. Ingate/outgate might have \"Truck In\" \"Truck Out\" \"Gate In\" \"Gate Out\". But \"Truck Ready Time\" may indicate ready for loading. This could be part of a logbook.\n\nThus classify as \"log\".\n\nWe must return one object for page 1 with page_no 1 and doc_type \"log\".\n\nReturn via tool classify_logistics_doc_type: documents array with single entry: {page_no:1, doc_type:\"log\"}.\n\nLet's produce the JSON accordingly."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"log\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2598, "outputTokens": 380, "totalTokens": 2978}, "metrics": {"latencyMs": 1777}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/e9db1b5d_DTA5S55B66Z5U1H1GDK5.jpeg", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nDATE: 16-Jun Metal Finishing\nUNIT #: 6063/283420 Receipt\nTruck Ready Time:\nDRIVER: KARRA\nTruck Departure:\nDEPARTURE TIME: 5am\nPICK\nUP DEL\nDock Time\nCUSTOMER\nWORK ORDER #\nISK\n1020\n2\n1sm Box\nAutomation & Modular\nX\nX ***********\n1030\nIsm SR\nIsk\n1105\n3\nWilliams Gunsight\nX\nX ***********\n1110\nISK\n0920\nD\nI\nRanger Products\nX ***********\n0925\nNumber\nReceived\nDetroit\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}