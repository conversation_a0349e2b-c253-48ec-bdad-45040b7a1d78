{"classification_result": {"documents": [{"page_no": 1, "doc_type": "weight_and_inspection_cert"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 6.21251743382345e-08, "Y": 1.3097367457248765e-07}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "19bff70e-0ce8-455d-a3fe-421f4c3507aa", "Relationships": [{"Type": "CHILD", "Ids": ["e9894398-8a2f-46e2-a690-23b647c7f05c", "8d82b517-cf3b-4738-9142-1ac6ac6483a4", "c2effcc7-3dc1-4749-9a28-7b8a1d9074fb", "455fc542-81f4-4f29-958c-8b8709afabd5", "923a82ba-b47d-494e-a2b6-63e4db710605", "424c0f22-9060-4187-802a-38db7e72b5e5", "723e4a1b-1a47-4d47-a004-8cfa34f87baa", "b312cde2-3163-407f-b158-b2d4ec0e12c1", "740a94f3-3d3a-4876-8f1e-189e7d4b49bc", "cdc63b59-86f1-4f70-b019-fe441421718e", "d22a0932-8691-46be-9f68-fa5179a310bc", "fa87b7ac-9227-4180-ab50-40cfd64dee99", "49272a55-6bd1-4d81-a9f7-b730c01b52cc", "51b16939-539c-4a0f-b370-7e52ef395178", "08290943-8947-4448-bcd2-305506384c6f", "50032fe7-3cca-443a-b007-b027a5ec25f2", "8e081fe4-62c4-4aa4-9287-089b0644cd07", "b1cc5ffd-eb91-46c5-99e8-0dc8321f9491", "10513d5c-8850-46bc-a975-d5015d34c09e", "b77ef8f8-4572-4c7f-b244-8a1f59c1c5c3", "ebd35bbf-54f4-4c06-8984-c000c015f09b", "4f92c018-31e2-4043-8659-4a938479d338", "234ffc72-e41c-4b1d-b6ae-03dff82d4209", "f2cc4c68-55d8-4ca8-a59e-9e91265c61fc", "a84364ca-837b-4006-9fa4-c8f265ebc7af", "ee1786d6-b3f0-4d4d-ba90-1036ac71cb6c", "2e78c609-0fea-4658-b899-ea30e3328ad4", "268550aa-0308-450b-bed3-162d361634d2", "848489f7-8b84-4eb9-9f52-301e803a1866", "aa9e5d10-a12e-4dac-b5ab-1390ea6b9a6c", "6c774529-c7c5-4e82-833b-26978be0644f", "e4876dd9-f011-45d2-aa92-fc9691e067c1", "ba2b591a-3ddb-491b-8324-3f891339ad33", "d9907373-d970-4072-965b-3b548c78988a", "363aedeb-8b4d-42d9-8133-52697a578d32", "e10e8e06-7ecf-46c4-9842-50433e9d6e48", "ed79e61b-17be-4a6b-88b4-bbf3a86a6fda", "609962eb-b5f9-4568-9414-59c68305ccd7", "958c8dc5-6cce-4a36-a5f8-da654ce021d6", "523aa32a-48d9-4704-864d-dc3a8e2db041", "cbba3bfc-32f0-45c1-8d2c-a448ea7b6732", "2e8506d7-84b4-4131-afb2-41c6cbcafaf5", "9b843980-7c34-4030-8f1d-856bce9d1eb3", "b5763c4a-3a90-429c-9c09-01d6017fdc4b", "ad9c68d8-7a1d-483b-9556-a940f102a1a0", "0139d599-0a2a-43a9-916f-a9b936d6c9b4", "b12eaac2-dcee-4b03-9b3e-b9610f0c4735", "41c15a6c-87f0-4e96-bf0a-f07c4348537b", "a83cfc2a-267d-49b3-8c39-d394223eb84f"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "Central Transport", "Geometry": {"BoundingBox": {"Width": 0.1148308515548706, "Height": 0.01157828513532877, "Left": 0.05718902871012688, "Top": 0.05624642223119736}, "Polygon": [{"X": 0.05718908831477165, "Y": 0.05624745413661003}, {"X": 0.17201988399028778, "Y": 0.05624642223119736}, {"X": 0.1720198094844818, "Y": 0.06782364845275879}, {"X": 0.05718902871012688, "Y": 0.06782470643520355}]}, "Id": "e9894398-8a2f-46e2-a690-23b647c7f05c", "Relationships": [{"Type": "CHILD", "Ids": ["c5740969-0a5f-4651-914f-6782fd539d15", "ac08efdc-2343-4ca9-801f-46fbbb4dad0d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "08/31/2025", "Geometry": {"BoundingBox": {"Width": 0.0731736347079277, "Height": 0.009412871673703194, "Left": 0.8657994270324707, "Top": 0.05629698932170868}, "Polygon": [{"X": 0.865799605846405, "Y": 0.05629764869809151}, {"X": 0.938973069190979, "Y": 0.05629698932170868}, {"X": 0.9389729499816895, "Y": 0.065709188580513}, {"X": 0.8657994270324707, "Y": 0.06570985913276672}]}, "Id": "8d82b517-cf3b-4738-9142-1ac6ac6483a4", "Relationships": [{"Type": "CHILD", "Ids": ["dbeb5353-6854-4a21-9b51-fcbbbf283f1d"]}]}, {"BlockType": "LINE", "Confidence": 99.89218139648438, "Text": "PRO#: 227407699", "Geometry": {"BoundingBox": {"Width": 0.12172986567020416, "Height": 0.009408773854374886, "Left": 0.057394713163375854, "Top": 0.07145050168037415}, "Polygon": [{"X": 0.057394761592149734, "Y": 0.07145162671804428}, {"X": 0.17912457883358002, "Y": 0.07145050168037415}, {"X": 0.17912451922893524, "Y": 0.08085812628269196}, {"X": 0.057394713163375854, "Y": 0.08085927367210388}]}, "Id": "c2effcc7-3dc1-4749-9a28-7b8a1d9074fb", "Relationships": [{"Type": "CHILD", "Ids": ["06a8c74f-b7ce-4cf0-adc7-80889a297c36", "07baac9a-5ba9-400e-bf50-91af1738a0a4"]}]}, {"BlockType": "LINE", "Confidence": 99.89476776123047, "Text": "Weight & Inspection", "Geometry": {"BoundingBox": {"Width": 0.13100199401378632, "Height": 0.011712157167494297, "Left": 0.8079518675804138, "Top": 0.07136138528585434}, "Polygon": [{"X": 0.8079520463943481, "Y": 0.07136259227991104}, {"X": 0.9389538764953613, "Y": 0.07136138528585434}, {"X": 0.938953697681427, "Y": 0.08307230472564697}, {"X": 0.8079518675804138, "Y": 0.08307354152202606}]}, "Id": "455fc542-81f4-4f29-958c-8b8709afabd5", "Relationships": [{"Type": "CHILD", "Ids": ["59fcecb7-ebbf-460f-bcf6-940a6b48902a", "4cf094aa-92df-4307-a8c1-08be32d5021c", "1cdb8fce-b350-4f4b-b4af-225afcfcb19d"]}]}, {"BlockType": "LINE", "Confidence": 99.94140625, "Text": "CENTRAL TRANSPORT", "Geometry": {"BoundingBox": {"Width": 0.16294315457344055, "Height": 0.013988837599754333, "Left": 0.4383789896965027, "Top": 0.1918865293264389}, "Polygon": [{"X": 0.43837910890579224, "Y": 0.19188834726810455}, {"X": 0.6013221144676208, "Y": 0.1918865293264389}, {"X": 0.6013219952583313, "Y": 0.205873504281044}, {"X": 0.4383789896965027, "Y": 0.20587536692619324}]}, "Id": "923a82ba-b47d-494e-a2b6-63e4db710605", "Relationships": [{"Type": "CHILD", "Ids": ["e05390b4-3559-4e6e-a86c-e80c10f332a2", "91ec3d8a-cdd6-45ff-b6c5-58abb4f19363"]}]}, {"BlockType": "LINE", "Confidence": 99.88745880126953, "Text": "12225 Stephens Road", "Geometry": {"BoundingBox": {"Width": 0.12882336974143982, "Height": 0.01064684335142374, "Left": 0.4399399757385254, "Top": 0.2119910717010498}, "Polygon": [{"X": 0.43994009494781494, "Y": 0.211992546916008}, {"X": 0.5687633752822876, "Y": 0.2119910717010498}, {"X": 0.568763256072998, "Y": 0.2226364016532898}, {"X": 0.4399399757385254, "Y": 0.22263790667057037}]}, "Id": "424c0f22-9060-4187-802a-38db7e72b5e5", "Relationships": [{"Type": "CHILD", "Ids": ["b0e29156-6db6-4eeb-85ed-4fddef36781f", "cc9a5bd2-b5b0-48f7-9ba6-e4c557c72533", "15051446-dc2c-402d-8a27-4c1f79e154e8"]}]}, {"BlockType": "LINE", "Confidence": 99.85464477539062, "Text": "Warren, MI 48089", "Geometry": {"BoundingBox": {"Width": 0.113776296377182, "Height": 0.010304750874638557, "Left": 0.44664397835731506, "Top": 0.22568704187870026}, "Polygon": [{"X": 0.4466440677642822, "Y": 0.2256883829832077}, {"X": 0.5604202747344971, "Y": 0.22568704187870026}, {"X": 0.5604201555252075, "Y": 0.23599044978618622}, {"X": 0.44664397835731506, "Y": 0.23599180579185486}]}, "Id": "723e4a1b-1a47-4d47-a004-8cfa34f87baa", "Relationships": [{"Type": "CHILD", "Ids": ["d2d00408-3617-48b9-953f-c8f7bf6bec10", "093738d8-f696-4248-92c2-145ba1ae3a6a", "a8622044-a397-47dc-b89f-9876bc6ae27e"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "CERTIFIED WEIGHT CERTIFICATE", "Geometry": {"BoundingBox": {"Width": 0.2301705777645111, "Height": 0.009217815473675728, "Left": 0.39221125841140747, "Top": 0.2900317907333374}, "Polygon": [{"X": 0.39221134781837463, "Y": 0.2900347113609314}, {"X": 0.622381865978241, "Y": 0.2900317907333374}, {"X": 0.6223817467689514, "Y": 0.2992466390132904}, {"X": 0.39221125841140747, "Y": 0.2992495894432068}]}, "Id": "b312cde2-3163-407f-b158-b2d4ec0e12c1", "Relationships": [{"Type": "CHILD", "Ids": ["4926b0f3-b565-4159-8ca5-38b6005ae6cf", "3601383c-c63d-45e4-8cce-be92f0c66329", "411329a3-e516-47cb-a40f-31aba112d032"]}]}, {"BlockType": "LINE", "Confidence": 99.87091827392578, "Text": "Pro Number: 227407699", "Geometry": {"BoundingBox": {"Width": 0.17372579872608185, "Height": 0.009269524365663528, "Left": 0.15433162450790405, "Top": 0.31617191433906555}, "Polygon": [{"X": 0.15433168411254883, "Y": 0.3161742091178894}, {"X": 0.3280574083328247, "Y": 0.31617191433906555}, {"X": 0.32805734872817993, "Y": 0.3254391551017761}, {"X": 0.15433162450790405, "Y": 0.3254414498806}]}, "Id": "740a94f3-3d3a-4876-8f1e-189e7d4b49bc", "Relationships": [{"Type": "CHILD", "Ids": ["29954b23-2fb2-4b56-a7cf-88b215e724a7", "d75c5565-f853-49e6-a6b6-a0a412791e47", "902409a5-3f75-4eb1-8e41-47519486db5e"]}]}, {"BlockType": "LINE", "Confidence": 99.96366119384766, "Text": "Bill of Lading", "Geometry": {"BoundingBox": {"Width": 0.08661317825317383, "Height": 0.011761363595724106, "Left": 0.6965249180793762, "Top": 0.3158475160598755}, "Polygon": [{"X": 0.6965250372886658, "Y": 0.3158486485481262}, {"X": 0.78313809633255, "Y": 0.3158475160598755}, {"X": 0.7831379175186157, "Y": 0.3276077210903168}, {"X": 0.6965249180793762, "Y": 0.3276088535785675}]}, "Id": "cdc63b59-86f1-4f70-b019-fe441421718e", "Relationships": [{"Type": "CHILD", "Ids": ["b722da54-2fc9-4dce-be4b-886d2673edc6", "b89a4180-8735-4eb8-a18c-72aae6f8b9c8", "7276cad5-dbe6-44f9-92e8-3d3aa53ee7dd"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "Date: 08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.1218271404504776, "Height": 0.01059777196496725, "Left": 0.15446539223194122, "Top": 0.32907816767692566}, "Polygon": [{"X": 0.154465451836586, "Y": 0.3290797770023346}, {"X": 0.2762925326824188, "Y": 0.32907816767692566}, {"X": 0.27629244327545166, "Y": 0.33967429399490356}, {"X": 0.15446539223194122, "Y": 0.3396759331226349}]}, "Id": "d22a0932-8691-46be-9f68-fa5179a310bc", "Relationships": [{"Type": "CHILD", "Ids": ["f8005084-0879-494f-a75b-741f4dff6c88", "ee316f69-7955-4616-bc81-fc3e3c20a5db"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "DIAMOND ENVELOPE", "Geometry": {"BoundingBox": {"Width": 0.10091961175203323, "Height": 0.006404740270227194, "Left": 0.16249734163284302, "Top": 0.3682035207748413}, "Polygon": [{"X": 0.1624973714351654, "Y": 0.3682049512863159}, {"X": 0.26341694593429565, "Y": 0.3682035207748413}, {"X": 0.2634168863296509, "Y": 0.3746068477630615}, {"X": 0.16249734163284302, "Y": 0.37460827827453613}]}, "Id": "fa87b7ac-9227-4180-ab50-40cfd64dee99", "Relationships": [{"Type": "CHILD", "Ids": ["32bb92f0-4fa7-456b-8c08-14414f5f0701", "5002caaa-0a0e-4af5-a0f3-7a53d1b9ceec"]}]}, {"BlockType": "LINE", "Confidence": 99.74091339111328, "Text": "Weight Certificate", "Geometry": {"BoundingBox": {"Width": 0.08476640284061432, "Height": 0.008311820216476917, "Left": 0.7232305407524109, "Top": 0.3676687777042389}, "Polygon": [{"X": 0.7232306599617004, "Y": 0.3676699697971344}, {"X": 0.807996928691864, "Y": 0.3676687777042389}, {"X": 0.8079968094825745, "Y": 0.37597939372062683}, {"X": 0.7232305407524109, "Y": 0.37598058581352234}]}, "Id": "49272a55-6bd1-4d81-a9f7-b730c01b52cc", "Relationships": [{"Type": "CHILD", "Ids": ["8ab676b5-eb20-47b9-b881-cbbcfb05043c", "f6eb78d3-eb68-4215-8969-ed7ed6747649"]}]}, {"BlockType": "LINE", "Confidence": 99.94100952148438, "Text": "2270 WHITE OAK CIR", "Geometry": {"BoundingBox": {"Width": 0.10405416041612625, "Height": 0.006522432900965214, "Left": 0.16246169805526733, "Top": 0.37667709589004517}, "Polygon": [{"X": 0.16246172785758972, "Y": 0.37667855620384216}, {"X": 0.266515851020813, "Y": 0.37667709589004517}, {"X": 0.2665157914161682, "Y": 0.3831980526447296}, {"X": 0.16246169805526733, "Y": 0.3831995129585266}]}, "Id": "51b16939-539c-4a0f-b370-7e52ef395178", "Relationships": [{"Type": "CHILD", "Ids": ["91a7ff38-92c2-4e98-b648-a8ec5b81f878", "ee3c22e6-e398-47f8-81fa-3ebfe27c3332", "fb459e9f-9f0f-46bd-9059-ce4cc506fc7e", "f1b72527-d4c8-4143-adf2-d794be4afa83"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "We Certify that this shipment", "Geometry": {"BoundingBox": {"Width": 0.13997326791286469, "Height": 0.008207225240767002, "Left": 0.7073788642883301, "Top": 0.37618619203567505}, "Polygon": [{"X": 0.7073789834976196, "Y": 0.37618815898895264}, {"X": 0.847352147102356, "Y": 0.37618619203567505}, {"X": 0.8473520278930664, "Y": 0.3843914270401001}, {"X": 0.7073788642883301, "Y": 0.3843934237957001}]}, "Id": "08290943-8947-4448-bcd2-305506384c6f", "Relationships": [{"Type": "CHILD", "Ids": ["58e44678-85d7-4992-bab1-e9f14f45c111", "7f8a29e2-0e50-4251-98a9-c1570a43dc6d", "531bd5b4-3089-409d-9042-0eef15b5bfdc", "d9b77405-7084-4c40-b4d5-92a5b4ad29a3", "61a0e89d-e2c1-4132-9b0b-552e755cab1a"]}]}, {"BlockType": "LINE", "Confidence": 99.71878814697266, "Text": "AURORA, IL 60502", "Geometry": {"BoundingBox": {"Width": 0.09084655344486237, "Height": 0.007452928461134434, "Left": 0.16181287169456482, "Top": 0.3850322663784027}, "Polygon": [{"X": 0.1618129163980484, "Y": 0.3850335478782654}, {"X": 0.2526594400405884, "Y": 0.3850322663784027}, {"X": 0.2526593804359436, "Y": 0.3924838900566101}, {"X": 0.16181287169456482, "Y": 0.39248520135879517}]}, "Id": "50032fe7-3cca-443a-b007-b027a5ec25f2", "Relationships": [{"Type": "CHILD", "Ids": ["78f990d3-4bad-43fe-8c67-c5a392a43380", "a25d5b0a-7560-4879-bac1-f25f07ba3a0f", "d859e529-c658-49dd-96dd-51911d5a4124"]}]}, {"BlockType": "LINE", "Confidence": 99.95428466796875, "Text": "has been reweighed and that", "Geometry": {"BoundingBox": {"Width": 0.1379849761724472, "Height": 0.008062225766479969, "Left": 0.7077588438987732, "Top": 0.3848668932914734}, "Polygon": [{"X": 0.7077589631080627, "Y": 0.384868860244751}, {"X": 0.8457438349723816, "Y": 0.3848668932914734}, {"X": 0.845743715763092, "Y": 0.3929271399974823}, {"X": 0.7077588438987732, "Y": 0.3929291069507599}]}, "Id": "8e081fe4-62c4-4aa4-9287-089b0644cd07", "Relationships": [{"Type": "CHILD", "Ids": ["8119431e-dff0-4455-ae31-9aa88115ed14", "8eefedeb-b9ea-4891-b722-de6d662ab546", "48856900-b2e7-4f12-a7ae-6cecf1095393", "9119facb-0ba1-439e-947b-c09792c68849", "66437fc3-1920-4a1d-bb76-67a426e1af2a"]}]}, {"BlockType": "LINE", "Confidence": 99.98030853271484, "Text": "the corrected weight as stated", "Geometry": {"BoundingBox": {"Width": 0.14533431828022003, "Height": 0.008183035999536514, "Left": 0.707314670085907, "Top": 0.39328068494796753}, "Polygon": [{"X": 0.7073147296905518, "Y": 0.39328277111053467}, {"X": 0.8526489734649658, "Y": 0.39328068494796753}, {"X": 0.8526488542556763, "Y": 0.4014616012573242}, {"X": 0.707314670085907, "Y": 0.40146371722221375}]}, "Id": "b1cc5ffd-eb91-46c5-99e8-0dc8321f9491", "Relationships": [{"Type": "CHILD", "Ids": ["848924cd-18f7-4a8e-be2a-25718fff61b8", "ae433cae-1342-4aa7-98cd-e4e110d04d5f", "a027d835-b7d6-405c-9ded-74a59ebe1bb5", "22c777d0-4055-4de8-8dad-6b158abcbe70", "03e5bbf5-5c71-47c6-8d58-e6811d6f272c"]}]}, {"BlockType": "LINE", "Confidence": 99.91458129882812, "Text": "is the true and accurate", "Geometry": {"BoundingBox": {"Width": 0.11372467130422592, "Height": 0.00710035115480423, "Left": 0.7076213955879211, "Top": 0.40173888206481934}, "Polygon": [{"X": 0.7076214551925659, "Y": 0.40174052119255066}, {"X": 0.8213460445404053, "Y": 0.40173888206481934}, {"X": 0.8213459253311157, "Y": 0.40883758664131165}, {"X": 0.7076213955879211, "Y": 0.40883922576904297}]}, "Id": "10513d5c-8850-46bc-a975-d5015d34c09e", "Relationships": [{"Type": "CHILD", "Ids": ["7778eb10-683a-46f5-a1dc-6a8341aa9ae8", "3ea4e9bf-1292-4f4d-998c-099afe7c066f", "2ac152a2-8d7e-4919-8bcf-2243a7fed975", "ef45d148-fc26-4037-81fe-8611fc2fc5c4", "1ba5c440-76ac-411e-b81b-8ec7c0b4d974"]}]}, {"BlockType": "LINE", "Confidence": 99.90074920654297, "Text": "Declared Weight: 1148", "Geometry": {"BoundingBox": {"Width": 0.10963373631238937, "Height": 0.008119854144752026, "Left": 0.16241343319416046, "Top": 0.41033288836479187}, "Polygon": [{"X": 0.16241347789764404, "Y": 0.4103344976902008}, {"X": 0.27204716205596924, "Y": 0.41033288836479187}, {"X": 0.27204710245132446, "Y": 0.41845113039016724}, {"X": 0.16241343319416046, "Y": 0.41845273971557617}]}, "Id": "b77ef8f8-4572-4c7f-b244-8a1f59c1c5c3", "Relationships": [{"Type": "CHILD", "Ids": ["af4bb3a5-d06d-4cda-ac66-4743c5a31c12", "4b5fed74-4324-4512-a0a8-1ad5e6af8918", "f41064d8-8b71-4113-be23-efedbea0f2b2"]}]}, {"BlockType": "LINE", "Confidence": 99.9280014038086, "Text": "weight of the shipment", "Geometry": {"BoundingBox": {"Width": 0.1111871749162674, "Height": 0.008168041706085205, "Left": 0.7069318294525146, "Top": 0.4102623462677002}, "Polygon": [{"X": 0.7069319486618042, "Y": 0.4102639853954315}, {"X": 0.8181189894676208, "Y": 0.4102623462677002}, {"X": 0.8181188702583313, "Y": 0.4184287488460541}, {"X": 0.7069318294525146, "Y": 0.4184303879737854}]}, "Id": "ebd35bbf-54f4-4c06-8984-c000c015f09b", "Relationships": [{"Type": "CHILD", "Ids": ["104f249d-c02d-40f4-b0e6-cdc474551cff", "5e1d14d5-19a5-4291-a77b-acb0b7972b9b", "ee0dd4e9-6ab9-4b07-a118-acc540ba5308", "f06b787c-380d-4fef-a0d6-9e9bb2a6fd19"]}]}, {"BlockType": "LINE", "Confidence": 99.86766815185547, "Text": "Corrected Weight: 1425", "Geometry": {"BoundingBox": {"Width": 0.11429888010025024, "Height": 0.008122419007122517, "Left": 0.16247409582138062, "Top": 0.41887912154197693}, "Polygon": [{"X": 0.1624741405248642, "Y": 0.41888079047203064}, {"X": 0.27677297592163086, "Y": 0.41887912154197693}, {"X": 0.2767729163169861, "Y": 0.4269998371601105}, {"X": 0.16247409582138062, "Y": 0.4270015358924866}]}, "Id": "4f92c018-31e2-4043-8659-4a938479d338", "Relationships": [{"Type": "CHILD", "Ids": ["6060dee0-0704-4d19-bcab-cdf3d0b6c4e3", "2f8110b3-bdc1-492f-8ed3-e699ae144b83", "a14834d5-3c0f-4462-9318-629392a42a96"]}]}, {"BlockType": "LINE", "Confidence": 99.7120132446289, "Text": "HU", "Geometry": {"BoundingBox": {"Width": 0.017055867239832878, "Height": 0.006777491886168718, "Left": 0.16441786289215088, "Top": 0.4541013836860657}, "Polygon": [{"X": 0.16441790759563446, "Y": 0.45410165190696716}, {"X": 0.1814737319946289, "Y": 0.4541013836860657}, {"X": 0.18147367238998413, "Y": 0.4608786106109619}, {"X": 0.16441786289215088, "Y": 0.4608788788318634}]}, "Id": "234ffc72-e41c-4b1d-b6ae-03dff82d4209", "Relationships": [{"Type": "CHILD", "Ids": ["79733ed5-778b-47cf-986c-9abb3935c66f"]}]}, {"BlockType": "LINE", "Confidence": 99.89058685302734, "Text": "<PERSON>an Date", "Geometry": {"BoundingBox": {"Width": 0.0600147508084774, "Height": 0.0071591404266655445, "Left": 0.19829589128494263, "Top": 0.45388081669807434}, "Polygon": [{"X": 0.1982959508895874, "Y": 0.45388174057006836}, {"X": 0.2583106458187103, "Y": 0.45388081669807434}, {"X": 0.25831058621406555, "Y": 0.4610390365123749}, {"X": 0.19829589128494263, "Y": 0.4610399603843689}]}, "Id": "f2cc4c68-55d8-4ca8-a59e-9e91265c61fc", "Relationships": [{"Type": "CHILD", "Ids": ["b04a3696-c92a-4886-8216-90222335e53f", "e0068536-bbad-473a-8f2e-5a9abe5bc866"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Time", "Geometry": {"BoundingBox": {"Width": 0.02890188619494438, "Height": 0.007359205279499292, "Left": 0.2874515652656555, "Top": 0.4534839987754822}, "Polygon": [{"X": 0.2874516248703003, "Y": 0.453484445810318}, {"X": 0.316353440284729, "Y": 0.4534839987754822}, {"X": 0.31635338068008423, "Y": 0.4608427584171295}, {"X": 0.2874515652656555, "Y": 0.46084320545196533}]}, "Id": "a84364ca-837b-4006-9fa4-c8f265ebc7af", "Relationships": [{"Type": "CHILD", "Ids": ["d776db4c-1c94-4818-a1e6-c9a3f5e27d54"]}]}, {"BlockType": "LINE", "Confidence": 99.78605651855469, "Text": "Weight Terminal", "Geometry": {"BoundingBox": {"Width": 0.09505319595336914, "Height": 0.009380130097270012, "Left": 0.3337138891220093, "Top": 0.45351311564445496}, "Polygon": [{"X": 0.33371397852897644, "Y": 0.45351457595825195}, {"X": 0.4287670850753784, "Y": 0.45351311564445496}, {"X": 0.42876699566841125, "Y": 0.4628917872905731}, {"X": 0.3337138891220093, "Y": 0.4628932476043701}]}, "Id": "ee1786d6-b3f0-4d4d-ba90-1036ac71cb6c", "Relationships": [{"Type": "CHILD", "Ids": ["66d9b8f5-59f7-4ff0-8c76-41a7b3f00641", "15f88dbc-7db4-4661-a827-31407506c055"]}]}, {"BlockType": "LINE", "Confidence": 99.86088562011719, "Text": "Pro Number", "Geometry": {"BoundingBox": {"Width": 0.0677749365568161, "Height": 0.007399872411042452, "Left": 0.46933484077453613, "Top": 0.4536535441875458}, "Polygon": [{"X": 0.4693349301815033, "Y": 0.45365455746650696}, {"X": 0.5371097922325134, "Y": 0.4536535441875458}, {"X": 0.5371096730232239, "Y": 0.4610523581504822}, {"X": 0.46933484077453613, "Y": 0.46105340123176575}]}, "Id": "2e78c609-0fea-4658-b899-ea30e3328ad4", "Relationships": [{"Type": "CHILD", "Ids": ["505d2c84-3675-4c18-b5f4-96ce6a4e109d", "e198446a-c80a-49da-9f4c-00234fa7cc2e"]}]}, {"BlockType": "LINE", "Confidence": 99.8828125, "Text": "33136199", "Geometry": {"BoundingBox": {"Width": 0.058709219098091125, "Height": 0.007329114247113466, "Left": 0.1687587946653366, "Top": 0.4650166928768158}, "Polygon": [{"X": 0.1687588393688202, "Y": 0.4650176167488098}, {"X": 0.22746801376342773, "Y": 0.4650166928768158}, {"X": 0.22746795415878296, "Y": 0.47234490513801575}, {"X": 0.1687587946653366, "Y": 0.47234582901000977}]}, "Id": "268550aa-0308-450b-bed3-162d361634d2", "Relationships": [{"Type": "CHILD", "Ids": ["3788abd3-332e-4fd5-a34d-493b4c02ac2e"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.0687561109662056, "Height": 0.008462921716272831, "Left": 0.24354542791843414, "Top": 0.46471866965293884}, "Polygon": [{"X": 0.24354548752307892, "Y": 0.4647197127342224}, {"X": 0.31230154633522034, "Y": 0.46471866965293884}, {"X": 0.3123014569282532, "Y": 0.47318050265312195}, {"X": 0.24354542791843414, "Y": 0.4731815755367279}]}, "Id": "848489f7-8b84-4eb9-9f52-301e803a1866", "Relationships": [{"Type": "CHILD", "Ids": ["3fdcc932-0118-47fa-8749-653efd686c72"]}]}, {"BlockType": "LINE", "Confidence": 99.89178466796875, "Text": "00:02 880", "Geometry": {"BoundingBox": {"Width": 0.06711716204881668, "Height": 0.007216433063149452, "Left": 0.3340480327606201, "Top": 0.4650701880455017}, "Polygon": [{"X": 0.3340480923652649, "Y": 0.4650712311267853}, {"X": 0.4011651873588562, "Y": 0.4650701880455017}, {"X": 0.4011651277542114, "Y": 0.47228556871414185}, {"X": 0.3340480327606201, "Y": 0.4722866117954254}]}, "Id": "aa9e5d10-a12e-4dac-b5ab-1390ea6b9a6c", "Relationships": [{"Type": "CHILD", "Ids": ["424c1803-f0f0-4764-bf2c-00274947c847", "ca3658cb-1da2-49e2-8446-aff38fc13ec2"]}]}, {"BlockType": "LINE", "Confidence": 99.95037078857422, "Text": "463", "Geometry": {"BoundingBox": {"Width": 0.021867085248231888, "Height": 0.00682410504668951, "Left": 0.43186819553375244, "Top": 0.46527284383773804}, "Polygon": [{"X": 0.4318682551383972, "Y": 0.4652732014656067}, {"X": 0.45373526215553284, "Y": 0.46527284383773804}, {"X": 0.45373520255088806, "Y": 0.47209662199020386}, {"X": 0.43186819553375244, "Y": 0.4720969498157501}]}, "Id": "6c774529-c7c5-4e82-833b-26978be0644f", "Relationships": [{"Type": "CHILD", "Ids": ["40940edb-1c9a-4735-bbe2-1b9a8c66e3b5"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "227407699", "Geometry": {"BoundingBox": {"Width": 0.060560133308172226, "Height": 0.007165390066802502, "Left": 0.5148974657058716, "Top": 0.4650382697582245}, "Polygon": [{"X": 0.5148975253105164, "Y": 0.4650391936302185}, {"X": 0.5754575729370117, "Y": 0.4650382697582245}, {"X": 0.5754575133323669, "Y": 0.47220271825790405}, {"X": 0.5148974657058716, "Y": 0.47220367193222046}]}, "Id": "e4876dd9-f011-45d2-aa92-fc9691e067c1", "Relationships": [{"Type": "CHILD", "Ids": ["349dc259-f48b-411f-9941-7aedb26f3730"]}]}, {"BlockType": "LINE", "Confidence": 99.873046875, "Text": "33136200", "Geometry": {"BoundingBox": {"Width": 0.058386050164699554, "Height": 0.006965055596083403, "Left": 0.16896452009677887, "Top": 0.47567445039749146}, "Polygon": [{"X": 0.16896456480026245, "Y": 0.4756753742694855}, {"X": 0.22735057771205902, "Y": 0.47567445039749146}, {"X": 0.22735051810741425, "Y": 0.4826385974884033}, {"X": 0.16896452009677887, "Y": 0.48263952136039734}]}, "Id": "ba2b591a-3ddb-491b-8324-3f891339ad33", "Relationships": [{"Type": "CHILD", "Ids": ["b438cfe6-54ff-4cad-8d4a-74ab68f5777f"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "08/26/2025", "Geometry": {"BoundingBox": {"Width": 0.06862763315439224, "Height": 0.008570958860218525, "Left": 0.24351204931735992, "Top": 0.47528213262557983}, "Polygon": [{"X": 0.2435121089220047, "Y": 0.4752832055091858}, {"X": 0.31213968992233276, "Y": 0.47528213262557983}, {"X": 0.3121396005153656, "Y": 0.48385199904441833}, {"X": 0.24351204931735992, "Y": 0.4838530719280243}]}, "Id": "d9907373-d970-4072-965b-3b548c78988a", "Relationships": [{"Type": "CHILD", "Ids": ["bf19c795-9684-4570-9fc0-36ba1ae39159"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "00:03", "Geometry": {"BoundingBox": {"Width": 0.033893387764692307, "Height": 0.0071488418616354465, "Left": 0.3341608941555023, "Top": 0.4757031202316284}, "Polygon": [{"X": 0.3341609537601471, "Y": 0.475703626871109}, {"X": 0.3680543005466461, "Y": 0.4757031202316284}, {"X": 0.36805421113967896, "Y": 0.48285141587257385}, {"X": 0.3341608941555023, "Y": 0.48285195231437683}]}, "Id": "363aedeb-8b4d-42d9-8133-52697a578d32", "Relationships": [{"Type": "CHILD", "Ids": ["7dc12de5-663f-43e9-9705-fe9a91d4a8af"]}]}, {"BlockType": "LINE", "Confidence": 99.93083953857422, "Text": "545", "Geometry": {"BoundingBox": {"Width": 0.02147347666323185, "Height": 0.006957860663533211, "Left": 0.3794381320476532, "Top": 0.4757300317287445}, "Polygon": [{"X": 0.379438191652298, "Y": 0.47573035955429077}, {"X": 0.4009115993976593, "Y": 0.4757300317287445}, {"X": 0.4009115397930145, "Y": 0.4826875627040863}, {"X": 0.3794381320476532, "Y": 0.48268789052963257}]}, "Id": "e10e8e06-7ecf-46c4-9842-50433e9d6e48", "Relationships": [{"Type": "CHILD", "Ids": ["8f060444-750a-40d3-944f-8955940a65f2"]}]}, {"BlockType": "LINE", "Confidence": 99.91051483154297, "Text": "463", "Geometry": {"BoundingBox": {"Width": 0.021909311413764954, "Height": 0.006979272235184908, "Left": 0.43184787034988403, "Top": 0.4756060838699341}, "Polygon": [{"X": 0.4318479299545288, "Y": 0.47560641169548035}, {"X": 0.4537571966648102, "Y": 0.4756060838699341}, {"X": 0.453757107257843, "Y": 0.48258501291275024}, {"X": 0.43184787034988403, "Y": 0.4825853407382965}]}, "Id": "ed79e61b-17be-4a6b-88b4-bbf3a86a6fda", "Relationships": [{"Type": "CHILD", "Ids": ["6822f664-69f4-4fca-a641-e205c0cd982f"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "227407699", "Geometry": {"BoundingBox": {"Width": 0.06058836355805397, "Height": 0.006870943587273359, "Left": 0.514869749546051, "Top": 0.4756676256656647}, "Polygon": [{"X": 0.5148698091506958, "Y": 0.4756685793399811}, {"X": 0.5754581093788147, "Y": 0.4756676256656647}, {"X": 0.5754579901695251, "Y": 0.4825376272201538}, {"X": 0.514869749546051, "Y": 0.4825385808944702}]}, "Id": "609962eb-b5f9-4568-9414-59c68305ccd7", "Relationships": [{"Type": "CHILD", "Ids": ["f79bc18f-5987-4e99-a130-fbb5bee0e7bb"]}]}, {"BlockType": "LINE", "Confidence": 99.83238983154297, "Text": "3", "Geometry": {"BoundingBox": {"Width": 0.0063613103702664375, "Height": 0.006829916033893824, "Left": 0.16887255012989044, "Top": 0.48619070649147034}, "Polygon": [{"X": 0.16887259483337402, "Y": 0.4861908257007599}, {"X": 0.175233855843544, "Y": 0.48619070649147034}, {"X": 0.17523381114006042, "Y": 0.49302053451538086}, {"X": 0.16887255012989044, "Y": 0.493020623922348}]}, "Id": "958c8dc5-6cce-4a36-a5f8-da654ce021d6", "Relationships": [{"Type": "CHILD", "Ids": ["05689d55-786f-42af-aa2b-b43bcbcec4f6"]}]}, {"BlockType": "LINE", "Confidence": 99.68511199951172, "Text": "4", "Geometry": {"BoundingBox": {"Width": 0.0070258076302707195, "Height": 0.006691001355648041, "Left": 0.16855113208293915, "Top": 0.49654772877693176}, "Polygon": [{"X": 0.16855117678642273, "Y": 0.4965478479862213}, {"X": 0.17557694017887115, "Y": 0.49654772877693176}, {"X": 0.17557689547538757, "Y": 0.5032386183738708}, {"X": 0.16855113208293915, "Y": 0.5032387375831604}]}, "Id": "523aa32a-48d9-4704-864d-dc3a8e2db041", "Relationships": [{"Type": "CHILD", "Ids": ["526f2e89-f73c-4dfc-aebd-b914cb8a617b"]}]}, {"BlockType": "LINE", "Confidence": 99.79252624511719, "Text": "5", "Geometry": {"BoundingBox": {"Width": 0.006197942886501551, "Height": 0.006747648119926453, "Left": 0.1691218912601471, "Top": 0.5072013139724731}, "Polygon": [{"X": 0.16912193596363068, "Y": 0.5072013735771179}, {"X": 0.1753198355436325, "Y": 0.5072013139724731}, {"X": 0.17531979084014893, "Y": 0.5139488577842712}, {"X": 0.1691218912601471, "Y": 0.5139489769935608}]}, "Id": "cbba3bfc-32f0-45c1-8d2c-a448ea7b6732", "Relationships": [{"Type": "CHILD", "Ids": ["9c978cb3-119d-49b1-819a-7b63829f97f5"]}]}, {"BlockType": "LINE", "Confidence": 99.71281433105469, "Text": "6", "Geometry": {"BoundingBox": {"Width": 0.00636710412800312, "Height": 0.0069289072416722775, "Left": 0.16888143122196198, "Top": 0.5180972814559937}, "Polygon": [{"X": 0.16888147592544556, "Y": 0.5180974006652832}, {"X": 0.17524853348731995, "Y": 0.5180972814559937}, {"X": 0.17524848878383636, "Y": 0.5250260829925537}, {"X": 0.16888143122196198, "Y": 0.5250262022018433}]}, "Id": "2e8506d7-84b4-4131-afb2-41c6cbcafaf5", "Relationships": [{"Type": "CHILD", "Ids": ["b5583230-fecb-4412-bcca-5a2643ab3592"]}]}, {"BlockType": "LINE", "Confidence": 99.88201141357422, "Text": "7", "Geometry": {"BoundingBox": {"Width": 0.006649814546108246, "Height": 0.006885642185807228, "Left": 0.16893725097179413, "Top": 0.5284852385520935}, "Polygon": [{"X": 0.1689372956752777, "Y": 0.5284853577613831}, {"X": 0.17558707296848297, "Y": 0.5284852385520935}, {"X": 0.1755870282649994, "Y": 0.5353707671165466}, {"X": 0.16893725097179413, "Y": 0.5353708863258362}]}, "Id": "9b843980-7c34-4030-8f1d-856bce9d1eb3", "Relationships": [{"Type": "CHILD", "Ids": ["d61c5fac-a123-48f4-995e-9aad73bb6ae5"]}]}, {"BlockType": "LINE", "Confidence": 99.67375183105469, "Text": "8", "Geometry": {"BoundingBox": {"Width": 0.006595203652977943, "Height": 0.006734260357916355, "Left": 0.1688777208328247, "Top": 0.5392356514930725}, "Polygon": [{"X": 0.1688777655363083, "Y": 0.5392357707023621}, {"X": 0.1754729151725769, "Y": 0.5392356514930725}, {"X": 0.17547287046909332, "Y": 0.5459697842597961}, {"X": 0.1688777208328247, "Y": 0.5459699034690857}]}, "Id": "b5763c4a-3a90-429c-9c09-01d6017fdc4b", "Relationships": [{"Type": "CHILD", "Ids": ["463a0e3a-e5a3-471a-a35e-dcac3bf24b5b"]}]}, {"BlockType": "LINE", "Confidence": 99.60299682617188, "Text": "9", "Geometry": {"BoundingBox": {"Width": 0.006715078372508287, "Height": 0.007085907738655806, "Left": 0.16874417662620544, "Top": 0.5495967268943787}, "Polygon": [{"X": 0.16874422132968903, "Y": 0.5495968461036682}, {"X": 0.17545926570892334, "Y": 0.5495967268943787}, {"X": 0.17545920610427856, "Y": 0.5566825270652771}, {"X": 0.16874417662620544, "Y": 0.5566826462745667}]}, "Id": "ad9c68d8-7a1d-483b-9556-a940f102a1a0", "Relationships": [{"Type": "CHILD", "Ids": ["382cbc6d-3154-48bc-a652-08edb3bb6dfa"]}]}, {"BlockType": "LINE", "Confidence": 99.85271453857422, "Text": "10", "Geometry": {"BoundingBox": {"Width": 0.012692058458924294, "Height": 0.006892743520438671, "Left": 0.1696051061153412, "Top": 0.5602093935012817}, "Polygon": [{"X": 0.16960515081882477, "Y": 0.5602095723152161}, {"X": 0.18229715526103973, "Y": 0.5602093935012817}, {"X": 0.18229711055755615, "Y": 0.5671018958091736}, {"X": 0.1696051061153412, "Y": 0.5671021342277527}]}, "Id": "0139d599-0a2a-43a9-916f-a9b936d6c9b4", "Relationships": [{"Type": "CHILD", "Ids": ["6e8e7c75-913b-4e27-b517-da4b2e73bca0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.027860941365361214, "Height": 0.007223647087812424, "Left": 0.16397152841091156, "Top": 0.5709370970726013}, "Polygon": [{"X": 0.16397157311439514, "Y": 0.5709375739097595}, {"X": 0.19183246791362762, "Y": 0.5709370970726013}, {"X": 0.19183242321014404, "Y": 0.5781602263450623}, {"X": 0.16397152841091156, "Y": 0.5781607031822205}]}, "Id": "b12eaac2-dcee-4b03-9b3e-b9610f0c4735", "Relationships": [{"Type": "CHILD", "Ids": ["0cc018f2-1d63-4a22-ac41-107662ec6558"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "1425", "Geometry": {"BoundingBox": {"Width": 0.02627403661608696, "Height": 0.006929267197847366, "Left": 0.33493518829345703, "Top": 0.5713433623313904}, "Polygon": [{"X": 0.3349352478981018, "Y": 0.5713438391685486}, {"X": 0.3612092137336731, "Y": 0.5713433623313904}, {"X": 0.3612091541290283, "Y": 0.5782721638679504}, {"X": 0.33493518829345703, "Y": 0.5782726407051086}]}, "Id": "41c15a6c-87f0-4e96-bf0a-f07c4348537b", "Relationships": [{"Type": "CHILD", "Ids": ["2f2be307-c6d6-40b3-95a6-434e7ab44a6c"]}]}, {"BlockType": "LINE", "Confidence": 99.75875091552734, "Text": "Page 3/3", "Geometry": {"BoundingBox": {"Width": 0.05226879566907883, "Height": 0.010070810094475746, "Left": 0.8169759511947632, "Top": 0.9619084596633911}, "Polygon": [{"X": 0.8169761300086975, "Y": 0.9619096517562866}, {"X": 0.8692447543144226, "Y": 0.9619084596633911}, {"X": 0.8692446351051331, "Y": 0.9719780087471008}, {"X": 0.8169759511947632, "Y": 0.9719792604446411}]}, "Id": "a83cfc2a-267d-49b3-8c39-d394223eb84f", "Relationships": [{"Type": "CHILD", "Ids": ["a6a732de-d645-495c-85c7-6f6bd09ddfcb", "a3e9113d-2751-452f-8e40-70b47795a864"]}]}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Central", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.047523383051157, "Height": 0.009486476890742779, "Left": 0.05718903988599777, "Top": 0.05624702572822571}, "Polygon": [{"X": 0.05718908831477165, "Y": 0.05624745413661003}, {"X": 0.10471241921186447, "Y": 0.05624702572822571}, {"X": 0.10471236705780029, "Y": 0.06573306769132614}, {"X": 0.05718903988599777, "Y": 0.06573350727558136}]}, "Id": "c5740969-0a5f-4651-914f-6782fd539d15"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Transport", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06371095031499863, "Height": 0.011445809155702591, "Left": 0.10830892622470856, "Top": 0.056378427892923355}, "Polygon": [{"X": 0.10830899327993393, "Y": 0.05637900158762932}, {"X": 0.17201988399028778, "Y": 0.056378427892923355}, {"X": 0.1720198094844818, "Y": 0.06782364845275879}, {"X": 0.10830892622470856, "Y": 0.06782423704862595}]}, "Id": "ac08efdc-2343-4ca9-801f-46fbbb4dad0d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "08/31/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0731736347079277, "Height": 0.009412871673703194, "Left": 0.8657994270324707, "Top": 0.05629698932170868}, "Polygon": [{"X": 0.865799605846405, "Y": 0.05629764869809151}, {"X": 0.938973069190979, "Y": 0.05629698932170868}, {"X": 0.9389729499816895, "Y": 0.065709188580513}, {"X": 0.8657994270324707, "Y": 0.06570985913276672}]}, "Id": "dbeb5353-6854-4a21-9b51-fcbbbf283f1d"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "PRO#:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043377768248319626, "Height": 0.009395409375429153, "Left": 0.057394713163375854, "Top": 0.07146386057138443}, "Polygon": [{"X": 0.057394761592149734, "Y": 0.07146426290273666}, {"X": 0.10077248513698578, "Y": 0.07146386057138443}, {"X": 0.1007724329829216, "Y": 0.08085886389017105}, {"X": 0.057394713163375854, "Y": 0.08085927367210388}]}, "Id": "06a8c74f-b7ce-4cf0-adc7-80889a297c36"}, {"BlockType": "WORD", "Confidence": 99.83318328857422, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07391535490751266, "Height": 0.009347072802484035, "Left": 0.10520923137664795, "Top": 0.07145050168037415}, "Polygon": [{"X": 0.10520928353071213, "Y": 0.07145117968320847}, {"X": 0.17912457883358002, "Y": 0.07145050168037415}, {"X": 0.17912451922893524, "Y": 0.08079687505960464}, {"X": 0.10520923137664795, "Y": 0.08079757541418076}]}, "Id": "07baac9a-5ba9-400e-bf50-91af1738a0a4"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04729338735342026, "Height": 0.011711383238434792, "Left": 0.8079518675804138, "Top": 0.07136216014623642}, "Polygon": [{"X": 0.8079520463943481, "Y": 0.07136259227991104}, {"X": 0.8552452921867371, "Y": 0.07136216014623642}, {"X": 0.8552451133728027, "Y": 0.08307309448719025}, {"X": 0.8079518675804138, "Y": 0.08307354152202606}]}, "Id": "59fcecb7-ebbf-460f-bcf6-940a6b48902a"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010260049253702164, "Height": 0.00927706342190504, "Left": 0.8587119579315186, "Top": 0.07156898826360703}, "Polygon": [{"X": 0.8587120771408081, "Y": 0.07156908512115479}, {"X": 0.8689720034599304, "Y": 0.07156898826360703}, {"X": 0.8689718842506409, "Y": 0.08084595203399658}, {"X": 0.8587119579315186, "Y": 0.08084604889154434}]}, "Id": "4cf094aa-92df-4307-a8c1-08be32d5021c"}, {"BlockType": "WORD", "Confidence": 99.84135437011719, "Text": "Inspection", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06620460003614426, "Height": 0.011348029598593712, "Left": 0.8727492690086365, "Top": 0.07158870249986649}, "Polygon": [{"X": 0.8727494478225708, "Y": 0.07158931344747543}, {"X": 0.9389538764953613, "Y": 0.07158870249986649}, {"X": 0.938953697681427, "Y": 0.0829361081123352}, {"X": 0.8727492690086365, "Y": 0.08293673396110535}]}, "Id": "1cdb8fce-b350-4f4b-b4af-225afcfcb19d"}, {"BlockType": "WORD", "Confidence": 99.921875, "Text": "CENTRAL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0677325576543808, "Height": 0.01386458333581686, "Left": 0.4383789896965027, "Top": 0.19190871715545654}, "Polygon": [{"X": 0.43837910890579224, "Y": 0.19190946221351624}, {"X": 0.5061115622520447, "Y": 0.19190871715545654}, {"X": 0.5061113834381104, "Y": 0.2057725191116333}, {"X": 0.4383789896965027, "Y": 0.20577329397201538}]}, "Id": "e05390b4-3559-4e6e-a86c-e80c10f332a2"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "TRANSPORT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09249085932970047, "Height": 0.013988036662340164, "Left": 0.508831262588501, "Top": 0.1918865293264389}, "Polygon": [{"X": 0.5088314414024353, "Y": 0.19188755750656128}, {"X": 0.6013221144676208, "Y": 0.1918865293264389}, {"X": 0.6013219952583313, "Y": 0.205873504281044}, {"X": 0.508831262588501, "Y": 0.20587456226348877}]}, "Id": "91ec3d8a-cdd6-45ff-b6c5-58abb4f19363"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "12225", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.035280805081129074, "Height": 0.00855326559394598, "Left": 0.4399400055408478, "Top": 0.2120484709739685}, "Polygon": [{"X": 0.43994009494781494, "Y": 0.21204887330532074}, {"X": 0.47522079944610596, "Y": 0.2120484709739685}, {"X": 0.4752207100391388, "Y": 0.22060133516788483}, {"X": 0.4399400055408478, "Y": 0.22060173749923706}]}, "Id": "b0e29156-6db6-4eeb-85ed-4fddef36781f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05482405051589012, "Height": 0.010645987465977669, "Left": 0.47985780239105225, "Top": 0.21199145913124084}, "Polygon": [{"X": 0.4798579216003418, "Y": 0.211992084980011}, {"X": 0.5346818566322327, "Y": 0.21199145913124084}, {"X": 0.5346817374229431, "Y": 0.22263680398464203}, {"X": 0.47985780239105225, "Y": 0.22263744473457336}]}, "Id": "cc9a5bd2-b5b0-48f7-9ba6-e4c557c72533"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Road", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02949291281402111, "Height": 0.008774659596383572, "Left": 0.5392704606056213, "Top": 0.2120930254459381}, "Polygon": [{"X": 0.5392705202102661, "Y": 0.21209336817264557}, {"X": 0.5687633752822876, "Y": 0.2120930254459381}, {"X": 0.568763256072998, "Y": 0.2208673506975174}, {"X": 0.5392704606056213, "Y": 0.22086769342422485}]}, "Id": "15051446-dc2c-402d-8a27-4c1f79e154e8"}, {"BlockType": "WORD", "Confidence": 99.71121215820312, "Text": "<PERSON>,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04800622910261154, "Height": 0.010209893807768822, "Left": 0.44664397835731506, "Top": 0.2257819026708603}, "Polygon": [{"X": 0.4466440677642822, "Y": 0.22578246891498566}, {"X": 0.4946502149105072, "Y": 0.2257819026708603}, {"X": 0.49465009570121765, "Y": 0.2359912246465683}, {"X": 0.44664397835731506, "Y": 0.23599180579185486}]}, "Id": "d2d00408-3617-48b9-953f-c8f7bf6bec10"}, {"BlockType": "WORD", "Confidence": 99.931640625, "Text": "MI", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0159319918602705, "Height": 0.00851416401565075, "Left": 0.49934086203575134, "Top": 0.22570796310901642}, "Polygon": [{"X": 0.4993409514427185, "Y": 0.22570815682411194}, {"X": 0.515272855758667, "Y": 0.22570796310901642}, {"X": 0.5152727365493774, "Y": 0.234221950173378}, {"X": 0.49934086203575134, "Y": 0.23422212898731232}]}, "Id": "093738d8-f696-4248-92c2-145ba1ae3a6a"}, {"BlockType": "WORD", "Confidence": 99.92107391357422, "Text": "48089", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036994144320487976, "Height": 0.008614152669906616, "Left": 0.5234261155128479, "Top": 0.22568704187870026}, "Polygon": [{"X": 0.5234262347221375, "Y": 0.22568747401237488}, {"X": 0.5604202747344971, "Y": 0.22568704187870026}, {"X": 0.5604201555252075, "Y": 0.23430076241493225}, {"X": 0.5234261155128479, "Y": 0.23430119454860687}]}, "Id": "a8622044-a397-47dc-b89f-9876bc6ae27e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CERTIFIED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07389842718839645, "Height": 0.008876699954271317, "Left": 0.39221125841140747, "Top": 0.29037290811538696}, "Polygon": [{"X": 0.39221134781837463, "Y": 0.290373831987381}, {"X": 0.4661096930503845, "Y": 0.29037290811538696}, {"X": 0.46610960364341736, "Y": 0.2992486357688904}, {"X": 0.39221125841140747, "Y": 0.2992495894432068}]}, "Id": "4926b0f3-b565-4159-8ca5-38b6005ae6cf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WEIGHT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05728071182966232, "Height": 0.009197513572871685, "Left": 0.47053226828575134, "Top": 0.2900329828262329}, "Polygon": [{"X": 0.4705323576927185, "Y": 0.2900336980819702}, {"X": 0.5278129577636719, "Y": 0.2900329828262329}, {"X": 0.5278128981590271, "Y": 0.29922977089881897}, {"X": 0.47053226828575134, "Y": 0.2992304861545563}]}, "Id": "3601383c-c63d-45e4-8cce-be92f0c66329"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CERTIFICATE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08967792242765427, "Height": 0.008884044364094734, "Left": 0.5327039361000061, "Top": 0.2903496026992798}, "Polygon": [{"X": 0.5327039957046509, "Y": 0.2903507351875305}, {"X": 0.622381865978241, "Y": 0.2903496026992798}, {"X": 0.6223817467689514, "Y": 0.29923248291015625}, {"X": 0.5327039361000061, "Y": 0.29923364520072937}]}, "Id": "411329a3-e516-47cb-a40f-31aba112d032"}, {"BlockType": "WORD", "Confidence": 99.81126403808594, "Text": "Pro", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021388905122876167, "Height": 0.008751227520406246, "Left": 0.15433162450790405, "Top": 0.3165864050388336}, "Polygon": [{"X": 0.15433166921138763, "Y": 0.3165866732597351}, {"X": 0.17572052776813507, "Y": 0.3165864050388336}, {"X": 0.1757204681634903, "Y": 0.32533735036849976}, {"X": 0.15433162450790405, "Y": 0.32533761858940125}]}, "Id": "29954b23-2fb2-4b56-a7cf-88b215e724a7"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "Number:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05895177647471428, "Height": 0.009268013760447502, "Left": 0.18463827669620514, "Top": 0.3161730170249939}, "Polygon": [{"X": 0.18463833630084991, "Y": 0.316173791885376}, {"X": 0.24359004199504852, "Y": 0.3161730170249939}, {"X": 0.24358998239040375, "Y": 0.32544025778770447}, {"X": 0.18463827669620514, "Y": 0.32544103264808655}]}, "Id": "d75c5565-f853-49e6-a6b6-a0a412791e47"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07552546262741089, "Height": 0.009020472876727581, "Left": 0.2525319457054138, "Top": 0.31638628244400024}, "Polygon": [{"X": 0.252532035112381, "Y": 0.3163872957229614}, {"X": 0.3280574083328247, "Y": 0.31638628244400024}, {"X": 0.32805734872817993, "Y": 0.3254057765007019}, {"X": 0.2525319457054138, "Y": 0.3254067599773407}]}, "Id": "902409a5-3f75-4eb1-8e41-47519486db5e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Bill", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01808108016848564, "Height": 0.009077761322259903, "Left": 0.6965249180793762, "Top": 0.3161868751049042}, "Polygon": [{"X": 0.6965250372886658, "Y": 0.3161871135234833}, {"X": 0.714605987071991, "Y": 0.3161868751049042}, {"X": 0.7146058678627014, "Y": 0.3252643942832947}, {"X": 0.6965249180793762, "Y": 0.3252646327018738}]}, "Id": "b722da54-2fc9-4dce-be4b-886d2673edc6"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014402875676751137, "Height": 0.009438899345695972, "Left": 0.7203461527824402, "Top": 0.31584814190864563}, "Polygon": [{"X": 0.7203463315963745, "Y": 0.31584832072257996}, {"X": 0.7347490787506104, "Y": 0.31584814190864563}, {"X": 0.734748899936676, "Y": 0.3252868354320526}, {"X": 0.7203461527824402, "Y": 0.3252870440483093}]}, "Id": "b89a4180-8735-4eb8-a18c-72aae6f8b9c8"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Lading", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04320087283849716, "Height": 0.011417851783335209, "Left": 0.7399371862411499, "Top": 0.3161904215812683}, "Polygon": [{"X": 0.7399373650550842, "Y": 0.31619101762771606}, {"X": 0.78313809633255, "Y": 0.3161904215812683}, {"X": 0.7831379175186157, "Y": 0.3276077210903168}, {"X": 0.7399371862411499, "Y": 0.32760828733444214}]}, "Id": "7276cad5-dbe6-44f9-92e8-3d3aa53ee7dd"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "Date:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03578847274184227, "Height": 0.008917699567973614, "Left": 0.15446539223194122, "Top": 0.3295671343803406}, "Polygon": [{"X": 0.154465451836586, "Y": 0.3295676112174988}, {"X": 0.1902538686990738, "Y": 0.3295671343803406}, {"X": 0.19025380909442902, "Y": 0.33848437666893005}, {"X": 0.15446539223194122, "Y": 0.33848485350608826}]}, "Id": "f8005084-0879-494f-a75b-741f4dff6c88"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07925864309072495, "Height": 0.010597198270261288, "Left": 0.19703388214111328, "Top": 0.32907816767692566}, "Polygon": [{"X": 0.19703395664691925, "Y": 0.32907921075820923}, {"X": 0.2762925326824188, "Y": 0.32907816767692566}, {"X": 0.27629244327545166, "Y": 0.33967429399490356}, {"X": 0.19703388214111328, "Y": 0.33967533707618713}]}, "Id": "ee316f69-7955-4616-bc81-fc3e3c20a5db"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "DIAMOND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04778068885207176, "Height": 0.006365248467773199, "Left": 0.16249734163284302, "Top": 0.368204265832901}, "Polygon": [{"X": 0.1624973714351654, "Y": 0.3682049512863159}, {"X": 0.21027801930904388, "Y": 0.368204265832901}, {"X": 0.2102779746055603, "Y": 0.3745688498020172}, {"X": 0.16249734163284302, "Y": 0.37456953525543213}]}, "Id": "32bb92f0-4fa7-456b-8c08-14414f5f0701"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "ENVELOPE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05010327696800232, "Height": 0.00638725608587265, "Left": 0.21331366896629333, "Top": 0.3682202994823456}, "Polygon": [{"X": 0.21331371366977692, "Y": 0.3682210147380829}, {"X": 0.26341694593429565, "Y": 0.3682202994823456}, {"X": 0.2634168863296509, "Y": 0.3746068477630615}, {"X": 0.21331366896629333, "Y": 0.37460756301879883}]}, "Id": "5002caaa-0a0e-4af5-a0f3-7a53d1b9ceec"}, {"BlockType": "WORD", "Confidence": 99.72098541259766, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03317679092288017, "Height": 0.008311101235449314, "Left": 0.7232305407524109, "Top": 0.3676694929599762}, "Polygon": [{"X": 0.7232306599617004, "Y": 0.3676699697971344}, {"X": 0.7564073204994202, "Y": 0.3676694929599762}, {"X": 0.7564072012901306, "Y": 0.3759801387786865}, {"X": 0.7232305407524109, "Y": 0.37598058581352234}]}, "Id": "8ab676b5-eb20-47b9-b881-cbbcfb05043c"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Certificate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04864736273884773, "Height": 0.006783640012145042, "Left": 0.7593495845794678, "Top": 0.3677155077457428}, "Polygon": [{"X": 0.7593496441841125, "Y": 0.3677161931991577}, {"X": 0.807996928691864, "Y": 0.3677155077457428}, {"X": 0.8079968094825745, "Y": 0.3744984567165375}, {"X": 0.7593495845794678, "Y": 0.3744991421699524}]}, "Id": "f6eb78d3-eb68-4215-8969-ed7ed6747649"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2270", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02383694052696228, "Height": 0.006322705652564764, "Left": 0.16246169805526733, "Top": 0.37672942876815796}, "Polygon": [{"X": 0.16246172785758972, "Y": 0.3767297565937042}, {"X": 0.18629863858222961, "Y": 0.37672942876815796}, {"X": 0.18629859387874603, "Y": 0.3830517828464508}, {"X": 0.16246169805526733, "Y": 0.38305214047431946}]}, "Id": "91a7ff38-92c2-4e98-b648-a8ec5b81f878"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "WHITE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.032461635768413544, "Height": 0.006380218081176281, "Left": 0.18896152079105377, "Top": 0.3766777217388153}, "Polygon": [{"X": 0.18896156549453735, "Y": 0.3766781687736511}, {"X": 0.22142314910888672, "Y": 0.3766777217388153}, {"X": 0.22142310440540314, "Y": 0.38305747509002686}, {"X": 0.18896152079105377, "Y": 0.38305795192718506}]}, "Id": "ee3c22e6-e398-47f8-81fa-3ebfe27c3332"}, {"BlockType": "WORD", "Confidence": 99.89178466796875, "Text": "OAK", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02111927978694439, "Height": 0.006493091583251953, "Left": 0.22455410659313202, "Top": 0.3767055571079254}, "Polygon": [{"X": 0.2245541661977768, "Y": 0.3767058551311493}, {"X": 0.24567338824272156, "Y": 0.3767055571079254}, {"X": 0.24567334353923798, "Y": 0.3831983506679535}, {"X": 0.22455410659313202, "Y": 0.38319864869117737}]}, "Id": "fb459e9f-9f0f-46bd-9059-ce4cc506fc7e"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "CIR", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017902487888932228, "Height": 0.00622687628492713, "Left": 0.2486133575439453, "Top": 0.3768872916698456}, "Polygon": [{"X": 0.2486134171485901, "Y": 0.3768875300884247}, {"X": 0.266515851020813, "Y": 0.3768872916698456}, {"X": 0.2665157914161682, "Y": 0.38311392068862915}, {"X": 0.2486133575439453, "Y": 0.38311415910720825}]}, "Id": "f1b72527-d4c8-4143-adf2-d794be4afa83"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "We", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.014652658253908157, "Height": 0.006238427013158798, "Left": 0.7073789238929749, "Top": 0.37680166959762573}, "Polygon": [{"X": 0.7073789834976196, "Y": 0.37680187821388245}, {"X": 0.7220315337181091, "Y": 0.37680166959762573}, {"X": 0.7220314741134644, "Y": 0.3830398917198181}, {"X": 0.7073789238929749, "Y": 0.38304010033607483}]}, "Id": "58e44678-85d7-4992-bab1-e9f14f45c111"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Certify", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03157906234264374, "Height": 0.008171954192221165, "Left": 0.7253687977790833, "Top": 0.3762212097644806}, "Polygon": [{"X": 0.7253689169883728, "Y": 0.3762216567993164}, {"X": 0.7569478750228882, "Y": 0.3762212097644806}, {"X": 0.7569477558135986, "Y": 0.38439270853996277}, {"X": 0.7253687977790833, "Y": 0.3843931555747986}]}, "Id": "7f8a29e2-0e50-4251-98a9-c1570a43dc6d"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019612392410635948, "Height": 0.006630921270698309, "Left": 0.7600220441818237, "Top": 0.37648865580558777}, "Polygon": [{"X": 0.7600221633911133, "Y": 0.37648892402648926}, {"X": 0.7796344757080078, "Y": 0.37648865580558777}, {"X": 0.7796343564987183, "Y": 0.38311928510665894}, {"X": 0.7600220441818237, "Y": 0.3831195831298828}]}, "Id": "531bd5b4-3089-409d-9042-0eef15b5bfdc"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "this", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0170767679810524, "Height": 0.00658020656555891, "Left": 0.7818982601165771, "Top": 0.37639084458351135}, "Polygon": [{"X": 0.7818983793258667, "Y": 0.37639108300209045}, {"X": 0.7989750504493713, "Y": 0.37639084458351135}, {"X": 0.7989749312400818, "Y": 0.38297080993652344}, {"X": 0.7818982601165771, "Y": 0.38297104835510254}]}, "Id": "d9b77405-7084-4c40-b4d5-92a5b4ad29a3"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044950954616069794, "Height": 0.008148849941790104, "Left": 0.8024011850357056, "Top": 0.37618619203567505}, "Polygon": [{"X": 0.8024013042449951, "Y": 0.3761868178844452}, {"X": 0.847352147102356, "Y": 0.37618619203567505}, {"X": 0.8473520278930664, "Y": 0.38433441519737244}, {"X": 0.8024011850357056, "Y": 0.3843350410461426}]}, "Id": "61a0e89d-e2c1-4132-9b0b-552e755cab1a"}, {"BlockType": "WORD", "Confidence": 99.2930908203125, "Text": "AURORA,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04494357109069824, "Height": 0.0072541264817118645, "Left": 0.16181287169456482, "Top": 0.385231077671051}, "Polygon": [{"X": 0.1618129163980484, "Y": 0.38523170351982117}, {"X": 0.20675644278526306, "Y": 0.385231077671051}, {"X": 0.2067563831806183, "Y": 0.39248454570770264}, {"X": 0.16181287169456482, "Y": 0.39248520135879517}]}, "Id": "78f990d3-4bad-43fe-8c67-c5a392a43380"}, {"BlockType": "WORD", "Confidence": 99.892578125, "Text": "IL", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009762809611856937, "Height": 0.006594124715775251, "Left": 0.21053430438041687, "Top": 0.3850327432155609}, "Polygon": [{"X": 0.21053434908390045, "Y": 0.38503286242485046}, {"X": 0.22029711306095123, "Y": 0.3850327432155609}, {"X": 0.22029706835746765, "Y": 0.3916267156600952}, {"X": 0.21053430438041687, "Y": 0.39162686467170715}]}, "Id": "a25d5b0a-7560-4879-bac1-f25f07ba3a0f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "60502", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029458606615662575, "Height": 0.006240761373192072, "Left": 0.22320081293582916, "Top": 0.38528716564178467}, "Polygon": [{"X": 0.22320085763931274, "Y": 0.3852875828742981}, {"X": 0.252659410238266, "Y": 0.38528716564178467}, {"X": 0.2526593804359436, "Y": 0.3915275037288666}, {"X": 0.22320081293582916, "Y": 0.39152792096138}]}, "Id": "d859e529-c658-49dd-96dd-51911d5a4124"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "has", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016709955409169197, "Height": 0.006623478606343269, "Left": 0.7077588438987732, "Top": 0.38500329852104187}, "Polygon": [{"X": 0.7077589631080627, "Y": 0.38500353693962097}, {"X": 0.7244688272476196, "Y": 0.38500329852104187}, {"X": 0.7244687080383301, "Y": 0.3916265368461609}, {"X": 0.7077588438987732, "Y": 0.39162677526474}]}, "Id": "8119431e-dff0-4455-ae31-9aa88115ed14"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "been", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023208731785416603, "Height": 0.006726184394210577, "Left": 0.7273632884025574, "Top": 0.3849447965621948}, "Polygon": [{"X": 0.7273633480072021, "Y": 0.3849451243877411}, {"X": 0.7505720257759094, "Y": 0.3849447965621948}, {"X": 0.7505719065666199, "Y": 0.3916706442832947}, {"X": 0.7273632884025574, "Y": 0.39167100191116333}]}, "Id": "8eefedeb-b9ea-4891-b722-de6d662ab546"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "reweighed", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05010595545172691, "Height": 0.00796002522110939, "Left": 0.75407475233078, "Top": 0.38496842980384827}, "Polygon": [{"X": 0.7540748715400696, "Y": 0.38496914505958557}, {"X": 0.8041807413101196, "Y": 0.38496842980384827}, {"X": 0.8041806221008301, "Y": 0.39292773604393005}, {"X": 0.75407475233078, "Y": 0.39292845129966736}]}, "Id": "48856900-b2e7-4f12-a7ae-6cecf1095393"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01778884045779705, "Height": 0.006604108493775129, "Left": 0.806389570236206, "Top": 0.3849635720252991}, "Polygon": [{"X": 0.8063896894454956, "Y": 0.38496384024620056}, {"X": 0.8241783976554871, "Y": 0.3849635720252991}, {"X": 0.8241783380508423, "Y": 0.3915674388408661}, {"X": 0.806389570236206, "Y": 0.3915676772594452}]}, "Id": "9119facb-0ba1-439e-947b-c09792c68849"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "that", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018977243453264236, "Height": 0.006806911434978247, "Left": 0.8267665505409241, "Top": 0.3848668932914734}, "Polygon": [{"X": 0.8267666697502136, "Y": 0.3848671615123749}, {"X": 0.8457438349723816, "Y": 0.3848668932914734}, {"X": 0.845743715763092, "Y": 0.3916735351085663}, {"X": 0.8267665505409241, "Y": 0.3916738033294678}]}, "Id": "66437fc3-1920-4a1d-bb76-67a426e1af2a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015432396903634071, "Height": 0.006619512569159269, "Left": 0.707314670085907, "Top": 0.39335307478904724}, "Polygon": [{"X": 0.7073147296905518, "Y": 0.39335328340530396}, {"X": 0.7227470874786377, "Y": 0.39335307478904724}, {"X": 0.7227469682693481, "Y": 0.3999723792076111}, {"X": 0.707314670085907, "Y": 0.3999725878238678}]}, "Id": "848924cd-18f7-4a8e-be2a-25718fff61b8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "corrected", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.045688241720199585, "Height": 0.006469257641583681, "Left": 0.7260426878929138, "Top": 0.3935489356517792}, "Polygon": [{"X": 0.7260427474975586, "Y": 0.3935495913028717}, {"X": 0.771730899810791, "Y": 0.3935489356517792}, {"X": 0.7717308402061462, "Y": 0.40001752972602844}, {"X": 0.7260426878929138, "Y": 0.40001818537712097}]}, "Id": "ae433cae-1342-4aa7-98cd-e4e110d04d5f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031995803117752075, "Height": 0.008181402459740639, "Left": 0.7740893959999084, "Top": 0.39328134059906006}, "Polygon": [{"X": 0.774089515209198, "Y": 0.3932817876338959}, {"X": 0.8060852289199829, "Y": 0.39328134059906006}, {"X": 0.8060850501060486, "Y": 0.40146228671073914}, {"X": 0.7740893959999084, "Y": 0.40146273374557495}]}, "Id": "a027d835-b7d6-405c-9ded-74a59ebe1bb5"}, {"BlockType": "WORD", "Confidence": 99.91131591796875, "Text": "as", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010669945739209652, "Height": 0.004868826363235712, "Left": 0.8092235326766968, "Top": 0.394987553358078}, "Polygon": [{"X": 0.8092235922813416, "Y": 0.39498770236968994}, {"X": 0.819893479347229, "Y": 0.394987553358078}, {"X": 0.8198934197425842, "Y": 0.39985620975494385}, {"X": 0.8092235326766968, "Y": 0.3998563587665558}]}, "Id": "22c777d0-4055-4de8-8dad-6b158abcbe70"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "stated", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029793880879878998, "Height": 0.006427379325032234, "Left": 0.8228550553321838, "Top": 0.39359018206596375}, "Polygon": [{"X": 0.8228551745414734, "Y": 0.39359062910079956}, {"X": 0.8526489734649658, "Y": 0.39359018206596375}, {"X": 0.8526488542556763, "Y": 0.4000171422958374}, {"X": 0.8228550553321838, "Y": 0.40001755952835083}]}, "Id": "03e5bbf5-5c71-47c6-8d58-e6811d6f272c"}, {"BlockType": "WORD", "Confidence": 99.88121795654297, "Text": "is", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.007551476359367371, "Height": 0.0070420317351818085, "Left": 0.7076213955879211, "Top": 0.4017404317855835}, "Polygon": [{"X": 0.7076214551925659, "Y": 0.40174052119255066}, {"X": 0.7151728868484497, "Y": 0.4017404317855835}, {"X": 0.7151727676391602, "Y": 0.40878233313560486}, {"X": 0.7076213955879211, "Y": 0.4087824523448944}]}, "Id": "7778eb10-683a-46f5-a1dc-6a8341aa9ae8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015589390881359577, "Height": 0.006525528617203236, "Left": 0.717993974685669, "Top": 0.40196681022644043}, "Polygon": [{"X": 0.7179940938949585, "Y": 0.40196701884269714}, {"X": 0.733583390712738, "Y": 0.40196681022644043}, {"X": 0.7335832715034485, "Y": 0.4084920883178711}, {"X": 0.717993974685669, "Y": 0.4084923267364502}]}, "Id": "3ea4e9bf-1292-4f4d-998c-099afe7c066f"}, {"BlockType": "WORD", "Confidence": 99.77140808105469, "Text": "true", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019698061048984528, "Height": 0.00635173823684454, "Left": 0.7363592386245728, "Top": 0.40228596329689026}, "Polygon": [{"X": 0.7363593578338623, "Y": 0.40228626132011414}, {"X": 0.7560573220252991, "Y": 0.40228596329689026}, {"X": 0.7560572028160095, "Y": 0.4086374044418335}, {"X": 0.7363592386245728, "Y": 0.4086377024650574}]}, "Id": "2ac152a2-8d7e-4919-8bcf-2243a7fed975"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "and", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01766594685614109, "Height": 0.006636554840952158, "Left": 0.7591797113418579, "Top": 0.4019549787044525}, "Polygon": [{"X": 0.7591797709465027, "Y": 0.401955246925354}, {"X": 0.7768456339836121, "Y": 0.4019549787044525}, {"X": 0.7768455743789673, "Y": 0.40859127044677734}, {"X": 0.7591797113418579, "Y": 0.40859153866767883}]}, "Id": "ef45d148-fc26-4037-81fe-8611fc2fc5c4"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "accurate", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0413362979888916, "Height": 0.0063436212949454784, "Left": 0.7800097465515137, "Top": 0.40249454975128174}, "Polygon": [{"X": 0.7800098657608032, "Y": 0.4024951756000519}, {"X": 0.8213460445404053, "Y": 0.40249454975128174}, {"X": 0.8213459253311157, "Y": 0.40883758664131165}, {"X": 0.7800097465515137, "Y": 0.4088381826877594}]}, "Id": "1ba5c440-76ac-411e-b81b-8ec7c0b4d974"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Declared", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04313170164823532, "Height": 0.0064966813661158085, "Left": 0.16241343319416046, "Top": 0.4106127619743347}, "Polygon": [{"X": 0.16241347789764404, "Y": 0.41061341762542725}, {"X": 0.20554512739181519, "Y": 0.4106127619743347}, {"X": 0.2055450826883316, "Y": 0.41710883378982544}, {"X": 0.16241343319416046, "Y": 0.4171094596385956}]}, "Id": "af4bb3a5-d06d-4cda-ac66-4743c5a31c12"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03596487268805504, "Height": 0.008118771947920322, "Left": 0.20843079686164856, "Top": 0.4103333055973053}, "Polygon": [{"X": 0.20843085646629333, "Y": 0.4103338122367859}, {"X": 0.2443956732749939, "Y": 0.4103333055973053}, {"X": 0.24439561367034912, "Y": 0.41845154762268066}, {"X": 0.20843079686164856, "Y": 0.41845208406448364}]}, "Id": "4b5fed74-4324-4512-a0a8-1ad5e6af8918"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "1148", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023435501381754875, "Height": 0.006363362539559603, "Left": 0.2486116588115692, "Top": 0.4108577370643616}, "Polygon": [{"X": 0.2486117035150528, "Y": 0.4108580946922302}, {"X": 0.27204716205596924, "Y": 0.4108577370643616}, {"X": 0.27204710245132446, "Y": 0.4172207713127136}, {"X": 0.2486116588115692, "Y": 0.4172210991382599}]}, "Id": "f41064d8-8b71-4113-be23-efedbea0f2b2"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03254435956478119, "Height": 0.008135346695780754, "Left": 0.7069318294525146, "Top": 0.4102950394153595}, "Polygon": [{"X": 0.7069319486618042, "Y": 0.4102955162525177}, {"X": 0.739476203918457, "Y": 0.4102950394153595}, {"X": 0.7394760847091675, "Y": 0.4184299111366272}, {"X": 0.7069318294525146, "Y": 0.4184303879737854}]}, "Id": "104f249d-c02d-40f4-b0e6-cdc474551cff"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "of", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009893991984426975, "Height": 0.006705030333250761, "Left": 0.7423079609870911, "Top": 0.410263329744339}, "Polygon": [{"X": 0.7423080205917358, "Y": 0.4102634787559509}, {"X": 0.7522019147872925, "Y": 0.410263329744339}, {"X": 0.7522018551826477, "Y": 0.4169681966304779}, {"X": 0.7423079609870911, "Y": 0.41696834564208984}]}, "Id": "5e1d14d5-19a5-4291-a77b-acb0b7972b9b"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "the", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.015859805047512054, "Height": 0.0065754735842347145, "Left": 0.7543373107910156, "Top": 0.4106237292289734}, "Polygon": [{"X": 0.7543374300003052, "Y": 0.4106239676475525}, {"X": 0.7701971530914307, "Y": 0.4106237292289734}, {"X": 0.7701970338821411, "Y": 0.4171989858150482}, {"X": 0.7543373107910156, "Y": 0.4171992242336273}]}, "Id": "ee0dd4e9-6ab9-4b07-a118-acc540ba5308"}, {"BlockType": "WORD", "Confidence": 99.84056091308594, "Text": "shipment", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04508283734321594, "Height": 0.007687367964535952, "Left": 0.7730361223220825, "Top": 0.41048917174339294}, "Polygon": [{"X": 0.7730362415313721, "Y": 0.4104898273944855}, {"X": 0.8181189894676208, "Y": 0.41048917174339294}, {"X": 0.8181188702583313, "Y": 0.4181758761405945}, {"X": 0.7730361223220825, "Y": 0.418176531791687}]}, "Id": "f06b787c-380d-4fef-a0d6-9e9bb2a6fd19"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Corrected", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04748039320111275, "Height": 0.006810094229876995, "Left": 0.16247409582138062, "Top": 0.4188801050186157}, "Polygon": [{"X": 0.1624741405248642, "Y": 0.41888079047203064}, {"X": 0.20995448529720306, "Y": 0.4188801050186157}, {"X": 0.20995444059371948, "Y": 0.4256894886493683}, {"X": 0.16247409582138062, "Y": 0.4256902039051056}]}, "Id": "6060dee0-0704-4d19-bcab-cdf3d0b6c4e3"}, {"BlockType": "WORD", "Confidence": 99.7315444946289, "Text": "Weight:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036091309040784836, "Height": 0.008014409802854061, "Left": 0.21280618011951447, "Top": 0.41898638010025024}, "Polygon": [{"X": 0.21280623972415924, "Y": 0.4189869165420532}, {"X": 0.2488974928855896, "Y": 0.41898638010025024}, {"X": 0.24889743328094482, "Y": 0.4270002543926239}, {"X": 0.21280618011951447, "Y": 0.4270007908344269}]}, "Id": "2f8110b3-bdc1-492f-8ed3-e699ae144b83"}, {"BlockType": "WORD", "Confidence": 99.951171875, "Text": "1425", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.023578299209475517, "Height": 0.006388806272298098, "Left": 0.253194659948349, "Top": 0.419284462928772}, "Polygon": [{"X": 0.2531947195529938, "Y": 0.41928479075431824}, {"X": 0.27677297592163086, "Y": 0.419284462928772}, {"X": 0.2767729163169861, "Y": 0.4256729185581207}, {"X": 0.253194659948349, "Y": 0.425673246383667}]}, "Id": "a14834d5-3c0f-4462-9318-629392a42a96"}, {"BlockType": "WORD", "Confidence": 99.7120132446289, "Text": "HU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.017055867239832878, "Height": 0.006777491886168718, "Left": 0.16441786289215088, "Top": 0.4541013836860657}, "Polygon": [{"X": 0.16441790759563446, "Y": 0.45410165190696716}, {"X": 0.1814737319946289, "Y": 0.4541013836860657}, {"X": 0.18147367238998413, "Y": 0.4608786106109619}, {"X": 0.16441786289215088, "Y": 0.4608788788318634}]}, "Id": "79733ed5-778b-47cf-986c-9abb3935c66f"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "<PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027246743440628052, "Height": 0.007028465624898672, "Left": 0.19829589128494263, "Top": 0.4539552330970764}, "Polygon": [{"X": 0.1982959508895874, "Y": 0.45395565032958984}, {"X": 0.22554263472557068, "Y": 0.4539552330970764}, {"X": 0.2255425900220871, "Y": 0.4609832763671875}, {"X": 0.19829589128494263, "Y": 0.4609836935997009}]}, "Id": "b04a3696-c92a-4886-8216-90222335e53f"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02633608691394329, "Height": 0.007158621214330196, "Left": 0.2319745570421219, "Top": 0.45388081669807434}, "Polygon": [{"X": 0.23197461664676666, "Y": 0.4538812041282654}, {"X": 0.2583106458187103, "Y": 0.45388081669807434}, {"X": 0.25831058621406555, "Y": 0.4610390365123749}, {"X": 0.2319745570421219, "Y": 0.4610394239425659}]}, "Id": "e0068536-bbad-473a-8f2e-5a9abe5bc866"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Time", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02890188619494438, "Height": 0.007359205279499292, "Left": 0.2874515652656555, "Top": 0.4534839987754822}, "Polygon": [{"X": 0.2874516248703003, "Y": 0.453484445810318}, {"X": 0.316353440284729, "Y": 0.4534839987754822}, {"X": 0.31635338068008423, "Y": 0.4608427584171295}, {"X": 0.2874515652656555, "Y": 0.46084320545196533}]}, "Id": "d776db4c-1c94-4818-a1e6-c9a3f5e27d54"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "Weight", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.040659863501787186, "Height": 0.009368103928864002, "Left": 0.3337138891220093, "Top": 0.4535251557826996}, "Polygon": [{"X": 0.33371397852897644, "Y": 0.4535257816314697}, {"X": 0.37437376379966736, "Y": 0.4535251557826996}, {"X": 0.3743736743927002, "Y": 0.4628926217556}, {"X": 0.3337138891220093, "Y": 0.4628932476043701}]}, "Id": "66d9b8f5-59f7-4ff0-8c76-41a7b3f00641"}, {"BlockType": "WORD", "Confidence": 99.89098358154297, "Text": "Terminal", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0507369227707386, "Height": 0.007401087787002325, "Left": 0.3780301809310913, "Top": 0.45351311564445496}, "Polygon": [{"X": 0.3780302405357361, "Y": 0.4535139203071594}, {"X": 0.4287670850753784, "Y": 0.45351311564445496}, {"X": 0.42876702547073364, "Y": 0.46091344952583313}, {"X": 0.3780301809310913, "Y": 0.4609142243862152}]}, "Id": "15f88dbc-7db4-4661-a827-31407506c055"}, {"BlockType": "WORD", "Confidence": 99.7217788696289, "Text": "Pro", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01893853209912777, "Height": 0.007110124453902245, "Left": 0.46933484077453613, "Top": 0.45394328236579895}, "Polygon": [{"X": 0.4693349301815033, "Y": 0.4539435803890228}, {"X": 0.48827338218688965, "Y": 0.45394328236579895}, {"X": 0.4882732927799225, "Y": 0.46105310320854187}, {"X": 0.46933484077453613, "Y": 0.46105340123176575}]}, "Id": "505d2c84-3675-4c18-b5f4-96ce6a4e109d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Number", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044754479080438614, "Height": 0.00717869633808732, "Left": 0.4923553168773651, "Top": 0.4536535441875458}, "Polygon": [{"X": 0.4923553764820099, "Y": 0.4536542296409607}, {"X": 0.5371097922325134, "Y": 0.4536535441875458}, {"X": 0.5371096730232239, "Y": 0.46083155274391174}, {"X": 0.4923553168773651, "Y": 0.46083223819732666}]}, "Id": "e198446a-c80a-49da-9f4c-00234fa7cc2e"}, {"BlockType": "WORD", "Confidence": 99.8828125, "Text": "33136199", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058709219098091125, "Height": 0.007329114247113466, "Left": 0.1687587946653366, "Top": 0.4650166928768158}, "Polygon": [{"X": 0.1687588393688202, "Y": 0.4650176167488098}, {"X": 0.22746801376342773, "Y": 0.4650166928768158}, {"X": 0.22746795415878296, "Y": 0.47234490513801575}, {"X": 0.1687587946653366, "Y": 0.47234582901000977}]}, "Id": "3788abd3-332e-4fd5-a34d-493b4c02ac2e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0687561109662056, "Height": 0.008462921716272831, "Left": 0.24354542791843414, "Top": 0.46471866965293884}, "Polygon": [{"X": 0.24354548752307892, "Y": 0.4647197127342224}, {"X": 0.31230154633522034, "Y": 0.46471866965293884}, {"X": 0.3123014569282532, "Y": 0.47318050265312195}, {"X": 0.24354542791843414, "Y": 0.4731815755367279}]}, "Id": "3fdcc932-0118-47fa-8749-653efd686c72"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "00:02", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03399926424026489, "Height": 0.007215920835733414, "Left": 0.3340480327606201, "Top": 0.4650706946849823}, "Polygon": [{"X": 0.3340480923652649, "Y": 0.4650712311267853}, {"X": 0.368047297000885, "Y": 0.4650706946849823}, {"X": 0.36804723739624023, "Y": 0.4722861051559448}, {"X": 0.3340480327606201, "Y": 0.4722866117954254}]}, "Id": "424c1803-f0f0-4764-bf2c-00274947c847"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "880", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02212643437087536, "Height": 0.006782433483749628, "Left": 0.3790387511253357, "Top": 0.46533575654029846}, "Polygon": [{"X": 0.37903881072998047, "Y": 0.4653360843658447}, {"X": 0.4011651873588562, "Y": 0.46533575654029846}, {"X": 0.4011651277542114, "Y": 0.4721178412437439}, {"X": 0.3790387511253357, "Y": 0.47211816906929016}]}, "Id": "ca3658cb-1da2-49e2-8446-aff38fc13ec2"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "463", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021867085248231888, "Height": 0.00682410504668951, "Left": 0.43186819553375244, "Top": 0.46527284383773804}, "Polygon": [{"X": 0.4318682551383972, "Y": 0.4652732014656067}, {"X": 0.45373526215553284, "Y": 0.46527284383773804}, {"X": 0.45373520255088806, "Y": 0.47209662199020386}, {"X": 0.43186819553375244, "Y": 0.4720969498157501}]}, "Id": "40940edb-1c9a-4735-bbe2-1b9a8c66e3b5"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.060560133308172226, "Height": 0.007165390066802502, "Left": 0.5148974657058716, "Top": 0.4650382697582245}, "Polygon": [{"X": 0.5148975253105164, "Y": 0.4650391936302185}, {"X": 0.5754575729370117, "Y": 0.4650382697582245}, {"X": 0.5754575133323669, "Y": 0.47220271825790405}, {"X": 0.5148974657058716, "Y": 0.47220367193222046}]}, "Id": "349dc259-f48b-411f-9941-7aedb26f3730"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "33136200", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.058386050164699554, "Height": 0.006965055596083403, "Left": 0.16896452009677887, "Top": 0.47567445039749146}, "Polygon": [{"X": 0.16896456480026245, "Y": 0.4756753742694855}, {"X": 0.22735057771205902, "Y": 0.47567445039749146}, {"X": 0.22735051810741425, "Y": 0.4826385974884033}, {"X": 0.16896452009677887, "Y": 0.48263952136039734}]}, "Id": "b438cfe6-54ff-4cad-8d4a-74ab68f5777f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "08/26/2025", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06862763315439224, "Height": 0.008570958860218525, "Left": 0.24351204931735992, "Top": 0.47528213262557983}, "Polygon": [{"X": 0.2435121089220047, "Y": 0.4752832055091858}, {"X": 0.31213968992233276, "Y": 0.47528213262557983}, {"X": 0.3121396005153656, "Y": 0.48385199904441833}, {"X": 0.24351204931735992, "Y": 0.4838530719280243}]}, "Id": "bf19c795-9684-4570-9fc0-36ba1ae39159"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "00:03", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.033893387764692307, "Height": 0.0071488418616354465, "Left": 0.3341608941555023, "Top": 0.4757031202316284}, "Polygon": [{"X": 0.3341609537601471, "Y": 0.475703626871109}, {"X": 0.3680543005466461, "Y": 0.4757031202316284}, {"X": 0.36805421113967896, "Y": 0.48285141587257385}, {"X": 0.3341608941555023, "Y": 0.48285195231437683}]}, "Id": "7dc12de5-663f-43e9-9705-fe9a91d4a8af"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "545", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02147347666323185, "Height": 0.006957860663533211, "Left": 0.3794381320476532, "Top": 0.4757300317287445}, "Polygon": [{"X": 0.379438191652298, "Y": 0.47573035955429077}, {"X": 0.4009115993976593, "Y": 0.4757300317287445}, {"X": 0.4009115397930145, "Y": 0.4826875627040863}, {"X": 0.3794381320476532, "Y": 0.48268789052963257}]}, "Id": "8f060444-750a-40d3-944f-8955940a65f2"}, {"BlockType": "WORD", "Confidence": 99.91051483154297, "Text": "463", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021909311413764954, "Height": 0.006979272235184908, "Left": 0.43184787034988403, "Top": 0.4756060838699341}, "Polygon": [{"X": 0.4318479299545288, "Y": 0.47560641169548035}, {"X": 0.4537571966648102, "Y": 0.4756060838699341}, {"X": 0.453757107257843, "Y": 0.48258501291275024}, {"X": 0.43184787034988403, "Y": 0.4825853407382965}]}, "Id": "6822f664-69f4-4fca-a641-e205c0cd982f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "227407699", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06058836355805397, "Height": 0.006870943587273359, "Left": 0.514869749546051, "Top": 0.4756676256656647}, "Polygon": [{"X": 0.5148698091506958, "Y": 0.4756685793399811}, {"X": 0.5754581093788147, "Y": 0.4756676256656647}, {"X": 0.5754579901695251, "Y": 0.4825376272201538}, {"X": 0.514869749546051, "Y": 0.4825385808944702}]}, "Id": "f79bc18f-5987-4e99-a130-fbb5bee0e7bb"}, {"BlockType": "WORD", "Confidence": 99.83238983154297, "Text": "3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0063613103702664375, "Height": 0.006829916033893824, "Left": 0.16887255012989044, "Top": 0.48619070649147034}, "Polygon": [{"X": 0.16887259483337402, "Y": 0.4861908257007599}, {"X": 0.175233855843544, "Y": 0.48619070649147034}, {"X": 0.17523381114006042, "Y": 0.49302053451538086}, {"X": 0.16887255012989044, "Y": 0.493020623922348}]}, "Id": "05689d55-786f-42af-aa2b-b43bcbcec4f6"}, {"BlockType": "WORD", "Confidence": 99.68511199951172, "Text": "4", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0070258076302707195, "Height": 0.006691001355648041, "Left": 0.16855113208293915, "Top": 0.49654772877693176}, "Polygon": [{"X": 0.16855117678642273, "Y": 0.4965478479862213}, {"X": 0.17557694017887115, "Y": 0.49654772877693176}, {"X": 0.17557689547538757, "Y": 0.5032386183738708}, {"X": 0.16855113208293915, "Y": 0.5032387375831604}]}, "Id": "526f2e89-f73c-4dfc-aebd-b914cb8a617b"}, {"BlockType": "WORD", "Confidence": 99.79252624511719, "Text": "5", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006197942886501551, "Height": 0.006747648119926453, "Left": 0.1691218912601471, "Top": 0.5072013139724731}, "Polygon": [{"X": 0.16912193596363068, "Y": 0.5072013735771179}, {"X": 0.1753198355436325, "Y": 0.5072013139724731}, {"X": 0.17531979084014893, "Y": 0.5139488577842712}, {"X": 0.1691218912601471, "Y": 0.5139489769935608}]}, "Id": "9c978cb3-119d-49b1-819a-7b63829f97f5"}, {"BlockType": "WORD", "Confidence": 99.71281433105469, "Text": "6", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00636710412800312, "Height": 0.0069289072416722775, "Left": 0.16888143122196198, "Top": 0.5180972814559937}, "Polygon": [{"X": 0.16888147592544556, "Y": 0.5180974006652832}, {"X": 0.17524853348731995, "Y": 0.5180972814559937}, {"X": 0.17524848878383636, "Y": 0.5250260829925537}, {"X": 0.16888143122196198, "Y": 0.5250262022018433}]}, "Id": "b5583230-fecb-4412-bcca-5a2643ab3592"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "7", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006649814546108246, "Height": 0.006885642185807228, "Left": 0.16893725097179413, "Top": 0.5284852385520935}, "Polygon": [{"X": 0.1689372956752777, "Y": 0.5284853577613831}, {"X": 0.17558707296848297, "Y": 0.5284852385520935}, {"X": 0.1755870282649994, "Y": 0.5353707671165466}, {"X": 0.16893725097179413, "Y": 0.5353708863258362}]}, "Id": "d61c5fac-a123-48f4-995e-9aad73bb6ae5"}, {"BlockType": "WORD", "Confidence": 99.67375183105469, "Text": "8", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006595203652977943, "Height": 0.006734260357916355, "Left": 0.1688777208328247, "Top": 0.5392356514930725}, "Polygon": [{"X": 0.1688777655363083, "Y": 0.5392357707023621}, {"X": 0.1754729151725769, "Y": 0.5392356514930725}, {"X": 0.17547287046909332, "Y": 0.5459697842597961}, {"X": 0.1688777208328247, "Y": 0.5459699034690857}]}, "Id": "463a0e3a-e5a3-471a-a35e-dcac3bf24b5b"}, {"BlockType": "WORD", "Confidence": 99.60299682617188, "Text": "9", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.006715078372508287, "Height": 0.007085907738655806, "Left": 0.16874417662620544, "Top": 0.5495967268943787}, "Polygon": [{"X": 0.16874422132968903, "Y": 0.5495968461036682}, {"X": 0.17545926570892334, "Y": 0.5495967268943787}, {"X": 0.17545920610427856, "Y": 0.5566825270652771}, {"X": 0.16874417662620544, "Y": 0.5566826462745667}]}, "Id": "382cbc6d-3154-48bc-a652-08edb3bb6dfa"}, {"BlockType": "WORD", "Confidence": 99.85271453857422, "Text": "10", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.012692058458924294, "Height": 0.006892743520438671, "Left": 0.1696051061153412, "Top": 0.5602093935012817}, "Polygon": [{"X": 0.16960515081882477, "Y": 0.5602095723152161}, {"X": 0.18229715526103973, "Y": 0.5602093935012817}, {"X": 0.18229711055755615, "Y": 0.5671018958091736}, {"X": 0.1696051061153412, "Y": 0.5671021342277527}]}, "Id": "6e8e7c75-913b-4e27-b517-da4b2e73bca0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.027860941365361214, "Height": 0.007223647087812424, "Left": 0.16397152841091156, "Top": 0.5709370970726013}, "Polygon": [{"X": 0.16397157311439514, "Y": 0.5709375739097595}, {"X": 0.19183246791362762, "Y": 0.5709370970726013}, {"X": 0.19183242321014404, "Y": 0.5781602263450623}, {"X": 0.16397152841091156, "Y": 0.5781607031822205}]}, "Id": "0cc018f2-1d63-4a22-ac41-107662ec6558"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "1425", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02627403661608696, "Height": 0.006929267197847366, "Left": 0.33493518829345703, "Top": 0.5713433623313904}, "Polygon": [{"X": 0.3349352478981018, "Y": 0.5713438391685486}, {"X": 0.3612092137336731, "Y": 0.5713433623313904}, {"X": 0.3612091541290283, "Y": 0.5782721638679504}, {"X": 0.33493518829345703, "Y": 0.5782726407051086}]}, "Id": "2f2be307-c6d6-40b3-95a6-434e7ab44a6c"}, {"BlockType": "WORD", "Confidence": 99.88201141357422, "Text": "Page", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030416401103138924, "Height": 0.010070300661027431, "Left": 0.8169759511947632, "Top": 0.9619089365005493}, "Polygon": [{"X": 0.8169761300086975, "Y": 0.9619096517562866}, {"X": 0.8473923802375793, "Y": 0.9619089365005493}, {"X": 0.847392201423645, "Y": 0.9719785451889038}, {"X": 0.8169759511947632, "Y": 0.9719792604446411}]}, "Id": "a6a732de-d645-495c-85c7-6f6bd09ddfcb"}, {"BlockType": "WORD", "Confidence": 99.63548278808594, "Text": "3/3", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.01798633299767971, "Height": 0.008184125646948814, "Left": 0.8512584567070007, "Top": 0.9621924757957458}, "Polygon": [{"X": 0.8512585759162903, "Y": 0.9621928930282593}, {"X": 0.8692447543144226, "Y": 0.9621924757957458}, {"X": 0.8692446351051331, "Y": 0.970376193523407}, {"X": 0.8512584567070007, "Y": 0.9703766107559204}]}, "Id": "a3e9113d-2751-452f-8e40-70b47795a864"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "8f45dd18-b5a4-47a4-adf8-cc9844d75ed8", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "8f45dd18-b5a4-47a4-adf8-cc9844d75ed8", "content-type": "application/x-amz-json-1.1", "content-length": "78383", "date": "Wed, 24 Sep 2025 09:18:52 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "aa941bf6-de2f-46d8-bef4-37dc236fe8f2", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 09:18:55 GMT", "content-type": "application/json", "content-length": "981", "connection": "keep-alive", "x-amzn-requestid": "aa941bf6-de2f-46d8-bef4-37dc236fe8f2"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page content. Determine document type. Keywords: \"Weight & Inspection\", \"CERTIFIED WEIGHT CERTIFICATE\", \"Weight Certificate\", \"Declared Weight\", \"Corrected Weight\", \"Pro Number\", \"Bill of Lading\", \"Scan Date\", \"Time\", \"Weight Terminal\". This indicates a weight and inspection certificate. According to enum: weight_and_inspection_cert. Check if it has \"nmfc_cert\" keywords: no NMFC, class, etc. So classify as weight_and_inspection_cert.\n\nWe need output via tool classify_logistics_doc_type with array of objects per page. There's only one page. Provide page_no 1, doc_type \"weight_and_inspection_cert\"."}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"weight_and_inspection_cert\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2685, "outputTokens": 164, "totalTokens": 2849}, "metrics": {"latencyMs": 865}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/a7cd079e_DFY1VDZWR7NBDLJV02G2.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nCentral Transport\n08/31/2025\nPRO#: 227407699\nWeight & Inspection\nCENTRAL TRANSPORT\n12225 Stephens Road\nWarren, MI 48089\nCERTIFIED WEIGHT CERTIFICATE\nPro Number: 227407699\nBill of Lading\nDate: 08/26/2025\nDIAMOND ENVELOPE\nWeight Certificate\n2270 WHITE OAK CIR\nWe Certify that this shipment\nAURORA, IL 60502\nhas been reweighed and that\nthe corrected weight as stated\nis the true and accurate\nDeclared Weight: 1148\nweight of the shipment\nCorrected Weight: 1425\nHU\nScan Date\nTime\nWeight Terminal\nPro Number\n33136199\n08/26/2025\n00:02 880\n463\n227407699\n33136200\n08/26/2025\n00:03\n545\n463\n227407699\n3\n4\n5\n6\n7\n8\n9\n10\nTotal\n1425\nPage 3/3\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}