{"classification_result": {"documents": [{"page_no": 1, "doc_type": "so_confirmation"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 0.9999501705169678, "Left": 0.0, "Top": 4.984682163922116e-05}, "Polygon": [{"X": 0.0, "Y": 5.4651842219755054e-05}, {"X": 1.0, "Y": 4.984682163922116e-05}, {"X": 1.0, "Y": 1.0}, {"X": 0.0, "Y": 1.0}]}, "Id": "3cf48030-a96a-4191-95fe-b2a87cb2ea7d", "Relationships": [{"Type": "CHILD", "Ids": ["6b978b31-93f0-483b-a20e-600c802d0432", "4137c0ea-7c58-4e39-8bfb-73f92ac63cb9", "9154cf70-ea7d-4b79-90c9-716d621e019a", "74e34b7b-e2c1-4e17-93c8-ff2e65681066", "e5ad7ba3-0155-4e18-a1d4-769836a35466", "de22dd59-44b3-4373-9d60-2f8bd0f5d4fd", "8ddb873a-89d1-4654-92b0-10aafd7a7b70", "8623e7d7-5a61-4c2b-a983-047932197460", "69e6ce16-04d1-40b3-a487-01eccc2b2681", "3767e1aa-72ca-4857-a8a0-62de7e0102b4", "eb340851-cd2c-4f29-a98b-b84b96ad550e", "ea1f6e47-60bd-468b-8941-1a988727c544", "887ebe81-af2b-41c4-bec8-caa07a6ab5f4", "c254a0fc-4ef6-4eea-9711-cc4efa8eed08", "b7704205-dec8-4308-9dea-dd3873756e79", "a298dca4-d0be-4174-ada4-371d7d68eba6", "20a8f77d-32a6-4544-b62e-a59c3fb51904", "986e69ae-7776-4fed-b6d9-b4cebe54a328", "48bc37db-1911-475d-82cf-333eacdcacb4", "3aa454b9-88f3-4b45-a21a-eba3c91a8b78", "5da017dd-a894-4d9d-a2ce-0a2f255e37f2", "2aa372ef-667a-40ad-aaa4-52bcf3232253", "ea491ccc-f5d2-4e80-99ef-5c7f5f44f0ef", "a20245cf-e4c1-41c1-a120-9f2dbb50057b", "390a4264-e375-4baa-b5a9-3dc97b7b6991", "e8b245ff-8b7f-46fa-a47d-9b865f28a500", "c21864c0-d998-4129-ade7-88da52eaeb55", "cfac937b-8c8b-4d79-aa68-63018ff63925", "041d0123-0031-4c02-968c-a10f9f3bf641", "45d42771-7fb9-44c2-bee0-7a5b435c37a8", "e0c06e39-1f4a-4cca-ade3-1ab7d0189100", "6cbebfef-6c87-4a70-b5ad-95ce2b6a49b2", "def649c0-c015-4009-8536-4677e834cd53", "66c79485-abf7-4881-ac2c-8c04ebb505d8", "0e060a64-cd76-47a6-a7bf-43378e15d4d8", "47446618-7e6b-4395-bbdc-c189637c6ed0", "270a1327-0c82-4e37-83b4-739227318f62", "486e3216-712e-424d-a05c-e77275c6eb72", "26546164-0601-4335-8c6d-3763818388a9", "2d53a9b0-6556-496b-bd57-c77bbd1288b5"]}]}, {"BlockType": "LINE", "Confidence": 99.78077697753906, "Text": "ORDER CONFIRMATION", "Geometry": {"BoundingBox": {"Width": 0.3797556459903717, "Height": 0.01104031503200531, "Left": 0.3046632707118988, "Top": 0.011260759085416794}, "Polygon": [{"X": 0.3046632707118988, "Y": 0.011262573301792145}, {"X": 0.6844189167022705, "Y": 0.011260759085416794}, {"X": 0.6844189167022705, "Y": 0.0222992692142725}, {"X": 0.3046633303165436, "Y": 0.022301074117422104}]}, "Id": "6b978b31-93f0-483b-a20e-600c802d0432", "Relationships": [{"Type": "CHILD", "Ids": ["4deae6cd-2689-42d5-b25f-6e2391a8969e", "189cbb29-c54f-43de-8eb2-a890ffa92d52"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "From", "Geometry": {"BoundingBox": {"Width": 0.04238623008131981, "Height": 0.0106004998087883, "Left": 0.02286210097372532, "Top": 0.04401431977748871}, "Polygon": [{"X": 0.02286210097372532, "Y": 0.044014520943164825}, {"X": 0.06524824351072311, "Y": 0.04401431977748871}, {"X": 0.06524833291769028, "Y": 0.05461462214589119}, {"X": 0.02286219783127308, "Y": 0.05461481958627701}]}, "Id": "4137c0ea-7c58-4e39-8bfb-73f92ac63cb9", "Relationships": [{"Type": "CHILD", "Ids": ["533e9752-33ad-4f85-a7c9-f15ad305316b"]}]}, {"BlockType": "LINE", "Confidence": 99.97518920898438, "Text": "GROSKOPF WAREHOUSE", "Geometry": {"BoundingBox": {"Width": 0.1946936547756195, "Height": 0.010600081644952297, "Left": 0.2934810519218445, "Top": 0.04407813400030136}, "Polygon": [{"X": 0.2934810519218445, "Y": 0.044079046696424484}, {"X": 0.488174706697464, "Y": 0.04407813400030136}, {"X": 0.488174706697464, "Y": 0.05467730388045311}, {"X": 0.29348111152648926, "Y": 0.05467821657657623}]}, "Id": "9154cf70-ea7d-4b79-90c9-716d621e019a", "Relationships": [{"Type": "CHILD", "Ids": ["623ec845-864e-4d99-bd3f-d20e8e9a3a41", "76a37f64-713d-4ca1-abb5-7e07924b65cf"]}]}, {"BlockType": "LINE", "Confidence": 99.97518920898438, "Text": "Our order#", "Geometry": {"BoundingBox": {"Width": 0.10823582112789154, "Height": 0.01094308402389288, "Left": 0.5438161492347717, "Top": 0.07638475298881531}, "Polygon": [{"X": 0.5438161492347717, "Y": 0.0763852596282959}, {"X": 0.6520519852638245, "Y": 0.07638475298881531}, {"X": 0.6520519256591797, "Y": 0.08732733875513077}, {"X": 0.5438161492347717, "Y": 0.08732783794403076}]}, "Id": "74e34b7b-e2c1-4e17-93c8-ff2e65681066", "Relationships": [{"Type": "CHILD", "Ids": ["02e4f3c4-21a3-419e-af2d-e2e68e937513", "9acbc01c-99ef-4e99-84e5-25021f0fc9e3"]}]}, {"BlockType": "LINE", "Confidence": 99.716796875, "Text": "733380.0", "Geometry": {"BoundingBox": {"Width": 0.08552885055541992, "Height": 0.01045298483222723, "Left": 0.7503993511199951, "Top": 0.07698287814855576}, "Polygon": [{"X": 0.7503994107246399, "Y": 0.07698327302932739}, {"X": 0.835928201675415, "Y": 0.07698287814855576}, {"X": 0.8359281420707703, "Y": 0.08743546903133392}, {"X": 0.7503993511199951, "Y": 0.08743586391210556}]}, "Id": "e5ad7ba3-0155-4e18-a1d4-769836a35466", "Relationships": [{"Type": "CHILD", "Ids": ["385ab6e7-5950-4389-8d92-d70ea7e38690"]}]}, {"BlockType": "LINE", "Confidence": 99.9560546875, "Text": "Winery order#", "Geometry": {"BoundingBox": {"Width": 0.14124083518981934, "Height": 0.013854186050593853, "Left": 0.5437501072883606, "Top": 0.09275916963815689}, "Polygon": [{"X": 0.5437501072883606, "Y": 0.09275981783866882}, {"X": 0.6849909424781799, "Y": 0.09275916963815689}, {"X": 0.6849909424781799, "Y": 0.10661271214485168}, {"X": 0.5437501072883606, "Y": 0.10661335289478302}]}, "Id": "de22dd59-44b3-4373-9d60-2f8bd0f5d4fd", "Relationships": [{"Type": "CHILD", "Ids": ["942f1681-da97-4d8f-bdd9-3e9324b3a45b", "6d594802-d53d-4d5f-a993-2ad0135bf4ad"]}]}, {"BlockType": "LINE", "Confidence": 85.99858093261719, "Text": ".....", "Geometry": {"BoundingBox": {"Width": 0.04609755426645279, "Height": 0.0025529044214636087, "Left": 0.6892065405845642, "Top": 0.10096529871225357}, "Polygon": [{"X": 0.6892065405845642, "Y": 0.10096550732851028}, {"X": 0.735304057598114, "Y": 0.10096529871225357}, {"X": 0.735304057598114, "Y": 0.10351799428462982}, {"X": 0.6892065405845642, "Y": 0.10351820290088654}]}, "Id": "8ddb873a-89d1-4654-92b0-10aafd7a7b70", "Relationships": [{"Type": "CHILD", "Ids": ["446c2043-bb1e-450d-b011-de45141ec221"]}]}, {"BlockType": "LINE", "Confidence": 99.970703125, "Text": "LBV21125", "Geometry": {"BoundingBox": {"Width": 0.0851137712597847, "Height": 0.010532609187066555, "Left": 0.751197338104248, "Top": 0.09331311285495758}, "Polygon": [{"X": 0.751197338104248, "Y": 0.09331350773572922}, {"X": 0.8363111019134521, "Y": 0.09331311285495758}, {"X": 0.8363110423088074, "Y": 0.10384533554315567}, {"X": 0.751197338104248, "Y": 0.10384572297334671}]}, "Id": "8623e7d7-5a61-4c2b-a983-047932197460", "Relationships": [{"Type": "CHILD", "Ids": ["30fe5f5e-3118-4ad1-a6ee-0c5d28dc8bbe"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Our order date", "Geometry": {"BoundingBox": {"Width": 0.15090641379356384, "Height": 0.010970167815685272, "Left": 0.022112101316452026, "Top": 0.10917115956544876}, "Polygon": [{"X": 0.022112101316452026, "Y": 0.10917184501886368}, {"X": 0.1730184406042099, "Y": 0.10917115956544876}, {"X": 0.17301851511001587, "Y": 0.12014064192771912}, {"X": 0.022112200036644936, "Y": 0.12014132738113403}]}, "Id": "69e6ce16-04d1-40b3-a487-01eccc2b2681", "Relationships": [{"Type": "CHILD", "Ids": ["dc6399ea-f8f9-4e64-a849-0b6941f42295", "7e5109db-29a8-4fa3-8e7c-036d5429f538", "3569a204-288a-48b8-85c4-b850e91a28da"]}]}, {"BlockType": "LINE", "Confidence": 99.65262603759766, "Text": "02/06/25", "Geometry": {"BoundingBox": {"Width": 0.08488275855779648, "Height": 0.012565511278808117, "Left": 0.2948756515979767, "Top": 0.10937362909317017}, "Polygon": [{"X": 0.2948756515979767, "Y": 0.1093740165233612}, {"X": 0.379758358001709, "Y": 0.10937362909317017}, {"X": 0.37975838780403137, "Y": 0.12193875759840012}, {"X": 0.2948756814002991, "Y": 0.12193914502859116}]}, "Id": "3767e1aa-72ca-4857-a8a0-62de7e0102b4", "Relationships": [{"Type": "CHILD", "Ids": ["7f1a3afc-2c7d-4358-8657-7d52437839f6"]}]}, {"BlockType": "LINE", "Confidence": 99.990234375, "Text": "Requested ship date", "Geometry": {"BoundingBox": {"Width": 0.20489786565303802, "Height": 0.013655452989041805, "Left": 0.022566067054867744, "Top": 0.12589521706104279}, "Polygon": [{"X": 0.022566067054867744, "Y": 0.1258961409330368}, {"X": 0.22746385633945465, "Y": 0.12589521706104279}, {"X": 0.22746393084526062, "Y": 0.13954974710941315}, {"X": 0.022566191852092743, "Y": 0.13955067098140717}]}, "Id": "eb340851-cd2c-4f29-a98b-b84b96ad550e", "Relationships": [{"Type": "CHILD", "Ids": ["33b0cef5-f55c-49e5-ab55-850bcc63de6c", "36bce3f2-c9a5-400a-bff2-62427cdef970", "f17de788-9b8c-4221-8414-e4d02a55d672"]}]}, {"BlockType": "LINE", "Confidence": 99.72417449951172, "Text": "02/11/25", "Geometry": {"BoundingBox": {"Width": 0.08506634086370468, "Height": 0.012250536121428013, "Left": 0.2947002053260803, "Top": 0.12625855207443237}, "Polygon": [{"X": 0.2947002053260803, "Y": 0.1262589395046234}, {"X": 0.3797664940357208, "Y": 0.12625855207443237}, {"X": 0.3797665238380432, "Y": 0.13850870728492737}, {"X": 0.2947002351284027, "Y": 0.13850907981395721}]}, "Id": "ea1f6e47-60bd-468b-8941-1a988727c544", "Relationships": [{"Type": "CHILD", "Ids": ["12d837c9-769d-4618-b993-d723009060e2"]}]}, {"BlockType": "LINE", "Confidence": 99.86088562011719, "Text": "Ship via", "Geometry": {"BoundingBox": {"Width": 0.08562792092561722, "Height": 0.0137452008202672, "Left": 0.022297628223896027, "Top": 0.14224711060523987}, "Polygon": [{"X": 0.022297628223896027, "Y": 0.1422474980354309}, {"X": 0.10792544484138489, "Y": 0.14224711060523987}, {"X": 0.10792554914951324, "Y": 0.15599192678928375}, {"X": 0.022297753021121025, "Y": 0.1559923142194748}]}, "Id": "887ebe81-af2b-41c4-bec8-caa07a6ab5f4", "Relationships": [{"Type": "CHILD", "Ids": ["7b96f075-7f97-4b5f-a30f-7c01e06b5e23", "138caf0a-a985-4565-acb6-8635d830ef23"]}]}, {"BlockType": "LINE", "Confidence": 99.79385375976562, "Text": "McClain & Associates", "Geometry": {"BoundingBox": {"Width": 0.21588343381881714, "Height": 0.01094588078558445, "Left": 0.29372793436050415, "Top": 0.1422564834356308}, "Polygon": [{"X": 0.29372793436050415, "Y": 0.1422574520111084}, {"X": 0.5096113681793213, "Y": 0.1422564834356308}, {"X": 0.5096113681793213, "Y": 0.1532014012336731}, {"X": 0.29372796416282654, "Y": 0.1532023698091507}]}, "Id": "c254a0fc-4ef6-4eea-9711-cc4efa8eed08", "Relationships": [{"Type": "CHILD", "Ids": ["d5f4833d-a8a0-464f-8b30-64ea5692166e", "8154b9a6-3554-4671-97f8-fe3dc33d4ffd", "494f38c1-a1e5-4778-93cc-2b2e85de25a1"]}]}, {"BlockType": "LINE", "Confidence": 58.1646614074707, "Text": "SHIP - TO", "Geometry": {"BoundingBox": {"Width": 0.07550638914108276, "Height": 0.010819684714078903, "Left": 0.1744898110628128, "Top": 0.17518393695354462}, "Polygon": [{"X": 0.1744898110628128, "Y": 0.17518426477909088}, {"X": 0.2499961405992508, "Y": 0.17518393695354462}, {"X": 0.24999620020389557, "Y": 0.18600328266620636}, {"X": 0.17448988556861877, "Y": 0.18600362539291382}]}, "Id": "b7704205-dec8-4308-9dea-dd3873756e79", "Relationships": [{"Type": "CHILD", "Ids": ["887992a8-9916-48f2-8fc9-ca9a79bc89ab"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "WINERY", "Geometry": {"BoundingBox": {"Width": 0.06498411297798157, "Height": 0.010651880875229836, "Left": 0.6306143403053284, "Top": 0.17541582882404327}, "Polygon": [{"X": 0.6306143403053284, "Y": 0.17541611194610596}, {"X": 0.6955984830856323, "Y": 0.17541582882404327}, {"X": 0.6955984234809875, "Y": 0.18606741726398468}, {"X": 0.6306143403053284, "Y": 0.18606770038604736}]}, "Id": "a298dca4-d0be-4174-ada4-371d7d68eba6", "Relationships": [{"Type": "CHILD", "Ids": ["e113eb7a-d188-41dc-9414-5d39d52453fd"]}]}, {"BlockType": "LINE", "Confidence": 99.98345947265625, "Text": "Les Bourgeois Vyds", "Geometry": {"BoundingBox": {"Width": 0.19337746500968933, "Height": 0.013685888610780239, "Left": 0.023027095943689346, "Top": 0.191428542137146}, "Polygon": [{"X": 0.023027095943689346, "Y": 0.19142939150333405}, {"X": 0.2164044827222824, "Y": 0.191428542137146}, {"X": 0.21640455722808838, "Y": 0.20511358976364136}, {"X": 0.023027218878269196, "Y": 0.2051144242286682}]}, "Id": "20a8f77d-32a6-4544-b62e-a59c3fb51904", "Relationships": [{"Type": "CHILD", "Ids": ["3fa7b7ac-eb03-4212-a336-24c9e46a1caa", "346c5b30-a325-4b7a-8d90-9c72a93dedb7", "0d5a01fe-3ada-4cd3-b7eb-0e2b0816f77d"]}]}, {"BlockType": "LINE", "Confidence": 96.99198913574219, "Text": "M4 - MTGA WINES", "Geometry": {"BoundingBox": {"Width": 0.1627953052520752, "Height": 0.010824033990502357, "Left": 0.4672410190105438, "Top": 0.1917201280593872}, "Polygon": [{"X": 0.4672410190105438, "Y": 0.1917208433151245}, {"X": 0.6300363540649414, "Y": 0.1917201280593872}, {"X": 0.6300362944602966, "Y": 0.2025434523820877}, {"X": 0.4672410488128662, "Y": 0.202544167637825}]}, "Id": "986e69ae-7776-4fed-b6d9-b4cebe54a328", "Relationships": [{"Type": "CHILD", "Ids": ["22ccc005-ace3-44de-84bb-15f958a446ae", "3ce40175-bbbc-4bb4-bad2-5dc49fde0d06", "1f864860-27b6-4f52-b1a1-edd30ee88b4e"]}]}, {"BlockType": "LINE", "Confidence": 95.8779067993164, "Text": "12847 Hwy BB", "Geometry": {"BoundingBox": {"Width": 0.1281391680240631, "Height": 0.013377455994486809, "Left": 0.023428060114383698, "Top": 0.20805267989635468}, "Polygon": [{"X": 0.023428060114383698, "Y": 0.20805323123931885}, {"X": 0.15156714618206024, "Y": 0.20805267989635468}, {"X": 0.1515672355890274, "Y": 0.22142958641052246}, {"X": 0.023428181186318398, "Y": 0.22143013775348663}]}, "Id": "48bc37db-1911-475d-82cf-333eacdcacb4", "Relationships": [{"Type": "CHILD", "Ids": ["03290ae7-4da3-47ac-b7a4-6a500897aa63", "bd1be3c7-3c74-43af-8dc6-173aac5c9d8e", "7a9acb05-d32a-47ea-be44-dad6bad0e44f"]}]}, {"BlockType": "LINE", "Confidence": 99.9867172241211, "Text": "958 MCCORKLE AVE", "Geometry": {"BoundingBox": {"Width": 0.1725010722875595, "Height": 0.010764109902083874, "Left": 0.46796679496765137, "Top": 0.20807306468486786}, "Polygon": [{"X": 0.46796679496765137, "Y": 0.20807380974292755}, {"X": 0.6404678821563721, "Y": 0.20807306468486786}, {"X": 0.6404678225517273, "Y": 0.21883642673492432}, {"X": 0.46796679496765137, "Y": 0.218837171792984}]}, "Id": "3aa454b9-88f3-4b45-a21a-eba3c91a8b78", "Relationships": [{"Type": "CHILD", "Ids": ["ed413f2c-edd8-4ad0-82c7-aac4406eef8a", "ff95c692-3c7b-41f1-bdaf-e7bca03d36d3", "6c306668-24a4-41df-911c-66db75a2f71f"]}]}, {"BlockType": "LINE", "Confidence": 99.69440460205078, "Text": "Rocheport, MO 65279", "Geometry": {"BoundingBox": {"Width": 0.20549507439136505, "Height": 0.013476775959134102, "Left": 0.02246864326298237, "Top": 0.22425955533981323}, "Polygon": [{"X": 0.02246864326298237, "Y": 0.22426043450832367}, {"X": 0.2279636412858963, "Y": 0.22425955533981323}, {"X": 0.22796371579170227, "Y": 0.23773545026779175}, {"X": 0.022468766197562218, "Y": 0.23773632943630219}]}, "Id": "5da017dd-a894-4d9d-a2ce-0a2f255e37f2", "Relationships": [{"Type": "CHILD", "Ids": ["b5db4e27-d137-452c-95b7-28ef12f907c2", "e6def69d-6099-44f8-b972-a0b40c7082b0", "8cc1a0d4-017c-4f60-907f-aae755c72792"]}]}, {"BlockType": "LINE", "Confidence": 99.07600402832031, "Text": "SAINT HELENA, CA 94574-1329", "Geometry": {"BoundingBox": {"Width": 0.2921502888202667, "Height": 0.01313093677163124, "Left": 0.4680693447589874, "Top": 0.2244042158126831}, "Polygon": [{"X": 0.4680693447589874, "Y": 0.2244054675102234}, {"X": 0.7602196335792542, "Y": 0.2244042158126831}, {"X": 0.7602195739746094, "Y": 0.23753391206264496}, {"X": 0.4680693447589874, "Y": 0.23753514885902405}]}, "Id": "2aa372ef-667a-40ad-aaa4-52bcf3232253", "Relationships": [{"Type": "CHILD", "Ids": ["a2dd6890-8fa2-49c8-91a2-d06fe9d85fe5", "f4e10ca0-a567-4a36-abce-ac9aea583174", "a726bd2f-3581-4af3-9fd3-9d95c8b86e6a", "f3aecc7a-58dc-4355-b167-38239aede48a"]}]}, {"BlockType": "LINE", "Confidence": 98.25414276123047, "Text": "Case/Btl Part", "Geometry": {"BoundingBox": {"Width": 0.161314457654953, "Height": 0.01239768136292696, "Left": 0.02230059914290905, "Top": 0.256886750459671}, "Polygon": [{"X": 0.02230059914290905, "Y": 0.25688743591308594}, {"X": 0.18361498415470123, "Y": 0.256886750459671}, {"X": 0.1836150586605072, "Y": 0.2692837417125702}, {"X": 0.022300710901618004, "Y": 0.2692844271659851}]}, "Id": "ea491ccc-f5d2-4e80-99ef-5c7f5f44f0ef", "Relationships": [{"Type": "CHILD", "Ids": ["9ec7f143-d674-4688-b288-951b7fba8c03", "2819bfd1-7198-4609-ac17-144f1248983b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Description", "Geometry": {"BoundingBox": {"Width": 0.11902705579996109, "Height": 0.013807103037834167, "Left": 0.36976295709609985, "Top": 0.25681793689727783}, "Polygon": [{"X": 0.36976295709609985, "Y": 0.2568184435367584}, {"X": 0.48878997564315796, "Y": 0.25681793689727783}, {"X": 0.48879000544548035, "Y": 0.2706245481967926}, {"X": 0.36976298689842224, "Y": 0.2706250250339508}]}, "Id": "a20245cf-e4c1-41c1-a120-9f2dbb50057b", "Relationships": [{"Type": "CHILD", "Ids": ["3e2cfa61-2861-4ca9-80bf-9f817cc8bca5"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "Location", "Geometry": {"BoundingBox": {"Width": 0.08463159948587418, "Height": 0.010890088975429535, "Left": 0.7078172564506531, "Top": 0.25687527656555176}, "Polygon": [{"X": 0.7078172564506531, "Y": 0.2568756341934204}, {"X": 0.7924488186836243, "Y": 0.25687527656555176}, {"X": 0.7924488186836243, "Y": 0.26776501536369324}, {"X": 0.7078172564506531, "Y": 0.2677653729915619}]}, "Id": "390a4264-e375-4baa-b5a9-3dc97b7b6991", "Relationships": [{"Type": "CHILD", "Ids": ["b00acb25-1a73-4fa3-9719-21e3f9d7d125"]}]}, {"BlockType": "LINE", "Confidence": 99.73682403564453, "Text": "12/0000 M4LBBTCF22", "Geometry": {"BoundingBox": {"Width": 0.20383675396442413, "Height": 0.012336011976003647, "Left": 0.045364126563072205, "Top": 0.30618318915367126}, "Polygon": [{"X": 0.045364126563072205, "Y": 0.3061840236186981}, {"X": 0.24920082092285156, "Y": 0.30618318915367126}, {"X": 0.24920088052749634, "Y": 0.31851837038993835}, {"X": 0.04536423459649086, "Y": 0.3185192048549652}]}, "Id": "e8b245ff-8b7f-46fa-a47d-9b865f28a500", "Relationships": [{"Type": "CHILD", "Ids": ["8c690b2f-385e-45f8-ad63-13bf91fd7a20", "75265150-f2f5-48e6-aeb9-d647a449327f"]}]}, {"BlockType": "LINE", "Confidence": 99.998046875, "Text": "2022 MTGA WINES CABERNET FRANC", "Geometry": {"BoundingBox": {"Width": 0.32493337988853455, "Height": 0.010548846796154976, "Left": 0.37024548649787903, "Top": 0.30626553297042847}, "Polygon": [{"X": 0.37024548649787903, "Y": 0.3062668442726135}, {"X": 0.6951788663864136, "Y": 0.30626553297042847}, {"X": 0.6951788663864136, "Y": 0.31681305170059204}, {"X": 0.3702455163002014, "Y": 0.3168143630027771}]}, "Id": "c21864c0-d998-4129-ade7-88da52eaeb55", "Relationships": [{"Type": "CHILD", "Ids": ["77b97dc8-d81a-4936-bf2c-163078c0e9e2", "6b77892e-61d5-48ce-9a6a-df399c4b04cb", "15f0e24b-6972-4e37-b003-c4ecd7f335ab", "e02a1dca-fbef-4792-b743-a81ba58fe47e", "f4ae7fd3-399b-4689-b1c5-8e823a7b96e8"]}]}, {"BlockType": "LINE", "Confidence": 99.78605651855469, "Text": "De<PERSON><PERSON>:", "Geometry": {"BoundingBox": {"Width": 0.12607413530349731, "Height": 0.01109256036579609, "Left": 0.07674475014209747, "Top": 0.32222557067871094}, "Polygon": [{"X": 0.07674475014209747, "Y": 0.32222607731819153}, {"X": 0.20281881093978882, "Y": 0.32222557067871094}, {"X": 0.2028188854455948, "Y": 0.3333176374435425}, {"X": 0.07674483954906464, "Y": 0.33331814408302307}]}, "Id": "cfac937b-8c8b-4d79-aa68-63018ff63925", "Relationships": [{"Type": "CHILD", "Ids": ["e4e64713-a554-441c-9da8-486f26da9a1b", "eb8ba0e7-fcd5-477f-afea-bd35397acc29"]}]}, {"BlockType": "LINE", "Confidence": 99.13019561767578, "Text": "GB LES BOU BLUFF 12/750ML %14.5", "Geometry": {"BoundingBox": {"Width": 0.3360629975795746, "Height": 0.012421170249581337, "Left": 0.33702990412712097, "Top": 0.32246124744415283}, "Polygon": [{"X": 0.33702990412712097, "Y": 0.32246261835098267}, {"X": 0.6730929017066956, "Y": 0.32246124744415283}, {"X": 0.6730928421020508, "Y": 0.3348810374736786}, {"X": 0.33702996373176575, "Y": 0.3348824083805084}]}, "Id": "041d0123-0031-4c02-968c-a10f9f3bf641", "Relationships": [{"Type": "CHILD", "Ids": ["b8c09112-5047-458d-ae89-cf0e456d800e", "689df8c9-d39f-4bde-8cf9-96486745521f", "c973132d-9658-4b9f-84ee-1c4b64aac11a", "2430aff5-36df-4134-9bda-11217635bb30", "dfe61e89-163d-40d7-96ad-ead2eef5737d", "01f30ba5-ba10-4a6d-ae40-dbcce1d12e4c"]}]}, {"BlockType": "LINE", "Confidence": 99.59811401367188, "Text": "52/0000 M4LBBTCS22", "Geometry": {"BoundingBox": {"Width": 0.2055002748966217, "Height": 0.012448308058083057, "Left": 0.04395051300525665, "Top": 0.35553133487701416}, "Polygon": [{"X": 0.04395051300525665, "Y": 0.35553213953971863}, {"X": 0.24945072829723358, "Y": 0.35553133487701416}, {"X": 0.24945078790187836, "Y": 0.3679788410663605}, {"X": 0.04395062103867531, "Y": 0.36797964572906494}]}, "Id": "45d42771-7fb9-44c2-bee0-7a5b435c37a8", "Relationships": [{"Type": "CHILD", "Ids": ["26a9458a-8692-44ea-ba8a-8c0e508f4d63", "96c0d219-12cd-42e9-954a-c4e7e1741b2f"]}]}, {"BlockType": "LINE", "Confidence": 99.98616790771484, "Text": "2022 MTGA WINES CABERNET SAUV", "Geometry": {"BoundingBox": {"Width": 0.31433871388435364, "Height": 0.010575723834335804, "Left": 0.3702676296234131, "Top": 0.3556366264820099}, "Polygon": [{"X": 0.3702676296234131, "Y": 0.35563787817955017}, {"X": 0.6846063733100891, "Y": 0.3556366264820099}, {"X": 0.6846063137054443, "Y": 0.3662111163139343}, {"X": 0.3702676594257355, "Y": 0.3662123680114746}]}, "Id": "e0c06e39-1f4a-4cca-ade3-1ab7d0189100", "Relationships": [{"Type": "CHILD", "Ids": ["3af68cc4-dcc0-48ff-bf67-1b2fdc938032", "83aabcbb-d5a7-4923-83e0-6b2d12af0f9d", "a68f34ff-e603-4e73-8c74-2928a0d288dc", "d62c7fd1-8da7-40f3-aca2-6e99b1291ce2", "8689dd0e-90b3-4c67-94fe-f7625ed29473"]}]}, {"BlockType": "LINE", "Confidence": 99.80598449707031, "Text": "De<PERSON><PERSON>:", "Geometry": {"BoundingBox": {"Width": 0.12604789435863495, "Height": 0.010911344550549984, "Left": 0.07668861001729965, "Top": 0.371828556060791}, "Polygon": [{"X": 0.07668861001729965, "Y": 0.3718290627002716}, {"X": 0.20273643732070923, "Y": 0.371828556060791}, {"X": 0.202736496925354, "Y": 0.38273942470550537}, {"X": 0.07668869197368622, "Y": 0.3827399015426636}]}, "Id": "6cbebfef-6c87-4a70-b5ad-95ce2b6a49b2", "Relationships": [{"Type": "CHILD", "Ids": ["55c52b16-d6b4-4904-a0ae-3bb529beca46", "f70512fc-832e-47a4-b534-7705fb66f343"]}]}, {"BlockType": "LINE", "Confidence": 99.02151489257812, "Text": "GB LES BOU BLUFF 12/750ML %14.5", "Geometry": {"BoundingBox": {"Width": 0.3472193479537964, "Height": 0.012522312812507153, "Left": 0.3371385931968689, "Top": 0.3718987703323364}, "Polygon": [{"X": 0.3371385931968689, "Y": 0.37190014123916626}, {"X": 0.6843579411506653, "Y": 0.3718987703323364}, {"X": 0.6843579411506653, "Y": 0.384419709444046}, {"X": 0.33713865280151367, "Y": 0.38442108035087585}]}, "Id": "def649c0-c015-4009-8536-4677e834cd53", "Relationships": [{"Type": "CHILD", "Ids": ["ce977e6a-bab9-44ff-a14d-0e8b32258cb6", "a7751f9a-32bb-4229-8718-09c60d0ad472", "af60dbb4-fa32-4cf0-9621-79eaaed31b9b", "621619e8-8279-4ad1-a622-17917267b766", "06bc49a9-a333-4aec-ae59-0af39ecbd158", "e44527c2-e8cd-4e77-91c0-3c586982f47b"]}]}, {"BlockType": "LINE", "Confidence": 99.40489196777344, "Text": "36/0000 M4LBBTRB22", "Geometry": {"BoundingBox": {"Width": 0.20554529130458832, "Height": 0.012471308931708336, "Left": 0.04382907226681709, "Top": 0.40474817156791687}, "Polygon": [{"X": 0.04382907226681709, "Y": 0.40474897623062134}, {"X": 0.24937430024147034, "Y": 0.40474817156791687}, {"X": 0.2493743598461151, "Y": 0.4172186851501465}, {"X": 0.04382918030023575, "Y": 0.41721948981285095}]}, "Id": "66c79485-abf7-4881-ac2c-8c04ebb505d8", "Relationships": [{"Type": "CHILD", "Ids": ["80d9fd24-8faf-4e94-b1be-fa3599c48af8", "bb5137b0-0e7f-4087-a995-4a43379ad1eb"]}]}, {"BlockType": "LINE", "Confidence": 99.994140625, "Text": "2022 MTGA WINES RED BLEND", "Geometry": {"BoundingBox": {"Width": 0.2706788182258606, "Height": 0.010619270615279675, "Left": 0.37027186155319214, "Top": 0.40467146039009094}, "Polygon": [{"X": 0.37027186155319214, "Y": 0.4046725034713745}, {"X": 0.6409506797790527, "Y": 0.40467146039009094}, {"X": 0.6409506797790527, "Y": 0.41528967022895813}, {"X": 0.3702719211578369, "Y": 0.4152907133102417}]}, "Id": "0e060a64-cd76-47a6-a7bf-43378e15d4d8", "Relationships": [{"Type": "CHILD", "Ids": ["1a29c43c-9aae-4d0a-99e0-76154b53e16a", "02282901-c2f5-43f9-90dc-d389db031084", "003da69c-accc-4a44-95b3-45c8f05a8838", "9824a6fd-1a93-4897-a349-a5cd2166a53c", "9ff1c7a5-e816-4275-9c82-668a2256e58f"]}]}, {"BlockType": "LINE", "Confidence": 99.76123809814453, "Text": "De<PERSON><PERSON>:", "Geometry": {"BoundingBox": {"Width": 0.1259976327419281, "Height": 0.01100279949605465, "Left": 0.07669971883296967, "Top": 0.4208570718765259}, "Polygon": [{"X": 0.07669971883296967, "Y": 0.4208575487136841}, {"X": 0.202697291970253, "Y": 0.4208570718765259}, {"X": 0.20269735157489777, "Y": 0.4318593740463257}, {"X": 0.07669980823993683, "Y": 0.4318598508834839}]}, "Id": "47446618-7e6b-4395-bbdc-c189637c6ed0", "Relationships": [{"Type": "CHILD", "Ids": ["686fd4df-55d8-4028-b575-19db48c9f7dc", "2dcfdfef-9614-4693-96e5-73cdb10719b2"]}]}, {"BlockType": "LINE", "Confidence": 99.3506851196289, "Text": "GB LES BOU BLUFF 12/750ML %14.5", "Geometry": {"BoundingBox": {"Width": 0.34685927629470825, "Height": 0.012578682973980904, "Left": 0.3371434509754181, "Top": 0.4210852086544037}, "Polygon": [{"X": 0.3371434509754181, "Y": 0.42108654975891113}, {"X": 0.6840027570724487, "Y": 0.4210852086544037}, {"X": 0.684002697467804, "Y": 0.4336625635623932}, {"X": 0.33714351058006287, "Y": 0.43366390466690063}]}, "Id": "270a1327-0c82-4e37-83b4-739227318f62", "Relationships": [{"Type": "CHILD", "Ids": ["152fd192-b7a0-4063-be43-e307c038e4f9", "521314a7-4bc0-42b2-9512-ec4c60b32591", "369eb5a0-f6a0-4374-8bc1-294e09ce6658", "aee242a3-b2ba-4c16-bc33-f99f221b6a17", "30d13ee6-60b8-4a48-ac47-61016e5310a6", "b3399ff4-f724-4703-a957-0e29e62cf749"]}]}, {"BlockType": "LINE", "Confidence": 99.96542358398438, "Text": "100 CASES", "Geometry": {"BoundingBox": {"Width": 0.10676116496324539, "Height": 0.010476085357367992, "Left": 0.08864060789346695, "Top": 0.4701801836490631}, "Polygon": [{"X": 0.08864060789346695, "Y": 0.47018060088157654}, {"X": 0.19540171325206757, "Y": 0.4701801836490631}, {"X": 0.19540177285671234, "Y": 0.48065587878227234}, {"X": 0.08864068984985352, "Y": 0.4806562662124634}]}, "Id": "486e3216-712e-424d-a05c-e77275c6eb72", "Relationships": [{"Type": "CHILD", "Ids": ["14f926b6-d7dc-4f7e-8f24-cd0ee07f8cd9", "e50f25c8-5e56-4b4f-86fb-877151c8dd41"]}]}, {"BlockType": "LINE", "Confidence": 99.921875, "Text": "3,600 POUNDS", "Geometry": {"BoundingBox": {"Width": 0.14031796157360077, "Height": 0.012925173155963421, "Left": 0.06559953838586807, "Top": 0.4866381287574768}, "Polygon": [{"X": 0.06559953838586807, "Y": 0.4866386353969574}, {"X": 0.20591741800308228, "Y": 0.4866381287574768}, {"X": 0.20591750741004944, "Y": 0.4995627701282501}, {"X": 0.06559964269399643, "Y": 0.4995633065700531}]}, "Id": "26546164-0601-4335-8c6d-3763818388a9", "Relationships": [{"Type": "CHILD", "Ids": ["bb33f82e-33cf-4898-a8b9-a3a1bdc738c9", "b3157a67-c4a4-4582-badf-0b05d672f688"]}]}, {"BlockType": "LINE", "Confidence": 99.7998046875, "Text": "1.78 PALLETS", "Geometry": {"BoundingBox": {"Width": 0.1395300030708313, "Height": 0.010655657388269901, "Left": 0.07768874615430832, "Top": 0.5029380917549133}, "Polygon": [{"X": 0.07768874615430832, "Y": 0.5029386281967163}, {"X": 0.21721869707107544, "Y": 0.5029380917549133}, {"X": 0.21721875667572021, "Y": 0.5135932564735413}, {"X": 0.07768883556127548, "Y": 0.5135937929153442}]}, "Id": "2d53a9b0-6556-496b-bd57-c77bbd1288b5", "Relationships": [{"Type": "CHILD", "Ids": ["f9135c15-ae5d-4e11-93bf-e3476e032f61", "cb91e40d-ca3f-4977-b6a1-bfa12d68a534"]}]}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "ORDER", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09711839258670807, "Height": 0.011006438173353672, "Left": 0.3046632707118988, "Top": 0.011294635944068432}, "Polygon": [{"X": 0.3046632707118988, "Y": 0.01129509974271059}, {"X": 0.4017816483974457, "Y": 0.011294635944068432}, {"X": 0.40178167819976807, "Y": 0.022300612181425095}, {"X": 0.3046633303165436, "Y": 0.022301074117422104}]}, "Id": "4deae6cd-2689-42d5-b25f-6e2391a8969e"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "CONFIRMATION", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.25030848383903503, "Height": 0.010952486656606197, "Left": 0.4341104328632355, "Top": 0.011260759085416794}, "Polygon": [{"X": 0.4341104328632355, "Y": 0.0112619549036026}, {"X": 0.6844189167022705, "Y": 0.011260759085416794}, {"X": 0.6844189167022705, "Y": 0.022212056443095207}, {"X": 0.43411046266555786, "Y": 0.022213244810700417}]}, "Id": "189cbb29-c54f-43de-8eb2-a890ffa92d52"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "From", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04238623008131981, "Height": 0.0106004998087883, "Left": 0.02286210097372532, "Top": 0.04401431977748871}, "Polygon": [{"X": 0.02286210097372532, "Y": 0.044014520943164825}, {"X": 0.06524824351072311, "Y": 0.04401431977748871}, {"X": 0.06524833291769028, "Y": 0.05461462214589119}, {"X": 0.02286219783127308, "Y": 0.05461481958627701}]}, "Id": "533e9752-33ad-4f85-a7c9-f15ad305316b"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "GROSKOPF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08603650331497192, "Height": 0.010570332407951355, "Left": 0.2934810519218445, "Top": 0.04407864436507225}, "Polygon": [{"X": 0.2934810519218445, "Y": 0.044079046696424484}, {"X": 0.379517525434494, "Y": 0.04407864436507225}, {"X": 0.3795175552368164, "Y": 0.05464857444167137}, {"X": 0.29348111152648926, "Y": 0.054648976773023605}]}, "Id": "623ec845-864e-4d99-bd3f-d20e8e9a3a41"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WAREHOUSE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0966741144657135, "Height": 0.010524777695536613, "Left": 0.3915005922317505, "Top": 0.04415297880768776}, "Polygon": [{"X": 0.3915005922317505, "Y": 0.04415343329310417}, {"X": 0.488174706697464, "Y": 0.04415297880768776}, {"X": 0.488174706697464, "Y": 0.05467730388045311}, {"X": 0.3915006220340729, "Y": 0.054677754640579224}]}, "Id": "76a37f64-713d-4ca1-abb5-7e07924b65cf"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Our", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03154822811484337, "Height": 0.010413103736937046, "Left": 0.5438161492347717, "Top": 0.07687549293041229}, "Polygon": [{"X": 0.5438161492347717, "Y": 0.07687564194202423}, {"X": 0.575364351272583, "Y": 0.07687549293041229}, {"X": 0.575364351272583, "Y": 0.08728845417499542}, {"X": 0.5438161492347717, "Y": 0.08728859573602676}]}, "Id": "02e4f3c4-21a3-419e-af2d-e2e68e937513"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "order#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06462794542312622, "Height": 0.010942882858216763, "Left": 0.5874240398406982, "Top": 0.07638475298881531}, "Polygon": [{"X": 0.5874240398406982, "Y": 0.07638505101203918}, {"X": 0.6520519852638245, "Y": 0.07638475298881531}, {"X": 0.6520519256591797, "Y": 0.08732733875513077}, {"X": 0.5874240398406982, "Y": 0.08732763677835464}]}, "Id": "9acbc01c-99ef-4e99-84e5-25021f0fc9e3"}, {"BlockType": "WORD", "Confidence": 99.716796875, "Text": "733380.0", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08552885055541992, "Height": 0.01045298483222723, "Left": 0.7503993511199951, "Top": 0.07698287814855576}, "Polygon": [{"X": 0.7503994107246399, "Y": 0.07698327302932739}, {"X": 0.835928201675415, "Y": 0.07698287814855576}, {"X": 0.8359281420707703, "Y": 0.08743546903133392}, {"X": 0.7503993511199951, "Y": 0.08743586391210556}]}, "Id": "385ab6e7-5950-4389-8d92-d70ea7e38690"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Winery", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06442717462778091, "Height": 0.013627036474645138, "Left": 0.5437501072883606, "Top": 0.0929863229393959}, "Polygon": [{"X": 0.5437501072883606, "Y": 0.09298661351203918}, {"X": 0.6081773042678833, "Y": 0.0929863229393959}, {"X": 0.6081773042678833, "Y": 0.10661306232213974}, {"X": 0.5437501072883606, "Y": 0.10661335289478302}]}, "Id": "942f1681-da97-4d8f-bdd9-3e9324b3a45b"}, {"BlockType": "WORD", "Confidence": 99.912109375, "Text": "order#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06492092460393906, "Height": 0.010980437509715557, "Left": 0.6200700402259827, "Top": 0.09275916963815689}, "Polygon": [{"X": 0.6200700402259827, "Y": 0.09275946766138077}, {"X": 0.6849909424781799, "Y": 0.09275916963815689}, {"X": 0.6849909424781799, "Y": 0.10373931378126144}, {"X": 0.6200700402259827, "Y": 0.10373960435390472}]}, "Id": "6d594802-d53d-4d5f-a993-2ad0135bf4ad"}, {"BlockType": "WORD", "Confidence": 85.99858093261719, "Text": ".....", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04609755426645279, "Height": 0.0025529044214636087, "Left": 0.6892065405845642, "Top": 0.10096529871225357}, "Polygon": [{"X": 0.6892065405845642, "Y": 0.10096550732851028}, {"X": 0.735304057598114, "Y": 0.10096529871225357}, {"X": 0.735304057598114, "Y": 0.10351799428462982}, {"X": 0.6892065405845642, "Y": 0.10351820290088654}]}, "Id": "446c2043-bb1e-450d-b011-de45141ec221"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "LBV21125", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0851137712597847, "Height": 0.010532609187066555, "Left": 0.751197338104248, "Top": 0.09331311285495758}, "Polygon": [{"X": 0.751197338104248, "Y": 0.09331350773572922}, {"X": 0.8363111019134521, "Y": 0.09331311285495758}, {"X": 0.8363110423088074, "Y": 0.10384533554315567}, {"X": 0.751197338104248, "Y": 0.10384572297334671}]}, "Id": "30fe5f5e-3118-4ad1-a6ee-0c5d28dc8bbe"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Our", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03136809170246124, "Height": 0.010439668782055378, "Left": 0.022112105041742325, "Top": 0.10963744670152664}, "Polygon": [{"X": 0.022112105041742325, "Y": 0.10963758826255798}, {"X": 0.053480107337236404, "Y": 0.10963744670152664}, {"X": 0.05348019674420357, "Y": 0.1200769767165184}, {"X": 0.022112200036644936, "Y": 0.12007711827754974}]}, "Id": "dc6399ea-f8f9-4e64-a849-0b6941f42295"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052772603929042816, "Height": 0.010969722643494606, "Left": 0.06590699404478073, "Top": 0.10917140543460846}, "Polygon": [{"X": 0.06590699404478073, "Y": 0.10917164385318756}, {"X": 0.11867951601743698, "Y": 0.10917140543460846}, {"X": 0.11867959797382355, "Y": 0.12014088779687881}, {"X": 0.0659070834517479, "Y": 0.12014112621545792}]}, "Id": "7e5109db-29a8-4fa3-8e7c-036d5429f538"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04202261194586754, "Height": 0.010902066715061665, "Left": 0.13099589943885803, "Top": 0.10919783264398575}, "Polygon": [{"X": 0.13099589943885803, "Y": 0.10919802635908127}, {"X": 0.1730184406042099, "Y": 0.10919783264398575}, {"X": 0.17301851511001587, "Y": 0.12009970843791962}, {"X": 0.1309959888458252, "Y": 0.12009990215301514}]}, "Id": "3569a204-288a-48b8-85c4-b850e91a28da"}, {"BlockType": "WORD", "Confidence": 99.65262603759766, "Text": "02/06/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08488275855779648, "Height": 0.012565511278808117, "Left": 0.2948756515979767, "Top": 0.10937362909317017}, "Polygon": [{"X": 0.2948756515979767, "Y": 0.1093740165233612}, {"X": 0.379758358001709, "Y": 0.10937362909317017}, {"X": 0.37975838780403137, "Y": 0.12193875759840012}, {"X": 0.2948756814002991, "Y": 0.12193914502859116}]}, "Id": "7f1a3afc-2c7d-4358-8657-7d52437839f6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Requested", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09615769237279892, "Height": 0.013530820608139038, "Left": 0.022566068917512894, "Top": 0.12599654495716095}, "Polygon": [{"X": 0.022566068917512894, "Y": 0.12599699199199677}, {"X": 0.11872366070747375, "Y": 0.12599654495716095}, {"X": 0.11872375756502151, "Y": 0.13952693343162537}, {"X": 0.022566189989447594, "Y": 0.1395273655653}]}, "Id": "33b0cef5-f55c-49e5-ab55-850bcc63de6c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04179380461573601, "Height": 0.013595743104815483, "Left": 0.1315508484840393, "Top": 0.12595443427562714}, "Polygon": [{"X": 0.1315508484840393, "Y": 0.12595462799072266}, {"X": 0.17334456741809845, "Y": 0.12595443427562714}, {"X": 0.1733446568250656, "Y": 0.13955000042915344}, {"X": 0.13155095279216766, "Y": 0.13955017924308777}]}, "Id": "36bce3f2-c9a5-400a-bff2-62427cdef970"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04213854670524597, "Height": 0.010886924341320992, "Left": 0.18532536923885345, "Top": 0.12589521706104279}, "Polygon": [{"X": 0.18532536923885345, "Y": 0.1258954107761383}, {"X": 0.22746385633945465, "Y": 0.12589521706104279}, {"X": 0.22746391594409943, "Y": 0.1367819607257843}, {"X": 0.18532542884349823, "Y": 0.13678213953971863}]}, "Id": "f17de788-9b8c-4221-8414-e4d02a55d672"}, {"BlockType": "WORD", "Confidence": 99.72417449951172, "Text": "02/11/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08506634086370468, "Height": 0.012250536121428013, "Left": 0.2947002053260803, "Top": 0.12625855207443237}, "Polygon": [{"X": 0.2947002053260803, "Y": 0.1262589395046234}, {"X": 0.3797664940357208, "Y": 0.12625855207443237}, {"X": 0.3797665238380432, "Y": 0.13850870728492737}, {"X": 0.2947002351284027, "Y": 0.13850907981395721}]}, "Id": "12d837c9-769d-4618-b993-d723009060e2"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Ship", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042231034487485886, "Height": 0.013731020502746105, "Left": 0.022297630086541176, "Top": 0.1422612965106964}, "Polygon": [{"X": 0.022297630086541176, "Y": 0.14226147532463074}, {"X": 0.06452854722738266, "Y": 0.1422612965106964}, {"X": 0.06452866643667221, "Y": 0.15599212050437927}, {"X": 0.022297753021121025, "Y": 0.1559923142194748}]}, "Id": "7b96f075-7f97-4b5f-a30f-7c01e06b5e23"}, {"BlockType": "WORD", "Confidence": 99.80149841308594, "Text": "via", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031205326318740845, "Height": 0.01085532084107399, "Left": 0.07672020047903061, "Top": 0.14224711060523987}, "Polygon": [{"X": 0.07672020047903061, "Y": 0.1422472447156906}, {"X": 0.10792544484138489, "Y": 0.14224711060523987}, {"X": 0.10792552679777145, "Y": 0.15310229361057281}, {"X": 0.07672028988599777, "Y": 0.15310242772102356}]}, "Id": "138caf0a-a985-4565-acb6-8635d830ef23"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "<PERSON><PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07474914938211441, "Height": 0.010898822918534279, "Left": 0.29372793436050415, "Top": 0.14225712418556213}, "Polygon": [{"X": 0.29372793436050415, "Y": 0.1422574520111084}, {"X": 0.3684770464897156, "Y": 0.14225712418556213}, {"X": 0.36847707629203796, "Y": 0.1531556099653244}, {"X": 0.29372796416282654, "Y": 0.15315593779087067}]}, "Id": "d5f4833d-a8a0-464f-8b30-64ea5692166e"}, {"BlockType": "WORD", "Confidence": 99.5703125, "Text": "&", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010979847982525826, "Height": 0.010541350580751896, "Left": 0.3806999623775482, "Top": 0.14264734089374542}, "Polygon": [{"X": 0.3806999623775482, "Y": 0.1426474004983902}, {"X": 0.3916797935962677, "Y": 0.14264734089374542}, {"X": 0.3916798233985901, "Y": 0.15318864583969116}, {"X": 0.3806999921798706, "Y": 0.15318869054317474}]}, "Id": "8154b9a6-3554-4671-97f8-fe3dc33d4ffd"}, {"BlockType": "WORD", "Confidence": 99.88042449951172, "Text": "Associates", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10806254297494888, "Height": 0.010838847607374191, "Left": 0.4015488028526306, "Top": 0.142363041639328}, "Polygon": [{"X": 0.4015488028526306, "Y": 0.1423635184764862}, {"X": 0.5096113681793213, "Y": 0.142363041639328}, {"X": 0.5096113681793213, "Y": 0.1532014012336731}, {"X": 0.401548832654953, "Y": 0.1532018929719925}]}, "Id": "494f38c1-a1e5-4778-93cc-2b2e85de25a1"}, {"BlockType": "WORD", "Confidence": 58.1646614074707, "Text": "SHIP - TO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07550638914108276, "Height": 0.010819684714078903, "Left": 0.1744898110628128, "Top": 0.17518393695354462}, "Polygon": [{"X": 0.1744898110628128, "Y": 0.17518426477909088}, {"X": 0.2499961405992508, "Y": 0.17518393695354462}, {"X": 0.24999620020389557, "Y": 0.18600328266620636}, {"X": 0.17448988556861877, "Y": 0.18600362539291382}]}, "Id": "887992a8-9916-48f2-8fc9-ca9a79bc89ab"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WINERY", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06498411297798157, "Height": 0.010651880875229836, "Left": 0.6306143403053284, "Top": 0.17541582882404327}, "Polygon": [{"X": 0.6306143403053284, "Y": 0.17541611194610596}, {"X": 0.6955984830856323, "Y": 0.17541582882404327}, {"X": 0.6955984234809875, "Y": 0.18606741726398468}, {"X": 0.6306143403053284, "Y": 0.18606770038604736}]}, "Id": "e113eb7a-d188-41dc-9414-5d39d52453fd"}, {"BlockType": "WORD", "Confidence": 99.95037078857422, "Text": "Les", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030343400314450264, "Height": 0.010542608797550201, "Left": 0.023027099668979645, "Top": 0.19174166023731232}, "Polygon": [{"X": 0.023027099668979645, "Y": 0.19174179434776306}, {"X": 0.053370408713817596, "Y": 0.19174166023731232}, {"X": 0.05337049812078476, "Y": 0.20228414237499237}, {"X": 0.023027194663882256, "Y": 0.20228427648544312}]}, "Id": "3fa7b7ac-eb03-4212-a336-24c9e46a1caa"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Bo<PERSON>ois", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.09619460999965668, "Height": 0.013582027517259121, "Left": 0.0656602755188942, "Top": 0.1915322244167328}, "Polygon": [{"X": 0.0656602755188942, "Y": 0.19153264164924622}, {"X": 0.1618547886610031, "Y": 0.1915322244167328}, {"X": 0.16185489296913147, "Y": 0.20511382818222046}, {"X": 0.06566038727760315, "Y": 0.2051142454147339}]}, "Id": "346c5b30-a325-4b7a-8d90-9c72a93dedb7"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042565569281578064, "Height": 0.013635765761137009, "Left": 0.17383898794651031, "Top": 0.191428542137146}, "Polygon": [{"X": 0.17383898794651031, "Y": 0.19142872095108032}, {"X": 0.2164044827222824, "Y": 0.191428542137146}, {"X": 0.21640455722808838, "Y": 0.20506411790847778}, {"X": 0.17383907735347748, "Y": 0.2050643116235733}]}, "Id": "0d5a01fe-3ada-4cd3-b7eb-0e2b0816f77d"}, {"BlockType": "WORD", "Confidence": 90.97596740722656, "Text": "M4 -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04133663326501846, "Height": 0.010707749053835869, "Left": 0.4672410190105438, "Top": 0.191836416721344}, "Polygon": [{"X": 0.4672410190105438, "Y": 0.19183659553527832}, {"X": 0.5085776448249817, "Y": 0.191836416721344}, {"X": 0.5085776448249817, "Y": 0.20254398882389069}, {"X": 0.4672410488128662, "Y": 0.202544167637825}]}, "Id": "22ccc005-ace3-44de-84bb-15f958a446ae"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MTGA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.044008202850818634, "Height": 0.010358341038227081, "Left": 0.521719753742218, "Top": 0.19181159138679504}, "Polygon": [{"X": 0.521719753742218, "Y": 0.19181178510189056}, {"X": 0.565727949142456, "Y": 0.19181159138679504}, {"X": 0.565727949142456, "Y": 0.2021697461605072}, {"X": 0.521719753742218, "Y": 0.20216993987560272}]}, "Id": "3ce40175-bbbc-4bb4-bad2-5dc49fde0d06"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WINES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05378499627113342, "Height": 0.010606586933135986, "Left": 0.5762513279914856, "Top": 0.1917201280593872}, "Polygon": [{"X": 0.5762513279914856, "Y": 0.1917203664779663}, {"X": 0.6300363540649414, "Y": 0.1917201280593872}, {"X": 0.6300362944602966, "Y": 0.20232649147510529}, {"X": 0.5762513279914856, "Y": 0.2023267149925232}]}, "Id": "1f864860-27b6-4f52-b1a1-edd30ee88b4e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "12847", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.052209336310625076, "Height": 0.010806288570165634, "Left": 0.023428060114383698, "Top": 0.20805300772190094}, "Polygon": [{"X": 0.023428060114383698, "Y": 0.20805323123931885}, {"X": 0.07563731074333191, "Y": 0.20805300772190094}, {"X": 0.07563739269971848, "Y": 0.21885907649993896}, {"X": 0.023428158834576607, "Y": 0.21885930001735687}]}, "Id": "03290ae7-4da3-47ac-b7a4-6a500897aa63"}, {"BlockType": "WORD", "Confidence": 89.35746002197266, "Text": "Hwy", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030925383791327477, "Height": 0.013180279172956944, "Left": 0.08778030425310135, "Top": 0.20824958384037018}, "Polygon": [{"X": 0.08778030425310135, "Y": 0.20824971795082092}, {"X": 0.11870559304952621, "Y": 0.20824958384037018}, {"X": 0.11870568990707397, "Y": 0.2214297205209732}, {"X": 0.0877804085612297, "Y": 0.22142985463142395}]}, "Id": "bd1be3c7-3c74-43af-8dc6-173aac5c9d8e"}, {"BlockType": "WORD", "Confidence": 98.2860336303711, "Text": "BB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.020612340420484543, "Height": 0.010509176179766655, "Left": 0.13095487654209137, "Top": 0.20816907286643982}, "Polygon": [{"X": 0.13095487654209137, "Y": 0.2081691473722458}, {"X": 0.15156714618206024, "Y": 0.20816907286643982}, {"X": 0.1515672206878662, "Y": 0.21867814660072327}, {"X": 0.13095495104789734, "Y": 0.21867823600769043}]}, "Id": "7a9acb05-d32a-47ea-be44-dad6bad0e44f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "958", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03153477609157562, "Height": 0.010656743310391903, "Left": 0.46796679496765137, "Top": 0.20818042755126953}, "Polygon": [{"X": 0.46796679496765137, "Y": 0.20818056166172028}, {"X": 0.4995015561580658, "Y": 0.20818042755126953}, {"X": 0.4995015561580658, "Y": 0.21883703768253326}, {"X": 0.46796679496765137, "Y": 0.218837171792984}]}, "Id": "ed413f2c-edd8-4ad0-82c7-aac4406eef8a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "MCCORKLE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08625959604978561, "Height": 0.010661439970135689, "Left": 0.5110337734222412, "Top": 0.20807324349880219}, "Polygon": [{"X": 0.5110337734222412, "Y": 0.20807361602783203}, {"X": 0.5972933769226074, "Y": 0.20807324349880219}, {"X": 0.5972933769226074, "Y": 0.21873430907726288}, {"X": 0.511033833026886, "Y": 0.21873468160629272}]}, "Id": "ff95c692-3c7b-41f1-bdaf-e7bca03d36d3"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "AVE", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0318334698677063, "Height": 0.010555732995271683, "Left": 0.6086344122886658, "Top": 0.20812535285949707}, "Polygon": [{"X": 0.6086344122886658, "Y": 0.208125501871109}, {"X": 0.6404678821563721, "Y": 0.20812535285949707}, {"X": 0.6404678225517273, "Y": 0.2186809480190277}, {"X": 0.6086344122886658, "Y": 0.21868109703063965}]}, "Id": "6c306668-24a4-41df-911c-66db75a2f71f"}, {"BlockType": "WORD", "Confidence": 99.64126586914062, "Text": "Rocheport,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10454972833395004, "Height": 0.013476342894136906, "Left": 0.02246864326298237, "Top": 0.22425998747348785}, "Polygon": [{"X": 0.02246864326298237, "Y": 0.22426043450832367}, {"X": 0.1270182728767395, "Y": 0.22425998747348785}, {"X": 0.12701836228370667, "Y": 0.23773588240146637}, {"X": 0.022468766197562218, "Y": 0.23773632943630219}]}, "Id": "b5db4e27-d137-452c-95b7-28ef12f907c2"}, {"BlockType": "WORD", "Confidence": 99.44196319580078, "Text": "MO", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021412702277302742, "Height": 0.010410783812403679, "Left": 0.14149537682533264, "Top": 0.22442860901355743}, "Polygon": [{"X": 0.14149537682533264, "Y": 0.2244286984205246}, {"X": 0.16290800273418427, "Y": 0.22442860901355743}, {"X": 0.16290807723999023, "Y": 0.2348392903804779}, {"X": 0.1414954513311386, "Y": 0.23483937978744507}]}, "Id": "e6def69d-6099-44f8-b972-a0b40c7082b0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "65279", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05358535051345825, "Height": 0.010638236999511719, "Left": 0.17437835037708282, "Top": 0.22447259724140167}, "Polygon": [{"X": 0.17437835037708282, "Y": 0.22447283565998077}, {"X": 0.2279636412858963, "Y": 0.22447259724140167}, {"X": 0.22796370089054108, "Y": 0.23511061072349548}, {"X": 0.1743784248828888, "Y": 0.2351108342409134}]}, "Id": "8cc1a0d4-017c-4f60-907f-aae755c72792"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SAINT", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05362704396247864, "Height": 0.010463900864124298, "Left": 0.4680693447589874, "Top": 0.2244681715965271}, "Polygon": [{"X": 0.4680693447589874, "Y": 0.224468395113945}, {"X": 0.5216963887214661, "Y": 0.2244681715965271}, {"X": 0.5216963887214661, "Y": 0.2349318414926529}, {"X": 0.4680693447589874, "Y": 0.2349320650100708}]}, "Id": "a2dd6890-8fa2-49c8-91a2-d06fe9d85fe5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "HELENA,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07168611884117126, "Height": 0.01312999241054058, "Left": 0.5333389639854431, "Top": 0.22440488636493683}, "Polygon": [{"X": 0.5333389639854431, "Y": 0.2244051843881607}, {"X": 0.605025053024292, "Y": 0.22440488636493683}, {"X": 0.605025053024292, "Y": 0.2375345677137375}, {"X": 0.5333389639854431, "Y": 0.23753488063812256}]}, "Id": "f4e10ca0-a567-4a36-abce-ac9aea583174"}, {"BlockType": "WORD", "Confidence": 99.83079528808594, "Text": "CA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02175949327647686, "Height": 0.010516087524592876, "Left": 0.6199764609336853, "Top": 0.22444947063922882}, "Polygon": [{"X": 0.6199764609336853, "Y": 0.22444956004619598}, {"X": 0.6417359709739685, "Y": 0.22444947063922882}, {"X": 0.6417359709739685, "Y": 0.2349654585123062}, {"X": 0.6199764609336853, "Y": 0.23496556282043457}]}, "Id": "a726bd2f-3581-4af3-9fd3-9d95c8b86e6a"}, {"BlockType": "WORD", "Confidence": 96.55293273925781, "Text": "94574-1329", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10741162300109863, "Height": 0.010374968871474266, "Left": 0.6528080105781555, "Top": 0.22468042373657227}, "Polygon": [{"X": 0.6528080105781555, "Y": 0.22468088567256927}, {"X": 0.7602196335792542, "Y": 0.22468042373657227}, {"X": 0.7602195739746094, "Y": 0.23505493998527527}, {"X": 0.6528080105781555, "Y": 0.23505540192127228}]}, "Id": "f3aecc7a-58dc-4355-b167-38239aede48a"}, {"BlockType": "WORD", "Confidence": 96.50828552246094, "Text": "Case/Btl", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0854765847325325, "Height": 0.012397361919283867, "Left": 0.02230059914290905, "Top": 0.2568870782852173}, "Polygon": [{"X": 0.02230059914290905, "Y": 0.25688743591308594}, {"X": 0.10777708888053894, "Y": 0.2568870782852173}, {"X": 0.1077771857380867, "Y": 0.26928406953811646}, {"X": 0.022300710901618004, "Y": 0.2692844271659851}]}, "Id": "9ec7f143-d674-4688-b288-951b7fba8c03"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Part", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0412776805460453, "Height": 0.010561053641140461, "Left": 0.142337366938591, "Top": 0.257302463054657}, "Polygon": [{"X": 0.142337366938591, "Y": 0.2573026418685913}, {"X": 0.18361498415470123, "Y": 0.257302463054657}, {"X": 0.1836150586605072, "Y": 0.26786336302757263}, {"X": 0.14233744144439697, "Y": 0.26786351203918457}]}, "Id": "2819bfd1-7198-4609-ac17-144f1248983b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Description", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.11902705579996109, "Height": 0.013807103037834167, "Left": 0.36976295709609985, "Top": 0.25681793689727783}, "Polygon": [{"X": 0.36976295709609985, "Y": 0.2568184435367584}, {"X": 0.48878997564315796, "Y": 0.25681793689727783}, {"X": 0.48879000544548035, "Y": 0.2706245481967926}, {"X": 0.36976298689842224, "Y": 0.2706250250339508}]}, "Id": "3e2cfa61-2861-4ca9-80bf-9f817cc8bca5"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Location", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08463159948587418, "Height": 0.010890088975429535, "Left": 0.7078172564506531, "Top": 0.25687527656555176}, "Polygon": [{"X": 0.7078172564506531, "Y": 0.2568756341934204}, {"X": 0.7924488186836243, "Y": 0.25687527656555176}, {"X": 0.7924488186836243, "Y": 0.26776501536369324}, {"X": 0.7078172564506531, "Y": 0.2677653729915619}]}, "Id": "b00acb25-1a73-4fa3-9719-21e3f9d7d125"}, {"BlockType": "WORD", "Confidence": 99.51351165771484, "Text": "12/0000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07293270528316498, "Height": 0.012335474602878094, "Left": 0.045364126563072205, "Top": 0.30618372559547424}, "Polygon": [{"X": 0.045364126563072205, "Y": 0.3061840236186981}, {"X": 0.11829674243927002, "Y": 0.30618372559547424}, {"X": 0.11829683184623718, "Y": 0.31851890683174133}, {"X": 0.04536423459649086, "Y": 0.3185192048549652}]}, "Id": "8c690b2f-385e-45f8-ad63-13bf91fd7a20"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "M4LBBTCF22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10764580219984055, "Height": 0.010602976195514202, "Left": 0.1415550708770752, "Top": 0.30630919337272644}, "Polygon": [{"X": 0.1415550708770752, "Y": 0.30630964040756226}, {"X": 0.24920082092285156, "Y": 0.30630919337272644}, {"X": 0.24920088052749634, "Y": 0.3169117569923401}, {"X": 0.14155514538288116, "Y": 0.3169121742248535}]}, "Id": "75265150-f2f5-48e6-aeb9-d647a449327f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "2022", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04221194237470627, "Height": 0.010285316035151482, "Left": 0.37024548649787903, "Top": 0.3064958155155182}, "Polygon": [{"X": 0.37024548649787903, "Y": 0.3064959943294525}, {"X": 0.4124574065208435, "Y": 0.3064958155155182}, {"X": 0.4124574363231659, "Y": 0.3167809545993805}, {"X": 0.3702455163002014, "Y": 0.3167811334133148}]}, "Id": "77b97dc8-d81a-4936-bf2c-163078c0e9e2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MTGA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04360552132129669, "Height": 0.010531936772167683, "Left": 0.42411601543426514, "Top": 0.3062664568424225}, "Polygon": [{"X": 0.42411601543426514, "Y": 0.3062666356563568}, {"X": 0.46772152185440063, "Y": 0.3062664568424225}, {"X": 0.467721551656723, "Y": 0.31679821014404297}, {"X": 0.4241160452365875, "Y": 0.3167983889579773}]}, "Id": "6b77892e-61d5-48ce-9a6a-df399c4b04cb"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WINES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05365057289600372, "Height": 0.01043833326548338, "Left": 0.47854259610176086, "Top": 0.30636072158813477}, "Polygon": [{"X": 0.47854259610176086, "Y": 0.3063609302043915}, {"X": 0.5321931838989258, "Y": 0.30636072158813477}, {"X": 0.5321931838989258, "Y": 0.3167988359928131}, {"X": 0.47854262590408325, "Y": 0.3167990446090698}]}, "Id": "15f0e24b-6972-4e37-b003-c4ecd7f335ab"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CABERNET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08657246828079224, "Height": 0.010447081178426743, "Left": 0.5438100695610046, "Top": 0.30636656284332275}, "Polygon": [{"X": 0.5438100695610046, "Y": 0.3063669204711914}, {"X": 0.6303825378417969, "Y": 0.30636656284332275}, {"X": 0.6303825378417969, "Y": 0.31681331992149353}, {"X": 0.5438100695610046, "Y": 0.3168136477470398}]}, "Id": "e02a1dca-fbef-4792-b743-a81ba58fe47e"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "FRANC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05284401401877403, "Height": 0.010406861081719398, "Left": 0.6423348784446716, "Top": 0.30635005235671997}, "Polygon": [{"X": 0.6423348784446716, "Y": 0.3063502609729767}, {"X": 0.6951788663864136, "Y": 0.30635005235671997}, {"X": 0.6951788663864136, "Y": 0.3167566955089569}, {"X": 0.6423348784446716, "Y": 0.3167569041252136}]}, "Id": "f4ae7fd3-399b-4689-b1c5-8e823a7b96e8"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07417281717061996, "Height": 0.011067940853536129, "Left": 0.07674475014209747, "Top": 0.32222577929496765}, "Polygon": [{"X": 0.07674475014209747, "Y": 0.32222607731819153}, {"X": 0.15091748535633087, "Y": 0.32222577929496765}, {"X": 0.15091755986213684, "Y": 0.33329343795776367}, {"X": 0.07674483954906464, "Y": 0.33329373598098755}]}, "Id": "e4e64713-a554-441c-9da8-486f26da9a1b"}, {"BlockType": "WORD", "Confidence": 99.57210540771484, "Text": "Loc:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03855987265706062, "Height": 0.010728944092988968, "Left": 0.16425900161266327, "Top": 0.32258883118629456}, "Polygon": [{"X": 0.16425900161266327, "Y": 0.3225890100002289}, {"X": 0.20281881093978882, "Y": 0.32258883118629456}, {"X": 0.2028188854455948, "Y": 0.3333176374435425}, {"X": 0.16425907611846924, "Y": 0.3333177864551544}]}, "Id": "eb8ba0e7-fcd5-477f-afea-bd35397acc29"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "GB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021253304556012154, "Height": 0.01061143446713686, "Left": 0.33702990412712097, "Top": 0.3225176930427551}, "Polygon": [{"X": 0.33702990412712097, "Y": 0.3225177824497223}, {"X": 0.3582831621170044, "Y": 0.3225176930427551}, {"X": 0.35828322172164917, "Y": 0.33312904834747314}, {"X": 0.33702993392944336, "Y": 0.3331291079521179}]}, "Id": "b8c09112-5047-458d-ae89-cf0e456d800e"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "LES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03120211511850357, "Height": 0.010553639382123947, "Left": 0.3707634508609772, "Top": 0.32269391417503357}, "Polygon": [{"X": 0.3707634508609772, "Y": 0.3226940333843231}, {"X": 0.40196552872657776, "Y": 0.32269391417503357}, {"X": 0.40196555852890015, "Y": 0.33324742317199707}, {"X": 0.37076348066329956, "Y": 0.3332475423812866}]}, "Id": "689df8c9-d39f-4bde-8cf9-96486745521f"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "BOU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03161616623401642, "Height": 0.010539514012634754, "Left": 0.4137805700302124, "Top": 0.32264435291290283}, "Polygon": [{"X": 0.4137805700302124, "Y": 0.32264450192451477}, {"X": 0.44539669156074524, "Y": 0.32264435291290283}, {"X": 0.4453967213630676, "Y": 0.33318376541137695}, {"X": 0.4137805700302124, "Y": 0.3331838846206665}]}, "Id": "c973132d-9658-4b9f-84ee-1c4b64aac11a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BLUFF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053007375448942184, "Height": 0.010437406599521637, "Left": 0.4573589861392975, "Top": 0.32271668314933777}, "Polygon": [{"X": 0.4573589861392975, "Y": 0.3227168917655945}, {"X": 0.5103663802146912, "Y": 0.32271668314933777}, {"X": 0.5103663802146912, "Y": 0.3331538736820221}, {"X": 0.4573590159416199, "Y": 0.3331540822982788}]}, "Id": "2430aff5-36df-4134-9bda-11217635bb30"}, {"BlockType": "WORD", "Confidence": 98.4851303100586, "Text": "12/750ML", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08458061516284943, "Height": 0.01225303579121828, "Left": 0.5233126282691956, "Top": 0.32262861728668213}, "Polygon": [{"X": 0.5233126282691956, "Y": 0.3226289749145508}, {"X": 0.6078932881355286, "Y": 0.32262861728668213}, {"X": 0.6078932881355286, "Y": 0.3348813056945801}, {"X": 0.5233126878738403, "Y": 0.33488166332244873}]}, "Id": "dfe61e89-163d-40d7-96ad-ead2eef5737d"}, {"BlockType": "WORD", "Confidence": 96.41482543945312, "Text": "%14.5", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05381787568330765, "Height": 0.010747176595032215, "Left": 0.6192750334739685, "Top": 0.32246124744415283}, "Polygon": [{"X": 0.6192750334739685, "Y": 0.32246145606040955}, {"X": 0.6730929017066956, "Y": 0.32246124744415283}, {"X": 0.6730929017066956, "Y": 0.33320820331573486}, {"X": 0.6192750334739685, "Y": 0.3332084119319916}]}, "Id": "01f30ba5-ba10-4a6d-ae40-dbcce1d12e4c"}, {"BlockType": "WORD", "Confidence": 99.19622802734375, "Text": "52/0000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07436112314462662, "Height": 0.012447784654796124, "Left": 0.04395051300525665, "Top": 0.35553187131881714}, "Polygon": [{"X": 0.04395051300525665, "Y": 0.35553213953971863}, {"X": 0.11831154674291611, "Y": 0.35553187131881714}, {"X": 0.11831163614988327, "Y": 0.36797934770584106}, {"X": 0.04395062103867531, "Y": 0.36797964572906494}]}, "Id": "26a9458a-8692-44ea-ba8a-8c0e508f4d63"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "M4LBBTCS22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10785095393657684, "Height": 0.010475629940629005, "Left": 0.14159981906414032, "Top": 0.35570502281188965}, "Polygon": [{"X": 0.14159981906414032, "Y": 0.35570546984672546}, {"X": 0.24945072829723358, "Y": 0.35570502281188965}, {"X": 0.24945078790187836, "Y": 0.3661802411079407}, {"X": 0.1415998935699463, "Y": 0.3661806583404541}]}, "Id": "96c0d219-12cd-42e9-954a-c4e7e1741b2f"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "2022", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042251210659742355, "Height": 0.010331892408430576, "Left": 0.3702676296234131, "Top": 0.35579341650009155}, "Polygon": [{"X": 0.3702676296234131, "Y": 0.3557935953140259}, {"X": 0.41251882910728455, "Y": 0.35579341650009155}, {"X": 0.41251885890960693, "Y": 0.3661251366138458}, {"X": 0.3702676594257355, "Y": 0.36612531542778015}]}, "Id": "3af68cc4-dcc0-48ff-bf67-1b2fdc938032"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MTGA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04367745295166969, "Height": 0.010514426976442337, "Left": 0.4240948557853699, "Top": 0.355680912733078}, "Polygon": [{"X": 0.4240948557853699, "Y": 0.35568109154701233}, {"X": 0.4677722752094269, "Y": 0.355680912733078}, {"X": 0.46777230501174927, "Y": 0.3661951720714569}, {"X": 0.4240948557853699, "Y": 0.36619532108306885}]}, "Id": "83aabcbb-d5a7-4923-83e0-6b2d12af0f9d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WINES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053666017949581146, "Height": 0.010514101944863796, "Left": 0.4785572588443756, "Top": 0.35563725233078003}, "Polygon": [{"X": 0.4785572588443756, "Y": 0.35563746094703674}, {"X": 0.5322232842445374, "Y": 0.35563725233078003}, {"X": 0.5322232842445374, "Y": 0.3661511242389679}, {"X": 0.4785572588443756, "Y": 0.3661513328552246}]}, "Id": "a68f34ff-e603-4e73-8c74-2928a0d288dc"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "CABERNET", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08651043474674225, "Height": 0.01051651407033205, "Left": 0.5438809990882874, "Top": 0.3556702435016632}, "Polygon": [{"X": 0.5438809990882874, "Y": 0.3556705713272095}, {"X": 0.6303914189338684, "Y": 0.3556702435016632}, {"X": 0.6303914189338684, "Y": 0.3661864101886749}, {"X": 0.5438809990882874, "Y": 0.3661867678165436}]}, "Id": "d62c7fd1-8da7-40f3-aca2-6e99b1291ce2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "SAUV", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.042695943266153336, "Height": 0.010573180392384529, "Left": 0.6419104337692261, "Top": 0.3556381165981293}, "Polygon": [{"X": 0.6419104337692261, "Y": 0.3556382656097412}, {"X": 0.6846063733100891, "Y": 0.3556381165981293}, {"X": 0.6846063137054443, "Y": 0.3662111163139343}, {"X": 0.6419104337692261, "Y": 0.36621129512786865}]}, "Id": "8689dd0e-90b3-4c67-94fe-f7625ed29473"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07440242171287537, "Height": 0.010891800746321678, "Left": 0.07668861001729965, "Top": 0.37182876467704773}, "Polygon": [{"X": 0.07668861001729965, "Y": 0.3718290627002716}, {"X": 0.15109094977378845, "Y": 0.37182876467704773}, {"X": 0.15109102427959442, "Y": 0.38272029161453247}, {"X": 0.07668869197368622, "Y": 0.38272055983543396}]}, "Id": "55c52b16-d6b4-4904-a0ae-3bb529beca46"}, {"BlockType": "WORD", "Confidence": 99.61196899414062, "Text": "Loc:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03850698098540306, "Height": 0.010646798647940159, "Left": 0.16422951221466064, "Top": 0.37209275364875793}, "Polygon": [{"X": 0.16422951221466064, "Y": 0.37209293246269226}, {"X": 0.20273643732070923, "Y": 0.37209275364875793}, {"X": 0.202736496925354, "Y": 0.38273942470550537}, {"X": 0.1642295867204666, "Y": 0.3827395737171173}]}, "Id": "f70512fc-832e-47a4-b534-7705fb66f343"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "GB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021158795803785324, "Height": 0.01052194181829691, "Left": 0.3371385931968689, "Top": 0.3719378411769867}, "Polygon": [{"X": 0.3371385931968689, "Y": 0.37193793058395386}, {"X": 0.35829734802246094, "Y": 0.3719378411769867}, {"X": 0.3582973778247833, "Y": 0.38245970010757446}, {"X": 0.3371386229991913, "Y": 0.3824597895145416}]}, "Id": "ce977e6a-bab9-44ff-a14d-0e8b32258cb6"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03094327263534069, "Height": 0.010658377781510353, "Left": 0.3818923234939575, "Top": 0.37195152044296265}, "Polygon": [{"X": 0.3818923234939575, "Y": 0.3719516396522522}, {"X": 0.41283556818962097, "Y": 0.37195152044296265}, {"X": 0.41283559799194336, "Y": 0.3826097548007965}, {"X": 0.3818923532962799, "Y": 0.38260987401008606}]}, "Id": "a7751f9a-32bb-4229-8718-09c60d0ad472"}, {"BlockType": "WORD", "Confidence": 99.80069732666016, "Text": "BOU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031584713608026505, "Height": 0.010590468533337116, "Left": 0.42443686723709106, "Top": 0.37189966440200806}, "Polygon": [{"X": 0.42443686723709106, "Y": 0.3718997836112976}, {"X": 0.4560215473175049, "Y": 0.37189966440200806}, {"X": 0.45602157711982727, "Y": 0.38249000906944275}, {"X": 0.42443689703941345, "Y": 0.3824901282787323}]}, "Id": "af60dbb4-fa32-4cf0-9621-79eaaed31b9b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BLUFF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05291613191366196, "Height": 0.010544463992118835, "Left": 0.46828070282936096, "Top": 0.371993750333786}, "Polygon": [{"X": 0.46828070282936096, "Y": 0.3719939589500427}, {"X": 0.5211968421936035, "Y": 0.371993750333786}, {"X": 0.5211968421936035, "Y": 0.3825380206108093}, {"X": 0.46828073263168335, "Y": 0.38253819942474365}]}, "Id": "621619e8-8279-4ad1-a622-17917267b766"}, {"BlockType": "WORD", "Confidence": 98.8781509399414, "Text": "12/750ML", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08412018418312073, "Height": 0.012326658703386784, "Left": 0.5348045825958252, "Top": 0.37209364771842957}, "Polygon": [{"X": 0.5348045825958252, "Y": 0.37209397554397583}, {"X": 0.6189247965812683, "Y": 0.37209364771842957}, {"X": 0.6189247369766235, "Y": 0.3844199776649475}, {"X": 0.5348045825958252, "Y": 0.3844203054904938}]}, "Id": "06bc49a9-a333-4aec-ae59-0af39ecbd158"}, {"BlockType": "WORD", "Confidence": 95.45997619628906, "Text": "%14.5", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05435970425605774, "Height": 0.010550499893724918, "Left": 0.6299982666969299, "Top": 0.3720422089099884}, "Polygon": [{"X": 0.6299982666969299, "Y": 0.3720424175262451}, {"X": 0.6843579411506653, "Y": 0.3720422089099884}, {"X": 0.6843579411506653, "Y": 0.38259249925613403}, {"X": 0.6299982666969299, "Y": 0.38259270787239075}]}, "Id": "e44527c2-e8cd-4e77-91c0-3c586982f47b"}, {"BlockType": "WORD", "Confidence": 98.8195571899414, "Text": "36/0000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0745113417506218, "Height": 0.01247080136090517, "Left": 0.04382907226681709, "Top": 0.40474867820739746}, "Polygon": [{"X": 0.04382907226681709, "Y": 0.40474897623062134}, {"X": 0.11834032088518143, "Y": 0.40474867820739746}, {"X": 0.11834041029214859, "Y": 0.4172191917896271}, {"X": 0.04382918030023575, "Y": 0.41721948981285095}]}, "Id": "80d9fd24-8faf-4e94-b1be-fa3599c48af8"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "M4LBBTRB22", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.10775204002857208, "Height": 0.010562974959611893, "Left": 0.14162230491638184, "Top": 0.40485879778862}, "Polygon": [{"X": 0.14162230491638184, "Y": 0.4048592150211334}, {"X": 0.24937430024147034, "Y": 0.40485879778862}, {"X": 0.24937434494495392, "Y": 0.41542136669158936}, {"X": 0.1416223794221878, "Y": 0.4154217541217804}]}, "Id": "bb5137b0-0e7f-4087-a995-4a43379ad1eb"}, {"BlockType": "WORD", "Confidence": 99.970703125, "Text": "2022", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04222588613629341, "Height": 0.010277231223881245, "Left": 0.3702718913555145, "Top": 0.40492817759513855}, "Polygon": [{"X": 0.3702718913555145, "Y": 0.4049283564090729}, {"X": 0.41249772906303406, "Y": 0.40492817759513855}, {"X": 0.41249775886535645, "Y": 0.4152052700519562}, {"X": 0.3702719211578369, "Y": 0.4152054190635681}]}, "Id": "1a29c43c-9aae-4d0a-99e0-76154b53e16a"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MTGA", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.043703678995370865, "Height": 0.010536635294556618, "Left": 0.42404043674468994, "Top": 0.4047130346298218}, "Polygon": [{"X": 0.42404043674468994, "Y": 0.4047132134437561}, {"X": 0.4677441120147705, "Y": 0.4047130346298218}, {"X": 0.4677441418170929, "Y": 0.4152494966983795}, {"X": 0.42404046654701233, "Y": 0.41524967551231384}]}, "Id": "02282901-c2f5-43f9-90dc-d389db031084"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "WINES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05372324213385582, "Height": 0.010518801398575306, "Left": 0.47842493653297424, "Top": 0.40467187762260437}, "Polygon": [{"X": 0.47842493653297424, "Y": 0.4046720862388611}, {"X": 0.5321481823921204, "Y": 0.40467187762260437}, {"X": 0.5321481823921204, "Y": 0.4151904582977295}, {"X": 0.47842493653297424, "Y": 0.4151906669139862}]}, "Id": "003da69c-accc-4a44-95b3-45c8f05a8838"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "RED", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03170373663306236, "Height": 0.010378534905612469, "Left": 0.5443547368049622, "Top": 0.40484434366226196}, "Polygon": [{"X": 0.5443547368049622, "Y": 0.4048444628715515}, {"X": 0.5760585069656372, "Y": 0.40484434366226196}, {"X": 0.5760584473609924, "Y": 0.41522273421287537}, {"X": 0.5443547368049622, "Y": 0.4152228534221649}]}, "Id": "9824a6fd-1a93-4897-a349-a5cd2166a53c"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BLEND", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05328192561864853, "Height": 0.010528858751058578, "Left": 0.587668776512146, "Top": 0.40476101636886597}, "Polygon": [{"X": 0.587668776512146, "Y": 0.4047612249851227}, {"X": 0.6409506797790527, "Y": 0.40476101636886597}, {"X": 0.6409506797790527, "Y": 0.41528967022895813}, {"X": 0.587668776512146, "Y": 0.41528987884521484}]}, "Id": "9ff1c7a5-e816-4275-9c82-668a2256e58f"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.0742100402712822, "Height": 0.011002600193023682, "Left": 0.07669971883296967, "Top": 0.4208572506904602}, "Polygon": [{"X": 0.07669971883296967, "Y": 0.4208575487136841}, {"X": 0.1509096771478653, "Y": 0.4208572506904602}, {"X": 0.15090975165367126, "Y": 0.4318595826625824}, {"X": 0.07669980823993683, "Y": 0.4318598508834839}]}, "Id": "686fd4df-55d8-4028-b575-19db48c9f7dc"}, {"BlockType": "WORD", "Confidence": 99.5224838256836, "Text": "Loc:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.038559358566999435, "Height": 0.010610988363623619, "Left": 0.16413800418376923, "Top": 0.42113789916038513}, "Polygon": [{"X": 0.16413800418376923, "Y": 0.42113804817199707}, {"X": 0.202697291970253, "Y": 0.42113789916038513}, {"X": 0.20269735157489777, "Y": 0.43174874782562256}, {"X": 0.164138063788414, "Y": 0.4317488968372345}]}, "Id": "2dcfdfef-9614-4693-96e5-73cdb10719b2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "GB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02115250937640667, "Height": 0.010609175078570843, "Left": 0.3371434509754181, "Top": 0.42108646035194397}, "Polygon": [{"X": 0.3371434509754181, "Y": 0.42108654975891113}, {"X": 0.3582959473133087, "Y": 0.42108646035194397}, {"X": 0.3582959771156311, "Y": 0.4316955506801605}, {"X": 0.33714351058006287, "Y": 0.4316956400871277}]}, "Id": "152fd192-b7a0-4063-be43-e307c038e4f9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030913900583982468, "Height": 0.01061723381280899, "Left": 0.3818955421447754, "Top": 0.4212048649787903}, "Polygon": [{"X": 0.3818955421447754, "Y": 0.42120498418807983}, {"X": 0.4128094017505646, "Y": 0.4212048649787903}, {"X": 0.41280943155288696, "Y": 0.4318219721317291}, {"X": 0.3818955719470978, "Y": 0.4318220913410187}]}, "Id": "521314a7-4bc0-42b2-9512-ec4c60b32591"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "BOU", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03127165883779526, "Height": 0.010393017902970314, "Left": 0.4246048033237457, "Top": 0.4212223291397095}, "Polygon": [{"X": 0.4246048033237457, "Y": 0.421222448348999}, {"X": 0.4558764398097992, "Y": 0.4212223291397095}, {"X": 0.4558764398097992, "Y": 0.4316152334213257}, {"X": 0.4246048331260681, "Y": 0.43161535263061523}]}, "Id": "369eb5a0-f6a0-4374-8bc1-294e09ce6658"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "BLUFF", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05302736163139343, "Height": 0.010462073609232903, "Left": 0.46819618344306946, "Top": 0.4212038815021515}, "Polygon": [{"X": 0.46819618344306946, "Y": 0.4212040603160858}, {"X": 0.5212235450744629, "Y": 0.4212038815021515}, {"X": 0.5212235450744629, "Y": 0.4316657483577728}, {"X": 0.46819618344306946, "Y": 0.43166595697402954}]}, "Id": "aee242a3-b2ba-4c16-bc33-f99f221b6a17"}, {"BlockType": "WORD", "Confidence": 99.20679473876953, "Text": "12/750ML", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08463748544454575, "Height": 0.012349716387689114, "Left": 0.5346502661705017, "Top": 0.42131343483924866}, "Polygon": [{"X": 0.5346502661705017, "Y": 0.4213137626647949}, {"X": 0.6192877888679504, "Y": 0.42131343483924866}, {"X": 0.6192877888679504, "Y": 0.4336628317832947}, {"X": 0.5346503257751465, "Y": 0.43366312980651855}]}, "Id": "30d13ee6-60b8-4a48-ac47-61016e5310a6"}, {"BlockType": "WORD", "Confidence": 96.97704315185547, "Text": "%14.5", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05379434674978256, "Height": 0.010712113231420517, "Left": 0.6302083730697632, "Top": 0.4211345911026001}, "Polygon": [{"X": 0.630208432674408, "Y": 0.4211347997188568}, {"X": 0.6840027570724487, "Y": 0.4211345911026001}, {"X": 0.684002697467804, "Y": 0.4318464994430542}, {"X": 0.6302083730697632, "Y": 0.4318467080593109}]}, "Id": "b3399ff4-f724-4703-a957-0e29e62cf749"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "100", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.029765600338578224, "Height": 0.010209846310317516, "Left": 0.08864060789346695, "Top": 0.4703648090362549}, "Polygon": [{"X": 0.08864060789346695, "Y": 0.47036489844322205}, {"X": 0.11840613186359406, "Y": 0.4703648090362549}, {"X": 0.11840620636940002, "Y": 0.4805745482444763}, {"X": 0.08864068984985352, "Y": 0.4805746376514435}]}, "Id": "14f926b6-d7dc-4f7e-8f24-cd0ee07f8cd9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "CASES", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.053766440600156784, "Height": 0.010475888848304749, "Left": 0.14163532853126526, "Top": 0.4701801836490631}, "Polygon": [{"X": 0.14163532853126526, "Y": 0.4701803922653198}, {"X": 0.19540171325206757, "Y": 0.4701801836490631}, {"X": 0.19540177285671234, "Y": 0.48065587878227234}, {"X": 0.14163540303707123, "Y": 0.48065608739852905}]}, "Id": "e50f25c8-5e56-4b4f-86fb-877151c8dd41"}, {"BlockType": "WORD", "Confidence": 99.84375, "Text": "3,600", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05260400101542473, "Height": 0.012622538954019547, "Left": 0.06559953838586807, "Top": 0.48694077134132385}, "Polygon": [{"X": 0.06559953838586807, "Y": 0.4869409501552582}, {"X": 0.11820344626903534, "Y": 0.48694077134132385}, {"X": 0.1182035356760025, "Y": 0.4995630979537964}, {"X": 0.06559964269399643, "Y": 0.4995633065700531}]}, "Id": "bb33f82e-33cf-4898-a8b9-a3a1bdc738c9"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "POUNDS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06369704753160477, "Height": 0.0102999871596694, "Left": 0.14222043752670288, "Top": 0.4866381287574768}, "Polygon": [{"X": 0.14222043752670288, "Y": 0.4866383671760559}, {"X": 0.20591741800308228, "Y": 0.4866381287574768}, {"X": 0.20591749250888824, "Y": 0.4969378709793091}, {"X": 0.14222051203250885, "Y": 0.4969381093978882}]}, "Id": "b3157a67-c4a4-4582-badf-0b05d672f688"}, {"BlockType": "WORD", "Confidence": 99.599609375, "Text": "1.78", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041147585958242416, "Height": 0.010585308074951172, "Left": 0.07768874615430832, "Top": 0.502938449382782}, "Polygon": [{"X": 0.07768874615430832, "Y": 0.5029386281967163}, {"X": 0.11883626133203506, "Y": 0.502938449382782}, {"X": 0.11883633583784103, "Y": 0.5135236382484436}, {"X": 0.07768883556127548, "Y": 0.5135237574577332}]}, "Id": "f9135c15-ae5d-4e11-93bf-e3476e032f61"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "PALLETS", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07508566230535507, "Height": 0.010635065846145153, "Left": 0.14213308691978455, "Top": 0.5029584765434265}, "Polygon": [{"X": 0.14213308691978455, "Y": 0.5029587149620056}, {"X": 0.21721869707107544, "Y": 0.5029584765434265}, {"X": 0.21721875667572021, "Y": 0.5135932564735413}, {"X": 0.14213316142559052, "Y": 0.5135935544967651}]}, "Id": "cb91e40d-ca3f-4977-b6a1-bfa12d68a534"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "1c7956f0-8023-4f4f-8d45-d179ff1563f9", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "1c7956f0-8023-4f4f-8d45-d179ff1563f9", "content-type": "application/x-amz-json-1.1", "content-length": "74085", "date": "Wed, 24 Sep 2025 08:58:46 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "594e2c0c-b861-4f72-a529-c54d063e6c57", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 08:58:48 GMT", "content-type": "application/json", "content-length": "751", "connection": "keep-alive", "x-amzn-requestid": "594e2c0c-b861-4f72-a529-c54d063e6c57"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We have a single page. Title: ORDER CONFIRMATION. It's about a purchase order or sales order confirmation from a warehouse. The page includes fields: Our order#, Winery order#, ship date, etc. It lists items and quantities. This matches \"so_confirmation\" (Sales Order Confirmation). The doc type \"so_confirmation\" is defined.\n\nWe should return a JSON object with page_no=1, doc_type=\"so_confirmation\". Use the tool.\n\n"}}}, {"text": "{\"documents\":[{\"page_no\":1,\"doc_type\":\"so_confirmation\"}]}"}]}}, "stopReason": "end_turn", "usage": {"inputTokens": 2728, "outputTokens": 109, "totalTokens": 2837}, "metrics": {"latencyMs": 723}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/d6b98bcd_FFJ2USKKEFCH3U0FO1S5.PDF", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nORDER CONFIRMATION\nFrom\nGROSKOPF WAREHOUSE\nOur order#\n733380.0\nWinery order#\n.....\nLBV21125\nOur order date\n02/06/25\nRequested ship date\n02/11/25\nShip via\nMcClain & Associates\nSHIP - TO\nWINERY\nLes Bourgeois Vyds\nM4 - <PERSON><PERSON>GA WINES\n12847 Hwy BB\n958 MCCORKLE AVE\nRocheport, MO 65279\nSAINT HELENA, CA 94574-1329\nCase/Btl Part\nDescription\nLocation\n12/0000 M4LBBTCF22\n2022 MTGA WINES CABERNET FRANC\nDefault Loc:\nGB LES BOU BLUFF 12/750ML %14.5\n52/0000 M4LBBTCS22\n2022 MTGA WINES CABERNET SAUV\nDefault Loc:\nGB LES BOU BLUFF 12/750ML %14.5\n36/0000 M4LBBTRB22\n2022 MTGA WINES RED BLEND\nDefault Loc:\nGB LES BOU BLUFF 12/750ML %14.5\n100 CASES\n3,600 POUNDS\n1.78 PALLETS\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}