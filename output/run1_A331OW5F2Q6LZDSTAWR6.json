{"classification_result": {"documents": [{"page_no": 1, "doc_type": "po"}]}, "textract_response_object": [{"DocumentMetadata": {"Pages": 1}, "Blocks": [{"BlockType": "PAGE", "Geometry": {"BoundingBox": {"Width": 1.0, "Height": 1.0, "Left": 0.0, "Top": 0.0}, "Polygon": [{"X": 0.0, "Y": 0.0}, {"X": 1.0, "Y": 0.0}, {"X": 1.0, "Y": 1.0}, {"X": 7.083059472279274e-08, "Y": 1.0}]}, "Id": "9422a6bd-2b1a-444e-a265-1d0a31a4c4df", "Relationships": [{"Type": "CHILD", "Ids": ["18884226-4986-4823-a4e3-43442d3b59b8", "f2e43b26-485c-4fb0-b0f6-e6ddc98a2b48", "4d69e648-63d2-4ce8-9c32-7eca887125f5", "ba8ad58e-40e0-405c-baf7-92c6840c3983", "228090c9-2585-4891-a0d3-5a5e0fe06d6c", "3e6b05ae-7b30-4565-91d2-9ba939e33efd", "3ad4d956-e746-448b-b736-37ff8d7e0fdf", "bd3182ca-da49-4c9d-9b99-aee939e789e3", "d67b222a-a6f6-4562-8316-4187a4b336c7", "d921b290-ffdf-42dc-ad0d-6c718b30ccfb", "664fd012-83b9-405d-a8d2-b1de579e6b64", "9003a13a-dc6d-4b53-95f8-d4f697ec2ca4", "5d2f5f51-dfc5-491d-8ac2-1da9f56c84d7", "1d91e1e3-bbd2-415b-a01e-7ab47e5feda9", "65b4ab68-73cf-41fd-a679-0f2788aa4c3d", "698656a2-1e40-4c51-a8b8-185b59a6280e", "30b43f6a-3410-4990-bebf-86f86200b30c", "07d7bf64-6620-4c7f-8571-4b706d6a23fa", "ebe456ae-812e-44ee-897f-fe3a36d5fa15", "797e4a04-f771-4025-bf37-076fdddb9331", "09773e09-509d-495c-96d1-b9095e8f574c", "b4c92ae5-6a9c-49f7-8905-3d3a5763a690"]}]}, {"BlockType": "LINE", "Confidence": 99.92028045654297, "Text": "MRB Logistics, LLC", "Geometry": {"BoundingBox": {"Width": 0.17665117979049683, "Height": 0.014412922784686089, "Left": 0.06951247155666351, "Top": 0.05207635089755058}, "Polygon": [{"X": 0.06951247155666351, "Y": 0.05207635089755058}, {"X": 0.24616362154483795, "Y": 0.052076950669288635}, {"X": 0.24616363644599915, "Y": 0.06648927181959152}, {"X": 0.06951253116130829, "Y": 0.06648864597082138}]}, "Id": "18884226-4986-4823-a4e3-43442d3b59b8", "Relationships": [{"Type": "CHILD", "Ids": ["1dd15854-5f95-45d5-9acd-bf15be533bc6", "fca300ff-8802-4a88-a24c-66b68f068c37", "ecfebfc8-71cc-460b-96f2-3b83e724ccda"]}]}, {"BlockType": "LINE", "Confidence": 99.95525360107422, "Text": "Integris Lighting", "Geometry": {"BoundingBox": {"Width": 0.1690031737089157, "Height": 0.016650453209877014, "Left": 0.7981168627738953, "Top": 0.0525997094810009}, "Polygon": [{"X": 0.7981169819831848, "Y": 0.0525997094810009}, {"X": 0.9671199917793274, "Y": 0.05260028317570686}, {"X": 0.9671198725700378, "Y": 0.06925015896558762}, {"X": 0.7981168627738953, "Y": 0.06924955546855927}]}, "Id": "f2e43b26-485c-4fb0-b0f6-e6ddc98a2b48", "Relationships": [{"Type": "CHILD", "Ids": ["ab9118a3-48d4-4cc2-9fa1-045e9da53011", "88117302-7fea-4163-b28f-0b199966c67f"]}]}, {"BlockType": "LINE", "Confidence": 99.88058471679688, "Text": "620 S Belt Line Rd", "Geometry": {"BoundingBox": {"Width": 0.12892566621303558, "Height": 0.009805528447031975, "Left": 0.06910860538482666, "Top": 0.07568755000829697}, "Polygon": [{"X": 0.06910860538482666, "Y": 0.07568755000829697}, {"X": 0.19803425669670105, "Y": 0.07568801939487457}, {"X": 0.19803428649902344, "Y": 0.08549308031797409}, {"X": 0.06910865008831024, "Y": 0.0854925885796547}]}, "Id": "4d69e648-63d2-4ce8-9c32-7eca887125f5", "Relationships": [{"Type": "CHILD", "Ids": ["b55d778b-8400-4079-b52d-bc71dd8204e6", "7a6add79-2f5a-40de-a4fd-46f96ad9b4ad", "8a6e4663-6531-4ef4-a4d2-23a58d11ed98", "2dac0263-c10c-46a1-8979-04c335984f60", "3864f0ca-0998-4018-9155-5729e08150c0"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Purchase Order", "Geometry": {"BoundingBox": {"Width": 0.13167858123779297, "Height": 0.011733204126358032, "Left": 0.8372079730033875, "Top": 0.07955512404441833}, "Polygon": [{"X": 0.8372080326080322, "Y": 0.07955512404441833}, {"X": 0.9688865542411804, "Y": 0.07955561578273773}, {"X": 0.9688864350318909, "Y": 0.09128832817077637}, {"X": 0.8372079730033875, "Y": 0.09128782153129578}]}, "Id": "ba8ad58e-40e0-405c-baf7-92c6840c3983", "Relationships": [{"Type": "CHILD", "Ids": ["8637320d-97d3-49b5-92f9-fa526e3b0f77", "70cd93d4-e777-42d5-8d7d-b57a354d077a"]}]}, {"BlockType": "LINE", "Confidence": 99.94359588623047, "Text": "Irving TX 75060", "Geometry": {"BoundingBox": {"Width": 0.11339801549911499, "Height": 0.012058268301188946, "Left": 0.07004725188016891, "Top": 0.10690657049417496}, "Polygon": [{"X": 0.07004725188016891, "Y": 0.10690657049417496}, {"X": 0.18344523012638092, "Y": 0.10690702497959137}, {"X": 0.1834452599287033, "Y": 0.11896483600139618}, {"X": 0.0700473040342331, "Y": 0.11896436661481857}]}, "Id": "228090c9-2585-4891-a0d3-5a5e0fe06d6c", "Relationships": [{"Type": "CHILD", "Ids": ["6af5ae77-6fc7-4b1d-98b5-21d4d536980c", "4c94656d-fd09-4cb0-9452-8abe8d8dc360", "9bcdeefa-11dd-4780-bc0d-2df745c57656"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "44426", "Geometry": {"BoundingBox": {"Width": 0.061477180570364, "Height": 0.012964711524546146, "Left": 0.9071230888366699, "Top": 0.10118379443883896}, "Polygon": [{"X": 0.9071232080459595, "Y": 0.10118379443883896}, {"X": 0.9686002731323242, "Y": 0.10118404030799866}, {"X": 0.9686001539230347, "Y": 0.11414850503206253}, {"X": 0.9071230888366699, "Y": 0.11414825171232224}]}, "Id": "3e6b05ae-7b30-4565-91d2-9ba939e33efd", "Relationships": [{"Type": "CHILD", "Ids": ["c19c5707-ed92-452a-ad82-9f0566797a29"]}]}, {"BlockType": "LINE", "Confidence": 99.82386779785156, "Text": "Terms: V Net 60", "Geometry": {"BoundingBox": {"Width": 0.12772350013256073, "Height": 0.009832704439759254, "Left": 0.06871244311332703, "Top": 0.12376604229211807}, "Polygon": [{"X": 0.06871244311332703, "Y": 0.12376604229211807}, {"X": 0.19643591344356537, "Y": 0.12376657873392105}, {"X": 0.19643594324588776, "Y": 0.13359874486923218}, {"X": 0.06871248781681061, "Y": 0.133598193526268}]}, "Id": "3ad4d956-e746-448b-b736-37ff8d7e0fdf", "Relationships": [{"Type": "CHILD", "Ids": ["c731ec7a-3a58-494d-a148-0a8e576db4b9", "11a6c3d0-6be7-4b02-ae4d-4b4ba7aa03d3", "ae080999-6d8c-4237-85e2-f2bea0f67b5c", "2cc4e527-8b7e-4346-bfe2-6b5d245eb883"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Date", "Geometry": {"BoundingBox": {"Width": 0.03239922970533371, "Height": 0.009803312830626965, "Left": 0.8189185857772827, "Top": 0.12386447191238403}, "Polygon": [{"X": 0.8189186453819275, "Y": 0.12386447191238403}, {"X": 0.851317822933197, "Y": 0.12386460602283478}, {"X": 0.8513177633285522, "Y": 0.13366778194904327}, {"X": 0.8189185857772827, "Y": 0.13366764783859253}]}, "Id": "bd3182ca-da49-4c9d-9b99-aee939e789e3", "Relationships": [{"Type": "CHILD", "Ids": ["0e345978-4996-4576-a5e9-d47f4f461c7b"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "09/17/25", "Geometry": {"BoundingBox": {"Width": 0.06048210710287094, "Height": 0.009667715057730675, "Left": 0.9081143736839294, "Top": 0.12391699105501175}, "Polygon": [{"X": 0.9081144332885742, "Y": 0.12391699105501175}, {"X": 0.9685964584350586, "Y": 0.12391724437475204}, {"X": 0.9685963988304138, "Y": 0.13358470797538757}, {"X": 0.9081143736839294, "Y": 0.13358443975448608}]}, "Id": "d67b222a-a6f6-4562-8316-4187a4b336c7", "Relationships": [{"Type": "CHILD", "Ids": ["54bce926-69b1-44f5-b8b0-15e11d4bd6db"]}]}, {"BlockType": "LINE", "Confidence": 99.9951171875, "Text": "# Description", "Geometry": {"BoundingBox": {"Width": 0.11013128608465195, "Height": 0.011947980150580406, "Left": 0.06128663942217827, "Top": 0.2139250487089157}, "Polygon": [{"X": 0.06128663942217827, "Y": 0.2139250487089157}, {"X": 0.17141789197921753, "Y": 0.21392562985420227}, {"X": 0.17141792178153992, "Y": 0.22587302327156067}, {"X": 0.061286695301532745, "Y": 0.2258724421262741}]}, "Id": "d921b290-ffdf-42dc-ad0d-6c718b30ccfb", "Relationships": [{"Type": "CHILD", "Ids": ["7b097c09-6b3b-42dd-8a3d-6841cb1c1fe2", "0aa4dd23-6d44-4b5d-a268-87f2757fbcee"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Price", "Geometry": {"BoundingBox": {"Width": 0.03718823939561844, "Height": 0.009635903872549534, "Left": 0.6918730139732361, "Top": 0.21382339298725128}, "Polygon": [{"X": 0.6918730735778809, "Y": 0.21382339298725128}, {"X": 0.7290612459182739, "Y": 0.2138235867023468}, {"X": 0.7290611863136292, "Y": 0.22345928847789764}, {"X": 0.6918730139732361, "Y": 0.22345909476280212}]}, "Id": "664fd012-83b9-405d-a8d2-b1de579e6b64", "Relationships": [{"Type": "CHILD", "Ids": ["c258ac83-1f5c-401c-8874-1912f9920c25"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.03688494861125946, "Height": 0.009917698800563812, "Left": 0.8682219982147217, "Top": 0.21372097730636597}, "Polygon": [{"X": 0.8682220578193665, "Y": 0.21372097730636597}, {"X": 0.9051069021224976, "Y": 0.2137211710214615}, {"X": 0.9051068425178528, "Y": 0.22363866865634918}, {"X": 0.8682219982147217, "Y": 0.22363847494125366}]}, "Id": "9003a13a-dc6d-4b53-95f8-d4f697ec2ca4", "Relationships": [{"Type": "CHILD", "Ids": ["d25f4caf-4c29-4ad9-862a-56b40376c72c"]}]}, {"BlockType": "LINE", "Confidence": 99.873046875, "Text": "1", "Geometry": {"BoundingBox": {"Width": 0.004654270596802235, "Height": 0.009236345067620277, "Left": 0.0626901313662529, "Top": 0.2377956658601761}, "Polygon": [{"X": 0.0626901313662529, "Y": 0.2377956658601761}, {"X": 0.06734436005353928, "Y": 0.23779569566249847}, {"X": 0.06734440475702286, "Y": 0.2470320165157318}, {"X": 0.06269017606973648, "Y": 0.24703198671340942}]}, "Id": "5d2f5f51-dfc5-491d-8ac2-1da9f56c84d7", "Relationships": [{"Type": "CHILD", "Ids": ["0924339a-c2d6-4002-b01f-523ff06f22d4"]}]}, {"BlockType": "LINE", "Confidence": 99.26358795166016, "Text": "PS-INT2025-T-2878", "Geometry": {"BoundingBox": {"Width": 0.14050935208797455, "Height": 0.009883232414722443, "Left": 0.08651310205459595, "Top": 0.23745156824588776}, "Polygon": [{"X": 0.08651310205459595, "Y": 0.23745156824588776}, {"X": 0.2270224392414093, "Y": 0.23745234310626984}, {"X": 0.2270224541425705, "Y": 0.2473347932100296}, {"X": 0.08651314675807953, "Y": 0.24733400344848633}]}, "Id": "1d91e1e3-bbd2-415b-a01e-7ab47e5feda9", "Relationships": [{"Type": "CHILD", "Ids": ["372dee85-006b-4ab2-a142-8215d6bb1c38"]}]}, {"BlockType": "LINE", "Confidence": 96.506103515625, "Text": "$ 400.00000", "Geometry": {"BoundingBox": {"Width": 0.08588708192110062, "Height": 0.011642426252365112, "Left": 0.7131158113479614, "Top": 0.23702426254749298}, "Polygon": [{"X": 0.7131158709526062, "Y": 0.23702426254749298}, {"X": 0.7990028858184814, "Y": 0.23702472448349}, {"X": 0.7990028262138367, "Y": 0.2486666887998581}, {"X": 0.7131158113479614, "Y": 0.2486661970615387}]}, "Id": "65b4ab68-73cf-41fd-a679-0f2788aa4c3d", "Relationships": [{"Type": "CHILD", "Ids": ["2df8d1d6-7e59-44d3-817e-8418b74bc74c"]}]}, {"BlockType": "LINE", "Confidence": 95.92613983154297, "Text": "$ 400.00000", "Geometry": {"BoundingBox": {"Width": 0.08629834651947021, "Height": 0.011887146160006523, "Left": 0.8818829655647278, "Top": 0.2368636578321457}, "Polygon": [{"X": 0.8818830847740173, "Y": 0.2368636578321457}, {"X": 0.968181312084198, "Y": 0.2368641346693039}, {"X": 0.9681811928749084, "Y": 0.24875080585479736}, {"X": 0.8818829655647278, "Y": 0.24875031411647797}]}, "Id": "698656a2-1e40-4c51-a8b8-185b59a6280e", "Relationships": [{"Type": "CHILD", "Ids": ["94114967-2d29-475d-a63f-7e10ab7730be"]}]}, {"BlockType": "LINE", "Confidence": 98.63680267333984, "Text": "Sprinter <PERSON> <PERSON> Intra DEN", "Geometry": {"BoundingBox": {"Width": 0.16780436038970947, "Height": 0.012306858785450459, "Left": 0.053552985191345215, "Top": 0.2689082622528076}, "Polygon": [{"X": 0.053552985191345215, "Y": 0.2689082622528076}, {"X": 0.2213573306798935, "Y": 0.2689092457294464}, {"X": 0.2213573455810547, "Y": 0.2812151312828064}, {"X": 0.05355304479598999, "Y": 0.2812141180038452}]}, "Id": "30b43f6a-3410-4990-bebf-86f86200b30c", "Relationships": [{"Type": "CHILD", "Ids": ["404fbc40-7202-4de7-8bdf-56353ec78ed3", "349117b6-634e-4a94-8ca4-384b93cf397d", "10e1056d-aae4-4375-aecf-9197ebbe34fe", "05fead5c-2f7f-4466-a64a-60c6f916897d"]}]}, {"BlockType": "LINE", "Confidence": 100.0, "Text": "Total", "Geometry": {"BoundingBox": {"Width": 0.03670176491141319, "Height": 0.009836472570896149, "Left": 0.6054854393005371, "Top": 0.2983285188674927}, "Polygon": [{"X": 0.6054854989051819, "Y": 0.2983285188674927}, {"X": 0.642187237739563, "Y": 0.2983287274837494}, {"X": 0.6421871781349182, "Y": 0.3081649839878082}, {"X": 0.6054854393005371, "Y": 0.3081647455692291}]}, "Id": "07d7bf64-6620-4c7f-8571-4b706d6a23fa", "Relationships": [{"Type": "CHILD", "Ids": ["c6b5a9b8-2553-4477-b6d0-a70b4887ad19"]}]}, {"BlockType": "LINE", "Confidence": 95.65768432617188, "Text": "$ 400.00000", "Geometry": {"BoundingBox": {"Width": 0.08665494620800018, "Height": 0.01172705553472042, "Left": 0.8816199898719788, "Top": 0.2975853681564331}, "Polygon": [{"X": 0.8816201090812683, "Y": 0.2975853681564331}, {"X": 0.9682749509811401, "Y": 0.2975859045982361}, {"X": 0.9682748317718506, "Y": 0.3093124330043793}, {"X": 0.8816199898719788, "Y": 0.3093118667602539}]}, "Id": "ebe456ae-812e-44ee-897f-fe3a36d5fa15", "Relationships": [{"Type": "CHILD", "Ids": ["15684089-07a5-4ff8-a5c5-707eb94d4b94"]}]}, {"BlockType": "LINE", "Confidence": 99.96014404296875, "Text": "Page", "Geometry": {"BoundingBox": {"Width": 0.03473186120390892, "Height": 0.011966117657721043, "Left": 0.8081220984458923, "Top": 0.8821167945861816}, "Polygon": [{"X": 0.8081221580505371, "Y": 0.8821167945861816}, {"X": 0.8428539633750916, "Y": 0.8821172118186951}, {"X": 0.842853844165802, "Y": 0.8940829038619995}, {"X": 0.8081220984458923, "Y": 0.8940824270248413}]}, "Id": "797e4a04-f771-4025-bf37-076fdddb9331", "Relationships": [{"Type": "CHILD", "Ids": ["a0611384-289c-49a0-8525-7b6e9f1d894e"]}]}, {"BlockType": "LINE", "Confidence": 99.25621795654297, "Text": "1 / 1", "Geometry": {"BoundingBox": {"Width": 0.05477549880743027, "Height": 0.014650152996182442, "Left": 0.8930217623710632, "Top": 0.8818439245223999}, "Polygon": [{"X": 0.8930218815803528, "Y": 0.8818439245223999}, {"X": 0.9477972388267517, "Y": 0.8818446397781372}, {"X": 0.9477971196174622, "Y": 0.8964940905570984}, {"X": 0.8930217623710632, "Y": 0.8964933753013611}]}, "Id": "09773e09-509d-495c-96d1-b9095e8f574c", "Relationships": [{"Type": "CHILD", "Ids": ["f7d717ad-7d42-4e50-9e09-ee0bbfaaf654", "6f502d52-f31c-493d-b91c-7fcd22e24cce", "f2940e28-2c9d-4303-af61-523089e1a152"]}]}, {"BlockType": "LINE", "Confidence": 99.91031646728516, "Text": "Created By: <PERSON>", "Geometry": {"BoundingBox": {"Width": 0.20058907568454742, "Height": 0.012475158087909222, "Left": 0.08244438469409943, "Top": 0.9060667157173157}, "Polygon": [{"X": 0.08244438469409943, "Y": 0.9060667157173157}, {"X": 0.28303343057632446, "Y": 0.9060693979263306}, {"X": 0.28303346037864685, "Y": 0.9185419082641602}, {"X": 0.0824444442987442, "Y": 0.9185392260551453}]}, "Id": "b4c92ae5-6a9c-49f7-8905-3d3a5763a690", "Relationships": [{"Type": "CHILD", "Ids": ["1768489b-9441-478e-8aa8-676020655b78", "05735acd-e52c-4e27-9da8-c4992f8a3a02", "b550a241-f316-4b8d-a4ea-9032b9fffb9e", "730080f7-4e84-460f-a3d4-5ffd12712c6d"]}]}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "MRB", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.041265178471803665, "Height": 0.011314043775200844, "Left": 0.06951247155666351, "Top": 0.052174460142850876}, "Polygon": [{"X": 0.06951247155666351, "Y": 0.052174460142850876}, {"X": 0.1107776015996933, "Y": 0.05217459797859192}, {"X": 0.11077764630317688, "Y": 0.06348850578069687}, {"X": 0.06951252371072769, "Y": 0.06348835676908493}]}, "Id": "1dd15854-5f95-45d5-9acd-bf15be533bc6"}, {"BlockType": "WORD", "Confidence": 99.7608413696289, "Text": "Logistics,", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08631979674100876, "Height": 0.014412608928978443, "Left": 0.11757082492113113, "Top": 0.052076514810323715}, "Polygon": [{"X": 0.11757082492113113, "Y": 0.052076514810323715}, {"X": 0.2038905918598175, "Y": 0.052076809108257294}, {"X": 0.2038906216621399, "Y": 0.06648912280797958}, {"X": 0.11757088452577591, "Y": 0.06648881733417511}]}, "Id": "fca300ff-8802-4a88-a24c-66b68f068c37"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "LLC", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03505083918571472, "Height": 0.011387979611754417, "Left": 0.21111279726028442, "Top": 0.052112337201833725}, "Polygon": [{"X": 0.21111279726028442, "Y": 0.052112337201833725}, {"X": 0.24616362154483795, "Y": 0.052112456411123276}, {"X": 0.24616363644599915, "Y": 0.063500314950943}, {"X": 0.2111128270626068, "Y": 0.06350019574165344}]}, "Id": "ecfebfc8-71cc-460b-96f2-3b83e724ccda"}, {"BlockType": "WORD", "Confidence": 99.92028045654297, "Text": "Integris", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07834439724683762, "Height": 0.01660870388150215, "Left": 0.7981168627738953, "Top": 0.0526118203997612}, "Polygon": [{"X": 0.7981169819831848, "Y": 0.0526118203997612}, {"X": 0.8764612674713135, "Y": 0.05261208489537239}, {"X": 0.8764610886573792, "Y": 0.06922052055597305}, {"X": 0.7981168627738953, "Y": 0.06922024488449097}]}, "Id": "ab9118a3-48d4-4cc2-9fa1-045e9da53011"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "Lighting", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08381601423025131, "Height": 0.016650162637233734, "Left": 0.8833039999008179, "Top": 0.05260000005364418}, "Polygon": [{"X": 0.8833041191101074, "Y": 0.05260000005364418}, {"X": 0.9671199917793274, "Y": 0.05260028317570686}, {"X": 0.9671198725700378, "Y": 0.06925015896558762}, {"X": 0.8833039999008179, "Y": 0.06924986094236374}]}, "Id": "88117302-7fea-4163-b28f-0b199966c67f"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "620", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.025242414325475693, "Height": 0.009549788199365139, "Left": 0.06910861283540726, "Top": 0.07585334777832031}, "Polygon": [{"X": 0.06910861283540726, "Y": 0.07585334777832031}, {"X": 0.09435098618268967, "Y": 0.07585343718528748}, {"X": 0.09435102343559265, "Y": 0.08540313690900803}, {"X": 0.06910865008831024, "Y": 0.08540304005146027}]}, "Id": "b55d778b-8400-4079-b52d-bc71dd8204e6"}, {"BlockType": "WORD", "Confidence": 99.41266632080078, "Text": "S", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.009489848278462887, "Height": 0.009678163565695286, "Left": 0.0995262861251831, "Top": 0.07569189369678497}, "Polygon": [{"X": 0.0995262861251831, "Y": 0.07569189369678497}, {"X": 0.10901609808206558, "Y": 0.07569193094968796}, {"X": 0.10901613533496857, "Y": 0.08537005633115768}, {"X": 0.09952632337808609, "Y": 0.0853700190782547}]}, "Id": "7a6add79-2f5a-40de-a4fd-46f96ad9b4ad"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Belt", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.026340026408433914, "Height": 0.009769010357558727, "Left": 0.11454437673091888, "Top": 0.0756877139210701}, "Polygon": [{"X": 0.11454437673091888, "Y": 0.0756877139210701}, {"X": 0.1408843696117401, "Y": 0.07568781077861786}, {"X": 0.1408843994140625, "Y": 0.0854567289352417}, {"X": 0.11454441398382187, "Y": 0.08545662462711334}]}, "Id": "8a6e4663-6531-4ef4-a4d2-23a58d11ed98"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Line", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02839970774948597, "Height": 0.009600983932614326, "Left": 0.14561741054058075, "Top": 0.0757332295179367}, "Polygon": [{"X": 0.14561741054058075, "Y": 0.0757332295179367}, {"X": 0.17401710152626038, "Y": 0.07573333382606506}, {"X": 0.17401713132858276, "Y": 0.08533421158790588}, {"X": 0.14561745524406433, "Y": 0.08533410727977753}]}, "Id": "2dac0263-c10c-46a1-8979-04c335984f60"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Rd", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.018431639298796654, "Height": 0.009771735407412052, "Left": 0.17960263788700104, "Top": 0.07572133839130402}, "Polygon": [{"X": 0.17960263788700104, "Y": 0.07572133839130402}, {"X": 0.19803425669670105, "Y": 0.07572141289710999}, {"X": 0.19803428649902344, "Y": 0.08549308031797409}, {"X": 0.17960266768932343, "Y": 0.08549300581216812}]}, "Id": "3864f0ca-0998-4018-9155-5729e08150c0"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Purchase", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.07758836448192596, "Height": 0.011629661545157433, "Left": 0.8372079730033875, "Top": 0.07965348660945892}, "Polygon": [{"X": 0.8372080326080322, "Y": 0.07965348660945892}, {"X": 0.9147963523864746, "Y": 0.0796537697315216}, {"X": 0.9147962331771851, "Y": 0.09128314256668091}, {"X": 0.8372079730033875, "Y": 0.09128284454345703}]}, "Id": "8637320d-97d3-49b5-92f9-fa526e3b0f77"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Order", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.048129115253686905, "Height": 0.011732893995940685, "Left": 0.9207574129104614, "Top": 0.0795554369688034}, "Polygon": [{"X": 0.920757532119751, "Y": 0.0795554369688034}, {"X": 0.9688865542411804, "Y": 0.07955561578273773}, {"X": 0.9688864350318909, "Y": 0.09128832817077637}, {"X": 0.9207574129104614, "Y": 0.09128814190626144}]}, "Id": "70cd93d4-e777-42d5-8d7d-b57a354d077a"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.036529291421175, "Height": 0.012054366059601307, "Left": 0.07004725188016891, "Top": 0.10691015422344208}, "Polygon": [{"X": 0.07004725188016891, "Y": 0.10691015422344208}, {"X": 0.10657649487257004, "Y": 0.10691029578447342}, {"X": 0.10657653957605362, "Y": 0.11896451562643051}, {"X": 0.0700473040342331, "Y": 0.11896436661481857}]}, "Id": "6af5ae77-6fc7-4b1d-98b5-21d4d536980c"}, {"BlockType": "WORD", "Confidence": 99.87065887451172, "Text": "TX", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.019999001175165176, "Height": 0.00962987169623375, "Left": 0.11153927445411682, "Top": 0.10690673440694809}, "Polygon": [{"X": 0.11153927445411682, "Y": 0.10690673440694809}, {"X": 0.1315382421016693, "Y": 0.10690681636333466}, {"X": 0.1315382719039917, "Y": 0.11653660982847214}, {"X": 0.1115393117070198, "Y": 0.11653652787208557}]}, "Id": "4c94656d-fd09-4cb0-9452-8abe8d8dc360"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "75060", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04266676679253578, "Height": 0.009414653293788433, "Left": 0.14077849686145782, "Top": 0.10714300721883774}, "Polygon": [{"X": 0.14077849686145782, "Y": 0.10714300721883774}, {"X": 0.18344523012638092, "Y": 0.10714318603277206}, {"X": 0.1834452599287033, "Y": 0.11655766516923904}, {"X": 0.1407785266637802, "Y": 0.11655748635530472}]}, "Id": "9bcdeefa-11dd-4780-bc0d-2df745c57656"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "44426", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.061477180570364, "Height": 0.012964711524546146, "Left": 0.9071230888366699, "Top": 0.10118379443883896}, "Polygon": [{"X": 0.9071232080459595, "Y": 0.10118379443883896}, {"X": 0.9686002731323242, "Y": 0.10118404030799866}, {"X": 0.9686001539230347, "Y": 0.11414850503206253}, {"X": 0.9071230888366699, "Y": 0.11414825171232224}]}, "Id": "c19c5707-ed92-452a-ad82-9f0566797a29"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Terms:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.04889010265469551, "Height": 0.009569301269948483, "Left": 0.06871245056390762, "Top": 0.12395673245191574}, "Polygon": [{"X": 0.06871245056390762, "Y": 0.12395673245191574}, {"X": 0.11760251224040985, "Y": 0.12395694106817245}, {"X": 0.11760254949331284, "Y": 0.1335260272026062}, {"X": 0.06871248781681061, "Y": 0.1335258185863495}]}, "Id": "c731ec7a-3a58-494d-a148-0a8e576db4b9"}, {"BlockType": "WORD", "Confidence": 99.41346740722656, "Text": "V", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.010259142145514488, "Height": 0.009661417454481125, "Left": 0.1361379623413086, "Top": 0.12376632541418076}, "Polygon": [{"X": 0.1361379623413086, "Y": 0.12376632541418076}, {"X": 0.14639708399772644, "Y": 0.12376637011766434}, {"X": 0.14639711380004883, "Y": 0.13342773914337158}, {"X": 0.13613800704479218, "Y": 0.133427694439888}]}, "Id": "11a6c3d0-6be7-4b02-ae4d-4b4ba7aa03d3"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "Net", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.02389880083501339, "Height": 0.009710069745779037, "Left": 0.1513485461473465, "Top": 0.12388858199119568}, "Polygon": [{"X": 0.1513485461473465, "Y": 0.12388858199119568}, {"X": 0.17524731159210205, "Y": 0.12388868629932404}, {"X": 0.17524734139442444, "Y": 0.13359865546226501}, {"X": 0.15134857594966888, "Y": 0.13359855115413666}]}, "Id": "ae080999-6d8c-4237-85e2-f2bea0f67b5c"}, {"BlockType": "WORD", "Confidence": 99.9609375, "Text": "60", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.016759997233748436, "Height": 0.009518975391983986, "Left": 0.17967595160007477, "Top": 0.12406382709741592}, "Polygon": [{"X": 0.17967595160007477, "Y": 0.12406382709741592}, {"X": 0.19643591344356537, "Y": 0.1240638941526413}, {"X": 0.19643594324588776, "Y": 0.13358280062675476}, {"X": 0.17967596650123596, "Y": 0.1335827261209488}]}, "Id": "2cc4e527-8b7e-4346-bfe2-6b5d245eb883"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Date", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03239922970533371, "Height": 0.009803312830626965, "Left": 0.8189185857772827, "Top": 0.12386447191238403}, "Polygon": [{"X": 0.8189186453819275, "Y": 0.12386447191238403}, {"X": 0.851317822933197, "Y": 0.12386460602283478}, {"X": 0.8513177633285522, "Y": 0.13366778194904327}, {"X": 0.8189185857772827, "Y": 0.13366764783859253}]}, "Id": "0e345978-4996-4576-a5e9-d47f4f461c7b"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "09/17/25", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.06048210710287094, "Height": 0.009667715057730675, "Left": 0.9081143736839294, "Top": 0.12391699105501175}, "Polygon": [{"X": 0.9081144332885742, "Y": 0.12391699105501175}, {"X": 0.9685964584350586, "Y": 0.12391724437475204}, {"X": 0.9685963988304138, "Y": 0.13358470797538757}, {"X": 0.9081143736839294, "Y": 0.13358443975448608}]}, "Id": "54bce926-69b1-44f5-b8b0-15e11d4bd6db"}, {"BlockType": "WORD", "Confidence": 99.990234375, "Text": "#", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.008948232047259808, "Height": 0.009573709219694138, "Left": 0.06128663942217827, "Top": 0.21394795179367065}, "Polygon": [{"X": 0.06128663942217827, "Y": 0.21394795179367065}, {"X": 0.07023482769727707, "Y": 0.21394799649715424}, {"X": 0.07023487240076065, "Y": 0.2235216498374939}, {"X": 0.06128668412566185, "Y": 0.22352160513401031}]}, "Id": "7b097c09-6b3b-42dd-8a3d-6841cb1c1fe2"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Description", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08462873101234436, "Height": 0.011947846040129662, "Left": 0.08678919076919556, "Top": 0.21392518281936646}, "Polygon": [{"X": 0.08678919076919556, "Y": 0.21392518281936646}, {"X": 0.17141789197921753, "Y": 0.21392562985420227}, {"X": 0.17141792178153992, "Y": 0.22587302327156067}, {"X": 0.08678924292325974, "Y": 0.22587257623672485}]}, "Id": "0aa4dd23-6d44-4b5d-a268-87f2757fbcee"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Price", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03718823939561844, "Height": 0.009635903872549534, "Left": 0.6918730139732361, "Top": 0.21382339298725128}, "Polygon": [{"X": 0.6918730735778809, "Y": 0.21382339298725128}, {"X": 0.7290612459182739, "Y": 0.2138235867023468}, {"X": 0.7290611863136292, "Y": 0.22345928847789764}, {"X": 0.6918730139732361, "Y": 0.22345909476280212}]}, "Id": "c258ac83-1f5c-401c-8874-1912f9920c25"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03688494861125946, "Height": 0.009917698800563812, "Left": 0.8682219982147217, "Top": 0.21372097730636597}, "Polygon": [{"X": 0.8682220578193665, "Y": 0.21372097730636597}, {"X": 0.9051069021224976, "Y": 0.2137211710214615}, {"X": 0.9051068425178528, "Y": 0.22363866865634918}, {"X": 0.8682219982147217, "Y": 0.22363847494125366}]}, "Id": "d25f4caf-4c29-4ad9-862a-56b40376c72c"}, {"BlockType": "WORD", "Confidence": 99.873046875, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004654270596802235, "Height": 0.009236345067620277, "Left": 0.0626901313662529, "Top": 0.2377956658601761}, "Polygon": [{"X": 0.0626901313662529, "Y": 0.2377956658601761}, {"X": 0.06734436005353928, "Y": 0.23779569566249847}, {"X": 0.06734440475702286, "Y": 0.2470320165157318}, {"X": 0.06269017606973648, "Y": 0.24703198671340942}]}, "Id": "0924339a-c2d6-4002-b01f-523ff06f22d4"}, {"BlockType": "WORD", "Confidence": 99.26358795166016, "Text": "PS-INT2025-T-2878", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.14050935208797455, "Height": 0.009883232414722443, "Left": 0.08651310205459595, "Top": 0.23745156824588776}, "Polygon": [{"X": 0.08651310205459595, "Y": 0.23745156824588776}, {"X": 0.2270224392414093, "Y": 0.23745234310626984}, {"X": 0.2270224541425705, "Y": 0.2473347932100296}, {"X": 0.08651314675807953, "Y": 0.24733400344848633}]}, "Id": "372dee85-006b-4ab2-a142-8215d6bb1c38"}, {"BlockType": "WORD", "Confidence": 96.506103515625, "Text": "$ 400.00000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08588708192110062, "Height": 0.011642426252365112, "Left": 0.7131158113479614, "Top": 0.23702426254749298}, "Polygon": [{"X": 0.7131158709526062, "Y": 0.23702426254749298}, {"X": 0.7990028858184814, "Y": 0.23702472448349}, {"X": 0.7990028262138367, "Y": 0.2486666887998581}, {"X": 0.7131158113479614, "Y": 0.2486661970615387}]}, "Id": "2df8d1d6-7e59-44d3-817e-8418b74bc74c"}, {"BlockType": "WORD", "Confidence": 95.92613983154297, "Text": "$ 400.00000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08629834651947021, "Height": 0.011887146160006523, "Left": 0.8818829655647278, "Top": 0.2368636578321457}, "Polygon": [{"X": 0.8818830847740173, "Y": 0.2368636578321457}, {"X": 0.968181312084198, "Y": 0.2368641346693039}, {"X": 0.9681811928749084, "Y": 0.24875080585479736}, {"X": 0.8818829655647278, "Y": 0.24875031411647797}]}, "Id": "94114967-2d29-475d-a63f-7e10ab7730be"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Sprinter", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.054710015654563904, "Height": 0.012170244939625263, "Left": 0.053552985191345215, "Top": 0.2690441906452179}, "Polygon": [{"X": 0.053552985191345215, "Y": 0.2690441906452179}, {"X": 0.10826295614242554, "Y": 0.26904451847076416}, {"X": 0.10826300084590912, "Y": 0.2812144458293915}, {"X": 0.05355304479598999, "Y": 0.2812141180038452}]}, "Id": "404fbc40-7202-4de7-8bdf-56353ec78ed3"}, {"BlockType": "WORD", "Confidence": 94.94100952148438, "Text": "<PERSON> -", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.037106022238731384, "Height": 0.00992443785071373, "Left": 0.11206800490617752, "Top": 0.26894083619117737}, "Polygon": [{"X": 0.11206800490617752, "Y": 0.26894083619117737}, {"X": 0.14917398989200592, "Y": 0.26894107460975647}, {"X": 0.1491740196943283, "Y": 0.2788652777671814}, {"X": 0.1120680421590805, "Y": 0.2788650691509247}]}, "Id": "349117b6-634e-4a94-8ca4-384b93cf397d"}, {"BlockType": "WORD", "Confidence": 99.67534637451172, "Text": "Intra", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030405644327402115, "Height": 0.009816109202802181, "Left": 0.15466482937335968, "Top": 0.26899340748786926}, "Polygon": [{"X": 0.15466482937335968, "Y": 0.26899340748786926}, {"X": 0.1850704401731491, "Y": 0.2689935863018036}, {"X": 0.1850704699754715, "Y": 0.278809517621994}, {"X": 0.15466485917568207, "Y": 0.2788093388080597}]}, "Id": "10e1056d-aae4-4375-aecf-9197ebbe34fe"}, {"BlockType": "WORD", "Confidence": 99.93083953857422, "Text": "DEN", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.031373511999845505, "Height": 0.009679088369011879, "Left": 0.18998382985591888, "Top": 0.2689090669155121}, "Polygon": [{"X": 0.18998382985591888, "Y": 0.2689090669155121}, {"X": 0.2213573306798935, "Y": 0.2689092457294464}, {"X": 0.2213573455810547, "Y": 0.2785881459712982}, {"X": 0.18998385965824127, "Y": 0.2785879671573639}]}, "Id": "05fead5c-2f7f-4466-a64a-60c6f916897d"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Total", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03670176491141319, "Height": 0.009836472570896149, "Left": 0.6054854393005371, "Top": 0.2983285188674927}, "Polygon": [{"X": 0.6054854989051819, "Y": 0.2983285188674927}, {"X": 0.642187237739563, "Y": 0.2983287274837494}, {"X": 0.6421871781349182, "Y": 0.3081649839878082}, {"X": 0.6054854393005371, "Y": 0.3081647455692291}]}, "Id": "c6b5a9b8-2553-4477-b6d0-a70b4887ad19"}, {"BlockType": "WORD", "Confidence": 95.65768432617188, "Text": "$ 400.00000", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.08665494620800018, "Height": 0.01172705553472042, "Left": 0.8816199898719788, "Top": 0.2975853681564331}, "Polygon": [{"X": 0.8816201090812683, "Y": 0.2975853681564331}, {"X": 0.9682749509811401, "Y": 0.2975859045982361}, {"X": 0.9682748317718506, "Y": 0.3093124330043793}, {"X": 0.8816199898719788, "Y": 0.3093118667602539}]}, "Id": "15684089-07a5-4ff8-a5c5-707eb94d4b94"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "Page", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.03473186120390892, "Height": 0.011966117657721043, "Left": 0.8081220984458923, "Top": 0.8821167945861816}, "Polygon": [{"X": 0.8081221580505371, "Y": 0.8821167945861816}, {"X": 0.8428539633750916, "Y": 0.8821172118186951}, {"X": 0.842853844165802, "Y": 0.8940829038619995}, {"X": 0.8081220984458923, "Y": 0.8940824270248413}]}, "Id": "a0611384-289c-49a0-8525-7b6e9f1d894e"}, {"BlockType": "WORD", "Confidence": 99.21875, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004734053276479244, "Height": 0.009379422292113304, "Left": 0.8930217623710632, "Top": 0.8818439245223999}, "Polygon": [{"X": 0.8930218815803528, "Y": 0.8818439245223999}, {"X": 0.8977558016777039, "Y": 0.8818439841270447}, {"X": 0.8977557420730591, "Y": 0.8912233710289001}, {"X": 0.8930217623710632, "Y": 0.8912233114242554}]}, "Id": "f7d717ad-7d42-4e50-9e09-ee0bbfaaf654"}, {"BlockType": "WORD", "Confidence": 99.2627944946289, "Text": "/", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.00541149266064167, "Height": 0.009907686151564121, "Left": 0.9240594506263733, "Top": 0.8865861296653748}, "Polygon": [{"X": 0.9240595102310181, "Y": 0.8865861296653748}, {"X": 0.929470956325531, "Y": 0.8865862488746643}, {"X": 0.9294708371162415, "Y": 0.8964938521385193}, {"X": 0.9240594506263733, "Y": 0.8964937925338745}]}, "Id": "6f502d52-f31c-493d-b91c-7fcd22e24cce"}, {"BlockType": "WORD", "Confidence": 99.287109375, "Text": "1", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.004829213954508305, "Height": 0.009200820699334145, "Left": 0.9429680109024048, "Top": 0.882173478603363}, "Polygon": [{"X": 0.9429680705070496, "Y": 0.882173478603363}, {"X": 0.9477972388267517, "Y": 0.8821735382080078}, {"X": 0.9477971196174622, "Y": 0.8913742899894714}, {"X": 0.9429680109024048, "Y": 0.8913742303848267}]}, "Id": "f2940e28-2c9d-4303-af61-523089e1a152"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "Created", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.05471651256084442, "Height": 0.0097710732370615, "Left": 0.08244438469409943, "Top": 0.9061111807823181}, "Polygon": [{"X": 0.08244438469409943, "Y": 0.9061111807823181}, {"X": 0.13716086745262146, "Y": 0.9061118960380554}, {"X": 0.13716089725494385, "Y": 0.9158822298049927}, {"X": 0.08244442939758301, "Y": 0.9158815145492554}]}, "Id": "1768489b-9441-478e-8aa8-676020655b78"}, {"BlockType": "WORD", "Confidence": 99.96014404296875, "Text": "By:", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.021305963397026062, "Height": 0.0121648870408535, "Left": 0.14243151247501373, "Top": 0.9063754081726074}, "Polygon": [{"X": 0.14243151247501373, "Y": 0.9063754081726074}, {"X": 0.1637374311685562, "Y": 0.9063757061958313}, {"X": 0.1637374758720398, "Y": 0.9185402989387512}, {"X": 0.14243154227733612, "Y": 0.9185400009155273}]}, "Id": "05735acd-e52c-4e27-9da8-c4992f8a3a02"}, {"BlockType": "WORD", "Confidence": 100.0, "Text": "<PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.030004138126969337, "Height": 0.009535243734717369, "Left": 0.17112968862056732, "Top": 0.9063012003898621}, "Polygon": [{"X": 0.17112968862056732, "Y": 0.9063012003898621}, {"X": 0.20113380253314972, "Y": 0.9063016176223755}, {"X": 0.2011338323354721, "Y": 0.9158364534378052}, {"X": 0.1711297184228897, "Y": 0.9158360362052917}]}, "Id": "b550a241-f316-4b8d-a4ea-9032b9fffb9e"}, {"BlockType": "WORD", "Confidence": 99.68112182617188, "Text": "<PERSON><PERSON><PERSON>", "TextType": "PRINTED", "Geometry": {"BoundingBox": {"Width": 0.077615886926651, "Height": 0.009708954021334648, "Left": 0.20541757345199585, "Top": 0.9060683846473694}, "Polygon": [{"X": 0.20541757345199585, "Y": 0.9060683846473694}, {"X": 0.28303343057632446, "Y": 0.9060693979263306}, {"X": 0.28303346037864685, "Y": 0.915777325630188}, {"X": 0.20541758835315704, "Y": 0.915776252746582}]}, "Id": "730080f7-4e84-460f-a3d4-5ffd12712c6d"}], "DetectDocumentTextModelVersion": "1.0", "ResponseMetadata": {"RequestId": "5f52ca0b-9691-4392-8f37-d03b5fb7e929", "HTTPStatusCode": 200, "HTTPHeaders": {"x-amzn-requestid": "5f52ca0b-9691-4392-8f37-d03b5fb7e929", "content-type": "application/x-amz-json-1.1", "content-length": "34819", "date": "Wed, 24 Sep 2025 09:00:10 GMT"}, "RetryAttempts": 0}}], "bedrock_response_object": {"ResponseMetadata": {"RequestId": "5583dea9-cf67-4828-83d4-df75faaf766e", "HTTPStatusCode": 200, "HTTPHeaders": {"date": "Wed, 24 Sep 2025 09:00:12 GMT", "content-type": "application/json", "content-length": "646", "connection": "keep-alive", "x-amzn-requestid": "5583dea9-cf67-4828-83d4-df75faaf766e"}, "RetryAttempts": 0}, "output": {"message": {"role": "assistant", "content": [{"reasoningContent": {"reasoningText": {"text": "We need to classify each page. There's only one page. It shows a purchase order. Keywords: \"Purchase Order\". So doc_type = \"po\". No continuation. So output an array with one object: page_no 1, doc_type \"po\". Use tool classify_logistics_doc_type."}}}, {"toolUse": {"toolUseId": "tooluse_cn6WV4FeQjGhoEQ37yabxA", "name": "classify_logistics_doc_type", "input": {"documents": [{"page_no": 1, "doc_type": "po"}]}}}]}}, "stopReason": "tool_use", "usage": {"inputTokens": 2567, "outputTokens": 77, "totalTokens": 2644}, "metrics": {"latencyMs": 525}}, "execution_info": {"textract": {"aws_region": "us-east-1", "file_path": "s3://document-extraction-logistically/temp/b1adc625_A331OW5F2Q6LZDSTAWR6.pdf", "bucket_name": "document-extraction-logistically"}, "bedrock": {"aws_region": "us-west-2", "model_id": "openai.gpt-oss-20b-1:0", "system_prompt": "\n    ## Task Summary:\n        You are an expert document classification AI specialized in classifying logistics documents. Use your intelligence and follow instructions mentioned and output only via the provided tool call `classify_logistics_doc_type`.\n\n    ## Model Instructions:\n        - Examine all keywords present anywhere on the page and use them to infer the document type. If an explicit document-type header exists, use it—but page can lack such headers or multiple headers might be present, so rely on keywords, field labels, and structural cues along with header.\n        - Use **only** the `classify_logistics_doc_type` tool to return results.\n        - For every page in the input PDF you MUST return exactly one object describing that page.\n        - Defination and keywords indication are mentioned below for each document type for reference only.\n        - Do NOT return any extra pages or skip pages. Output pages in ascending page order.\n        - If a page is part of a multi-page single document: each page still gets the same `doc_type` (repeat the type on every page).\n        - If document is not from any of catagories mentioned, classify as `other`. But before that check if it is continuation of previous page. If it is continuation then assign the same `doc_type` as previous page.\n        - Strictly check whether the current page is a continuation of the previous page (and document type) by checking if the page starts with any of the following: \"continued\", \"continued on next page\", \"continued on next\", etc., or if it indicates pagination like \"page 2 of 3\", or any other signal indicating continuation of the previous page/document.\n\n    ## Enum details for doc_type:\n\n        invoice — Carrier Invoice\n        Definition: Bill issued by a carrier for goods being transported.\n        Keywords indication: Invoice, Invoice No, Amount Due, Total Due, Bill To, Bill From, Remit to, etc.\n        Key fields/structure: carrier billing address, shipment ID, line charges, total due.\n        Note: \n            1. Difference between invoice and comm_invoice: If the invoice/receipt has HS/HTS code, Country of Origin, terms of sale (inco terms, FOB, etc.), etc. then it is comm_invoice; otherwise, it is invoice.\n            2. Difference between invoice and lumper_receipt: If the invoice/receipt has lumper-related descriptions or keywords, then it is lumper_receipt; otherwise, it is invoice.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments.\n        Keywords indication: HS or HTS Code, Country of Origin, terms of sale (inco terms, FOB, etc.), Customs Value, declared value, importer/exporter details.\n\n        lumper_receipt — Lumper Receipt \n        Definition: Invoice or Receipt for services provided (loading/unloading labor).\n        Keywords indication: PO, Door number, Date-Time, Initial pallets, finished pallets, carrier, service charge, total amount\n\n        bol — Bill of Lading\n        Definition: Legal transport document issued by carrier to shipper describing goods, quantity, consignee and terms; acts as receipt and contract.\n        Keywords indication: \"Bill of Lading\", B/L, Shipper, Consignee, Notify Party, Vessel, Port of Loading\n        Key fields/structure: shipper/consignee blocks, cargo description rows, marks & numbers, carrier signature, BOL number.\n\n        pod — Proof of Delivery\n        Definition: Record confirming recipient received goods; typically contains signatures, dates, and condition notes.\n        Keywords indication: \"Proof of Delivery\", \"Delivery Ticket\", POD, Received by, Delivered, Delivery Receipt, Date Received\n        Key fields/structure: signature/date block, delivery address, condition remarks, signature line.\n\n        rate_confirmation — Carrier Rate Confirmation\n        Definition: Agreement from a carrier confirming rate/terms for a specific load.\n        Keywords indication: \"Carrier Rate Confirmation\", Carrier Rate, Rate Confirmation, Carrier:\n        Key fields/structure: carrier name, load/date/pickup/delivery, rate breakdown, signature from carrier.\n            \n        cust_rate_confirmation — Customer Rate Confirmation\n        Definition: Rate confirmation directed at/issued to the customer; customer-focused pricing/terms.\n        Keywords indication: \"Customer Rate Confirmation\", Customer Rate, Quote to\n        Key fields/structure: customer name, quoted rates, validity dates, customer signature/approval.\n\n        clear_to_pay — Clear to Pay\n        Definition: Authorization indicating invoice is approved for payment.\n        Keywords indication: \"Clear to Pay\", Approved for Payment, Payment Authorization, Clear to Pay Stamp\n        Key fields/structure: approval stamps, audit/verification notes, approver name/date.\n\n        scale_ticket — Scale Ticket\n        Definition: Weight record from a scale for vehicle/load (used for billing/compliance).\n        Keywords indication: Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no.\n        Note: A scale ticket may contain keywords like Bill of lading, Invoice, etc., but if it contains weight-related keywords like Gross, Tare, Net, Weighed At, Truck, Driver, scale location, weight (lb or lbs or Ton), Date, Time, Ticket no., then it is a scale ticket.\n\n        log — Log\n        Definition: 1. Activity record (driver log, tracking log) with dock time for operational/regulatory use or 2. Temperature log/reading or 3. Driver detention certificate\n        Keywords indication: (not necessary that all keys will be present)\n            1. Activity record: Driver Log, Logbook, Hours of Service, HOS, Activity, Timestamp, driver/vehicle IDs, mileage or status entries, Pick up, Delivery, Dock, etc.\n            2. Temperature log/reading: Temperature, degree fehrenheit, degree celsius, humidity, etc.\n            3. Driver detention certificate: Detention Date and Time, Detention Amount\n\n        fuel_receipt — Fuel Receipt\n        Definition: Receipt for fuel purchase (expense item).\n        Keywords indication: Fuel, Diesel, Pump #, Gallons, Ltrs, Fuel Receipt\n        Key fields/structure: fuel quantity, unit price, station name/address, payment method.\n\n        combined_carrier_documents — Combined Carrier Documents\n        Definition: Page containing multiple carrier documents bundled together (BOL + invoice + POD, etc.).\n        Keywords indication: multiple distinct document headers on one page, Bundle, Combined\n        Key fields/structure: visual splits, multiple headers, cover sheet referencing multiple docs.\n\n        pack_list — Packing List\n        Definition: Itemized list of shipment contents for inventory/receiving checks.\n        Keywords indication: Packing List, Pack List, Contents, Qty, Item Description\n        Key fields/structure: SKU/description rows, package counts, net/gross units.\n\n        po — Purchase Order\n        Definition: Buyer's order to a seller specifying items, qty, and price.\n        Keywords indication: Purchase Order, PO#, Buyer, PO Number\n        Key fields/structure: PO number, buyer/seller blocks, line item quantities/prices.\n\n        comm_invoice — Commercial Invoice\n        Definition: Customs-focused invoice for international shipments (value, HS codes).\n        Keywords indication: Commercial Invoice, HS Code, Country of Origin, Customs Value\n        Key fields/structure: declared value, HS codes, importer/exporter details.\n\n        customs_doc — Customs Document, Certificate of Origin\n        Definition: General customs paperwork (declarations, certificates, permits).\n        Keywords indication: Customs, Declaration, Import/Export Declaration, Customs Broker\n        Key fields/structure: declaration forms, license numbers\n\n        weight_and_inspection_cert - Weight and Inspection Certificate\n        Definition: Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted\n        Keywords indication: Certificate of Analysis, Measurement Certificate, Weight Certificate, Inspection certificate, W&I, Calibration Certificate\n        Note: \n            1. Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert.\n            2. Strickly check if inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify it as inspection_cert.\n\n        inspection_cert - Inspection Certificate, Cube Measurement certificate\n        Definition: Inspection certificate other than weight and inspection certificate which doesn't have weight mentioned.\n\n        nmfc_cert — NMFC Classification Certificate or Correction notice or Weight & inspection certificate but strickly with Inspected against original or Corrected against Actual\n        Definition: When correction is made in weight_and_inspection_cert, nmfc_cert is issued. \n        Keywords indication: As described and As found or Original and inspection or Corrected class or Correction information\n        Other keywords indication (Optional):  NMFC Code, class #\n\n        other — Other\n        Definition: Any logistics-related page that doesn't fit the above categories. Use sparingly.\n        Keywords indication: none specific.\n        Key fields/structure: photos, ads, ambiguous notes, or unreadable pages.\n\n        tender_from_cust — Load Tender from Customer\n        Definition: Customer's load tender or request to a carrier to transport a load.\n        Keywords indication: Load Tender, Tender, Tender from, Request to Carrier\n        Key fields/structure: tender number, pickup/delivery instructions, special requirements.\n\n        so_confirmation — Sales Order Confirmation or Order acknowledgement\n        Definition: Seller's confirmation of a sales order (acknowledges order details).\n        Keywords indication: Sales Order Confirmation, SO#, Order Confirmation\n        Key fields/structure: SO number, confirmed quantities/dates/prices.\n\n        ingate — Ingate Document\n        Definition: Record of vehicle/container entering a facility (gate-in).\n        Keywords indication: Equipment In-Gated, Arrival Activity, Time In, Truck In, Entry Time, Ingate Road-In\n        Key fields/structure: truck/container number, time-in stamp, inspection notes, seal #.\n\n        outgate — Outgate Document\n        Definition: Record of vehicle/container exiting a facility (gate-out/release).\n        Keywords indication: Outgate, Departed, Gate Out, Time Out, Release, Exit Time\n        Key fields/structure: release stamp, exit time, fees, container/truck number.\n", "user_prompt": "<page1>\nMRB Logistics, LLC\nIntegris Lighting\n620 S Belt Line Rd\nPurchase Order\nIrving TX 75060\n44426\nTerms: V Net 60\nDate\n09/17/25\n# Description\nPrice\nTotal\n1\nPS-INT2025-T-2878\n$ 400.00000\n$ 400.00000\nSprinter Van - Intra DEN\nTotal\n$ 400.00000\nPage\n1 / 1\nCreated By: <PERSON>\n</page1>", "tool_call": {"tools": [{"toolSpec": {"name": "classify_logistics_doc_type", "description": "Classify logistics document type from multi-page documents", "inputSchema": {"json": {"type": "object", "properties": {"documents": {"type": "array", "description": "Array of extracted document type summaries from the multi-page document", "items": {"type": "object", "properties": {"page_no": {"type": "integer", "description": "Current page no for which the document type is mentioned"}, "doc_type": {"type": "string", "enum": ["invoice", "bol", "pod", "rate_confirmation", "cust_rate_confirmation", "clear_to_pay", "scale_ticket", "pack_list", "tender_from_cust", "po", "ingate", "outgate", "log", "fuel_receipt", "combined_carrier_documents", "comm_invoice", "customs_doc", "other", "lumper_receipt", "so_confirmation", "weight_and_inspection_cert", "inspection_cert", "nmfc_cert"], "description": "For detailed description of each enum, refer to the system prompt"}}, "required": ["page_no", "doc_type"]}}}, "required": ["documents"]}}}}]}, "reasoning_effort": "medium", "temperature": 0.7, "max_tokens": 1000}}}