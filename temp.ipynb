import re
import json
import ast
from typing import Any, Dict

def extract_json_from_backticks(text: str) -> Dict[str, Any]:
    """
    Extract JSON from text - handles both backtick-fenced and plain JSON.
    - First tries to find <PERSON><PERSON><PERSON> enclosed in triple backticks (``` ... ```).
    - If no backticks found, tries to parse the entire text as JSON.
    - Accepts optional fence label (e.g. ```json or ```json\n).
    - Tries json.loads first, then tries extracting {...} or [...] slices,
      then falls back to a best-effort ast.literal_eval after simple literal replacements.
    - Never raises: returns a dict with 'working_json' True and 'data' on success,
      or 'working_json' False with 'error' and the extracted 'content' on failure.

    Returns:
      {'working_json': True, 'data': <parsed object>}
      OR
      {'working_json': False, 'error': 'message', 'content': '<extracted_text (trimmed)>'}
    """
    # First try to find fenced block with triple backticks
    fence_pat = re.compile(r'```(?:[^\n]*)\n?(.*?)```', re.DOTALL)
    m = fence_pat.search(text)

    if m:
        # Found backtick-fenced content
        content = m.group(1)
        stripped = content.strip()
    else:
        # No backticks found, try to parse the entire text as JSON
        stripped = text.strip()

    # 1) Try strict JSON
    try:
        parsed = json.loads(stripped)
        return {'working_json': True, 'data': parsed}
    except json.JSONDecodeError as first_err:
        last_err = str(first_err)

    # 2) Try to locate a JSON object/array substring within the content (first {...} or [...])
    for start_ch, end_ch in (('{', '}'), ('[', ']')):
        s_idx = stripped.find(start_ch)
        e_idx = stripped.rfind(end_ch)
        if s_idx != -1 and e_idx != -1 and e_idx > s_idx:
            candidate = stripped[s_idx:e_idx+1].strip()
            try:
                parsed = json.loads(candidate)
                return {'working_json': True, 'data': parsed}
            except json.JSONDecodeError as e:
                last_err = f"{last_err}; fallback slice JSON error: {e}"

    # 3) Best-effort Python-literal parse (handles single quotes, trailing commas sometimes)
    #    Convert JS literals to Python equivalents (simple replacement).
    #    This may modify inside strings in extreme cases, but it's a pragmatic fallback.
    try:
        alt = re.sub(r'\bnull\b', 'None', stripped, flags=re.IGNORECASE)
        alt = re.sub(r'\btrue\b', 'True', alt, flags=re.IGNORECASE)
        alt = re.sub(r'\bfalse\b', 'False', alt, flags=re.IGNORECASE)

        # Attempt to isolate braces/brackets first (if present)
        s_idx = alt.find('{')
        e_idx = alt.rfind('}')
        if s_idx != -1 and e_idx != -1 and e_idx > s_idx:
            alt_candidate = alt[s_idx:e_idx+1]
        else:
            s_idx = alt.find('[')
            e_idx = alt.rfind(']')
            alt_candidate = alt[s_idx:e_idx+1] if (s_idx != -1 and e_idx != -1 and e_idx > s_idx) else alt

        parsed = ast.literal_eval(alt_candidate)
        return {'working_json': True, 'data': parsed}
    except Exception as e:
        last_err = f"{last_err}; ast fallback error: {e}"

    # If everything fails, return failure with debugging info (trim content for safety)
    debug_content = stripped
    return {
        'working_json': False,
        'error': 'Failed to parse JSON from text; last error: ' + last_err,
        'content': debug_content
    }


response = {'ResponseMetadata': {'RequestId': '384c8b9c-3eaa-48e7-aa88-ebf469976d78', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 09:19:03 GMT', 'content-type': 'application/json', 'content-length': '4974', 'connection': 'keep-alive', 'x-amzn-requestid': '384c8b9c-3eaa-48e7-aa88-ebf469976d78'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a PDF with two pages. Need to classify each page.\n\nThe content looks like a weight and research program (maybe weight and inspection certificate). The header: "Weight and Research Program" and terms like "We certify that this shipment has been inspected and/or reweighed" and "Weight and Research Section". Also includes "As found upon Examination". This likely is a "weight_and_inspection_cert" or "inspection_cert" or "nmfc_cert" if correction. The description includes "Original" and "Correction". It says "Original Correction". It shows "Original Correction" date. It shows "We certify that this shipment has been inspected and/or reweighed". That matches weight and inspection certificate. Does it mention any correction? The header says "Original Correction". But does it have "Original" or "Correction"? It says "Original Correction" maybe means original or corrected. But no explicit "Original" vs "Correction" section. The text: "Original Correction 9/04/25". It might be a corrected certificate. The instruction for weight_and_inspection_cert: If it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert otherwise keep as weight_and_inspection_cert. Also check if inspection certificate is related to weight and has weight mentioned in lbs or tons classify as weight_and_inspection_cert; else inspection_cert.\n\nWe see weight details: "Weight and Research Program". The weight is indicated: "Weights", "Gross", "Tare" maybe? It lists "Weights" but not explicit weight numbers. It does show "CUBE 22.84 UNITS 1" and "51.00x43.00x18.00" and "UPD PER ITEM 111460 DENS14.97 PCF". It mentions "WEIGHT" maybe? It says "Weight and Research Section". It doesn\'t show explicit weight numbers like "Gross: 500 lbs". But might still be weight and inspection certificate. The presence of "Original Correction" suggests a corrected certificate. For nmfc_cert, keywords: "Original and inspection" or "Corrected class" etc. The text says: "As found upon Examination" and "We certify that this shipment has been inspected and/or reweighed". This seems like a certificate of inspection. It doesn\'t explicitly mention "Original and inspection" or "Corrected". But it says "Original Correction". That might be a combined phrase. But the actual header says "Weight and Research Program". The doc type likely "weight_and_inspection_cert".\n\nBut check if any nmfc_cert keywords present: "As found upon Examination" is part of weight_and_inspection_cert description. But nmfc_cert keywords: "As found or Original and inspection or Corrected class or Correction information". The phrase "Original Correction" might be a keyword? But the guidelines say "If it has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep as weight_and_inspection_cert." The provided keywords for nmfc_cert: "As found or Original and inspection or Corrected class or Correction information". It says "As found upon Examination" appears. That phrase "As found" is a keyword. That might lead to nmfc_cert classification. But also weight_and_inspection_cert also has "As found upon Examination" as part of its description. Actually weight_and_inspection_cert description: "If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify as weight_and_inspection_cert; otherwise inspection_cert." But it also says "If it has keywords that are mentioned in nmfc_cert, classify as nmfc_cert else keep as weight_and_inspection_cert." That means if we see nmfc_cert keywords, we choose nmfc_cert. The phrase "As found upon Examination" is listed under weight_and_inspection_cert too? Let\'s re-read: In weight_and_inspection_cert description: "If inspection certificate is related to weight and has weight mentioned in lbs or tons, classify it as weight_and_inspection_cert; otherwise, classify as inspection_cert." Then the note: "Strickly check if weight_and_inspection_cert is having keywords that are mentioend in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." The nmfc_cert keywords: "As found or Original and inspection or Corrected class or Correction information". So "As found" appears in weight_and_inspection_cert as well? Actually weight_and_inspection_cert description didn\'t mention "As found". But the note says if weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, then classify as nmfc_cert. So if we see "As found" on the page, we might classify as nmfc_cert. The page has "As found upon Examination". That matches "As found" keyword. So likely nmfc_cert.\n\nBut the page also includes "Original'}}}]}}, 'stopReason': 'max_tokens', 'usage': {'inputTokens': 3302, 'outputTokens': 997, 'totalTokens': 4299}, 'metrics': {'latencyMs': 4283}}

response

if response['stopReason'] == 'end_turn':
    if len(response['output']['message']['content']) > 1:
        content_in_string = response['output']['message']['content'][1]["text"]
    else:
        content_in_string = response['output']['message']['content'][0]["text"]
    content = extract_json_from_backticks(content_in_string)["data"]
    print(content)



response2 = {'ResponseMetadata': {'RequestId': '76698621-4387-44a3-b0c0-51c1e3ca08f1', 'HTTPStatusCode': 200, 'HTTPHeaders': {'date': 'Wed, 24 Sep 2025 08:20:46 GMT', 'content-type': 'application/json', 'content-length': '4181', 'connection': 'keep-alive', 'x-amzn-requestid': '76698621-4387-44a3-b0c0-51c1e3ca08f1'}, 'RetryAttempts': 0}, 'output': {'message': {'role': 'assistant', 'content': [{'reasoningContent': {'reasoningText': {'text': 'We have a multi-page document. It appears to be a "Weight and Research Program" document. The content: includes "Weight and Research Program" header. It has weight, density, cube, etc. The description says "We certify that this shipment has been inspected and/or reweighed ...". It\'s a weight & research program. It seems like a "Weight and Inspection Certificate" or "Weight and Inspection Certificate"? However the definitions: weight_and_inspection_cert is a certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted. Not exactly. There\'s also nmfc_cert which is a NMFC Classification Certificate or Correction notice or Weight & inspection certificate but with "Inspected against original or Corrected against Actual". The document has "As found upon Examination" and "We certify that this shipment has been inspected and/or reweighed". That could be a weight and inspection certificate. The content also includes "Cube" measurement. So it fits weight_and_inspection_cert. The classification: weight_and_inspection_cert. But we need to check if nmfc_cert: "If weight_and_inspection_cert has keywords that are mentioned in nmfc_cert, classify it as nmfc_cert else keep it as weight_and_inspection_cert." What are nmfc_cert keywords? It says: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued. Keywords indication: As described or As found or Original and inspection or Corrected class or Correction information. Other keywords optional: NMFC Code, class #." The doc has "As found upon Examination" phrase. That might be a keyword for nmfc_cert. But does it have NMFC code? Not seeing. But "As found upon Examination" is in the doc. That phrase appears in the page.\n\nI think weight_and_inspection_cert is correct. But check nmfc_cert: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." The doc says "Original Correction" at top. There\'s a field "Original Correction" and "We certify that this shipment has been inspected and/or reweighed". That seems like a correction. It states "Original Correction 9/04/25". That might indicate a correction. The presence of "As found upon Examination" might be a key. But it\'s not "Original and inspection" or "Corrected class". It doesn\'t show NMFC code. The phrase "As found upon Examination" may be a keyword for nmfc_cert. The doc also says "Original Correction". This might indeed be a correction notice. But we don\'t see NMFC code. The definition says "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." So if this is a correction, it might be nmfc_cert. But do we have evidence? There\'s "Original Correction" and "We certify ... inspected". But the phrase "As found upon Examination" suggests it\'s a "weight & inspection certificate"? But it\'s possibly a correction.\n\nLet\'s read the spec: weight_and_inspection_cert: "Certificate/Report issued by authority confirming weight possibly with length, width and height but not rechecked or reweighted". nmfc_cert: "When correction is made in weight_and_inspection_cert, nmfc_cert is issued." So if it\'s a correction, it\'s nmfc_cert. But we need to see if it\'s a correction. It has "Original Correction" header, "Date of Issuing" and "Original Correction". The doc has "Date of Issuing" and "Original Correction". So yes, it\'s a corrected weight & inspection certificate. The phrase "As found upon Examination" appears. So likely nmfc_cert.\n\nLet\'s choose nmfc_cert. The doc is multi-page: page1 and page2. So both pages get same doc_type: nmfc_cert.\n\nWe need to output via tool call classify_logistics_doc_type with array of objects: each has page_no and doc_type. For 2 pages.\n\nLet\'s produce that.'}}}, {'text': '{"documents":[{"page_no":1,"doc_type":"nmfc_cert"},{"page_no":2,"doc_type":"nmfc_cert"}]}'}]}}, 'stopReason': 'end_turn', 'usage': {'inputTokens': 3299, 'outputTokens': 846, 'totalTokens': 4145}, 'metrics': {'latencyMs': 3751}}



import boto3

boto3.__version__

